{"timestamp": "2025-08-05T17:38:52.944Z", "files": {".env": {"exists": true, "varCount": 10, "vars": ["VITE_APP_NAME", "VITE_APP_VERSION", "VITE_APP_DESCRIPTION", "VITE_ENABLE_CONSOLE_LOG", "VITE_ENABLE_DEVICE_FINGERPRINT", "VITE_ENABLE_TOKEN_REFRESH", "VITE_REQUEST_TIMEOUT", "VITE_REQUEST_RETRY_COUNT", "VITE_ENABLE_HTTPS_ONLY", "VITE_ENABLE_CSRF_PROTECTION"]}, ".env.development": {"exists": true, "varCount": 18, "vars": ["VITE_API_BASE_URL", "VITE_APP_ENV", "VITE_APP_NAME", "VITE_APP_VERSION", "VITE_LOG_LEVEL", "VITE_ENABLE_CONSOLE_LOG", "VITE_ENABLE_MOCK", "VITE_ENABLE_DEBUG_TOOLS", "VITE_ENABLE_HOT_RELOAD", "VITE_ENABLE_SOURCE_MAP", "VITE_ENABLE_PERFORMANCE_MONITOR", "VITE_ENABLE_HTTPS_ONLY", "VITE_ENABLE_STRICT_MODE", "VITE_CAPTCHA_ENABLED", "VITE_CAPTCHA_DEBUG", "VITE_IP_SERVICE_URL", "VITE_DEVICE_FINGERPRINT_DEBUG", "VITE_DEVICE_FINGERPRINT_CACHE_TTL"]}, ".env.production": {"exists": true, "varCount": 22, "vars": ["VITE_API_BASE_URL", "VITE_APP_ENV", "VITE_APP_NAME", "VITE_APP_VERSION", "VITE_LOG_LEVEL", "VITE_ENABLE_CONSOLE_LOG", "VITE_ENABLE_MOCK", "VITE_ENABLE_DEBUG_TOOLS", "VITE_ENABLE_HOT_RELOAD", "VITE_ENABLE_SOURCE_MAP", "VITE_ENABLE_PERFORMANCE_MONITOR", "VITE_ENABLE_HTTPS_ONLY", "VITE_ENABLE_STRICT_MODE", "VITE_ENABLE_CSRF_PROTECTION", "VITE_CAPTCHA_ENABLED", "VITE_CAPTCHA_DEBUG", "VITE_CAPTCHA_API_URL", "VITE_IP_SERVICE_URL", "VITE_DEVICE_FINGERPRINT_DEBUG", "VITE_DEVICE_FINGERPRINT_CACHE_TTL", "VITE_CDN_BASE_URL", "VITE_STATIC_RESOURCE_VERSION"]}, ".env.staging": {"exists": true, "varCount": 23, "vars": ["VITE_API_BASE_URL", "VITE_APP_ENV", "VITE_APP_NAME", "VITE_APP_VERSION", "VITE_LOG_LEVEL", "VITE_ENABLE_CONSOLE_LOG", "VITE_ENABLE_MOCK", "VITE_ENABLE_DEBUG_TOOLS", "VITE_ENABLE_HOT_RELOAD", "VITE_ENABLE_SOURCE_MAP", "VITE_ENABLE_PERFORMANCE_MONITOR", "VITE_ENABLE_HTTPS_ONLY", "VITE_ENABLE_STRICT_MODE", "VITE_ENABLE_CSRF_PROTECTION", "VITE_CAPTCHA_ENABLED", "VITE_CAPTCHA_DEBUG", "VITE_CAPTCHA_API_URL", "VITE_IP_SERVICE_URL", "VITE_DEVICE_FINGERPRINT_DEBUG", "VITE_DEVICE_FINGERPRINT_CACHE_TTL", "VITE_ENABLE_TEST_DATA", "VITE_ENABLE_AUTO_LOGIN", "VITE_TEST_USER_TOKEN"]}, ".env.local.example": {"exists": true, "varCount": 12, "vars": ["VITE_API_BASE_URL", "VITE_DEVELOPER_NAME", "VITE_DEVELOPER_EMAIL", "VITE_ENABLE_CONSOLE_LOG", "VITE_ENABLE_VCONSOLE", "VITE_ENABLE_ERUDA", "VITE_ENABLE_MOCK", "VITE_MOCK_USER_ID", "VITE_TEST_API_DELAY", "VITE_CAPTCHA_API_URL", "VITE_IP_SERVICE_URL", "VITE_ENABLE_DEBUG_TOOLS"]}}, "summary": {"totalFiles": 5, "existingFiles": 5, "totalVars": 85, "missingRequired": 0}}