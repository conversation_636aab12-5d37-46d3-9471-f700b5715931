{"name": "uni-preset-vue", "version": "0.0.0", "scripts": {"dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-xhs": "uni -p mp-xhs", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-xhs": "uni build -p mp-xhs", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "type-check": "vue-tsc --noEmit", "lint": "eslint src --ext .ts,.vue", "lint:fix": "eslint src --ext .ts,.vue --fix", "env:dev": "cross-env NODE_ENV=development", "env:prod": "cross-env NODE_ENV=production", "env:staging": "cross-env NODE_ENV=staging", "build:dev": "cross-env NODE_ENV=development uni build", "build:staging": "cross-env NODE_ENV=staging uni build", "build:prod": "cross-env NODE_ENV=production uni build", "config:validate": "node scripts/validate-config.js", "config:check": "node scripts/check-env.js", "env:copy": "cp .env.local.example .env.local", "postinstall": "npm run config:validate"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4070520250711001", "@dcloudio/uni-app-harmony": "3.0.0-4070520250711001", "@dcloudio/uni-app-plus": "3.0.0-4070520250711001", "@dcloudio/uni-components": "3.0.0-4070520250711001", "@dcloudio/uni-h5": "3.0.0-4070520250711001", "@dcloudio/uni-mp-alipay": "3.0.0-4070520250711001", "@dcloudio/uni-mp-baidu": "3.0.0-4070520250711001", "@dcloudio/uni-mp-harmony": "3.0.0-4070520250711001", "@dcloudio/uni-mp-jd": "3.0.0-4070520250711001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4070520250711001", "@dcloudio/uni-mp-lark": "3.0.0-4070520250711001", "@dcloudio/uni-mp-qq": "3.0.0-4070520250711001", "@dcloudio/uni-mp-toutiao": "3.0.0-4070520250711001", "@dcloudio/uni-mp-weixin": "3.0.0-4070520250711001", "@dcloudio/uni-mp-xhs": "3.0.0-4070520250711001", "@dcloudio/uni-quickapp-webview": "3.0.0-4070520250711001", "@dcloudio/uni-ui": "^1.5.7", "@playwright/test": "^1.54.1", "@uni-helper/axios-adapter": "^1.5.2", "axios": "^1.10.0", "crypto-js": "^4.2.0", "pinia": "^2.1.7", "vue": "^3.5.18", "vue-i18n": "^9.1.9"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-4070520250711001", "@dcloudio/uni-cli-shared": "3.0.0-4070520250711001", "@dcloudio/uni-stacktracey": "3.0.0-4070520250711001", "@dcloudio/vite-plugin-uni": "3.0.0-4070520250711001", "@types/crypto-js": "^4.2.2", "@types/node": "^22.15.18", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "@vue/runtime-core": "^3.5.18", "@vue/tsconfig": "^0.1.3", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "glob": "^10.3.10", "got": "^13.0.0", "lru-cache": "^10.2.0", "prettier": "^3.5.3", "rimraf": "^5.0.5", "sass": "^1.89.0", "sass-loader": "^16.0.5", "terser": "^5.39.2", "typescript": "^4.9.4", "vite": "5.2.8", "vue-tsc": "^1.8.27"}, "overrides": {"inflight": "npm:lru-cache@^10.2.0", "phin": "^3.7.1", "rimraf": "^5.0.5", "glob": "^10.3.10", "domexception": "npm:webidl-conversions@^7.0.0", "abab": "npm:@jspm/core@^2.0.1", "w3c-hr-time": "npm:perf_hooks@^0.0.1", "jpeg-js": "^0.4.4", "vue-template-compiler": "^2.7.16", "esbuild": "^0.25.4", "vue-tsc": "^1.8.27", "@intlify/core-base": "^9.9.1", "@intlify/message-resolver": "^9.9.1", "@intlify/message-compiler": "^9.9.1", "@intlify/runtime": "^9.9.1", "@intlify/vue-devtools": "^9.9.1"}}