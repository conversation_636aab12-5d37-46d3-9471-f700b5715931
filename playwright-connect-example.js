// Playwright连接系统Chrome示例
const { chromium } = require('playwright');

async function connectToChrome() {
  try {
    // 方法1: 连接到调试端口
    const browser = await chromium.connectOverCDP('http://localhost:9222');
    console.log('✅ 成功连接到系统Chrome');
    
    const page = await browser.newPage();
    await page.goto('http://localhost:5173');
    
    // 在这里进行你的操作...
    
    await browser.close();
  } catch (error) {
    console.error('❌ 连接失败:', error.message);
    console.log('💡 请先运行: ./start-chrome-debug.sh');
  }
}

// 方法2: 直接启动系统Chrome
async function launchSystemChrome() {
  const browser = await chromium.launch({
    executablePath: '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
    headless: false,
    args: [
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor'
    ]
  });
  
  const page = await browser.newPage();
  await page.goto('http://localhost:5173');
  
  // 在这里进行你的操作...
  
  await browser.close();
}

// 运行示例
if (require.main === module) {
  connectToChrome();
}

module.exports = { connectToChrome, launchSystemChrome };
