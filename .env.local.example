# 本地开发环境配置模板
# 复制此文件为 .env.local 并根据需要修改
# 注意：.env.local 文件会被 Git 忽略，适合存放个人开发配置

# ===========================================
# 本地开发配置（个人定制）
# ===========================================

# API服务器地址（本地开发）
VITE_API_BASE_URL=http://localhost:3000

# 开发者个人配置
VITE_DEVELOPER_NAME=YourName
VITE_DEVELOPER_EMAIL=<EMAIL>

# 本地调试开关
VITE_ENABLE_CONSOLE_LOG=true
VITE_ENABLE_VCONSOLE=true
VITE_ENABLE_ERUDA=false

# 本地Mock配置
VITE_ENABLE_MOCK=true
VITE_MOCK_USER_ID=dev-user-123

# 本地测试配置
VITE_TEST_API_DELAY=500

# 验证码配置
VITE_CAPTCHA_API_URL=http://localhost:8081/captcha

# 第三方服务
VITE_IP_SERVICE_URL=https://api.ipify.org?format=json

# 个人开发工具
VITE_ENABLE_DEBUG_TOOLS=true

# ===========================================
# 注意事项
# ===========================================
# 1. 此文件仅用于本地开发
# 2. 不要提交包含真实密钥的 .env.local 文件
# 3. 生产环境配置应通过 CI/CD 注入