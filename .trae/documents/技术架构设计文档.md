# 乐享生活平台技术架构设计文档

## 1. 架构设计

```mermaid
graph TD
    A[用户终端] --> B[Vue3 + UniApp 前端应用]
    B --> C[Supabase SDK]
    C --> D[Supabase 服务]
    B --> E[第三方服务集成]
    
    subgraph "前端层"
        B
        F[iOS App]
        G[Android App]
        H[微信小程序]
    end
    
    subgraph "服务层 (Supabase提供)"
        D
        I[PostgreSQL 数据库]
        J[实时订阅]
        K[文件存储]
        L[身份认证]
    end
    
    subgraph "外部服务"
        E
        M[地图服务 - 高德/腾讯]
        N[支付服务 - 微信/支付宝]
        O[短信服务 - 阿里云]
        P[实名认证 - 阿里云]
    end
```

## 2. 技术描述

* **前端**: <Vue3@3.x> + UniApp + TypeScript + Pinia + Vite

* **后端**: Supabase (PostgreSQL + 实时API + 认证 + 存储)

* **UI框架**: uni-ui + 自定义组件库

* **状态管理**: Pinia

* **HTTP客户端**: Axios + Supabase Client

* **构建工具**: Vite + HBuilderX

## 3. 路由定义

| 路由                        | 用途                 |
| ------------------------- | ------------------ |
| /pages/home/<USER>
| /pages/service/category   | 服务分类页，展示具体分类下的所有服务 |
| /pages/service/detail     | 服务详情页，服务信息和预约功能    |
| /pages/publish/index      | 发布页面，发布服务单或需求单     |
| /pages/user/mine          | 个人中心，用户信息和订单管理     |
| /pages/auth/login         | 登录页面，用户身份验证        |
| /pages/auth/register      | 注册页面，新用户注册         |
| /pages/auth/certification | 认证页面，服务提供者资质认证     |
| /pages/order/list         | 订单列表，查看所有订单记录      |
| /pages/message/index      | 消息中心，预约通知和系统消息     |
| /pages/massage/index      | 按摩服务页，上门按摩服务列表     |
| /pages/housekeeping/index | 家政服务页，家政服务分类和列表    |

## 4. API定义

### 4.1 核心API

**用户认证相关**

```
POST /auth/v1/signup
```

请求参数:

| 参数名      | 参数类型   | 是否必需 | 描述   |
| -------- | ------ | ---- | ---- |
| email    | string | true | 用户邮箱 |
| password | string | true | 用户密码 |
| phone    | string | true | 手机号码 |

响应参数:

| 参数名     | 参数类型   | 描述     |
| ------- | ------ | ------ |
| user    | object | 用户信息对象 |
| session | object | 会话信息   |

**服务管理相关**

```
POST /rest/v1/services
```

请求参数:

| 参数名         | 参数类型   | 是否必需 | 描述   |
| ----------- | ------ | ---- | ---- |
| title       | string | true | 服务标题 |
| category    | string | true | 服务分类 |
| price       | number | true | 服务价格 |
| description | text   | true | 服务描述 |
| location    | object | true | 服务地点 |

**预约管理相关**

```
POST /rest/v1/bookings
```

请求参数:

| 参数名                   | 参数类型      | 是否必需  | 描述   |
| --------------------- | --------- | ----- | ---- |
| service\_id           | uuid      | true  | 服务ID |
| booking\_time         | timestamp | true  | 预约时间 |
| customer\_address     | text      | true  | 客户地址 |
| special\_requirements | text      | false | 特殊要求 |

示例:

```json
{
  "service_id": "123e4567-e89b-12d3-a456-************",
  "booking_time": "2024-01-15T14:30:00Z",
  "customer_address": "北京市朝阳区xxx小区xxx号",
  "special_requirements": "需要准备按摩油"
}
```

## 5. 服务器架构图

```mermaid
graph TD
    A[客户端/前端] --> B[Supabase Edge Functions]
    B --> C[业务逻辑层]
    C --> D[数据访问层]
    D --> E[(Supabase PostgreSQL)]
    
    subgraph "Supabase 服务"
        B
        C
        D
        E
        F[实时订阅服务]
        G[文件存储服务]
        H[认证服务]
    end
```

## 6. 数据模型

### 6.1 数据模型定义

```mermaid
erDiagram
    USERS ||--o{ SERVICES : provides
    USERS ||--o{ BOOKINGS : makes
    SERVICES ||--o{ BOOKINGS : receives
    USERS ||--o{ CERTIFICATIONS : has
    SERVICES ||--o{ REVIEWS : receives
    BOOKINGS ||--o{ REVIEWS : generates
    
    USERS {
        uuid id PK
        string email
        string phone
        string nickname
        string avatar_url
        string role
        timestamp created_at
        timestamp updated_at
    }
    
    SERVICES {
        uuid id PK
        uuid provider_id FK
        string title
        string category
        text description
        decimal price
        json location
        json available_times
        string status
        timestamp created_at
    }
    
    BOOKINGS {
        uuid id PK
        uuid service_id FK
        uuid customer_id FK
        timestamp booking_time
        text customer_address
        text special_requirements
        string status
        decimal amount
        timestamp created_at
    }
    
    CERTIFICATIONS {
        uuid id PK
        uuid user_id FK
        string cert_type
        string cert_number
        string cert_image_url
        string status
        timestamp verified_at
    }
    
    REVIEWS {
        uuid id PK
        uuid booking_id FK
        uuid reviewer_id FK
        uuid reviewee_id FK
        integer rating
        text comment
        timestamp created_at
    }
```

### 6.2 数据定义语言

**用户表 (users)**

```sql
-- 创建用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20) UNIQUE NOT NULL,
    nickname VARCHAR(50) NOT NULL,
    avatar_url TEXT,
    role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('user', 'provider', 'admin')),
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_role ON users(role);
```

**服务表 (services)**

```sql
-- 创建服务表
CREATE TABLE services (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    provider_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    category VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    location JSONB NOT NULL,
    available_times JSONB,
    images TEXT[],
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_services_provider ON services(provider_id);
CREATE INDEX idx_services_category ON services(category);
CREATE INDEX idx_services_status ON services(status);
CREATE INDEX idx_services_location ON services USING GIN(location);
```

**预约表 (bookings)**

```sql
-- 创建预约表
CREATE TABLE bookings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    service_id UUID NOT NULL REFERENCES services(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    booking_time TIMESTAMP WITH TIME ZONE NOT NULL,
    customer_address TEXT NOT NULL,
    special_requirements TEXT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'completed', 'cancelled')),
    amount DECIMAL(10,2) NOT NULL,
    payment_status VARCHAR(20) DEFAULT 'unpaid' CHECK (payment_status IN ('unpaid', 'paid', 'refunded')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_bookings_service ON bookings(service_id);
CREATE INDEX idx_bookings_customer ON bookings(customer_id);
CREATE INDEX idx_bookings_status ON bookings(status);
CREATE INDEX idx_bookings_time ON bookings(booking_time);
```

**认证表 (certifications)**

```sql
-- 创建认证表
CREATE TABLE certifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    cert_type VARCHAR(50) NOT NULL,
    cert_number VARCHAR(100),
    cert_image_url TEXT,
    real_name VARCHAR(50),
    id_number VARCHAR(20),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    verified_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_certifications_user ON certifications(user_id);
CREATE INDEX idx_certifications_type ON certifications(cert_type);
CREATE INDEX idx_certifications_status ON certifications(status);
```

**权限设置**

```sql
-- 为匿名用户授予基本读取权限
GRANT SELECT ON services TO anon;
GRANT SELECT ON users TO anon;

-- 为认证用户授予完整权限
GRANT ALL PRIVILEGES ON users TO authenticated;
GRANT ALL PRIVILEGES ON services TO authenticated;
GRANT ALL PRIVILEGES ON bookings TO authenticated;
GRANT ALL PRIVILEGES ON certifications TO authenticated;
GRANT ALL PRIVILEGES ON reviews TO authenticated;
```

**初始数据**

```sql
-- 插入服务分类数据
INSERT INTO service_categories (name, icon, color, sort_order) VALUES
('运动健身', '🏃‍♂️', '#FF6B35', 1),
('游戏陪玩', '🎮', '#4A90E2', 2),
('美食聚餐', '🍽️', '#F39C12', 3),
('学习成长', '📚', '#27AE60', 4),
('上门按摩', '💆‍♀️', '#E74C3C', 5),
('家政服务', '🏠', '#9B59B6', 6),
('美容美发', '💄', '#E91E63', 7),
('宠物服务', '🐕', '#FF9800', 8);
```

