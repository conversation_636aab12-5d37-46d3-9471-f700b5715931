# 乐享生活平台重新设计实施方案

## 📋 项目概述

基于用户确认的产品文档和原型图优化方案，本文档制定了详细的技术实施计划，将现有的简单页面升级为功能完整的双向服务聚合平台。

## 🎯 实施目标

### 核心目标
1. **首页金刚位改造**：将现有简单首页改造为金刚位布局的服务发现页面
2. **服务分类体系**：建立完整的服务分类和展示体系
3. **双向角色切换**：实现服务提供者和需求者角色切换功能
4. **认证体系完善**：建立服务提供者认证流程
5. **发布功能实现**：支持服务发布和需求发布

### 技术目标
- 保持现有技术栈：Vue3 + TypeScript + UniApp + Pinia
- 复用现有组件和工具函数
- 遵循项目现有的代码规范和目录结构

## 🏗️ 实施阶段规划

### 第一阶段：基础架构升级（优先级：高）

#### 1.1 数据模型设计
**文件位置**：`src/types/business.ts`
```typescript
// 服务分类
export interface ServiceCategory {
  id: string
  name: string
  icon: string
  description: string
  parentId?: string
  children?: ServiceCategory[]
}

// 服务信息
export interface ServiceInfo {
  id: string
  title: string
  description: string
  categoryId: string
  providerId: string
  price: number
  location: string
  images: string[]
  tags: string[]
  status: 'active' | 'inactive' | 'pending'
  createdAt: string
}

// 用户角色
export interface UserRole {
  userId: string
  role: 'seeker' | 'provider' | 'both'
  isVerified: boolean
  verificationInfo?: VerificationInfo
}
```

#### 1.2 状态管理升级
**文件位置**：`src/store/modules/service.ts`
- 服务分类状态管理
- 用户角色状态管理
- 服务列表状态管理

#### 1.3 API接口设计
**文件位置**：`src/api/service/`
- `categoryService.ts` - 服务分类接口
- `serviceService.ts` - 服务管理接口
- `roleService.ts` - 角色管理接口

### 第二阶段：首页金刚位改造（优先级：高）

#### 2.1 首页组件重构
**目标文件**：`src/pages/home/<USER>

**改造内容**：
1. **头部区域**：
   - 角色切换按钮（寻找/提供）
   - 搜索栏
   - 位置信息

2. **金刚位区域**：
   ```vue
   <view class="service-grid">
     <view class="grid-row">
       <service-item icon="🏃‍♂️" title="运动健身" @click="goToCategory('fitness')" />
       <service-item icon="🎮" title="游戏陪玩" @click="goToCategory('gaming')" />
       <service-item icon="🍽️" title="美食聚餐" @click="goToCategory('dining')" />
       <service-item icon="📚" title="学习成长" @click="goToCategory('learning')" />
     </view>
     <view class="grid-row">
       <service-item icon="💆‍♀️" title="上门按摩" @click="goToCategory('massage')" />
       <service-item icon="🏠" title="家政服务" @click="goToCategory('housekeeping')" />
       <service-item icon="💄" title="美容美发" @click="goToCategory('beauty')" />
       <service-item icon="🐕" title="宠物服务" @click="goToCategory('pet')" />
     </view>
   </view>
   ```

3. **推荐内容区域**：
   - 热门服务推荐
   - 附近服务推荐
   - 个性化推荐

#### 2.2 服务项组件开发
**文件位置**：`src/components/business/ServiceItem.vue`
- 金刚位单个服务项组件
- 支持图标、标题、点击事件
- 响应式设计

### 第三阶段：服务分类页面开发（优先级：高）

#### 3.1 服务分类页面
**文件位置**：`src/pages/service/category.vue`

**功能特性**：
1. 分类服务列表展示
2. 筛选和排序功能
3. 服务卡片展示
4. 分页加载

#### 3.2 服务详情页面
**文件位置**：`src/pages/service/detail.vue`

**功能特性**：
1. 服务详细信息展示
2. 服务提供者信息
3. 预约/联系功能
4. 评价和评论

### 第四阶段：角色切换功能（优先级：中）

#### 4.1 角色切换组件
**文件位置**：`src/components/business/RoleSwitch.vue`

**功能特性**：
1. 寻找者/提供者角色切换
2. 角色状态持久化
3. 界面内容动态调整

#### 4.2 角色相关页面调整
- 首页内容根据角色显示不同内容
- 个人中心显示角色相关功能
- 发布页面根据角色显示不同表单

### 第五阶段：发布功能开发（优先级：中）

#### 5.1 发布页面重构
**目标文件**：`src/pages/service/publish.vue`

**功能模块**：
1. **服务发布**（提供者角色）：
   - 服务类型选择
   - 服务信息填写
   - 价格设置
   - 图片上传
   - 服务地点设置

2. **需求发布**（寻找者角色）：
   - 需求类型选择
   - 需求描述
   - 预算设置
   - 期望地点
   - 期望时间

#### 5.2 发布表单组件
**文件位置**：`src/components/business/PublishForm.vue`
- 动态表单组件
- 表单验证
- 图片上传功能

### 第六阶段：认证体系完善（优先级：中）

#### 6.1 认证页面升级
**目标文件**：`src/pages/auth/certification.vue`

**认证类型**：
1. 基础认证：身份证验证
2. 专业认证：
   - 瑜伽教练认证
   - 健身教练认证
   - 按摩师认证
   - 家政服务认证
   - 美容师认证

#### 6.2 认证状态管理
**文件位置**：`src/store/modules/certification.ts`
- 认证状态跟踪
- 认证进度管理
- 认证结果处理

### 第七阶段：用户体验优化（优先级：低）

#### 7.1 搜索功能
**文件位置**：`src/components/business/SearchBar.vue`
- 智能搜索建议
- 搜索历史
- 热门搜索

#### 7.2 地图集成
**文件位置**：`src/components/business/LocationMap.vue`
- 服务位置展示
- 附近服务查找
- 路线规划

## 📁 新增文件清单

### 页面文件
```
src/pages/
├── service/
│   ├── category.vue          # 服务分类页面
│   ├── detail.vue            # 服务详情页面
│   └── publish.vue           # 发布页面
├── auth/
│   └── certification.vue     # 认证页面
└── search/
    └── result.vue            # 搜索结果页面
```

### 组件文件
```
src/components/business/
├── ServiceItem.vue           # 金刚位服务项
├── ServiceCard.vue           # 服务卡片
├── RoleSwitch.vue           # 角色切换
├── PublishForm.vue          # 发布表单
├── SearchBar.vue            # 搜索栏
├── LocationMap.vue          # 地图组件
└── CertificationForm.vue    # 认证表单
```

### API文件
```
src/api/
├── service/
│   ├── categoryService.ts    # 分类服务
│   ├── serviceService.ts     # 服务管理
│   └── publishService.ts     # 发布服务
├── certification/
│   └── certificationService.ts # 认证服务
└── search/
    └── searchService.ts      # 搜索服务
```

### 状态管理文件
```
src/store/modules/
├── service.ts               # 服务状态
├── role.ts                  # 角色状态
├── certification.ts         # 认证状态
└── search.ts               # 搜索状态
```

### 类型定义文件
```
src/types/
├── business.ts              # 业务类型
├── service.ts               # 服务类型
└── certification.ts         # 认证类型
```

## 🔧 技术实施细节

### 路由配置更新
**文件位置**：`src/pages.json`
```json
{
  "pages": [
    {
      "path": "pages/service/category",
      "style": {
        "navigationBarTitleText": "服务分类"
      }
    },
    {
      "path": "pages/service/detail",
      "style": {
        "navigationBarTitleText": "服务详情"
      }
    },
    {
      "path": "pages/service/publish",
      "style": {
        "navigationBarTitleText": "发布服务"
      }
    }
  ],
  "tabBar": {
    "list": [
      {
        "pagePath": "pages/home/<USER>",
        "text": "发现",
        "iconPath": "static/tabbar/home.png",
        "selectedIconPath": "static/tabbar/home-active.png"
      },
      {
        "pagePath": "pages/service/service",
        "text": "服务广场",
        "iconPath": "static/tabbar/service.png",
        "selectedIconPath": "static/tabbar/service-active.png"
      },
      {
        "pagePath": "pages/service/publish",
        "text": "发布",
        "iconPath": "static/tabbar/publish.png",
        "selectedIconPath": "static/tabbar/publish-active.png"
      },
      {
        "pagePath": "pages/message/message",
        "text": "消息",
        "iconPath": "static/tabbar/message.png",
        "selectedIconPath": "static/tabbar/message-active.png"
      },
      {
        "pagePath": "pages/user/mine/Mine",
        "text": "我的",
        "iconPath": "static/tabbar/mine.png",
        "selectedIconPath": "static/tabbar/mine-active.png"
      }
    ]
  }
}
```

### 样式规范
**文件位置**：`src/styles/business.scss`
```scss
// 金刚位样式
.service-grid {
  padding: 20rpx;
  
  .grid-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30rpx;
  }
  
  .service-item {
    flex: 1;
    text-align: center;
    padding: 20rpx;
    
    .service-icon {
      font-size: 48rpx;
      margin-bottom: 10rpx;
    }
    
    .service-title {
      font-size: 24rpx;
      color: #333;
    }
  }
}

// 角色切换样式
.role-switch {
  display: flex;
  background: #f5f5f5;
  border-radius: 25rpx;
  padding: 4rpx;
  
  .role-btn {
    flex: 1;
    text-align: center;
    padding: 12rpx 0;
    border-radius: 20rpx;
    font-size: 28rpx;
    
    &.active {
      background: #007aff;
      color: white;
    }
  }
}
```

## 📊 实施时间计划

| 阶段 | 预计时间 | 主要交付物 |
|------|----------|------------|
| 第一阶段 | 2-3天 | 数据模型、状态管理、API接口 |
| 第二阶段 | 3-4天 | 首页金刚位改造完成 |
| 第三阶段 | 4-5天 | 服务分类和详情页面 |
| 第四阶段 | 2-3天 | 角色切换功能 |
| 第五阶段 | 4-5天 | 发布功能完整实现 |
| 第六阶段 | 3-4天 | 认证体系完善 |
| 第七阶段 | 2-3天 | 用户体验优化 |

**总计预估时间：20-27天**

## 🎯 下一步行动

### 立即开始的任务
1. **创建业务类型定义文件**
2. **设计服务状态管理模块**
3. **开发金刚位服务项组件**
4. **重构首页为金刚位布局**

### 需要确认的问题
1. 是否需要集成地图功能？
2. 支付功能是否在当前阶段实现？
3. 消息聊天功能的优先级？
4. 是否需要实时通知功能？

## 📝 备注

- 本方案基于现有代码结构制定，最大程度复用现有组件和工具
- 所有新增功能都遵循项目现有的代码规范和架构模式
- 实施过程中会根据实际情况调整优先级和时间安排
- 每个阶段完成后都会进行功能测试和代码审查