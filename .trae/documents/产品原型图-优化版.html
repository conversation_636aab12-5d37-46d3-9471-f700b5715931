<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>乐享生活 - 产品原型图（优化版）</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .phone-container {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: #000;
            border-radius: 25px;
            padding: 8px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
        }
        
        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #FF6B35, #F39C12);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 500;
        }
        
        .header {
            height: 60px;
            background: linear-gradient(135deg, #FF6B35, #F39C12);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            color: white;
        }
        
        .role-switch {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 20px;
            padding: 6px 12px;
            color: white;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .role-switch:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }
        
        .search-bar {
            background: #f8f8f8;
            border-radius: 25px;
            padding: 12px 20px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .search-bar input {
            border: none;
            background: none;
            flex: 1;
            font-size: 14px;
            outline: none;
        }
        
        .diamond-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .diamond-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px 8px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .diamond-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }
        
        .diamond-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-bottom: 8px;
        }
        
        .diamond-text {
            font-size: 12px;
            color: #333;
            text-align: center;
            line-height: 1.2;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        
        .service-card {
            background: white;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .service-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }
        
        .service-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .service-price {
            font-size: 18px;
            font-weight: 700;
            color: #FF6B35;
        }
        
        .service-meta {
            display: flex;
            gap: 15px;
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .service-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
            margin-bottom: 10px;
        }
        
        .service-tags {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .tag {
            background: #f0f0f0;
            color: #666;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
        }
        
        .bottom-nav {
            height: 80px;
            background: white;
            border-top: 1px solid #eee;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding-bottom: 20px;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .nav-item.active {
            color: #FF6B35;
        }
        
        .nav-icon {
            font-size: 20px;
        }
        
        .nav-text {
            font-size: 11px;
        }
        
        .page {
            display: none;
            height: calc(100% - 44px - 80px);
            flex-direction: column;
        }
        
        .page.active {
            display: flex;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s ease;
        }
        
        .form-input:focus {
            border-color: #FF6B35;
        }
        
        .form-textarea {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            outline: none;
            resize: vertical;
            min-height: 80px;
        }
        
        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #FF6B35, #F39C12);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(255,107,53,0.3);
        }
        
        .btn-secondary {
            background: #f8f8f8;
            color: #333;
        }
        
        .certification-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            border: 2px solid #e8f5e8;
        }
        
        .cert-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .cert-status.pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .cert-status.approved {
            background: #d4edda;
            color: #155724;
        }
        
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #FF6B35;
            background: #fff8f5;
        }
        
        /* 金刚位颜色配置 */
        .fitness { background: linear-gradient(135deg, #FF6B35, #FF8A50); }
        .gaming { background: linear-gradient(135deg, #4A90E2, #5BA0F2); }
        .food { background: linear-gradient(135deg, #F39C12, #F7B731); }
        .study { background: linear-gradient(135deg, #27AE60, #2ECC71); }
        .massage { background: linear-gradient(135deg, #E74C3C, #EC7063); }
        .housekeeping { background: linear-gradient(135deg, #9B59B6, #AF7AC5); }
        .beauty { background: linear-gradient(135deg, #E91E63, #F06292); }
        .pet { background: linear-gradient(135deg, #FF9800, #FFB74D); }
        
        .provider-mode {
            background: linear-gradient(135deg, #FF6B35, #F39C12);
        }
        
        .seeker-mode {
            background: linear-gradient(135deg, #4A90E2, #5BA0F2);
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <span>乐享生活</span>
                <span>🔋100%</span>
            </div>
            
            <!-- 首页 -->
            <div class="page active" id="home">
                <div class="header">
                    <h1>乐享生活</h1>
                    <div class="role-switch" onclick="toggleRole()">🔄 切换角色</div>
                </div>
                
                <div class="content">
                    <!-- 搜索栏 -->
                    <div class="search-bar">
                        <span>🔍</span>
                        <input type="text" placeholder="搜索服务或需求...">
                    </div>
                    
                    <!-- 金刚位导航 -->
                    <div class="section-title">服务分类</div>
                    <div class="diamond-grid">
                        <div class="diamond-item" onclick="showPage('fitness')">
                            <div class="diamond-icon fitness">🏃‍♂️</div>
                            <div class="diamond-text">运动健身</div>
                        </div>
                        <div class="diamond-item" onclick="showPage('gaming')">
                            <div class="diamond-icon gaming">🎮</div>
                            <div class="diamond-text">游戏陪玩</div>
                        </div>
                        <div class="diamond-item" onclick="showPage('food')">
                            <div class="diamond-icon food">🍽️</div>
                            <div class="diamond-text">美食聚餐</div>
                        </div>
                        <div class="diamond-item" onclick="showPage('study')">
                            <div class="diamond-icon study">📚</div>
                            <div class="diamond-text">学习成长</div>
                        </div>
                        <div class="diamond-item" onclick="showPage('massage')">
                            <div class="diamond-icon massage">💆‍♀️</div>
                            <div class="diamond-text">上门按摩</div>
                        </div>
                        <div class="diamond-item" onclick="showPage('housekeeping')">
                            <div class="diamond-icon housekeeping">🏠</div>
                            <div class="diamond-text">家政服务</div>
                        </div>
                        <div class="diamond-item" onclick="showPage('beauty')">
                            <div class="diamond-icon beauty">💄</div>
                            <div class="diamond-text">美容美发</div>
                        </div>
                        <div class="diamond-item" onclick="showPage('pet')">
                            <div class="diamond-icon pet">🐕</div>
                            <div class="diamond-text">宠物服务</div>
                        </div>
                    </div>
                    
                    <!-- 推荐内容 -->
                    <div class="section-title">热门推荐</div>
                    <div class="service-card">
                        <div class="service-header">
                            <div>
                                <div class="service-title">专业瑜伽私教</div>
                                <div class="service-meta">
                                    <span>⭐ 4.9</span>
                                    <span>📍 2.5km</span>
                                    <span>👥 128人预约</span>
                                </div>
                            </div>
                            <div class="service-price">¥120/小时</div>
                        </div>
                        <div class="service-desc">5年教学经验，擅长哈他瑜伽和阴瑜伽，可上门教学</div>
                        <div class="service-tags">
                            <span class="tag">认证教练</span>
                            <span class="tag">上门服务</span>
                            <span class="tag">一对一</span>
                        </div>
                    </div>
                    
                    <div class="service-card">
                        <div class="service-header">
                            <div>
                                <div class="service-title">专业上门按摩</div>
                                <div class="service-meta">
                                    <span>⭐ 4.8</span>
                                    <span>📍 1.2km</span>
                                    <span>👥 89人预约</span>
                                </div>
                            </div>
                            <div class="service-price">¥180/次</div>
                        </div>
                        <div class="service-desc">中医推拿师，10年从业经验，专业缓解肩颈腰椎问题</div>
                        <div class="service-tags">
                            <span class="tag">持证上岗</span>
                            <span class="tag">上门服务</span>
                            <span class="tag">中医推拿</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 按摩服务页 -->
            <div class="page" id="massage">
                <div class="header">
                    <span onclick="showPage('home')">← 返回</span>
                    <h2>上门按摩</h2>
                    <span>筛选</span>
                </div>
                
                <div class="content">
                    <div class="search-bar">
                        <span>🔍</span>
                        <input type="text" placeholder="搜索按摩服务...">
                    </div>
                    
                    <div class="service-card">
                        <div class="service-header">
                            <div>
                                <div class="service-title">中医推拿按摩</div>
                                <div class="service-meta">
                                    <span>⭐ 4.9</span>
                                    <span>📍 1.5km</span>
                                    <span>🕐 60分钟</span>
                                </div>
                            </div>
                            <div class="service-price">¥180</div>
                        </div>
                        <div class="service-desc">专业中医推拿，擅长颈椎、腰椎调理，缓解疲劳酸痛</div>
                        <div class="service-tags">
                            <span class="tag">中医师证</span>
                            <span class="tag">上门服务</span>
                            <span class="tag">推拿按摩</span>
                        </div>
                    </div>
                    
                    <div class="service-card">
                        <div class="service-header">
                            <div>
                                <div class="service-title">足疗按摩</div>
                                <div class="service-meta">
                                    <span>⭐ 4.7</span>
                                    <span>📍 2.1km</span>
                                    <span>🕐 45分钟</span>
                                </div>
                            </div>
                            <div class="service-price">¥120</div>
                        </div>
                        <div class="service-desc">专业足疗师，穴位按摩，改善血液循环，缓解疲劳</div>
                        <div class="service-tags">
                            <span class="tag">足疗师证</span>
                            <span class="tag">上门服务</span>
                            <span class="tag">穴位按摩</span>
                        </div>
                    </div>
                    
                    <div class="service-card">
                        <div class="service-header">
                            <div>
                                <div class="service-title">SPA精油按摩</div>
                                <div class="service-meta">
                                    <span>⭐ 4.8</span>
                                    <span>📍 3.2km</span>
                                    <span>🕐 90分钟</span>
                                </div>
                            </div>
                            <div class="service-price">¥280</div>
                        </div>
                        <div class="service-desc">高端SPA体验，进口精油，全身放松按摩，美容养颜</div>
                        <div class="service-tags">
                            <span class="tag">SPA师证</span>
                            <span class="tag">上门服务</span>
                            <span class="tag">精油按摩</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 家政服务页 -->
            <div class="page" id="housekeeping">
                <div class="header">
                    <span onclick="showPage('home')">← 返回</span>
                    <h2>家政服务</h2>
                    <span>筛选</span>
                </div>
                
                <div class="content">
                    <div class="search-bar">
                        <span>🔍</span>
                        <input type="text" placeholder="搜索家政服务...">
                    </div>
                    
                    <div class="service-card">
                        <div class="service-header">
                            <div>
                                <div class="service-title">家庭深度清洁</div>
                                <div class="service-meta">
                                    <span>⭐ 4.9</span>
                                    <span>📍 1.8km</span>
                                    <span>🕐 3小时</span>
                                </div>
                            </div>
                            <div class="service-price">¥150</div>
                        </div>
                        <div class="service-desc">专业保洁员，全屋深度清洁，厨房卫生间重点清理</div>
                        <div class="service-tags">
                            <span class="tag">持证上岗</span>
                            <span class="tag">深度清洁</span>
                            <span class="tag">自带工具</span>
                        </div>
                    </div>
                    
                    <div class="service-card">
                        <div class="service-header">
                            <div>
                                <div class="service-title">家电维修</div>
                                <div class="service-meta">
                                    <span>⭐ 4.8</span>
                                    <span>📍 2.5km</span>
                                    <span>🕐 1小时</span>
                                </div>
                            </div>
                            <div class="service-price">¥80起</div>
                        </div>
                        <div class="service-desc">专业维修师傅，洗衣机、冰箱、空调等家电维修</div>
                        <div class="service-tags">
                            <span class="tag">维修师证</span>
                            <span class="tag">上门维修</span>
                            <span class="tag">质保服务</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 发布页面 -->
            <div class="page" id="publish">
                <div class="header">
                    <span></span>
                    <h2>发布服务</h2>
                    <span></span>
                </div>
                
                <div class="content">
                    <div class="form-group">
                        <label class="form-label">服务类型</label>
                        <select class="form-input">
                            <option>选择服务类型</option>
                            <option>运动健身</option>
                            <option>上门按摩</option>
                            <option>家政服务</option>
                            <option>美容美发</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">服务标题</label>
                        <input type="text" class="form-input" placeholder="请输入服务标题">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">服务描述</label>
                        <textarea class="form-textarea" placeholder="详细描述您的服务内容、经验、特色等"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">服务价格</label>
                        <input type="number" class="form-input" placeholder="请输入价格（元）">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">服务地点</label>
                        <input type="text" class="form-input" placeholder="可服务的地区范围">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">可服务时间</label>
                        <input type="text" class="form-input" placeholder="请选择可服务的时间段">
                    </div>
                    
                    <button class="btn">发布服务</button>
                </div>
            </div>
            
            <!-- 个人中心 -->
            <div class="page" id="profile">
                <div class="header">
                    <span></span>
                    <h2>个人中心</h2>
                    <span>设置</span>
                </div>
                
                <div class="content">
                    <div class="service-card">
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <div style="width: 60px; height: 60px; border-radius: 50%; background: linear-gradient(135deg, #FF6B35, #F39C12); display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">👤</div>
                            <div>
                                <div class="service-title">张小明</div>
                                <div class="service-meta">
                                    <span>⭐ 4.8分</span>
                                    <span>📱 138****8888</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="certification-card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <div class="service-title">认证状态</div>
                            <div class="cert-status approved">已认证</div>
                        </div>
                        <div style="font-size: 14px; color: #666;">您已通过实名认证和按摩师资质认证</div>
                    </div>
                    
                    <div class="service-card">
                        <div class="service-title" style="margin-bottom: 15px;">我的服务</div>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">
                            <div style="text-align: center; padding: 15px; background: #f8f8f8; border-radius: 8px;">
                                <div style="font-size: 20px; font-weight: 600; color: #FF6B35;">12</div>
                                <div style="font-size: 12px; color: #666;">发布的服务</div>
                            </div>
                            <div style="text-align: center; padding: 15px; background: #f8f8f8; border-radius: 8px;">
                                <div style="font-size: 20px; font-weight: 600; color: #FF6B35;">89</div>
                                <div style="font-size: 12px; color: #666;">完成订单</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="service-card">
                        <div class="service-title" style="margin-bottom: 15px;">快捷操作</div>
                        <div style="display: flex; flex-direction: column; gap: 10px;">
                            <button class="btn btn-secondary">我的订单</button>
                            <button class="btn btn-secondary">收益统计</button>
                            <button class="btn btn-secondary">认证管理</button>
                            <button class="btn btn-secondary">客服帮助</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 认证页面 -->
            <div class="page" id="certification">
                <div class="header">
                    <span onclick="showPage('profile')">← 返回</span>
                    <h2>服务认证</h2>
                    <span></span>
                </div>
                
                <div class="content">
                    <div class="certification-card">
                        <div class="service-title" style="margin-bottom: 15px;">实名认证</div>
                        <div class="form-group">
                            <label class="form-label">真实姓名</label>
                            <input type="text" class="form-input" placeholder="请输入真实姓名">
                        </div>
                        <div class="form-group">
                            <label class="form-label">身份证号</label>
                            <input type="text" class="form-input" placeholder="请输入身份证号">
                        </div>
                        <div class="form-group">
                            <label class="form-label">身份证照片</label>
                            <div class="upload-area">
                                <div>📷</div>
                                <div>点击上传身份证正反面</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="certification-card">
                        <div class="service-title" style="margin-bottom: 15px;">专业资质认证</div>
                        <div class="form-group">
                            <label class="form-label">认证类型</label>
                            <select class="form-input">
                                <option>选择认证类型</option>
                                <option>按摩师资格证</option>
                                <option>中医推拿师证</option>
                                <option>足疗师证</option>
                                <option>SPA技师证</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">证书编号</label>
                            <input type="text" class="form-input" placeholder="请输入证书编号">
                        </div>
                        <div class="form-group">
                            <label class="form-label">从业年限</label>
                            <select class="form-input">
                                <option>选择从业年限</option>
                                <option>1年以下</option>
                                <option>1-3年</option>
                                <option>3-5年</option>
                                <option>5年以上</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">资格证书</label>
                            <div class="upload-area">
                                <div>📄</div>
                                <div>点击上传资格证书照片</div>
                            </div>
                        </div>
                    </div>
                    
                    <button class="btn">提交认证申请</button>
                </div>
            </div>
            
            <!-- 底部导航 -->
            <div class="bottom-nav">
                <div class="nav-item active" onclick="showPage('home')">
                    <div class="nav-icon">🏠</div>
                    <div class="nav-text">首页</div>
                </div>
                <div class="nav-item" onclick="showPage('publish')">
                    <div class="nav-icon">➕</div>
                    <div class="nav-text">发布</div>
                </div>
                <div class="nav-item" onclick="showPage('message')">
                    <div class="nav-icon">💬</div>
                    <div class="nav-text">消息</div>
                </div>
                <div class="nav-item" onclick="showPage('profile')">
                    <div class="nav-icon">👤</div>
                    <div class="nav-text">我的</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let currentRole = 'seeker'; // seeker 或 provider
        
        function showPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            
            // 显示目标页面
            document.getElementById(pageId).classList.add('active');
            
            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 根据页面设置对应的导航项为活跃状态
            const navMap = {
                'home': 0,
                'fitness': 0,
                'gaming': 0,
                'food': 0,
                'study': 0,
                'massage': 0,
                'housekeeping': 0,
                'beauty': 0,
                'pet': 0,
                'publish': 1,
                'message': 2,
                'profile': 3,
                'certification': 3
            };
            
            if (navMap[pageId] !== undefined) {
                document.querySelectorAll('.nav-item')[navMap[pageId]].classList.add('active');
            }
        }
        
        function toggleRole() {
            const header = document.querySelector('.header');
            const statusBar = document.querySelector('.status-bar');
            const roleSwitch = document.querySelector('.role-switch');
            
            if (currentRole === 'seeker') {
                currentRole = 'provider';
                header.className = 'header provider-mode';
                statusBar.className = 'status-bar provider-mode';
                roleSwitch.textContent = '🔄 需求者模式';
            } else {
                currentRole = 'seeker';
                header.className = 'header seeker-mode';
                statusBar.className = 'status-bar seeker-mode';
                roleSwitch.textContent = '🔄 服务者模式';
            }
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            showPage('home');
        });
    </script>
</body>
</html>