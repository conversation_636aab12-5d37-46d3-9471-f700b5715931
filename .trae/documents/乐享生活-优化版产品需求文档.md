# 乐享生活 - 双向服务聚合平台产品需求文档

## 1. 产品概述

乐享生活是一个集运动玩乐、生活服务一体的双向服务聚合平台，支持用户既可以提供服务也可以寻求服务。平台采用金刚位设计模式，提供预约健身瑜伽、游戏陪玩、上门按摩、足疗、家政等多元化服务。

目标用户为18-40岁的大学生、职场人群，通过双向服务模式构建真正的共享经济生态，类似美团+携程+支付宝的综合服务平台。

## 2. 核心功能

### 2.1 用户角色

| 角色 | 注册方式 | 核心权限 |
|------|----------|----------|
| 普通用户 | 手机号注册 | 浏览服务、发布需求、预约服务 |
| 服务提供者 | 实名认证+资质审核 | 发布服务、接受预约、提供专业服务 |
| 平台管理员 | 内部分配 | 用户管理、服务审核、平台运营 |

### 2.2 功能模块

我们的平台包含以下主要页面：

1. **首页（金刚位设计）**：服务分类导航、热门推荐、个性化内容
2. **服务分类页**：具体服务类别展示、筛选搜索、服务列表
3. **服务详情页**：服务介绍、提供者信息、预约功能、评价展示
4. **发布页面**：发布服务单、发布需求单、快速发布模板
5. **个人中心**：用户信息、订单管理、收益统计、认证中心
6. **消息中心**：预约通知、服务沟通、系统消息
7. **认证页面**：服务提供者资质认证、身份验证
8. **订单管理页**：服务订单、需求订单、交易记录

### 2.3 页面详情

| 页面名称 | 模块名称 | 功能描述 |
|----------|----------|----------|
| 首页 | 金刚位导航 | 展示运动健身、游戏陪玩、上门按摩、家政服务等8个核心服务分类，点击跳转对应服务页 |
| 首页 | 推荐内容区 | 显示热门服务、附近服务、个性化推荐和平台活动 |
| 首页 | 角色切换 | 一键切换服务提供者/需求者模式，改变页面主题色和内容展示 |
| 服务分类页 | 服务列表 | 展示该分类下所有服务，支持价格、距离、评分筛选 |
| 服务分类页 | 搜索功能 | 关键词搜索、地理位置筛选、服务时间筛选 |
| 服务详情页 | 服务信息 | 展示服务标题、价格、时长、地点、详细描述 |
| 服务详情页 | 提供者信息 | 显示头像、昵称、认证状态、评分、服务次数 |
| 服务详情页 | 预约功能 | 选择时间、确认地点、在线支付、即时预约 |
| 发布页面 | 服务发布 | 填写服务类型、标题、描述、价格、可服务时间和地点 |
| 发布页面 | 需求发布 | 填写需求类型、描述、预算、期望时间和地点 |
| 个人中心 | 认证中心 | 实名认证、专业资质上传、技能证书验证 |
| 个人中心 | 订单管理 | 查看服务订单、需求订单、交易记录、收益统计 |
| 消息中心 | 预约通知 | 新预约提醒、预约确认、服务提醒、评价提醒 |
| 认证页面 | 身份认证 | 上传身份证、人脸识别、手机号验证 |
| 认证页面 | 资质认证 | 上传专业证书、从业经历、技能展示、平台审核 |

## 3. 核心流程

### 普通用户流程
用户注册登录 → 浏览首页金刚位 → 点击感兴趣的服务分类 → 查看服务列表 → 选择具体服务 → 查看服务详情 → 预约并支付 → 享受服务 → 评价反馈

### 服务提供者流程
用户注册 → 实名认证 → 专业资质认证 → 平台审核通过 → 发布服务 → 接收预约 → 确认服务 → 提供服务 → 获得收益 → 查看评价

### 双向服务流程
用户可随时切换角色：提供服务时发布服务单等待预约，需要服务时发布需求单等待响应，或直接预约他人服务。

```mermaid
graph TD
    A[首页金刚位] --> B[运动健身页]
    A --> C[上门按摩页]
    A --> D[家政服务页]
    A --> E[游戏陪玩页]
    B --> F[服务详情页]
    C --> F
    D --> F
    E --> F
    F --> G[预约确认页]
    G --> H[支付页面]
    H --> I[服务完成页]
    I --> J[评价页面]
    
    K[发布页面] --> L[服务发布]
    K --> M[需求发布]
    L --> N[等待预约]
    M --> O[等待响应]
```

## 4. 用户界面设计

### 4.1 设计风格

- **主色调**：活力橙#FF6B35（服务提供者模式）、清新蓝#4A90E2（需求者模式）
- **辅助色**：温暖灰#F5F5F5、深灰#333333
- **按钮样式**：圆角矩形，渐变色彩，微阴影效果
- **字体**：苹方/思源黑体，标题18px，正文14px，辅助文字12px
- **布局风格**：卡片式设计，金刚位网格布局，底部导航
- **图标风格**：线性图标配合面性图标，统一的视觉语言

### 4.2 页面设计概览

| 页面名称 | 模块名称 | UI元素 |
|----------|----------|--------|
| 首页 | 金刚位导航 | 3x3网格布局，彩色图标，服务名称，点击动效 |
| 首页 | 推荐区域 | 横向滑动卡片，服务封面图，价格标签，评分星级 |
| 服务分类页 | 筛选栏 | 下拉选择器，价格滑块，地理位置选择，重置按钮 |
| 服务详情页 | 服务卡片 | 大图展示，价格突出显示，标签云，立即预约按钮 |
| 发布页面 | 表单组件 | 分步骤表单，图片上传，地图选点，价格输入框 |
| 个人中心 | 认证状态 | 进度条显示，认证徽章，状态说明，操作按钮 |

### 4.3 响应式设计

产品采用移动端优先设计，支持iOS、Android和微信小程序。金刚位在不同屏幕尺寸下自适应调整，确保触控体验优化和视觉效果一致性。