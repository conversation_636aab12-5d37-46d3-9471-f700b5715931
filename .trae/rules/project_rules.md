
# 强制规则
 每次任务过程中都需要使用 feedback mcp 与我进行交互 。接受我的新指令
 比如 ： 询问我 当前方案是否满意  、 下一步需要执行什么、你期望我下一步怎么做 等 交互。
 且 没有我的确认 不可以结束当前任务对话 ，除非 得到我的明确指令 "结束任务"方法结束此次任务或者对话。

# 项目背景

乐享生活app，是一个集运动玩乐一体的程序。支持预约健身瑜伽、预约陪玩、预约陪吃、搭子等众多功能。类似于美团和携程等聚合服务的平台 目标用户 大学生、学生、职场人等。年龄范围大概18-40岁 性别 男女不限。
 支持 安卓 、ios 和 微信小程序。
使用：Typescript + Vue3 + Pinia + UniApp + Axios 等

# 项目结构

oto-fit-home/
├── .husky/                      # Git hooks
├── .vscode/                     # IDE配置
├── .trae/                       # Trae AI 配置
│   └── rules/                   # 项目规范文档
├── docs/                        # 项目文档
├── src/
│   ├── api/                     # 接口层
│   │   ├── auth/                # 认证相关接口
│   │   │   ├── auth.ts          # 认证接口
│   │   │   └── types.ts         # 认证类型
│   │   ├── device/              # 设备管理接口（空目录）
│   │   ├── member/              # 会员相关接口
│   │   │   └── index.ts         # 会员接口
│   │   ├── modules/             # 接口模块
│   │   │   ├── captcha.ts       # 验证码模块
│   │   │   └── member.ts        # 会员模块
│   │   ├── types/               # 接口类型定义
│   │   │   ├── common.ts        # 通用类型
│   │   │   └── index.ts         # 类型导出
│   │   ├── cache.ts             # 缓存服务
│   │   ├── config.ts            # API配置
│   │   ├── errorHandler.ts      # 错误处理
│   │   ├── index.ts             # 接口统一导出
│   │   ├── interceptors.ts      # 拦截器
│   │   ├── manager.ts           # API管理器
│   │   └── types.ts             # 通用类型定义
│   ├── components/              # 组件库
│   │   ├── base/                # 基础组件（无状态）
│   │   │   ├── BaseButton.vue   # 基础按钮组件
│   │   │   ├── BaseCard.vue     # 基础卡片组件
│   │   │   ├── BaseInput.vue    # 基础输入组件
│   │   │   ├── BaseLoading.vue  # 基础加载组件
│   │   │   ├── Button/          # 按钮组件目录
│   │   │   └── index.ts         # 基础组件导出
│   │   ├── business/            # 业务组件
│   │   │   ├── CaptchaModal/    # 验证码模态框
│   │   │   ├── verification/    # 验证相关组件
│   │   │   │   ├── Verify.vue   # 主验证组件
│   │   │   │   ├── utils/       # 验证工具组件
│   │   │   │   │   ├── VerifyPoints.vue # 点选验证
│   │   │   │   │   └── VerifySlide.vue  # 滑动验证
│   │   │   │   └── verify/      # 验证子组件
│   │   │   │       └── verify.vue # 验证组件
│   │   │   └── index.ts         # 业务组件导出
│   │   └── index.ts             # 组件统一导出
│   ├── hooks/                   # Composition API (实际目录名)
│   │   ├── useAuth.ts           # 认证逻辑
│   │   ├── useCaptcha.ts        # 验证码逻辑
│   │   ├── useValidation.ts     # 表单验证
│   │   └── index.ts             # 统一导出
│   ├── config/                  # 配置中心
│   │   ├── app.ts               # 应用配置
│   │   ├── constant.ts          # 常量配置
│   │   ├── constants.ts         # 常量定义
│   │   ├── env.ts               # 环境配置
│   │   ├── index.ts             # 配置统一导出
│   │   └── router.ts            # 路由配置
│   ├── custom-tab-bar/          # 自定义底部导航
│   │   ├── index.vue            # 导航组件
│   │   └── index.js             # 导航逻辑
│   ├── device/                  # 设备相关（空目录）
│   ├── examples/                # 示例文件
│   │   ├── SmartLoadingExample.vue # 智能加载示例
│   │   └── StorageUsageExample.vue # 存储使用示例
│   ├── layouts/                 # 布局组件
│   │   ├── BlankLayout.vue      # 空白布局
│   │   ├── DefaultLayout.vue    # 默认布局
│   │   └── index.ts             # 布局导出
│   ├── directives/              # 自定义指令
│   │   └── index.ts             # 指令注册
│   ├── http/                    # HTTP 请求层
│   │   ├── config.ts            # 请求配置
│   │   ├── configService.ts     # 配置服务
│   │   ├── http.ts              # 请求封装
│   │   ├── interceptors.ts      # 拦截器
│   │   └── index.ts             # 统一导出
│   ├── i18n/                    # 国际化
│   │   └── index.ts             # 国际化配置
│   ├── pages/                   # 页面模块
│   │   ├── BasePage.vue         # 基础页面组件
│   │   ├── auth/                # 认证模块
│   │   │   ├── composables/     # 认证相关逻辑
│   │   │   │   └── useLogin.ts  # 登录逻辑
│   │   │   ├── login.vue        # 登录页面
│   │   │   ├── register.vue     # 注册页面
│   │   │   └── realAuth.vue     # 实名认证页面
│   │   ├── composables/         # 页面通用逻辑
│   │   │   ├── usePage.ts       # 页面通用逻辑
│   │   │   └── index.ts         # 页面逻辑导出
│   │   ├── device/              # 设备管理模块
│   │   │   └── management/      # 设备管理
│   │   │       └── deviceManagement.vue # 设备管理页面
│   │   ├── home/                # 首页模块
│   │   │   └── index/           # 首页
│   │   │       └── home.vue     # 首页组件
│   │   ├── match/               # 匹配模块
│   │   │   └── index/           # 匹配首页
│   │   │       └── match.vue    # 匹配页面
│   │   ├── message/             # 消息模块
│   │   │   └── index/           # 消息首页
│   │   │       └── message.vue  # 消息页面
│   │   ├── realAuth/            # 实名认证模块
│   │   │   └── composables/     # 实名认证逻辑
│   │   │       └── useRealAuth.ts # 实名认证逻辑
│   │   ├── service/             # 服务模块
│   │   │   └── index/           # 服务首页
│   │   │       └── service.vue  # 服务页面
│   │   └── user/                # 用户模块
│   │       ├── extendInfo/      # 扩展信息
│   │       │   └── ExtendInfo.vue # 扩展信息页面
│   │       ├── infomation/      # 用户信息
│   │       │   └── infomation.vue # 用户信息页面
│   │       ├── mine/            # 个人中心
│   │       │   └── mine.vue     # 个人中心页面
│   │       └── setupProfile/    # 设置资料
│   │           └── setupProfile.vue # 设置资料页面
│   ├── router/                  # 路由管理
│   │   ├── guard/               # 路由守卫
│   │   │   └── index.ts         # 守卫入口
│   │   ├── index.ts             # 路由入口
│   │   ├── manager.ts           # 路由管理器
│   │   ├── routes.ts            # 路由配置
│   │   └── types.ts             # 路由类型
│   ├── static/                  # 静态资源
│   │   ├── data/                # 静态数据
│   │   ├── styles/              # 静态样式
│   │   ├── tabbar/              # 底部导航图标
│   │   └── logo.png             # 应用图标
│   ├── store/                   # Pinia 状态管理
│   │   ├── modules/             # 状态模块
│   │   │   ├── app.ts           # 应用状态
│   │   │   ├── auth.ts          # 认证状态
│   │   │   ├── chat.ts          # 聊天状态
│   │   │   └── user.ts          # 用户状态
│   │   ├── config.ts            # 状态配置
│   │   ├── index.ts             # 状态入口
│   │   ├── types.ts             # 状态类型
│   │   └── utils.ts             # 状态工具
│   ├── styles/                  # 样式文件
│   │   ├── global.scss          # 全局样式
│   │   ├── mixins.scss          # 样式混入
│   │   └── variables.scss       # 样式变量
│   ├── types/                   # 全局类型定义
│   │   ├── api.ts               # API类型
│   │   ├── env.d.ts             # 环境类型
│   │   ├── global.d.ts          # 全局类型
│   │   ├── global.ts            # 全局类型实现
│   │   ├── index.ts             # 类型导出
│   │   └── request.ts           # 请求类型
│   ├── utils/                   # 工具库
│   │   ├── common/              # 通用工具
│   │   │   ├── addressData.ts   # 地址数据
│   │   │   ├── addressUtils.ts  # 地址工具
│   │   │   ├── browserCompatibility.ts # 浏览器兼容
│   │   │   ├── deviceInfo.ts    # 设备信息
│   │   │   ├── errorHandler.ts  # 错误处理
│   │   │   ├── loadingManager.ts # 加载管理
│   │   │   ├── navigationProtection.ts # 导航保护
│   │   │   ├── pageVisibility.ts # 页面可见性
│   │   │   └── requestWithProtection.ts # 受保护请求
│   │   ├── deviceFingerprint/   # 设备指纹工具
│   │   ├── format/              # 格式化工具
│   │   ├── helpers/             # 辅助工具
│   │   │   └── crypto.ts        # 加密工具
│   │   ├── request/             # 请求工具
│   │   │   └── retry.ts         # 重试工具
│   │   ├── storage/             # 存储工具
│   │   │   ├── StoreUtil.ts     # 存储工具类
│   │   │   ├── adapters/        # 存储适配器
│   │   │   ├── index.ts         # 存储导出
│   │   │   └── storageService.ts # 存储服务
│   │   ├── token/               # Token工具
│   │   │   ├── TokenUtil.ts     # Token工具类
│   │   │   ├── index.ts         # Token导出
│   │   │   └── tokenService.ts  # Token服务
│   │   ├── validate/            # 验证工具
│   │   └── index.ts             # 工具统一导出
│   ├── App.vue                  # 应用主组件
│   ├── main.ts                  # 入口文件
│   ├── manifest.json            # uni-app 配置
│   ├── pages.json               # uni-app 页面配置
│   ├── uni.scss                 # uni-app 样式
│   ├── env.d.ts                 # 环境类型定义
│   └── shime-uni.d.ts           # uni-app 类型声明
├── static/                      # 静态资源（直接拷贝）
├── test-results/                # 测试结果
│   └── .last-run.json           # 最后运行记录
├── tests/                       # 测试文件
├── scripts/                     # 脚本文件
│   ├── check-env.js             # 环境检查
│   └── validate-config.js       # 配置验证
├── .env                         # 环境变量
├── .env.development             # 开发环境变量
├── .env.local.example           # 本地环境变量示例
├── .env.production              # 生产环境变量
├── .env.staging                 # 预发环境变量
├── .eslintrc.js                 # ESLint 配置
├── .prettierrc.js               # Prettier 配置
├── eslint.config.js             # ESLint 新配置
├── package.json                 # 包管理配置
├── package-lock.json            # NPM 锁定文件
├── pnpm-lock.yaml               # PNPM 锁定文件
├── tsconfig.json                # TypeScript 配置
├── vite.config.ts               # Vite 配置
├── shims-uni.d.ts               # UniApp 类型声明
├── index.html                   # 入口HTML
├── favicon.ico                  # 网站图标
└── env-check-report.json        # 环境检查报告

---

# 命名规范（企业级标准）

## 页面命名规范

### 页面文件命名
- **业务页面**：使用业务语义化命名，如 `login.vue`、`register.vue`、`realAuth.vue`
- **功能页面**：使用功能描述命名，如 `deviceManagement.vue`、`setupProfile.vue`
- **首页类页面**：可使用 `home.vue`、`index.vue` 等，但需在对应业务目录下
- **避免**：使用无意义的通用名称，如 `page1.vue`、`component.vue`

### 页面目录结构
- **按业务模块分组**：`src/pages/{module}/{feature}/`
- **模块示例**：
  - `auth/` - 认证相关（登录、注册、实名认证）
  - `user/` - 用户相关（个人信息、设置、扩展信息）
  - `home/` - 首页相关
  - `match/` - 匹配相关
  - `message/` - 消息相关
  - `service/` - 服务相关
  - `device/` - 设备管理相关

### 页面内部结构
- **composables 目录**：存放页面相关的组合式 API，如 `useLogin.ts`、`useRealAuth.ts`
- **components 目录**：存放页面专用组件（如需要）

## 组件命名规范

### 组件文件命名
- **基础组件**：使用大驼峰命名，如 `BaseButton.vue`、`BaseInput.vue`
- **业务组件**：使用大驼峰命名，如 `UserCard.vue`、`LoginForm.vue`
- **验证组件**：使用大驼峰命名，如 `VerifyPoints.vue`、`VerifySlide.vue`

### 组件目录分类
- **base/**：基础组件（无状态、可复用）
  - 如：BaseButton、BaseCard、BaseInput、BaseLoading
- **business/**：业务组件（有业务逻辑）
  - CaptchaModal：验证码模态框组件
  - verification：验证相关组件集合
    - Verify.vue：主验证组件
    - utils/：验证工具组件（VerifyPoints、VerifySlide）
    - verify/：验证子组件

## API 接口命名规范

### 接口文件命名
- **服务文件**：使用 `{module}Service.ts` 格式，如 `captchaService.ts`、`memberService.ts`
- **模块接口**：使用业务模块名，如 `auth.ts`、`user.ts`、`device.ts`
- **类型定义**：使用 `types.ts` 或 `{module}Types.ts`

### 接口方法命名
- **获取数据**：`get{Resource}`，如 `getPuzzleCaptcha`、`getUserInfo`
- **创建数据**：`create{Resource}`，如 `createUser`、`createOrder`
- **更新数据**：`update{Resource}`，如 `updateProfile`、`updateSettings`
- **删除数据**：`delete{Resource}`，如 `deleteUser`、`deleteDevice`
- **校验数据**：`check{Resource}`，如 `checkCaptcha`、`checkToken`

## 工具函数命名规范

### 工具文件命名
- **功能描述**：使用功能描述命名，如 `storageService.ts`、`tokenService.ts`
- **工具类别**：使用类别 + 功能，如 `deviceFingerprint.ts`、`addressUtils.ts`
- **适配器**：使用 `{type}Adapter.ts` 格式

### Composables 命名
- **统一前缀**：使用 `use` 前缀，如 `useAuth`、`useCaptcha`
- **功能描述**：清晰描述功能，如 `useDeviceFingerprint`、`useValidation`

## 路由命名规范

### 路由路径
- **模块化路径**：`/pages/{module}/{feature}`
- **示例**：
  - `/pages/auth/login` - 登录页面
  - `/pages/user/mine` - 个人中心
  - `/pages/home/<USER>

### 路由名称
- **大驼峰命名**：与页面业务一致，如 `Login`、`Register`、`RealAuth`
- **模块前缀**：可使用模块前缀，如 `UserMine`、`AuthLogin`

## 常量和配置命名

### 常量命名
- **全大写 + 下划线**：如 `API_PREFIX`、`STORAGE_KEY`、`DEFAULT_TIMEOUT`
- **分组管理**：按功能分组，如路由常量、存储键常量等

### 配置文件命名
- **功能描述**：如 `constant.ts`、`router.ts`、`env.ts`
- **服务配置**：如 `configService.ts`、`httpConfig.ts`

## 样式文件命名

### 样式文件
- **全局样式**：`global.scss`、`variables.scss`、`mixins.scss`
- **组件样式**：与组件文件同名，使用 scoped
- **工具样式**：`utils.scss`、`helpers.scss`

## 类型定义命名

### 接口类型
- **请求类型**：`{Action}Request`，如 `LoginRequest`、`RegisterRequest`
- **响应类型**：`{Action}Response`，如 `LoginResponse`、`CaptchaResponse`
- **数据类型**：`{Entity}Data`，如 `UserData`、`DeviceData`

### 枚举类型
- **大驼峰命名**：如 `UserStatus`、`DeviceType`
- **值使用常量**：如 `ACTIVE`、`INACTIVE`

---
