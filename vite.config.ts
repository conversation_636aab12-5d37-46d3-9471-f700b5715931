import { defineConfig, loadEnv } from "vite";
import uni from "@dcloudio/vite-plugin-uni";
import { resolve } from "path";
import * as sass from 'sass';

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')
  
  return {
  plugins: [uni()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@/api': resolve(__dirname, 'src/api'),
      '@/components': resolve(__dirname, 'src/components'),
      '@/utils': resolve(__dirname, 'src/utils'),
      '@/types': resolve(__dirname, 'src/types'),
      '@/store': resolve(__dirname, 'src/store'),
      '@/config': resolve(__dirname, 'src/config')
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        implementation: sass,
        // 企业级开发规范：现代化Sass配置，App端优先
        api: 'modern-compiler', // 使用现代编译器API
        silenceDeprecations: ['legacy-js-api'], // 静默旧版JS API警告
        // 禁用全局自动注入，避免App端打包问题
      }
    }
  },
  server: {
    port: 5173,
    host: '0.0.0.0',
    cors: true,
    // 🔧 HMR配置：正确配置热模块替换
    hmr: {
      port: 5173, // HMR WebSocket端口
      clientPort: 5173 // 客户端连接端口
    },
    watch: {
      usePolling: false, // 禁用轮询，减少CPU使用
      ignored: ['!**/node_modules/**'] // 忽略node_modules变化
    },
    // 开发环境代理配置（解决CORS跨域问题）
    proxy: env.VITE_API_BASE_URL?.includes('localhost') ? {
      '/oto': {
        target: env.VITE_API_BASE_URL,
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/oto/, '/oto')
      }
    } : undefined
  },
  build: {
    // App端打包优化配置
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: process.env.NODE_ENV === 'production',
        drop_debugger: process.env.NODE_ENV === 'production'
      }
    },
    rollupOptions: {
      output: {
        // 修复App端打包格式问题：禁用代码分割以兼容IIFE格式
        format: 'es', // 使用ES模块格式
        // 有条件地启用代码分割（仅非App端平台）
        ...(process.env.UNI_PLATFORM !== 'app-android' && process.env.UNI_PLATFORM !== 'app-ios' ? {
          manualChunks: (id) => {
            if (id.includes('node_modules')) {
              if (id.includes('vue')) return 'vue'
              if (id.includes('pinia')) return 'pinia'
              if (id.includes('crypto-js')) return 'crypto'
              return 'vendor'
            }
          }
        } : {})
      }
    }
  },
  define: {
    // 平台环境变量
    'process.env.UNI_PLATFORM': JSON.stringify(process.env.UNI_PLATFORM),
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
    // H5平台特定配置(仅调试用)
    'process.env.UNI_ROUTER_MODE': JSON.stringify(process.env.UNI_ROUTER_MODE || 'history'),
    
    // 环境变量注入（以VITE_开头的变量会自动注入）
    // 手动注入关键配置变量
    'process.env.VITE_API_BASE_URL': JSON.stringify(env.VITE_API_BASE_URL),
    'process.env.VITE_APP_ENV': JSON.stringify(env.VITE_APP_ENV),
    'process.env.VITE_APP_VERSION': JSON.stringify(env.VITE_APP_VERSION),
    'process.env.VITE_ENABLE_CONSOLE_LOG': JSON.stringify(env.VITE_ENABLE_CONSOLE_LOG),
    'process.env.VITE_ENABLE_MOCK': JSON.stringify(env.VITE_ENABLE_MOCK),
    'process.env.VITE_REQUEST_TIMEOUT': JSON.stringify(env.VITE_REQUEST_TIMEOUT),
    'process.env.VITE_CAPTCHA_API_URL': JSON.stringify(env.VITE_CAPTCHA_API_URL),
    'process.env.VITE_DEVICE_FINGERPRINT_SECRET': JSON.stringify(env.VITE_DEVICE_FINGERPRINT_SECRET)
  },
  
  // 环境变量文件配置
  envDir: './', // 环境变量文件目录
  envPrefix: ['VITE_', 'UNI_'], // 环境变量前缀
  
  // 开发环境特殊配置
  ...(command === 'serve' && {
    // 开发环境额外配置可以在这里添加
  })
  }
});
