// Global Playwright configuration to force system Chrome usage
const { defineConfig } = require('@playwright/test');

module.exports = defineConfig({
  use: {
    // Force use of system Chrome
    launchOptions: {
      executablePath: '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-web-security'
      ]
    }
  },
  projects: [
    {
      name: 'chromium',
      use: {
        launchOptions: {
          executablePath: '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-web-security'
          ]
        }
      }
    }
  ]
});