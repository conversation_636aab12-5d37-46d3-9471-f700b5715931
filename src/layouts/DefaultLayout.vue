<template>
  <view class="default-layout">
    <!-- 头部导航 -->
    <view class="layout-header" v-if="showHeader">
      <slot name="header">
        <view class="header-content">
          <view class="header-left">
            <view class="back-btn" v-if="showBack" @click="handleBack">
              <text class="iconfont icon-back"></text>
            </view>
            <text class="header-title">{{ title }}</text>
          </view>
          <view class="header-right">
            <slot name="headerRight"></slot>
          </view>
        </view>
      </slot>
    </view>

    <!-- 主体内容 -->
    <view class="layout-main" :class="{ 'has-header': showHeader, 'has-footer': showFooter }">
      <slot></slot>
    </view>

    <!-- 底部导航 -->
    <view class="layout-footer" v-if="showFooter">
      <slot name="footer">
        <view class="footer-content">
          <!-- 默认底部内容 -->
        </view>
      </slot>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 组件属性
interface Props {
  title?: string
  showHeader?: boolean
  showFooter?: boolean
  showBack?: boolean
  backgroundColor?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  showHeader: true,
  showFooter: false,
  showBack: true,
  backgroundColor: '#ffffff'
})

// 事件定义
const emit = defineEmits<{
  back: []
}>()

// 返回处理
const handleBack = () => {
  emit('back')
  uni.navigateBack()
}

// 计算样式
const layoutStyle = computed(() => ({
  backgroundColor: props.backgroundColor
}))
</script>

<style lang="scss" scoped>
.default-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-color, #f5f5f5);
}

.layout-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #ffffff;
  border-bottom: 1px solid #e5e5e5;
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    padding: 0 32rpx;
    padding-top: var(--status-bar-height, 0);
  }
  
  .header-left {
    display: flex;
    align-items: center;
    flex: 1;
    
    .back-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;
      
      .icon-back {
        font-size: 36rpx;
        color: #333333;
      }
    }
    
    .header-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333333;
      flex: 1;
      text-align: center;
    }
  }
  
  .header-right {
    width: 60rpx;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}

.layout-main {
  flex: 1;
  
  &.has-header {
    margin-top: calc(88rpx + var(--status-bar-height, 0));
  }
  
  &.has-footer {
    margin-bottom: 100rpx;
  }
}

.layout-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #ffffff;
  border-top: 1px solid #e5e5e5;
  
  .footer-content {
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 32rpx;
    padding-bottom: var(--safe-area-inset-bottom, 0);
  }
}
</style>