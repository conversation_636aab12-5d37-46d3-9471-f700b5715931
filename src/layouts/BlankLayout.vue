<template>
  <view class="blank-layout" :style="layoutStyle">
    <slot></slot>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 组件属性
interface Props {
  backgroundColor?: string
  minHeight?: string
}

const props = withDefaults(defineProps<Props>(), {
  backgroundColor: '#ffffff',
  minHeight: '100vh'
})

// 计算样式
const layoutStyle = computed(() => ({
  backgroundColor: props.backgroundColor,
  minHeight: props.minHeight
}))
</script>

<style lang="scss" scoped>
.blank-layout {
  width: 100%;
  position: relative;
}
</style>