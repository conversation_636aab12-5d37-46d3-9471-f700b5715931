/**
 * HTTP 工具类 - 基于 Axios 适配 UniApp
 * @description 统一的网络请求工具，支持多平台（H5、小程序、App）
 * <AUTHOR>
 * @since 2024-01-01
 */

/// <reference types="@dcloudio/types" />

import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { createUniAppAxiosAdapter } from '@uni-helper/axios-adapter'
import { tokenUtil } from '@/utils/token/TokenUtil'
import { getBaseURL } from './config'
import { StoreUtil, StorageModule, StorageType } from '@/utils/storage/StoreUtil'
import { ErrorHandler } from '../utils/errorHandler'

// ==================== 类型定义 ====================

/** 请求配置接口 */
interface RequestConfig extends AxiosRequestConfig {
  /** 是否显示加载提示 */
  showLoading?: boolean
  /** 加载提示文本 */
  loadingText?: string
  /** 是否显示错误提示 */
  showError?: boolean
  /** 是否需要token */
  needToken?: boolean
  /** 是否需要认证 */
  requireAuth?: boolean
  /** 是否防止缓存 */
  preventCache?: boolean
  /** 是否重试 */
  retry?: boolean
  /** 重试次数 */
  retryCount?: number
  /** 错误处理配置 */
  errorHandle?: {
    autoHandleError?: boolean
    errorTypes?: ('server' | 'business' | 'network')[]
    showToast?: boolean
    customHandler?: (error: any) => boolean
  }
}

/** 扩展 Axios 请求配置类型 */
declare module 'axios' {
  interface AxiosRequestConfig {
    /** 是否显示加载提示 */
    showLoading?: boolean
    /** 加载提示文本 */
    loadingText?: string
    /** 是否显示错误提示 */
    showError?: boolean
    /** 是否需要token */
    needToken?: boolean
    /** 是否需要认证 */
    requireAuth?: boolean
    /** 是否防止缓存 */
    preventCache?: boolean
    /** 是否重试 */
    retry?: boolean
    /** 重试次数 */
    retryCount?: number
    /** 错误处理配置 */
    errorHandle?: {
      autoHandleError?: boolean
      errorTypes?: ('server' | 'business' | 'network')[]
      showToast?: boolean
      customHandler?: (error: any) => boolean
    }
  }
}

import type { ResponseData } from '@/types/request'

import { STATUS_CODES } from '@/config/constant'

/** HTTP 状态码枚举 */
enum HttpStatus {
  SUCCESS = STATUS_CODES.SUCCESS,
  UNAUTHORIZED = STATUS_CODES.UNAUTHORIZED,
  FORBIDDEN = STATUS_CODES.FORBIDDEN,
  NOT_FOUND = STATUS_CODES.NOT_FOUND,
  SERVER_ERROR = STATUS_CODES.SERVER_ERROR,
  BAD_GATEWAY = 502,
  SERVICE_UNAVAILABLE = 503,
  GATEWAY_TIMEOUT = 504
}

// ==================== HTTP 工具类 ====================

class HttpUtil {
  private instance: AxiosInstance
  private baseURL: string
  private timeout: number
  private loadingCount: number = 0

  constructor() {
    // 根据环境设置基础URL
    this.baseURL = getBaseURL()
    this.timeout = 10000

    // 创建axios实例
    this.instance = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      adapter: createUniAppAxiosAdapter(), // 使用UniApp适配器
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    })

    // 注意：拦截器已在 interceptors.ts 中统一配置
    // 避免重复设置，此处不再调用 setupInterceptors()
  }

  // 注意：getBaseURL方法已迁移到 ./config.ts 中
  // 统一使用配置文件管理API地址

  // 注意：拦截器设置已迁移到 src/api/http/interceptors.ts
  // 统一使用独立拦截器模块，避免重复配置

  // 注意：Token过期处理已迁移到 interceptors.ts 中的 handleTokenExpired 函数
  // 避免重复实现，统一使用拦截器中的处理逻辑

  /**
   * 显示加载提示
   */
  private showLoading(title: string = '加载中...'): void {
    this.loadingCount++
    if (this.loadingCount === 1) {
      uni.showLoading({
        title,
        mask: true
      })
    }
  }

  /**
   * 隐藏加载提示
   */
  private hideLoading(): void {
    this.loadingCount--
    if (this.loadingCount <= 0) {
      this.loadingCount = 0
      uni.hideLoading()
    }
  }

  /**
   * 显示错误提示
   */
  private showError(message: string): void {
    ErrorHandler.showError(message)
  }

  /**
   * 获取设备类型
   */
  private getDeviceType(): string {
    // #ifdef H5
    return 'h5'
    // #endif
    // #ifdef MP-WEIXIN
    return 'mp-weixin'
    // #endif
    // #ifdef MP-ALIPAY
    return 'mp-alipay'
    // #endif
    // #ifdef APP-PLUS
    return 'app'
    // #endif
    return 'unknown'
  }

  /**
   * 获取平台信息
   */
  private getPlatform(): string {
    return uni.getSystemInfoSync().platform
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  // ==================== 公共方法 ====================

  /**
   * 带保护的请求方法（支持重试、缓存、Token自动刷新）
   */
  async requestWithProtection<T = any>(
    requestFn: () => Promise<ResponseData<T>>,
    options: {
      maxRetries?: number
      cacheKey?: string
      cacheTTL?: number
    } = {}
  ): Promise<ResponseData<T>> {
    const { maxRetries = 3, cacheKey, cacheTTL = 300000 } = options
    
    // 检查缓存
    if (cacheKey) {
      const cached = await this.getCache(cacheKey)
      if (cached && Date.now() - cached.timestamp < cacheTTL) {
        // console.log(`[HTTP] 使用缓存数据: ${cacheKey}`)
        return cached.data
      }
    }
    
    // 重试机制
    for (let i = 0; i < maxRetries; i++) {
      try {
        const result = await requestFn()
        
        // 保存到缓存
        if (cacheKey) {
          await this.setCache(cacheKey, {
            data: result,
            timestamp: Date.now()
          })
        }
        
        return result
      } catch (error: any) {
        // console.error(`[HTTP] 请求失败 (${i + 1}/${maxRetries}):`, error)
        
        // 最后一次重试失败
        if (i === maxRetries - 1) {
          throw error
        }
        
        // 延迟重试（指数退避）
        const delay = Math.min(1000 * Math.pow(2, i), 10000)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
    
    throw new Error('请求失败')
  }

  /**
   * 缓存管理
   */
  private async getCache(key: string): Promise<{ data: any; timestamp: number } | null> {
    try {
      const cached = await StoreUtil.get(`http_cache_${key}`, StorageModule.CONFIG, StorageType.CACHE)
      return cached || null
    } catch {
      return null
    }
  }

  private async setCache(key: string, value: { data: any; timestamp: number }): Promise<void> {
    try {
      await StoreUtil.set(`http_cache_${key}`, value, StorageModule.CONFIG, StorageType.CACHE)
    } catch (error) {
      // console.warn('[HTTP] 缓存设置失败:', error)
    }
  }

  /**
   * 清除指定缓存
   */
  async clearCache(cacheKey: string): Promise<void> {
    try {
      await StoreUtil.remove(`http_cache_${cacheKey}`, StorageModule.CONFIG, StorageType.CACHE)
    } catch (error) {
      // console.warn('[HTTP] 缓存清除失败:', error)
    }
  }

  /**
   * 清除所有HTTP缓存
   */
  async clearAllCache(): Promise<void> {
    try {
      await StoreUtil.clearModule(StorageModule.CONFIG)
      // console.log('[HTTP] 已清除所有缓存')
    } catch (error) {
      // console.error('[HTTP] 清除缓存失败:', error)
    }
  }

  /**
   * 文件上传
   */
  uploadFile<T = any>(
    url: string,
    filePath: string,
    name: string = 'file',
    formData?: Record<string, any>
  ): Promise<ResponseData<T>> {
    return new Promise(async (resolve, reject) => {
      const token = await tokenUtil.getAccessToken()
      
      uni.uploadFile({
        url: this.baseURL + url,
        filePath,
        name,
        formData,
        header: {
          'Authorization': token ? `Bearer ${token}` : ''
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data)
            resolve(data)
          } catch (error) {
            reject(new Error('响应数据解析失败'))
          }
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  }

  /**
   * GET 请求
   */
  get<T = any>(url: string, params?: any, config?: RequestConfig): Promise<ResponseData<T>> {
    return this.instance.get(url, {
      params,
      ...config
    })
  }

  /**
   * POST 请求
   */
  post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ResponseData<T>> {
    return this.instance.post(url, data, config)
  }

  /**
   * PUT 请求
   */
  put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ResponseData<T>> {
    return this.instance.put(url, data, config)
  }

  /**
   * DELETE 请求
   */
  delete<T = any>(url: string, config?: RequestConfig): Promise<ResponseData<T>> {
    return this.instance.delete(url, config)
  }

  /**
   * PATCH 请求
   */
  patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ResponseData<T>> {
    return this.instance.patch(url, data, config)
  }

  /**
   * 文件上传
   */
  async upload<T = any>(url: string, filePath: string, formData?: any, config?: RequestConfig): Promise<ResponseData<T>> {
    const token = await tokenUtil.getAccessToken()
    
    return new Promise((resolve, reject) => {
      const uploadTask = uni.uploadFile({
        url: this.baseURL + url,
        filePath,
        name: 'file',
        formData,
        header: {
          'Authorization': `Bearer ${token}`
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data)
            if (data.code === HttpStatus.SUCCESS) {
              resolve(data)
            } else {
              reject(new Error(data.msg || '上传失败'))
            }
          } catch (error) {
            reject(new Error('响应数据解析失败'))
          }
        },
        fail: (error) => {
          reject(error)
        }
      })

      // 监听上传进度
      uploadTask.onProgressUpdate((res) => {
        // console.log('📤 [Upload Progress]', `${res.progress}%`)
      })
    })
  }

  /**
   * 文件下载
   */
  async download(url: string, config?: RequestConfig): Promise<string> {
    const token = await tokenUtil.getAccessToken()
    
    return new Promise((resolve, reject) => {
      uni.downloadFile({
        url: this.baseURL + url,
        header: {
          'Authorization': `Bearer ${token}`
        },
        success: (res) => {
          if (res.statusCode === HttpStatus.SUCCESS) {
            resolve(res.tempFilePath)
          } else {
            reject(new Error('下载失败'))
          }
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  }

  /**
   * 取消请求
   */
  cancelRequest(requestId: string): void {
    // 这里可以实现请求取消逻辑
    // console.log('🚫 [Cancel Request]', requestId)
  }

  /**
   * 设置基础URL
   */
  setBaseURL(baseURL: string): void {
    this.baseURL = baseURL
    this.instance.defaults.baseURL = baseURL
  }

  /**
   * 设置超时时间
   */
  setTimeout(timeout: number): void {
    this.timeout = timeout
    this.instance.defaults.timeout = timeout
  }

  /**
   * 获取实例
   */
  getInstance(): AxiosInstance {
    return this.instance
  }

  /**
   * 获取拦截器（用于外部配置）
   */
  getInterceptors() {
    return this.instance.interceptors
  }
}

// ==================== 导出 ====================

// 创建默认实例
const http = new HttpUtil()

// 导出实例和类
export { http, HttpUtil, type RequestConfig, type ResponseData }
export default http