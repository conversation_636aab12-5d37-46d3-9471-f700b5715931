/**
 * HTTP请求配置
 * @description 统一的HTTP请求配置管理，基于环境变量的企业级配置
 * <AUTHOR>
 * @since 2024-01-01
 * @updated 2024-01-01 - 重构为基于环境变量的配置方式
 */

import type { RequestConfig } from '@/types/request'
import { appConfig, envUtils } from '@/config/env'

/**
 * 获取API基础URL
 * @description 根据环境变量和平台条件返回对应的API地址
 * @returns {string} API基础URL
 */
export const getBaseURL = (): string => {
  // 优先使用环境变量配置
  const baseURL = appConfig.API_BASE_URL
  
  // 平台特定处理（如果需要）
  // #ifdef MP-WEIXIN
  // 微信小程序可能需要特殊处理
  if (envUtils.isProduction() && !baseURL.startsWith('https://')) {
    // console.warn('微信小程序生产环境必须使用HTTPS')
  }
  // #endif
  
  // #ifdef APP-PLUS
  // App端可能需要特殊处理
  // #endif
  
  // 开发环境下的额外验证
  if (envUtils.isDevelopment() && appConfig.ENABLE_CONSOLE_LOG) {
    // console.log(`[HTTP Config] Using API Base URL: ${baseURL}`)
  }
  
  return baseURL
}

/**
 * 获取当前平台标识
 * @returns {string} 平台标识
 */
const getPlatform = (): string => {
  // #ifdef H5
  return 'h5'
  // #endif
  // #ifdef MP-WEIXIN
  return 'mp-weixin'
  // #endif
  // #ifdef APP-PLUS
  return 'app'
  // #endif
  return 'unknown'
}

/**
 * 请求配置常量
 * @description 基于环境变量的请求配置
 */
export const REQUEST_CONFIG = {
  baseURL: getBaseURL(),
  timeout: appConfig.REQUEST_TIMEOUT,
  retryCount: appConfig.REQUEST_RETRY_COUNT,
  
  // 请求头配置
  headers: {
    'Content-Type': 'application/json',
    'X-App-Version': appConfig.APP_VERSION,
    'X-App-Platform': getPlatform()
  },
  
  // 开发环境特殊配置
  ...(envUtils.isDevelopment() && {
    validateStatus: (status: number) => {
      // 开发环境下记录所有HTTP状态
      if (appConfig.ENABLE_CONSOLE_LOG) {
        // console.log(`[HTTP] Response status: ${status}`)
      }
      return status >= 200 && status < 300
    }
  })
}

/**
 * 环境特定配置
 */
export const ENV_CONFIG = {
  // 是否启用请求日志
  enableRequestLog: envUtils.isDevelopment() && appConfig.ENABLE_CONSOLE_LOG,
  
  // 是否启用Mock数据
  enableMock: appConfig.ENABLE_MOCK,
  
  // 是否启用严格模式
  enableStrictMode: appConfig.ENABLE_STRICT_MODE,
  
  // 是否启用HTTPS检查
  enableHttpsOnly: appConfig.ENABLE_HTTPS_ONLY
}

// 兼容性导出
export const config: RequestConfig = REQUEST_CONFIG

// 配置验证
if (envUtils.isProduction() && ENV_CONFIG.enableHttpsOnly) {
  if (!getBaseURL().startsWith('https://')) {
    throw new Error('生产环境必须使用HTTPS协议')
  }
}