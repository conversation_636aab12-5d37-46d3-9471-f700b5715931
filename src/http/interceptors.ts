/**
 * HTTP拦截器配置
 * @description 统一配置HTTP请求和响应拦截器
 * <AUTHOR>
 * @since 2024-01-01
 */

import { http } from './http'
import { tokenUtil } from '@/utils/token/TokenUtil'
import { ErrorHandler } from '../utils/errorHandler'
import { showLoading, hideLoading } from '@/utils/common/loadingManager'
import { getDeviceType, getPlatform } from '@/utils/common/deviceInfo'
// TokenManager已被TokenUtil替换，统一使用TokenUtil进行Token管理

// 设备信息获取函数已移至统一的设备信息服务 @/utils/common/deviceInfo

/**
 * 生成请求ID
 */
const generateRequestId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 设置HTTP拦截器
 * 注意：此函数只需要调用一次，之后所有请求都会自动应用这些拦截器
 */
export const setupInterceptors = () => {
  // 请求拦截器
  setupRequestInterceptors()
  
  // 响应拦截器
  setupResponseInterceptors()
}

/**
 * 配置请求拦截器
 * 一次配置，所有请求都会自动应用
 */
const setupRequestInterceptors = () => {
  http.getInterceptors().request.use(
    async (config) => {

      
      // 1. 自动添加Token
      try {
        const token = tokenUtil.getAccessToken()
        if (token && config.requireAuth !== false) {
          config.headers.Authorization = `Bearer ${token}`
        }
      } catch (_error) {
          // console.warn('获取Token失败:', _error)
      }
      
      // 2. 添加公共头部
      config.headers['Content-Type'] = config.headers['Content-Type'] || 'application/json'
      config.headers['X-Requested-With'] = 'XMLHttpRequest'
      
      // 3. 添加设备信息
      config.headers['X-Device-Type'] = getDeviceType()
      config.headers['X-Platform'] = getPlatform()
      
      // 4. 添加请求ID用于追踪
      config.headers['X-Request-ID'] = generateRequestId()
      
      // 5. 添加时间戳防止缓存（GET请求）
      if (config.method === 'get' && config.preventCache !== false) {
        config.params = {
          ...config.params,
          _t: Date.now()
        }
      }
      
      // 4. 智能加载提示控制 - 使用统一的loading管理
      if (config.showLoading !== false) {
        const configAny = config as any
        const loadingDelay = configAny.loadingDelay || 200 // 默认200ms延迟
        const loadingText = configAny.loadingText || '加载中...'

        // 使用统一的loading管理器
        const taskId = showLoading(loadingText, {
          mask: configAny.loadingMask !== false,
          timeout: configAny.loadingTimeout || 30000 // 30秒超时
        })

        // 将taskId保存到config中，用于后续隐藏
        configAny._loadingTaskId = taskId

        // 如果设置了延迟，先隐藏然后延迟显示
        if (loadingDelay > 0) {
          hideLoading(taskId)
          configAny._loadingTimer = setTimeout(() => {
            configAny._loadingTaskId = showLoading(loadingText, {
              mask: configAny.loadingMask !== false,
              timeout: configAny.loadingTimeout || 30000
            })
          }, loadingDelay)
        }
      }
      
      return config
    },
    (_error) => {
        // console.error('❌ [请求拦截器] 请求错误:', _error)

      // 清除loading定时器和隐藏loading - 使用统一的loading管理
      const configAny = _error.config as any || {}
      if (configAny._loadingTimer) {
        clearTimeout(configAny._loadingTimer)
        configAny._loadingTimer = null
      }

      // 使用统一的loading管理器隐藏loading
      if (configAny._loadingTaskId) {
        hideLoading(configAny._loadingTaskId)
        configAny._loadingTaskId = null
      }

      return Promise.reject(_error)
    }
  )
}

/**
 * 配置响应拦截器
 * 一次配置，所有响应都会自动处理
 */
/**
 * 错误处理配置接口
 */
interface ErrorHandleConfig {
  autoHandleError?: boolean // 是否自动处理错误
  errorTypes?: ('server' | 'business' | 'network')[] // 处理哪些类型的错误
  showToast?: boolean // 是否显示错误提示
  customHandler?: (error: any) => boolean // 自定义错误处理函数，返回true表示已处理
}

const setupResponseInterceptors = () => {
  http.getInterceptors().response.use(
    (response) => {
      // console.log('✅ [响应拦截器] 收到响应:', response.config.url)
      
      // 智能隐藏加载提示 - 使用统一的loading管理
      const configAny = response.config as any
      if (configAny._loadingTimer) {
        // 清除延迟显示的定时器
        clearTimeout(configAny._loadingTimer)
        configAny._loadingTimer = null
      }

      // 使用统一的loading管理器隐藏loading
      if (configAny._loadingTaskId) {
        hideLoading(configAny._loadingTaskId)
        configAny._loadingTaskId = null
      }
      
      const { data } = response
      const config = response.config as any
      const errorConfig: ErrorHandleConfig = config.errorHandle || {}
      
      // 统一处理业务状态码
      if (data.code === 200) {
        // 🔧 修正：返回data而不是response，确保业务代码可以直接访问code、msg等字段
        return data
      } else if (data.code === 401) {
        // Token过期，自动处理
        // console.warn('🔑 [响应拦截器] Token过期，尝试刷新')
        return handleTokenExpired(response.config)
      } else {
        // 🎯 混合错误处理策略
        return handleMixedError(response, errorConfig)
      }
    },
    (_error) => {
        // console.error('❌ [响应拦截器] 响应错误:', _error)
      
      // 智能隐藏加载提示 - 使用统一的loading管理
      const configAny = _error.config as any || {}
      if (configAny._loadingTimer) {
        // 清除延迟显示的定时器
        clearTimeout(configAny._loadingTimer)
        configAny._loadingTimer = null
      }

      // 使用统一的loading管理器隐藏loading
      if (configAny._loadingTaskId) {
        hideLoading(configAny._loadingTaskId)
        configAny._loadingTaskId = null
      }
      
      const config = _error.config || {}
      const errorConfig: ErrorHandleConfig = config.errorHandle || {}
      
      // 🎯 混合网络错误处理
      return handleMixedNetworkError(_error, errorConfig)
    }
  )
}

/**
 * 混合错误处理策略
 */
const handleMixedError = (response: any, errorConfig: ErrorHandleConfig) => {
  const { data } = response
  const config = response.config
  
  // 1. 优先执行自定义处理函数
  if (errorConfig.customHandler) {
    const handled = errorConfig.customHandler(data)
    if (handled) {
      return {
        ...data,
        handled: true
      }
    }
  }
  
  // 2. 检查是否启用自动处理
  const autoHandle = errorConfig.autoHandleError !== false // 默认启用
  const errorTypes = errorConfig.errorTypes || ['server'] // 默认处理服务器错误
  const showToast = errorConfig.showToast !== false // 默认显示提示
  
  if (autoHandle) {
    // 服务器错误处理 (500-599)
    if (data.code >= 500 && data.code < 600 && errorTypes.includes('server')) {
      if (showToast) {
        ErrorHandler.showError(data.msg || data.message || '服务器错误，请稍后重试')
      }
      console.error('🔥 [服务器错误] 自动处理:', data.code, data.msg || data.message)
      
      // 返回标准化错误响应，不抛异常
      return {
        ...data,
        handled: true
      }
    }
    
    // 业务错误处理 (400-499)
    if (data.code >= 400 && data.code < 500 && errorTypes.includes('business')) {
      if (showToast) {
        ErrorHandler.showError(data.msg || data.message || '操作失败')
      }
      console.warn('⚠️ [业务错误] 自动处理:', data.code, data.msg || data.message)
      
      return {
        ...data,
        handled: true
      }
    }
  }
  
  // 3. 未配置自动处理或不在处理范围内，使用原有逻辑
  if (config.showError !== false && showToast) {
    ErrorHandler.showError(data.msg || data.message || '操作失败')
  }
  
  // 返回响应而不抛异常，让业务代码处理
  return data
}

/**
 * 混合网络错误处理
 */
const handleMixedNetworkError = (error: any, errorConfig: ErrorHandleConfig) => {
  // 1. 优先执行自定义处理函数
  if (errorConfig.customHandler) {
    const handled = errorConfig.customHandler(error)
    if (handled) {
      return Promise.resolve({
        code: error.status || 500,
        message: error.message || '网络错误',
        data: null,
        handled: true
      })
    }
  }
  
  // 2. 检查是否启用自动处理
  const autoHandle = errorConfig.autoHandleError !== false
  const errorTypes = errorConfig.errorTypes || ['network']
  const showToast = errorConfig.showToast !== false
  
  if (autoHandle && errorTypes.includes('network')) {
    if (showToast) {
      handleResponseError(error)
    }
    
    // 返回标准化错误响应
    return Promise.resolve({
      code: error.status || 500,
      message: error.message || '网络错误',
      data: null,
      handled: true
    })
  }
  
  // 3. 未配置自动处理，使用原有逻辑
  handleResponseError(error)
  return Promise.reject(error)
}

/**
 * 处理Token过期
 */
const handleTokenExpired = async (originalConfig: any) => {
  try {
    // 使用TokenUtil刷新Token
    await tokenUtil.refreshToken()
    
    // 重新发送原始请求
    const newToken = tokenUtil.getAccessToken()
    originalConfig.headers.Authorization = `Bearer ${newToken}`
    
    // 重新发送请求并返回业务数据
    const response = await http.getInstance().request(originalConfig)
    return response.data  // 返回业务数据，保持一致性
  } catch (refreshError) {
    // 刷新失败，跳转登录
    console.error('🔑 Token刷新失败，跳转登录页')
    
    // 使用TokenUtil清除Token
    tokenUtil.clearToken()
    
    // 跳转到登录页
    uni.navigateTo({ 
      url: '/pages/auth/login',
      fail: () => {
        // 如果跳转失败，尝试重定向
        uni.reLaunch({ url: '/pages/auth/login' })
      }
    })
    
    return Promise.reject(refreshError)
  }
}

/**
 * 统一响应错误处理
 */
const handleResponseError = (error: any) => {
  let message = '网络错误'
  
  if (error.response) {
    // HTTP状态码错误
    const { status, data } = error.response
    
    switch (status) {
      case 400:
        message = data?.msg || data?.message || '请求参数错误'
        break
      case 401:
        message = '未授权，请重新登录'
        break
      case 403:
        message = '权限不足'
        break
      case 404:
        message = '请求的资源不存在'
        break
      case 500:
        message = '服务器内部错误'
        break
      case 502:
        message = '网关错误'
        break
      case 503:
        message = '服务暂时不可用'
        break
      case 504:
        message = '网关超时'
        break
      default:
        message = data?.msg || data?.message || `HTTP ${status} 错误`
    }
  } else if (error.request) {
    // 网络连接错误
    if (error.code === 'ECONNABORTED') {
      message = '请求超时，请稍后重试'
    } else {
      message = '网络连接失败，请检查网络设置'
    }
  } else {
    // 其他错误
    message = error.message || '未知错误'
  }
  
  // 显示错误提示
  ErrorHandler.showError(message)
}

/**
 * 开发环境专用拦截器
 * 注意：这些拦截器只用于日志记录，不影响主要的业务逻辑处理
 */
const setupDevInterceptors = () => {
  // 开发环境的详细日志已经在主拦截器中处理
  // 这里可以添加额外的开发工具，如性能监控等
  console.log('🔧 [HTTP] 开发环境拦截器已启用')
}

/**
 * 生产环境专用拦截器
 */
const setupProdInterceptors = () => {
  // 生产环境可以添加错误上报等逻辑
  // 注意：这个拦截器只用于错误上报，不影响数据流
  // 主要的响应处理在 setupInterceptors 中完成
  http.getInterceptors().response.use(
    (response) => response, // 让主拦截器处理数据转换
    (error) => {
      // 错误上报逻辑
      reportError(error)
      return Promise.reject(error)
    }
  )
}

/**
 * 错误上报
 */
const reportError = (error: any) => {
  // 这里可以集成错误监控服务，如 Sentry、Bugsnag 等
  console.error('上报错误:', {
    message: error.message,
    stack: error.stack,
    url: error.config?.url,
    method: error.config?.method,
    timestamp: new Date().toISOString()
  })
}

/**
 * 根据环境设置拦截器
 */
export const setupEnvironmentInterceptors = () => {
  if (process.env.NODE_ENV === 'development') {
    setupDevInterceptors()
  } else if (process.env.NODE_ENV === 'production') {
    setupProdInterceptors()
  }
}

/**
 * 完整的拦截器设置（包含环境特定的拦截器）
 */
export const setupAllInterceptors = () => {
  // 先设置环境特定的拦截器（日志、错误上报等）
  setupEnvironmentInterceptors()
  // 再设置主拦截器（业务逻辑处理）
  setupInterceptors()
}