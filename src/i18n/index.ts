/**
 * 国际化配置
 */

import { STORAGE_KEYS } from '@/config/constant'
import { StoreUtil } from '@/utils/storage/StoreUtil'
import { StorageModule } from '@/utils/storage'

/**
 * 语言类型
 */
export type Language = 'zh-CN' | 'en-US'

/**
 * 翻译文本接口
 */
export interface TranslationMessages {
  [key: string]: string | TranslationMessages
}

/**
 * 中文语言包
 */
const zhCN: TranslationMessages = {
  common: {
    confirm: '确认',
    cancel: '取消',
    submit: '提交',
    reset: '重置',
    search: '搜索',
    loading: '加载中...',
    noData: '暂无数据',
    networkError: '网络错误，请稍后重试',
    success: '操作成功',
    failed: '操作失败'
  },
  auth: {
    login: '登录',
    register: '注册',
    logout: '退出登录',
    username: '用户名',
    password: '密码',
    confirmPassword: '确认密码',
    phone: '手机号',
    verificationCode: '验证码',
    loginSuccess: '登录成功',
    loginFailed: '登录失败',
    registerSuccess: '注册成功',
    registerFailed: '注册失败'
  },
  course: {
    courseList: '课程列表',
    courseDetail: '课程详情',
    bookCourse: '预约课程',
    cancelBooking: '取消预约',
    bookingSuccess: '预约成功',
    bookingFailed: '预约失败',
    courseFull: '课程已满',
    courseTime: '课程时间',
    courseLocation: '课程地点',
    instructor: '教练'
  },
  user: {
    profile: '个人资料',
    myBookings: '我的预约',
    settings: '设置',
    editProfile: '编辑资料',
    changePassword: '修改密码',
    about: '关于我们'
  }
}

/**
 * 英文语言包
 */
const enUS: TranslationMessages = {
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    submit: 'Submit',
    reset: 'Reset',
    search: 'Search',
    loading: 'Loading...',
    noData: 'No Data',
    networkError: 'Network error, please try again later',
    success: 'Success',
    failed: 'Failed'
  },
  auth: {
    login: 'Login',
    register: 'Register',
    logout: 'Logout',
    username: 'Username',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    phone: 'Phone',
    verificationCode: 'Verification Code',
    loginSuccess: 'Login successful',
    loginFailed: 'Login failed',
    registerSuccess: 'Registration successful',
    registerFailed: 'Registration failed'
  },
  course: {
    courseList: 'Course List',
    courseDetail: 'Course Detail',
    bookCourse: 'Book Course',
    cancelBooking: 'Cancel Booking',
    bookingSuccess: 'Booking successful',
    bookingFailed: 'Booking failed',
    courseFull: 'Course is full',
    courseTime: 'Course Time',
    courseLocation: 'Location',
    instructor: 'Instructor'
  },
  user: {
    profile: 'Profile',
    myBookings: 'My Bookings',
    settings: 'Settings',
    editProfile: 'Edit Profile',
    changePassword: 'Change Password',
    about: 'About Us'
  }
}

/**
 * 语言包映射
 */
const messages = {
  'zh-CN': zhCN,
  'en-US': enUS
}

/**
 * 国际化类
 */
export class I18n {
  private static currentLanguage: Language = 'zh-CN'
  
  /**
   * 初始化国际化
   */
  static async init() {
    // 从本地存储获取语言设置
    const savedLanguage = await StoreUtil.get(STORAGE_KEYS.LANGUAGE, StorageModule.CONFIG) as Language
    if (savedLanguage && messages[savedLanguage]) {
      this.currentLanguage = savedLanguage
    } else {
      // 获取系统语言
      const systemLanguage = uni.getSystemInfoSync().language
      this.currentLanguage = systemLanguage?.startsWith('zh') ? 'zh-CN' : 'en-US'
    }
  }
  
  /**
   * 获取翻译文本
   */
  static t(key: string): string {
    const keys = key.split('.')
    let result: any = messages[this.currentLanguage]
    
    for (const k of keys) {
      if (result && typeof result === 'object' && k in result) {
        result = result[k]
      } else {
        // console.warn(`Translation key not found: ${key}`)
        return key
      }
    }
    
    return typeof result === 'string' ? result : key
  }
  
  /**
   * 设置语言
   */
  static async setLanguage(language: Language) {
    if (messages[language]) {
      this.currentLanguage = language
      await StoreUtil.set(STORAGE_KEYS.LANGUAGE, language, StorageModule.CONFIG)
    }
  }
  
  /**
   * 获取当前语言
   */
  static getCurrentLanguage(): Language {
    return this.currentLanguage
  }
  
  /**
   * 获取可用语言列表
   */
  static getAvailableLanguages(): { code: Language; name: string }[] {
    return [
      { code: 'zh-CN', name: '简体中文' },
      { code: 'en-US', name: 'English' }
    ]
  }
}

// 全局翻译函数
export const t = (key: string) => I18n.t(key)

// 初始化国际化
I18n.init()