/**
 * 加载状态管理服务
 * 提供统一的加载状态管理，避免多个加载状态冲突
 */
class LoadingManager {
  private static loadingCount = 0
  private static currentTitle = '加载中...'
  private static isShowing = false

  /**
   * 显示加载状态
   * @param title 加载提示文字
   * @param mask 是否显示透明蒙层，防止触摸穿透
   */
  static show(title = '加载中...', mask = true) {
    this.loadingCount++
    this.currentTitle = title
    
    if (this.loadingCount === 1 && !this.isShowing) {
      this.isShowing = true
      uni.showLoading({
        title,
        mask
      })
    }
  }

  /**
   * 隐藏加载状态
   */
  static hide() {
    this.loadingCount = Math.max(0, this.loadingCount - 1)
    
    if (this.loadingCount === 0 && this.isShowing) {
      this.isShowing = false
      uni.hideLoading()
    }
  }

  /**
   * 强制隐藏加载状态
   * 用于异常情况下的清理
   */
  static forceHide() {
    this.loadingCount = 0
    this.isShowing = false
    uni.hideLoading()
  }

  /**
   * 获取当前加载状态
   */
  static getStatus() {
    return {
      isShowing: this.isShowing,
      count: this.loadingCount,
      title: this.currentTitle
    }
  }

  /**
   * 检查是否正在加载
   */
  static isLoading(): boolean {
    return this.isShowing
  }

  /**
   * 更新加载提示文字
   * @param title 新的提示文字
   */
  static updateTitle(title: string) {
    if (this.isShowing) {
      this.currentTitle = title
      // 重新显示以更新标题
      uni.hideLoading()
      uni.showLoading({
        title,
        mask: true
      })
    }
  }

  /**
   * 异步操作包装器
   * 自动管理加载状态
   * @param asyncFn 异步函数
   * @param title 加载提示文字
   * @param showError 是否显示错误提示
   */
  static async withLoading<T>(
    asyncFn: () => Promise<T>,
    title = '加载中...',
    showError = true
  ): Promise<T> {
    this.show(title)
    
    try {
      const result = await asyncFn()
      return result
    } catch (error) {
      if (showError) {
        // 动态导入错误处理器以避免循环依赖
        const { ErrorHandler } = await import('./errorHandler')
        ErrorHandler.handleApiError(error, 'LoadingManager.withLoading')
      }
      throw error
    } finally {
      this.hide()
    }
  }

  /**
   * 延迟显示加载状态
   * 避免短时间操作时的闪烁
   * @param delay 延迟时间（毫秒）
   * @param title 加载提示文字
   */
  static showWithDelay(delay = 300, title = '加载中...') {
    const timer = setTimeout(() => {
      this.show(title)
    }, delay)

    return {
      cancel: () => clearTimeout(timer),
      hide: () => this.hide()
    }
  }

  /**
   * 批量操作包装器
   * 为多个并发操作提供统一的加载状态
   * @param operations 操作数组
   * @param title 加载提示文字
   */
  static async withBatchLoading<T>(
    operations: Array<() => Promise<T>>,
    title = '处理中...'
  ): Promise<T[]> {
    this.show(title)
    
    try {
      const results = await Promise.all(operations.map(op => op()))
      return results
    } finally {
      this.hide()
    }
  }

  /**
   * 进度加载包装器
   * 支持显示进度信息
   * @param steps 步骤数组
   * @param baseTitle 基础标题
   */
  static async withProgressLoading<T>(
    steps: Array<{ fn: () => Promise<T>; title: string }>,
    baseTitle = '处理中'
  ): Promise<T[]> {
    const results: T[] = []
    
    try {
      for (let i = 0; i < steps.length; i++) {
        const step = steps[i]
        const progressTitle = `${baseTitle} (${i + 1}/${steps.length}) ${step.title}`
        
        if (i === 0) {
          this.show(progressTitle)
        } else {
          this.updateTitle(progressTitle)
        }
        
        const result = await step.fn()
        results.push(result)
      }
      
      return results
    } finally {
      this.hide()
    }
  }
}

// 导出便捷方法
export const {
  show: showLoading,
  hide: hideLoading,
  forceHide: forceHideLoading,
  getStatus: getLoadingStatus,
  isLoading,
  updateTitle: updateLoadingTitle,
  withLoading,
  showWithDelay: showLoadingWithDelay,
  withBatchLoading,
  withProgressLoading
} = LoadingManager

export { LoadingManager }