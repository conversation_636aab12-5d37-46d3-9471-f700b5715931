/**
 * 设备信息服务
 * 提供统一的设备信息获取功能，避免重复调用系统API
 */
class DeviceInfoService {
  private static instance: DeviceInfoService
  private systemInfo: any | null = null
  private networkType: string = 'unknown'
  private isOnline: boolean = true

  private constructor() {
    this.initSystemInfo()
    this.initNetworkInfo()
    this.setupNetworkListener()
  }

  /**
   * 获取单例实例
   */
  static getInstance(): DeviceInfoService {
    if (!this.instance) {
      this.instance = new DeviceInfoService()
    }
    return this.instance
  }

  /**
   * 初始化系统信息
   */
  private initSystemInfo() {
    try {
      this.systemInfo = uni.getSystemInfoSync()
    } catch (error) {
      console.error('❌ [DeviceInfo] Failed to get system info:', error)
    }
  }

  /**
   * 初始化网络信息
   */
  private initNetworkInfo() {
    uni.getNetworkType({
      success: (res) => {
        this.networkType = res.networkType
        this.isOnline = res.networkType !== 'none'
      },
      fail: (error) => {
        console.error('❌ [DeviceInfo] Failed to get network type:', error)
      }
    })
  }

  /**
   * 设置网络状态监听
   */
  private setupNetworkListener() {
    uni.onNetworkStatusChange((res) => {
      this.networkType = res.networkType
      this.isOnline = res.isConnected
    })
  }

  /**
   * 获取系统信息
   */
  getSystemInfo(): any | null {
    if (!this.systemInfo) {
      this.initSystemInfo()
    }
    return this.systemInfo
  }

  /**
   * 获取设备平台
   */
  getPlatform(): string {
    return this.systemInfo?.platform || 'unknown'
  }

  /**
   * 获取设备型号
   */
  getModel(): string {
    return this.systemInfo?.model || 'unknown'
  }

  /**
   * 获取系统版本
   */
  getSystemVersion(): string {
    return this.systemInfo?.system || 'unknown'
  }

  /**
   * 获取应用版本
   */
  getAppVersion(): string {
    return this.systemInfo?.version || 'unknown'
  }

  /**
   * 获取屏幕宽度
   */
  getScreenWidth(): number {
    return this.systemInfo?.screenWidth || 0
  }

  /**
   * 获取屏幕高度
   */
  getScreenHeight(): number {
    return this.systemInfo?.screenHeight || 0
  }

  /**
   * 获取窗口宽度
   */
  getWindowWidth(): number {
    return this.systemInfo?.windowWidth || 0
  }

  /**
   * 获取窗口高度
   */
  getWindowHeight(): number {
    return this.systemInfo?.windowHeight || 0
  }

  /**
   * 获取状态栏高度
   */
  getStatusBarHeight(): number {
    return this.systemInfo?.statusBarHeight || 0
  }

  /**
   * 获取安全区域
   */
  getSafeArea(): any {
    return this.systemInfo?.safeArea
  }

  /**
   * 获取安全区域插入
   */
  getSafeAreaInsets(): any {
    return this.systemInfo?.safeAreaInsets
  }

  /**
   * 获取网络类型
   */
  getNetworkType(): string {
    return this.networkType
  }

  /**
   * 检查是否在线
   */
  isNetworkOnline(): boolean {
    return this.isOnline
  }

  /**
   * 检查是否为iOS平台
   */
  isIOS(): boolean {
    return this.getPlatform() === 'ios'
  }

  /**
   * 检查是否为Android平台
   */
  isAndroid(): boolean {
    return this.getPlatform() === 'android'
  }

  /**
   * 检查是否为微信小程序
   */
  isWeChat(): boolean {
    // @ts-ignore
    // #ifdef MP-WEIXIN
    return true
    // #endif
    return false
  }

  /**
   * 检查是否为H5
   */
  isH5(): boolean {
    // @ts-ignore
    // #ifdef H5
    return true
    // #endif
    return false
  }

  /**
   * 检查是否为App
   */
  isApp(): boolean {
    // @ts-ignore
    // #ifdef APP-PLUS
    return true
    // #endif
    return false
  }

  /**
   * 获取设备唯一标识
   */
  getDeviceId(): string {
    return this.systemInfo?.deviceId || 'unknown'
  }

  /**
   * 获取设备品牌
   */
  getBrand(): string {
    return this.systemInfo?.brand || 'unknown'
  }

  /**
   * 获取像素比
   */
  getPixelRatio(): number {
    return this.systemInfo?.pixelRatio || 1
  }

  /**
   * 刷新系统信息
   */
  refresh(): void {
    this.initSystemInfo()
    this.initNetworkInfo()
  }

  /**
   * 获取完整的设备信息摘要
   */
  getDeviceSummary(): Record<string, any> {
    return {
      platform: this.getPlatform(),
      model: this.getModel(),
      system: this.getSystemVersion(),
      version: this.getAppVersion(),
      brand: this.getBrand(),
      deviceId: this.getDeviceId(),
      screenSize: {
        width: this.getScreenWidth(),
        height: this.getScreenHeight()
      },
      windowSize: {
        width: this.getWindowWidth(),
        height: this.getWindowHeight()
      },
      statusBarHeight: this.getStatusBarHeight(),
      pixelRatio: this.getPixelRatio(),
      networkType: this.getNetworkType(),
      isOnline: this.isNetworkOnline(),
      environment: {
        isIOS: this.isIOS(),
        isAndroid: this.isAndroid(),
        isWeChat: this.isWeChat(),
        isH5: this.isH5(),
        isApp: this.isApp()
      }
    }
  }
}

// 导出单例实例
export const deviceInfo = DeviceInfoService.getInstance()

// 导出类型
export type { DeviceInfoService }

// 导出便捷方法
export const {
  getSystemInfo,
  getPlatform,
  getModel,
  getSystemVersion,
  getAppVersion,
  getScreenWidth,
  getScreenHeight,
  getWindowWidth,
  getWindowHeight,
  getStatusBarHeight,
  getSafeArea,
  getSafeAreaInsets,
  getNetworkType,
  isNetworkOnline,
  isIOS,
  isAndroid,
  isWeChat,
  isH5,
  isApp,
  getDeviceId,
  getBrand,
  getPixelRatio,
  getDeviceSummary
} = deviceInfo