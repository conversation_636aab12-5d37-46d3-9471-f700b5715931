/**
 * Chrome浏览器兼容性工具
 * 处理Chrome特有的行为和限制
 */

import { createContextLogger } from './logger'

// 浏览器信息
export interface BrowserInfo {
  name: string
  version: string
  isChrome: boolean
  isSafari: boolean
  isFirefox: boolean
  isMobile: boolean
}

// 获取浏览器信息
export const getBrowserInfo = (): BrowserInfo => {
  const userAgent = navigator.userAgent
  
  const isChrome = /Chrome/.test(userAgent) && /Google Inc/.test(navigator.vendor)
  const isSafari = /Safari/.test(userAgent) && /Apple Computer/.test(navigator.vendor)
  const isFirefox = /Firefox/.test(userAgent)
  const isMobile = /Mobile|Android|iPhone|iPad/.test(userAgent)
  
  let name = 'Unknown'
  let version = 'Unknown'
  
  if (isChrome) {
    name = 'Chrome'
    const match = userAgent.match(/Chrome\/(\d+)/)
    version = match ? match[1] : 'Unknown'
  } else if (isSafari) {
    name = 'Safari'
    const match = userAgent.match(/Version\/(\d+)/)
    version = match ? match[1] : 'Unknown'
  } else if (isFirefox) {
    name = 'Firefox'
    const match = userAgent.match(/Firefox\/(\d+)/)
    version = match ? match[1] : 'Unknown'
  }
  
  return {
    name,
    version,
    isChrome,
    isSafari,
    isFirefox,
    isMobile
  }
}

// Chrome特有的页面生命周期管理
export class ChromeLifecycleManager {
  private static instance: ChromeLifecycleManager
  private isPageFreezed = false
  private beforeFreezeCallbacks: Array<() => void> = []
  private afterResumeCallbacks: Array<() => void> = []
  
  static getInstance(): ChromeLifecycleManager {
    if (!ChromeLifecycleManager.instance) {
      ChromeLifecycleManager.instance = new ChromeLifecycleManager()
    }
    return ChromeLifecycleManager.instance
  }
  
  constructor() {
    this.setupChromeLifecycleListeners()
  }
  
  private setupChromeLifecycleListeners() {
    const browser = getBrowserInfo()
    
    if (!browser.isChrome) {
      const logger = createContextLogger('ChromeLifecycleManager')
      logger.debug('非Chrome浏览器，跳过特殊处理')
      return
    }
    
    const logger = createContextLogger('ChromeLifecycleManager')
    logger.debug('设置Chrome生命周期监听器')
    
    // 监听页面冻结事件（Chrome特有）
    document.addEventListener('freeze', () => {
      const logger = createContextLogger('ChromeLifecycleManager')
      logger.debug('页面被冻结')
      this.isPageFreezed = true
      this.beforeFreezeCallbacks.forEach(callback => {
        try {
          callback()
        } catch (error) {
          logger.error('冻结回调执行错误:', error)
        }
      })
    })
    
    // 监听页面恢复事件（Chrome特有）
    document.addEventListener('resume', () => {
      const logger = createContextLogger('ChromeLifecycleManager')
      logger.debug('页面从冻结状态恢复')
      this.isPageFreezed = false
      this.afterResumeCallbacks.forEach(callback => {
        try {
          callback()
        } catch (error) {
          logger.error('恢复回调执行错误:', error)
        }
      })
    })
    
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', () => {
      const logger = createContextLogger('ChromeLifecycleManager')
      if (document.hidden) {
        // logger.debug('页面变为不可见')
      } else {
        // logger.debug('页面变为可见')
        // 页面重新可见时，检查是否从冻结状态恢复
        if (this.isPageFreezed) {
          // logger.debug('从冻结状态恢复，触发恢复回调')
          this.isPageFreezed = false
          this.afterResumeCallbacks.forEach(callback => {
            try {
              callback()
            } catch (error) {
              logger.error('恢复回调执行错误:', error)
            }
          })
        }
      }
    })
    
    // 监听内存压力事件（Chrome特有）
    if ('onmemory' in window) {
      // @ts-ignore
      window.addEventListener('memory', (event) => {
        const logger = createContextLogger('ChromeLifecycleManager')
        logger.warn('内存压力:', event)
      })
    }
  }
  
  // 添加页面冻结前的回调
  addBeforeFreezeCallback(callback: () => void): () => void {
    this.beforeFreezeCallbacks.push(callback)
    return () => {
      const index = this.beforeFreezeCallbacks.indexOf(callback)
      if (index > -1) {
        this.beforeFreezeCallbacks.splice(index, 1)
      }
    }
  }
  
  // 添加页面恢复后的回调
  addAfterResumeCallback(callback: () => void): () => void {
    this.afterResumeCallbacks.push(callback)
    return () => {
      const index = this.afterResumeCallbacks.indexOf(callback)
      if (index > -1) {
        this.afterResumeCallbacks.splice(index, 1)
      }
    }
  }
  
  // 检查页面是否被冻结
  isFreezed(): boolean {
    return this.isPageFreezed
  }
}

// 防止Chrome自动刷新的工具
export const preventChromeAutoRefresh = () => {
  const browser = getBrowserInfo()
  
  if (!browser.isChrome) {
    return
  }
  
  const logger = createContextLogger('preventChromeAutoRefresh')
  logger.debug('设置防自动刷新机制')
  
  // 禁用Chrome的自动刷新机制
  let isUserNavigating = false
  
  // 监听用户主动导航
  const markUserNavigation = () => {
    isUserNavigating = true
    setTimeout(() => {
      isUserNavigating = false
    }, 1000)
  }
  
  // 监听可能触发导航的用户事件
  document.addEventListener('click', markUserNavigation)
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      markUserNavigation()
    }
  })
  
  // 拦截非用户主动的页面刷新
  window.addEventListener('beforeunload', (e) => {
    if (!isUserNavigating && performance.navigation.type === 1) {
      const logger = createContextLogger('preventChromeAutoRefresh')
      logger.warn('检测到非用户主动的页面刷新')
      e.preventDefault()
      e.returnValue = ''
      return ''
    }
    return undefined
  })
}

// Chrome存储优化
export const optimizeChromeStorage = () => {
  const browser = getBrowserInfo()
  
  if (!browser.isChrome) {
    return
  }
  
  const logger = createContextLogger('optimizeChromeStorage')
  logger.debug('优化Chrome存储使用')
  
  // 定期清理过期的本地存储
  const cleanupExpiredStorage = () => {
    try {
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith('captcha_state_') || key.startsWith('form_data_')) {
          const data = localStorage.getItem(key)
          if (data) {
            try {
              const parsed = JSON.parse(data)
              if (parsed.savedAt && Date.now() - parsed.savedAt > 24 * 60 * 60 * 1000) {
                localStorage.removeItem(key)
                const logger = createContextLogger('optimizeChromeStorage')
                logger.debug('清理过期存储:', key)
              }
            } catch (e) {
              // 解析失败的数据也清理掉
              localStorage.removeItem(key)
            }
          }
        }
      })
    } catch (error) {
      const logger = createContextLogger('optimizeChromeStorage')
      logger.error('清理存储失败:', error)
    }
  }
  
  // 每5分钟清理一次
  setInterval(cleanupExpiredStorage, 5 * 60 * 1000)
  
  // 页面卸载时也清理一次
  window.addEventListener('beforeunload', cleanupExpiredStorage)
}

// 初始化Chrome兼容性设置
export const initChromeCompatibility = () => {
  const browser = getBrowserInfo()
  
  const logger = createContextLogger('initChromeCompatibility')
  // logger.debug('浏览器信息:', browser)
  
  if (browser.isChrome) {
    // logger.debug('检测到Chrome浏览器，启用兼容性优化')
    
    // 初始化生命周期管理器
    ChromeLifecycleManager.getInstance()
    
    // 防止自动刷新
    preventChromeAutoRefresh()
    
    // 优化存储
    optimizeChromeStorage()
    
    // 设置Chrome特有的性能优化
    if ('requestIdleCallback' in window) {
      // logger.debug('使用requestIdleCallback进行性能优化')
    }
  }
}

// 导出单例
export const chromeLifecycleManager = ChromeLifecycleManager.getInstance()

// 检查是否是Chrome浏览器
export const isChromeBrowser = (): boolean => {
  return getBrowserInfo().isChrome
}