/**
 * 带保护的网络请求工具（已迁移到主HTTP工具类）
 * @deprecated 请使用 http.requestWithProtection() 方法
 */
import { http } from '@/http/http'
import type { ResponseData } from '@/types/request'

/**
 * 带保护的请求函数（已迁移到主HTTP工具类）
 * @deprecated 请使用 http.requestWithProtection() 方法
 */
export async function requestWithProtection<T = any>(
  requestFn: () => Promise<ResponseData<T>>,
  options: {
    maxRetries?: number
    cacheKey?: string
    cacheTTL?: number
  } = {}
): Promise<ResponseData<T>> {
  // console.warn('[requestWithProtection] 此方法已废弃，请使用 http.requestWithProtection()')
  return http.requestWithProtection(requestFn, options)
}
