/**
 * 设备信息服务
 * 提供统一的设备信息获取和缓存功能
 */

import { logError } from './errorHandler'

export interface SystemInfo {
  platform: string
  system: string
  version: string
  windowWidth: number
  windowHeight: number
  screenWidth: number
  screenHeight: number
  pixelRatio: number
  statusBarHeight: number
  safeArea: {
    left: number
    right: number
    top: number
    bottom: number
    width: number
    height: number
  }
  safeAreaInsets: {
    left: number
    right: number
    top: number
    bottom: number
  }
  deviceType: string
  brand: string
  model: string
  language: string
  fontSizeSetting: number
  [key: string]: any
}

export interface DeviceFingerprint {
  deviceId: string
  platform: string
  brand: string
  model: string
  system: string
  version: string
  screenResolution: string
  pixelRatio: number
  language: string
  timezone: string
  userAgent?: string
}

/**
 * 设备信息服务类
 */
class DeviceInfoService {
  private static instance: DeviceInfoService
  private systemInfo: SystemInfo | null = null
  private deviceFingerprint: DeviceFingerprint | null = null
  private lastUpdateTime = 0
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): DeviceInfoService {
    if (!this.instance) {
      this.instance = new DeviceInfoService()
    }
    return this.instance
  }

  /**
   * 获取系统信息
   * @param forceRefresh 是否强制刷新
   */
  getSystemInfo(forceRefresh = false): SystemInfo {
    const now = Date.now()
    
    // 检查缓存是否有效
    if (!forceRefresh && this.systemInfo && (now - this.lastUpdateTime) < this.CACHE_DURATION) {
      return this.systemInfo
    }

    try {
      this.systemInfo = uni.getSystemInfoSync() as SystemInfo
      this.lastUpdateTime = now
      return this.systemInfo
    } catch (_error) {
    logError('DeviceInfoService', '获取系统信息失败', { extra: _error })
      
      // 返回默认值
      return this.getDefaultSystemInfo()
    }
  }

  /**
   * 获取平台信息
   */
  getPlatform(): string {
    const systemInfo = this.getSystemInfo()
    return systemInfo.platform || 'unknown'
  }

  /**
   * 获取设备类型
   */
  getDeviceType(): string {
    const systemInfo = this.getSystemInfo()
    
    // 根据平台和屏幕尺寸判断设备类型
    const platform = systemInfo.platform
    const windowWidth = systemInfo.windowWidth
    
    if (platform === 'ios' || platform === 'android') {
      return windowWidth > 768 ? 'tablet' : 'mobile'
    } else if (platform === 'windows' || platform === 'mac') {
      return 'desktop'
    } else {
      return windowWidth > 768 ? 'tablet' : 'mobile'
    }
  }

  /**
   * 获取屏幕尺寸信息
   */
  getScreenInfo() {
    const systemInfo = this.getSystemInfo()
    return {
      windowWidth: systemInfo.windowWidth,
      windowHeight: systemInfo.windowHeight,
      screenWidth: systemInfo.screenWidth,
      screenHeight: systemInfo.screenHeight,
      pixelRatio: systemInfo.pixelRatio,
      deviceType: this.getDeviceType()
    }
  }

  /**
   * 获取安全区域信息
   */
  getSafeAreaInfo() {
    const systemInfo = this.getSystemInfo()
    return {
      safeArea: systemInfo.safeArea,
      safeAreaInsets: systemInfo.safeAreaInsets,
      statusBarHeight: systemInfo.statusBarHeight
    }
  }

  /**
   * 获取设备指纹
   * @param forceRefresh 是否强制刷新
   */
  getDeviceFingerprint(forceRefresh = false): DeviceFingerprint {
    if (!forceRefresh && this.deviceFingerprint) {
      return this.deviceFingerprint
    }

    const systemInfo = this.getSystemInfo()
    
    this.deviceFingerprint = {
      deviceId: this.generateDeviceId(systemInfo),
      platform: systemInfo.platform,
      brand: systemInfo.brand || 'unknown',
      model: systemInfo.model || 'unknown',
      system: systemInfo.system,
      version: systemInfo.version,
      screenResolution: `${systemInfo.screenWidth}x${systemInfo.screenHeight}`,
      pixelRatio: systemInfo.pixelRatio,
      language: systemInfo.language,
      timezone: this.getTimezone(),
      userAgent: this.getUserAgent()
    }

    return this.deviceFingerprint
  }

  /**
   * 检查是否为移动设备
   */
  isMobile(): boolean {
    const deviceType = this.getDeviceType()
    return deviceType === 'mobile'
  }

  /**
   * 检查是否为平板设备
   */
  isTablet(): boolean {
    const deviceType = this.getDeviceType()
    return deviceType === 'tablet'
  }

  /**
   * 检查是否为桌面设备
   */
  isDesktop(): boolean {
    const deviceType = this.getDeviceType()
    return deviceType === 'desktop'
  }

  /**
   * 检查是否为iOS设备
   */
  isIOS(): boolean {
    const platform = this.getPlatform()
    return platform === 'ios'
  }

  /**
   * 检查是否为Android设备
   */
  isAndroid(): boolean {
    const platform = this.getPlatform()
    return platform === 'android'
  }

  /**
   * 获取响应式尺寸配置
   * @param baseWidth 基准宽度
   * @param baseHeight 基准高度
   */
  getResponsiveSize(baseWidth: number, baseHeight: number) {
    const screenInfo = this.getScreenInfo()
    const scale = Math.min(1, (screenInfo.windowWidth - 40) / baseWidth)
    
    return {
      width: Math.floor(baseWidth * scale),
      height: Math.floor(baseHeight * scale),
      scale
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.systemInfo = null
    this.deviceFingerprint = null
    this.lastUpdateTime = 0
  }

  /**
   * 生成设备ID
   */
  private generateDeviceId(systemInfo: SystemInfo): string {
    const components = [
      systemInfo.platform,
      systemInfo.brand,
      systemInfo.model,
      systemInfo.system,
      systemInfo.screenWidth,
      systemInfo.screenHeight,
      systemInfo.pixelRatio
    ]
    
    const fingerprint = components.join('|')
    return this.simpleHash(fingerprint)
  }

  /**
   * 简单哈希函数
   */
  private simpleHash(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash).toString(36)
  }

  /**
   * 获取时区
   */
  private getTimezone(): string {
    try {
      return Intl.DateTimeFormat().resolvedOptions().timeZone
    } catch {
      return 'Asia/Shanghai'
    }
  }

  /**
   * 获取用户代理
   */
  private getUserAgent(): string | undefined {
    try {
      // @ts-ignore
      return navigator?.userAgent
    } catch {
      return undefined
    }
  }

  /**
   * 获取默认系统信息
   */
  private getDefaultSystemInfo(): SystemInfo {
    return {
      platform: 'unknown',
      system: 'unknown',
      version: '0.0.0',
      windowWidth: 375,
      windowHeight: 667,
      screenWidth: 375,
      screenHeight: 667,
      pixelRatio: 2,
      statusBarHeight: 20,
      safeArea: {
        left: 0,
        right: 375,
        top: 20,
        bottom: 667,
        width: 375,
        height: 647
      },
      safeAreaInsets: {
        left: 0,
        right: 0,
        top: 20,
        bottom: 0
      },
      deviceType: 'mobile',
      brand: 'unknown',
      model: 'unknown',
      language: 'zh_CN',
      fontSizeSetting: 16
    }
  }
}

// 导出单例实例
export const deviceInfo = DeviceInfoService.getInstance()

// 导出便捷方法
export const getSystemInfo = () => deviceInfo.getSystemInfo()
export const getPlatform = () => deviceInfo.getPlatform()
export const getDeviceType = () => deviceInfo.getDeviceType()
export const getScreenInfo = () => deviceInfo.getScreenInfo()
export const getSafeAreaInfo = () => deviceInfo.getSafeAreaInfo()
export const getDeviceFingerprint = () => deviceInfo.getDeviceFingerprint()
export const isMobile = () => deviceInfo.isMobile()
export const isTablet = () => deviceInfo.isTablet()
export const isDesktop = () => deviceInfo.isDesktop()
export const isIOS = () => deviceInfo.isIOS()
export const isAndroid = () => deviceInfo.isAndroid()
export const getResponsiveSize = (baseWidth: number, baseHeight: number) => 
  deviceInfo.getResponsiveSize(baseWidth, baseHeight)