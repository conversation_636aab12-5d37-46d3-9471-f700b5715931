/**
 * 页面可见性管理工具
 * 处理Chrome浏览器的页面休眠/唤醒状态
 */

// 页面可见性状态
export interface PageVisibilityState {
  isVisible: boolean
  wasHidden: boolean
  lastVisibleTime: number
  lastHiddenTime: number
}

// 回调函数类型
export type VisibilityChangeCallback = (state: PageVisibilityState) => void

class PageVisibilityManager {
  private state: PageVisibilityState = {
    isVisible: true,
    wasHidden: false,
    lastVisibleTime: Date.now(),
    lastHiddenTime: 0
  }
  
  private callbacks: Set<VisibilityChangeCallback> = new Set()
  private initialized = false
  
  constructor() {
    this.init()
  }
  
  private init() {
    if (this.initialized) return
    
    // 检查浏览器支持
    const hidden = this.getHiddenProperty()
    const visibilityChange = this.getVisibilityEvent()
    
    if (hidden && visibilityChange) {
      // 初始状态
      this.state.isVisible = !(document as any)[hidden]
      
      // 监听可见性变化
      document.addEventListener(visibilityChange, () => {
        this.handleVisibilityChange()
      }, false)
      
      // console.log('[页面可见性] 监听器已初始化')
    } else {
      // console.warn('[页面可见性] 浏览器不支持Page Visibility API')
    }
    
    // 监听窗口焦点变化（作为备选方案）
    window.addEventListener('focus', () => {
      this.handlePageVisible()
    })
    
    window.addEventListener('blur', () => {
      this.handlePageHidden()
    })
    
    // 监听应用状态变化（uni-app特有）
    if (typeof uni !== 'undefined') {
      uni.onAppShow(() => {
        this.handlePageVisible()
      })
      
      uni.onAppHide(() => {
        this.handlePageHidden()
      })
    }
    
    this.initialized = true
  }
  
  private getHiddenProperty(): string | null {
    if (typeof document.hidden !== 'undefined') {
      return 'hidden'
    } else if (typeof (document as any).msHidden !== 'undefined') {
      return 'msHidden'
    } else if (typeof (document as any).webkitHidden !== 'undefined') {
      return 'webkitHidden'
    }
    return null
  }
  
  private getVisibilityEvent(): string | null {
    if (typeof document.hidden !== 'undefined') {
      return 'visibilitychange'
    } else if (typeof (document as any).msHidden !== 'undefined') {
      return 'msvisibilitychange'
    } else if (typeof (document as any).webkitHidden !== 'undefined') {
      return 'webkitvisibilitychange'
    }
    return null
  }
  
  private handleVisibilityChange() {
    const hidden = this.getHiddenProperty()
    if (!hidden) return
    
    const isHidden = (document as any)[hidden]
    
    if (isHidden) {
      this.handlePageHidden()
    } else {
      this.handlePageVisible()
    }
  }
  
  private handlePageVisible() {
    if (this.state.isVisible) return // 已经是可见状态
    
    const now = Date.now()
    const hiddenDuration = this.state.lastHiddenTime ? now - this.state.lastHiddenTime : 0
    
    // console.log(`[页面可见性] 页面变为可见，隐藏时长: ${hiddenDuration}ms`)
    
    this.state.isVisible = true
    this.state.wasHidden = true
    this.state.lastVisibleTime = now
    
    this.notifyCallbacks()
  }
  
  private handlePageHidden() {
    if (!this.state.isVisible) return // 已经是隐藏状态
    
    const now = Date.now()
    
    // console.log('[页面可见性] 页面变为隐藏')
    
    this.state.isVisible = false
    this.state.lastHiddenTime = now
    
    this.notifyCallbacks()
  }
  
  private notifyCallbacks() {
    this.callbacks.forEach(callback => {
      try {
        callback({ ...this.state })
      } catch (error) {
        // console.error('[页面可见性] 回调执行错误:', error)
      }
    })
  }
  
  /**
   * 添加可见性变化监听器
   */
  addListener(callback: VisibilityChangeCallback): () => void {
    this.callbacks.add(callback)
    
    // 返回移除监听器的函数
    return () => {
      this.callbacks.delete(callback)
    }
  }
  
  /**
   * 获取当前状态
   */
  getState(): PageVisibilityState {
    return { ...this.state }
  }
  
  /**
   * 检查页面是否可见
   */
  isVisible(): boolean {
    return this.state.isVisible
  }
  
  /**
   * 检查页面是否曾经被隐藏过
   */
  wasHidden(): boolean {
    return this.state.wasHidden
  }
  
  /**
   * 获取页面隐藏的时长（毫秒）
   */
  getHiddenDuration(): number {
    if (this.state.isVisible) {
      return this.state.lastHiddenTime ? this.state.lastVisibleTime - this.state.lastHiddenTime : 0
    } else {
      return this.state.lastHiddenTime ? Date.now() - this.state.lastHiddenTime : 0
    }
  }
  
  /**
   * 重置状态
   */
  reset() {
    this.state.wasHidden = false
    this.state.lastVisibleTime = Date.now()
    this.state.lastHiddenTime = 0
  }
}

// 创建全局实例
const pageVisibilityManager = new PageVisibilityManager()

// 导出工具函数
export const usePageVisibility = () => {
  return {
    addListener: pageVisibilityManager.addListener.bind(pageVisibilityManager),
    getState: pageVisibilityManager.getState.bind(pageVisibilityManager),
    isVisible: pageVisibilityManager.isVisible.bind(pageVisibilityManager),
    wasHidden: pageVisibilityManager.wasHidden.bind(pageVisibilityManager),
    getHiddenDuration: pageVisibilityManager.getHiddenDuration.bind(pageVisibilityManager),
    reset: pageVisibilityManager.reset.bind(pageVisibilityManager)
  }
}

export default pageVisibilityManager