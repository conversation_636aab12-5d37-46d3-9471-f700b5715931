import { addressData as data } from './addressData';
import type { AddressDataType } from './addressData';

interface AddressData {
  province: string[];
  cities: { [key: string]: string[] };
  districts: { [key: string]: { [key: string]: string[] } };
}

let addressData: AddressData | null = null;

// 初始化地址数据
export const initAddressData = (): AddressData => {
  if (addressData) return addressData;

  try {
    addressData = {
      province: [],
      cities: {},
      districts: {}
    };

    // 提取省份列表
    addressData.province = Object.keys(data);

    // 提取城市和区县数据
    addressData.province.forEach(province => {
      const provinceData = data[province] as { [key: string]: string[] };
      addressData!.cities[province] = Object.keys(provinceData);
      addressData!.districts[province] = {};

      addressData!.cities[province].forEach(city => {
        addressData!.districts[province][city] = provinceData[city];
      });
    });

    return addressData;
  } catch (_error) {

    return {
      province: [],
      cities: {},
      districts: {}
    };
  }
};

// 获取省份列表
export const getProvinces = (): string[] => {
  const data = initAddressData();
  return data.province.sort((a, b) => a.localeCompare(b, 'zh-CN'));
};

// 获取指定省份的城市列表
export const getCities = (province: string): string[] => {
  const data = initAddressData();
  return data.cities[province] || [];
};

// 获取指定省份和城市的区县列表
export const getDistricts = (province: string, city: string): string[] => {
  const data = initAddressData();
  return data.districts[province]?.[city] || [];
};