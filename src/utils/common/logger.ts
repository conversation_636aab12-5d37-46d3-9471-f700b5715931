/**
 * 统一日志服务
 * 支持环境变量控制日志级别，生产环境自动过滤调试信息
 */

// 日志级别枚举
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4
}

// 导出枚举值以避免linter警告
export const { DEBUG, INFO, WARN, ERROR, NONE } = LogLevel

// 日志配置接口
interface LoggerConfig {
  level: LogLevel
  enableConsole: boolean
  enableRemote: boolean
  prefix?: string
}

// 默认配置
const defaultConfig: LoggerConfig = {
  level: process.env.NODE_ENV === 'production' ? LogLevel.WARN : LogLevel.DEBUG,
  enableConsole: true,
  enableRemote: false,
  prefix: '[App]'
}

class Logger {
  private config: LoggerConfig
  private static instance: Logger

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = { ...defaultConfig, ...config }
  }

  static getInstance(config?: Partial<LoggerConfig>): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger(config)
    }
    return Logger.instance
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.config.level
  }

  private formatMessage(level: string, context: string, message: any): string {
    const timestamp = new Date().toISOString()
    const prefix = this.config.prefix || ''
    return `${prefix} [${timestamp}] [${level}] [${context}] ${message}`
  }

  private logToConsole(level: LogLevel, context: string, message: any, ...args: any[]): void {
    if (!this.config.enableConsole || !this.shouldLog(level)) {
      return
    }

    const formattedMessage = this.formatMessage(LogLevel[level], context, message)

    switch (level) {
      case LogLevel.DEBUG:
        // console.debug(formattedMessage, ...args)
        break
      case LogLevel.INFO:
        // console.info(formattedMessage, ...args)
        break
      case LogLevel.WARN:
        // console.warn(formattedMessage, ...args)
        break
      case LogLevel.ERROR:
        // console.error(formattedMessage, ...args)
        break
    }
  }

  // 调试日志 - 仅开发环境
  debug(context: string, message: any, ...args: any[]): void {
    this.logToConsole(LogLevel.DEBUG, context, message, ...args)
  }

  // 信息日志
  info(context: string, message: any, ...args: any[]): void {
    this.logToConsole(LogLevel.INFO, context, message, ...args)
  }

  // 警告日志
  warn(context: string, message: any, ...args: any[]): void {
    this.logToConsole(LogLevel.WARN, context, message, ...args)
  }

  // 错误日志
  error(context: string, message: any, ...args: any[]): void {
    this.logToConsole(LogLevel.ERROR, context, message, ...args)
  }

  // 更新配置
  updateConfig(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config }
  }

  // 获取当前配置
  getConfig(): LoggerConfig {
    return { ...this.config }
  }
}

// 创建默认实例
export const logger = Logger.getInstance()

// 导出便捷方法
export const log = {
  debug: (context: string, message: any, ...args: any[]) => logger.debug(context, message, ...args),
  info: (context: string, message: any, ...args: any[]) => logger.info(context, message, ...args),
  warn: (context: string, message: any, ...args: any[]) => logger.warn(context, message, ...args),
  error: (context: string, message: any, ...args: any[]) => logger.error(context, message, ...args)
}

// 兼容旧的console调用（逐步迁移用）
export const createContextLogger = (context: string) => ({
  debug: (message: any, ...args: any[]) => logger.debug(context, message, ...args),
  info: (message: any, ...args: any[]) => logger.info(context, message, ...args),
  warn: (message: any, ...args: any[]) => logger.warn(context, message, ...args),
  error: (message: any, ...args: any[]) => logger.error(context, message, ...args)
})

export default logger