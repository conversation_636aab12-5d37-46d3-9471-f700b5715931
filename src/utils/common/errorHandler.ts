/**
 * 统一错误处理服务
 * 提供标准化的错误提示和日志记录功能
 */

import { createContextLogger } from './logger'

export interface ErrorOptions {
  title?: string
  icon?: 'success' | 'error' | 'loading' | 'none'
  duration?: number
  mask?: boolean
}

export interface LogOptions {
  level?: 'error' | 'warn' | 'info' | 'debug'
  context?: string
  extra?: any
}

/**
 * 错误处理器
 */
export class ErrorHandler {
  /**
   * 显示错误提示
   * @param message 错误消息
   * @param options 提示选项
   */
  static showError(message: string, options: ErrorOptions = {}) {
    const defaultOptions: ErrorOptions = {
      icon: 'none',
      duration: 2000,
      mask: false
    }
    
    const finalOptions = { ...defaultOptions, ...options }
    
    uni.showToast({
      title: message,
      icon: finalOptions.icon,
      duration: finalOptions.duration,
      mask: finalOptions.mask
    })
  }
  
  /**
   * 显示成功提示
   * @param message 成功消息
   * @param options 提示选项
   */
  static showSuccess(message: string, options: ErrorOptions = {}) {
    this.showError(message, { ...options, icon: 'success' })
  }
  
  /**
   * 显示警告提示
   * @param message 警告消息
   * @param options 提示选项
   */
  static showWarning(message: string, options: ErrorOptions = {}) {
    this.showError(message, { ...options, icon: 'none' })
  }
  
  /**
   * 记录错误日志
   * @param context 上下文
   * @param error 错误对象
   * @param options 日志选项
   */
  static logError(context: string, error: any, options: LogOptions = {}) {
    const { level = 'error', extra } = options
    
    // 使用统一的logger服务
    const logger = createContextLogger(context)
    
    switch (level) {
      case 'error':
        logger.error(error, extra)
        break
      case 'warn':
        logger.warn(error, extra)
        break
      case 'info':
        logger.info(error, extra)
        break
      case 'debug':
        logger.debug(error, extra)
        break
    }
  }
  
  /**
   * 记录警告日志
   * @param context 上下文
   * @param message 警告消息
   * @param extra 额外信息
   */
  static logWarning(context: string, message: string, extra?: any) {
    this.logError(context, message, { level: 'warn', extra })
  }
  
  /**
   * 记录信息日志
   * @param context 上下文
   * @param message 信息消息
   * @param extra 额外信息
   */
  static logInfo(context: string, message: string, extra?: any) {
    this.logError(context, message, { level: 'info', extra })
  }
  
  /**
   * 处理API错误
   * @param context 上下文
   * @param error 错误对象
   * @param showToast 是否显示提示
   */
  static handleApiError(context: string, error: any, showToast = true) {
    let message = '操作失败，请重试'
    
    if (error?.response?.data?.message) {
      message = error.response.data.message
    } else if (error?.message) {
      message = error.message
    } else if (typeof error === 'string') {
      message = error
    }
    
    this.logError(context, error)
    
    if (showToast) {
      this.showError(message)
    }
    
    return message
  }
  
  /**
   * 处理网络错误
   * @param context 上下文
   * @param error 错误对象
   */
  static handleNetworkError(context: string, error: any) {
    let message = '网络连接失败，请检查网络设置'
    
    if (error?.code === 'NETWORK_ERROR') {
      message = '网络连接超时，请重试'
    } else if (error?.code === 'TIMEOUT') {
      message = '请求超时，请重试'
    }
    
    this.logError(context, error)
    this.showError(message)
    
    return message
  }
  
  /**
   * 处理表单验证错误
   * @param context 上下文
   * @param errors 验证错误对象
   */
  static handleValidationError(context: string, errors: Record<string, string>) {
    const firstError = Object.values(errors)[0]
    if (firstError) {
      this.showError(firstError)
    }
    
    this.logWarning(context, '表单验证失败', errors)
  }
  
  /**
   * 创建错误处理装饰器
   * @param context 上下文
   * @param showToast 是否显示提示
   */
  static createErrorHandler(context: string, showToast = true) {
    return (error: any) => {
      return this.handleApiError(context, error, showToast)
    }
  }
}

/**
 * 错误处理装饰器
 * @param context 上下文
 */
export function withErrorHandler(context: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = async function (...args: any[]) {
      try {
        return await originalMethod.apply(this, args)
      } catch (error) {
        ErrorHandler.handleApiError(`${context}.${propertyKey}`, error)
        throw error
      }
    }
    
    return descriptor
  }
}

// 导出便捷方法
export const showError = ErrorHandler.showError.bind(ErrorHandler)
export const showSuccess = ErrorHandler.showSuccess.bind(ErrorHandler)
export const showWarning = ErrorHandler.showWarning.bind(ErrorHandler)
export const logError = ErrorHandler.logError.bind(ErrorHandler)
export const logWarning = ErrorHandler.logWarning.bind(ErrorHandler)
export const logInfo = ErrorHandler.logInfo.bind(ErrorHandler)
export const handleApiError = ErrorHandler.handleApiError.bind(ErrorHandler)
export const handleNetworkError = ErrorHandler.handleNetworkError.bind(ErrorHandler)
export const handleValidationError = ErrorHandler.handleValidationError.bind(ErrorHandler)