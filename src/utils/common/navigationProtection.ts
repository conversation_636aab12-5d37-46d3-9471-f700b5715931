/**
 * 路由跳转保护工具
 * 确保页面切换时数据安全保存和恢复
 */
import { StoreUtil, StorageModule } from '../storage/StoreUtil'

/**
 * 受保护的路由跳转
 */
export const navigateWithProtection = (
  url: string,
  options: {
    pageData?: any
    saveCurrentState?: boolean
    navigationType?: 'navigateTo' | 'redirectTo' | 'reLaunch' | 'switchTab'
  } = {}
) => {
  const { pageData, saveCurrentState = true, navigationType = 'navigateTo' } = options
  
  if (saveCurrentState) {
    // 获取当前页面信息
    const pages = getCurrentPages() as any[]
    const currentPage = pages[pages.length - 1]
    const pageKey = currentPage?.route || 'unknown'
    
    // 保存当前页面数据
    if (pageData) {
      StoreUtil.set(`page_data_${pageKey}`, pageData, StorageModule.CONFIG)
      // console.log(`[NavigationProtection] 保存页面数据: ${pageKey}`, pageData)
    }
    
    // 保存页面栈信息
    const pageStack = pages.map((page: any) => ({
      route: page.route,
      options: page.options
    }))
    StoreUtil.set('page_stack', pageStack, StorageModule.CONFIG)
  }
  
  // 执行跳转
  switch (navigationType) {
    case 'navigateTo':
      uni.navigateTo({ url })
      break
    case 'redirectTo':
      uni.redirectTo({ url })
      break
    case 'reLaunch':
      uni.reLaunch({ url })
      break
    case 'switchTab':
      uni.switchTab({ url })
      break
  }
}

/**
 * 获取页面恢复数据
 */
export const getPageRecoveryData = () => {
  const pages = getCurrentPages() as any[]
  const currentPage = pages[pages.length - 1]
  const pageKey = currentPage?.route || 'unknown'
  
  return StoreUtil.get(`page_data_${pageKey}`, StorageModule.CONFIG)
}

/**
 * 清除页面数据
 */
export const clearPageData = (pageRoute?: string) => {
  const pages = getCurrentPages() as any[]
  const currentPage = pages[pages.length - 1]
  const pageKey = pageRoute || currentPage?.route || 'unknown'
  
  StoreUtil.remove(`page_data_${pageKey}`, StorageModule.CONFIG)
  // console.log(`[NavigationProtection] 清除页面数据: ${pageKey}`)
}

/**
 * 页面栈恢复管理
 */
export class PageStackManager {
  /**
   * 保存当前页面栈
   */
  static saveCurrentStack() {
    const pages = getCurrentPages() as any[]
    const pageStack = pages.map((page: any) => ({
      route: page.route,
      options: page.options
    }))
    StoreUtil.set('page_stack_backup', pageStack, StorageModule.CONFIG)
  }
  
  /**
   * 获取保存的页面栈
   */
  static getSavedStack() {
    return StoreUtil.get('page_stack_backup', StorageModule.CONFIG) as unknown as any[] | null
  }
  
  /**
   * 清除页面栈备份
   */
  static clearStackBackup() {
    StoreUtil.remove('page_stack_backup', StorageModule.CONFIG)
  }
  
  /**
   * 恢复页面栈（用于应用崩溃恢复）
   */
  static restoreStack() {
    const savedStack = this.getSavedStack()
    if (savedStack && savedStack.length > 0) {
      const lastPage = savedStack[savedStack.length - 1] as any
      if (lastPage.route) {
        // console.log('[PageStack] 恢复到最后页面:', lastPage.route)
        uni.reLaunch({ url: `/${lastPage.route}` })
      }
    }
  }
}