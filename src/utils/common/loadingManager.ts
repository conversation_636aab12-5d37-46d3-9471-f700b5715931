/**
 * 加载状态管理服务
 * 提供统一的加载状态管理，避免多个加载状态冲突
 */

import { createContextLogger } from './logger'

export interface LoadingOptions {
  title?: string
  mask?: boolean
  timeout?: number
}

export interface LoadingTask {
  id: string
  title: string
  startTime: number
  timeout?: number
}

/**
 * 加载管理器类
 */
class LoadingManagerService {
  private static instance: LoadingManagerService
  private loadingTasks: Map<string, LoadingTask> = new Map()
  private currentTitle = '加载中...'
  private isShowing = false
  private timeoutHandlers: Map<string, NodeJS.Timeout> = new Map()

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): LoadingManagerService {
    if (!this.instance) {
      this.instance = new LoadingManagerService()
    }
    return this.instance
  }

  /**
   * 显示加载状态
   * @param options 加载选项
   * @returns 任务ID，用于后续隐藏
   */
  show(options: LoadingOptions = {}): string {
    const taskId = this.generateTaskId()
    const {
      title = '加载中...',
      mask = true,
      timeout
    } = options

    const task: LoadingTask = {
      id: taskId,
      title,
      startTime: Date.now(),
      timeout
    }

    this.loadingTasks.set(taskId, task)

    // 设置超时处理
    if (timeout && timeout > 0) {
      const timeoutHandler = setTimeout(() => {
        this.hide(taskId)
        const logger = createContextLogger('LoadingManager')
        logger.warn(`加载超时自动隐藏: ${title}`, { taskId, timeout })
      }, timeout)
      
      this.timeoutHandlers.set(taskId, timeoutHandler)
    }

    this.updateLoadingState(title, mask)
    
    return taskId
  }

  /**
   * 隐藏加载状态
   * @param taskId 任务ID
   */
  hide(taskId: string): void {
    if (!this.loadingTasks.has(taskId)) {
      return
    }

    // 清除超时处理器
    const timeoutHandler = this.timeoutHandlers.get(taskId)
    if (timeoutHandler) {
      clearTimeout(timeoutHandler)
      this.timeoutHandlers.delete(taskId)
    }

    this.loadingTasks.delete(taskId)
    this.updateLoadingState()
  }

  /**
   * 隐藏所有加载状态
   */
  hideAll(): void {
    // 清除所有超时处理器
    this.timeoutHandlers.forEach(handler => clearTimeout(handler))
    this.timeoutHandlers.clear()
    
    this.loadingTasks.clear()
    this.updateLoadingState()
  }

  /**
   * 获取当前加载任务数量
   */
  getTaskCount(): number {
    return this.loadingTasks.size
  }

  /**
   * 检查是否正在加载
   */
  isLoading(): boolean {
    return this.loadingTasks.size > 0
  }

  /**
   * 获取当前加载任务列表
   */
  getTasks(): LoadingTask[] {
    return Array.from(this.loadingTasks.values())
  }

  /**
   * 获取最长运行的任务
   */
  getLongestRunningTask(): LoadingTask | null {
    if (this.loadingTasks.size === 0) {
      return null
    }

    let longestTask: LoadingTask | null = null
    let longestDuration = 0

    this.loadingTasks.forEach(task => {
      const duration = Date.now() - task.startTime
      if (duration > longestDuration) {
        longestDuration = duration
        longestTask = task
      }
    })

    return longestTask
  }

  /**
   * 清理超时任务
   * @param maxDuration 最大持续时间（毫秒）
   */
  cleanupTimeoutTasks(maxDuration = 30000): void {
    const now = Date.now()
    const tasksToRemove: string[] = []

    this.loadingTasks.forEach(task => {
      if (now - task.startTime > maxDuration) {
        tasksToRemove.push(task.id)
        const logger = createContextLogger('LoadingManager')
        logger.warn(`清理超时任务: ${task.title}`, {
          taskId: task.id,
          duration: now - task.startTime
        })
      }
    })

    tasksToRemove.forEach(taskId => this.hide(taskId))
  }

  /**
   * 更新加载状态
   */
  private updateLoadingState(title?: string, mask = true): void {
    const taskCount = this.loadingTasks.size

    if (taskCount > 0) {
      // 确定显示的标题
      let displayTitle = title || this.currentTitle
      
      // 如果有多个任务，显示最新的标题
      if (!title && this.loadingTasks.size > 0) {
        const latestTask = Array.from(this.loadingTasks.values())
          .sort((a, b) => b.startTime - a.startTime)[0]
        displayTitle = latestTask.title
      }

      this.currentTitle = displayTitle

      // 只在没有显示时才调用uni.showLoading
      if (!this.isShowing) {
        try {
          uni.showLoading({
            title: displayTitle,
            mask
          })
          this.isShowing = true
        } catch (error) {
          const logger = createContextLogger('LoadingManager')
          logger.warn('显示加载状态失败', { error, title: displayTitle })
        }
      }
    } else {
      // 没有任务时隐藏加载状态
      if (this.isShowing) {
        try {
          uni.hideLoading()
          this.isShowing = false
        } catch (error) {
          const logger = createContextLogger('LoadingManager')
          logger.warn('隐藏加载状态失败', { error })
        }
      }
    }
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    return `loading_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

// 导出单例实例
export const loadingManager = LoadingManagerService.getInstance()

/**
 * 便捷的加载管理函数
 */
export class LoadingManager {
  /**
   * 显示加载状态
   * @param title 加载标题
   * @param options 加载选项
   */
  static show(title = '加载中...', options: Omit<LoadingOptions, 'title'> = {}): string {
    return loadingManager.show({ title, ...options })
  }

  /**
   * 隐藏加载状态
   * @param taskId 任务ID
   */
  static hide(taskId: string): void {
    loadingManager.hide(taskId)
  }

  /**
   * 隐藏所有加载状态
   */
  static hideAll(): void {
    loadingManager.hideAll()
  }

  /**
   * 检查是否正在加载
   */
  static isLoading(): boolean {
    return loadingManager.isLoading()
  }

  /**
   * 获取任务数量
   */
  static getTaskCount(): number {
    return loadingManager.getTaskCount()
  }

  /**
   * 包装异步函数，自动管理加载状态
   * @param asyncFn 异步函数
   * @param title 加载标题
   * @param options 加载选项
   */
  static async withLoading<T>(
    asyncFn: () => Promise<T>,
    title = '加载中...',
    options: Omit<LoadingOptions, 'title'> = {}
  ): Promise<T> {
    const taskId = this.show(title, options)
    try {
      return await asyncFn()
    } finally {
      this.hide(taskId)
    }
  }

  /**
   * 创建加载装饰器
   * @param title 加载标题
   * @param options 加载选项
   */
  static createLoadingDecorator(title = '加载中...', options: Omit<LoadingOptions, 'title'> = {}) {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
      const originalMethod = descriptor.value

      descriptor.value = async function (...args: any[]) {
        return LoadingManager.withLoading(
          () => originalMethod.apply(this, args),
          title,
          options
        )
      }

      return descriptor
    }
  }
}

// 导出便捷方法
export const showLoading = LoadingManager.show.bind(LoadingManager)
export const hideLoading = LoadingManager.hide.bind(LoadingManager)
export const hideAllLoading = LoadingManager.hideAll.bind(LoadingManager)
export const isLoading = LoadingManager.isLoading.bind(LoadingManager)
export const withLoading = LoadingManager.withLoading.bind(LoadingManager)

/**
 * 加载装饰器
 * @param title 加载标题
 * @param options 加载选项
 */
export function loading(title = '加载中...', options: Omit<LoadingOptions, 'title'> = {}) {
  return LoadingManager.createLoadingDecorator(title, options)
}