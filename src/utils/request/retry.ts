/**
 * 重试机制配置
 */

export interface RetryConfig {
  maxRetries: number
  retryDelay: number
  retryCondition: (error: any) => boolean
}

/**
 * 创建重试机制
 */
export const createRetryMechanism = (): RetryConfig => {
  return {
    maxRetries: 3,
    retryDelay: 1000,
    retryCondition: (error: any) => {
      // 网络错误或超时错误才重试
      return error.message?.includes('Network Error') || 
             error.message?.includes('timeout') ||
             error.message?.includes('request:fail')
    }
  }
} 