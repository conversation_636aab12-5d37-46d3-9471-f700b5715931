/**
 * 统一加密服务
 * 解决项目中重复导入和使用加密工具的问题
 */

import { aesEncrypt, aesDecrypt } from '@/utils/helpers/crypto'

/**
 * 加密服务类
 */
export class CryptoService {
  /**
   * AES加密
   * @param data 要加密的数据
   * @param key 加密密钥（可选）
   * @returns 加密后的字符串
   */
  static encrypt(data: string, key?: string): string {
    try {
      return aesEncrypt(data, key || '')
    } catch (error) {
      console.error('加密失败:', error)
      throw new Error('数据加密失败')
    }
  }

  /**
   * AES解密
   * @param encryptedData 要解密的数据
   * @param key 解密密钥（可选）
   * @returns 解密后的字符串
   */
  static decrypt(encryptedData: string, key?: string): string {
    try {
      return aesDecrypt(encryptedData, key || '')
    } catch (error) {
      console.error('解密失败:', error)
      throw new Error('数据解密失败')
    }
  }

  /**
   * 安全加密（带错误处理）
   * @param data 要加密的数据
   * @param key 加密密钥（可选）
   * @returns 加密结果或原始数据
   */
  static safeEncrypt(data: string, key?: string): string {
    if (!key) {
      console.warn('CryptoService: 加密密钥为空，返回原始数据');
      return data;
    }
    try {
      return this.encrypt(data, key)
    } catch {
      return data
    }
  }

  /**
   * 安全解密（带错误处理）
   * @param encryptedData 要解密的数据
   * @param key 解密密钥（可选）
   * @returns 解密结果或原始数据
   */
  static safeDecrypt(encryptedData: string, key?: string): string {
    if (!key) {
      console.warn('CryptoService: 解密密钥为空，返回原始数据');
      return encryptedData;
    }
    try {
      return this.decrypt(encryptedData, key)
    } catch {
      return encryptedData
    }
  }

  /**
   * 批量加密
   * @param dataList 要加密的数据列表
   * @param key 加密密钥（可选）
   * @returns 加密后的数据列表
   */
  static encryptBatch(dataList: string[], key?: string): string[] {
    return dataList.map(data => this.encrypt(data, key))
  }

  /**
   * 批量解密
   * @param encryptedDataList 要解密的数据列表
   * @param key 解密密钥（可选）
   * @returns 解密后的数据列表
   */
  static decryptBatch(encryptedDataList: string[], key?: string): string[] {
    return encryptedDataList.map(data => this.decrypt(data, key))
  }

  /**
   * 验证数据完整性（加密后再解密验证）
   * @param data 原始数据
   * @param key 密钥（可选）
   * @returns 是否验证通过
   */
  static verifyIntegrity(data: string, key?: string): boolean {
    try {
      const encrypted = this.encrypt(data, key)
      const decrypted = this.decrypt(encrypted, key)
      return data === decrypted
    } catch {
      return false
    }
  }
}

/**
 * 便捷的加密函数（向后兼容）
 */
export const encrypt = CryptoService.encrypt
export const decrypt = CryptoService.decrypt
export const safeEncrypt = CryptoService.safeEncrypt
export const safeDecrypt = CryptoService.safeDecrypt

/**
 * 默认导出加密服务
 */
export default CryptoService