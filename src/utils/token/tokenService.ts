/**
 * Token解析和管理服务
 * 统一处理JWT Token的解析、存储和获取
 */
import { tokenUtil } from './TokenUtil';

// JWT解析后的Claims数据结构
export interface TokenClaims {
  loginType?: string;
  loginId?: string;
  rnStr?: string;
  tenantId?: string;
  userId?: number;
  userName?: string;
  [key: string]: unknown;
}

// JWT解析后的完整结构
export interface JwtPayload {
  claimsJson?: string | {
    loginType?: string;
    loginId?: string;
    rnStr?: string;
    tenantId?: string;
    userId?: number;
    userName?: string;
    [key: string]: unknown;
  };
  exp?: number;
  iat?: number;
  userName?: string;
  userId?: number;
  user?: {
    userName?: string;
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

/**
 * 解析JWT Token
 * @returns 解析后的Token信息或null
 */
export async function parseJwtToken(): Promise<JwtPayload | null> {
  const token = await tokenUtil.getAccessToken();
  if (!token) return null;
  
  try {
    // JWT Token由三部分组成，以.分隔：header.payload.signature
    const parts = token.split('.');
    if (parts.length !== 3) return null;
    
    // 解码payload部分
    const payload = parts[1];
    const decodedPayload = decodeBase64Url(payload);
    const payloadObj = JSON.parse(decodedPayload);
    
    return payloadObj;
  } catch (_error) {
    // console.error('解析JWT Token失败:', _error);
    return null;
  }
}

/**
 * 获取Token中的用户Claims信息
 * @returns 用户Claims或null
 */
export async function getUserClaims(): Promise<TokenClaims | null> {
  const payload = await parseJwtToken();
  if (!payload) return null;
  
  // 从嵌套的claimsJson中获取用户信息
  if (payload.claimsJson) {
    // 如果claimsJson是字符串，尝试解析它
    if (typeof payload.claimsJson === 'string') {
      try {
        return JSON.parse(payload.claimsJson);
      } catch (_e) {
          // console.error('解析claimsJson字符串失败:', _e);
      }
    } else {
      // 已经是对象
      return payload.claimsJson as TokenClaims;
    }
  }
  
  return null;
}

/**
 * 获取用户ID
 * @returns 用户ID或null
 */
export async function getUserId(): Promise<number | null> {
  const claims = await getUserClaims();
  return claims?.userId || null;
}

/**
 * 获取用户名
 * @returns 用户名或空字符串
 */
export async function getUserName(): Promise<string> {
  // 尝试从token中提取用户名
  try {
    const payload = await parseJwtToken();
    // console.log('完整token payload:', payload);
    
    if (!payload) {
      // console.log('未获取到token payload');
      return '';
    }
    
    // 处理最新结构: claimsJson为字符串的情况
    if (payload.claimsJson && typeof payload.claimsJson === 'string') {
      try {
        const parsedClaims = JSON.parse(payload.claimsJson) as TokenClaims;
        // console.log('解析claimsJson字符串得到:', parsedClaims);
        
        if (parsedClaims.userName) {
          // console.log('从claimsJson字符串中获取到用户名:', parsedClaims.userName);
          return parsedClaims.userName;
        }
      } catch (_e) {
          // console.error('解析claimsJson字符串失败:', _e);
      }
    }
    
    // 处理claimsJson为对象的情况
    if (payload.claimsJson && typeof payload.claimsJson === 'object') {
      const claims = payload.claimsJson as TokenClaims;
      if (claims.userName) {
        // console.log('从claimsJson对象中获取到用户名:', claims.userName);
        return claims.userName;
      }
    }
    
    // 直接从payload根级别寻找
    if (payload.userName) {
      // console.log('从payload根级别获取到用户名:', payload.userName);
      return payload.userName;
    }
    
    // 从user对象获取
    if (payload.user && payload.user.userName) {
      // console.log('从user对象中获取到用户名:', payload.user.userName);}]}}
      return payload.user.userName;
    }
    
    // 最后尝试从名为userId的字段
    if (payload.userId) {
      // console.log('从userId字段获取到用户名:', payload.userId);
      return String(payload.userId);
    }
    
    // console.log('未能从token中获取到用户名');
    return '';
  } catch (_error) {
    // console.error('获取用户名时发生错误:', _error);
    return '';
  }
}

/**
 * 获取登录类型
 * @returns 登录类型或空字符串
 */
export async function getLoginType(): Promise<string> {
  const claims = await getUserClaims();
  return claims?.loginType || '';
}

/**
 * 获取租户ID
 * @returns 租户ID或空字符串
 */
export async function getTenantId(): Promise<string> {
  const claims = await getUserClaims();
  return claims?.tenantId || '';
}

/**
 * 解码Base64URL编码的字符串
 * @param input Base64URL编码的字符串
 * @returns 解码后的字符串
 */
function decodeBase64Url(input: string): string {
  // 将Base64URL转换为标准Base64
  const base64 = input.replace(/-/g, '+').replace(/_/g, '/');
  
  // 处理填充
  const pad = base64.length % 4;
  const paddedBase64 = pad ? base64 + '='.repeat(4 - pad) : base64;
  
  // 解码
  try {
    // 在浏览器环境中
    return decodeURIComponent(
      atob(paddedBase64)
        .split('')
        .map(c => `%${(`00${  c.charCodeAt(0).toString(16)}`).slice(-2)}`)
        .join('')
    );
  } catch (_error) {
    // console.error('解码Base64URL失败:', _error);
    return '';
  }
}

/**
 * 检查Token是否过期
 * @returns 是否过期
 */
export async function isTokenExpired(): Promise<boolean> {
  const payload = await parseJwtToken();
  if (!payload || !payload.exp) return true;
  
  // exp是Unix时间戳（秒）
  const now = Math.floor(Date.now() / 1000);
  return payload.exp < now;
}