/**
 * Token工具类
 * 提供Token的存储、获取、刷新、验证等功能
 * 支持自动刷新和安全存储
 */

import { StoreUtil, StorageModule } from '../storage/StoreUtil'
import { secureStorage } from '../storage/adapters/SecureStorageAdapter'
// import { createContextLogger } from '../common/logger'

// ==================== 类型定义 ====================

/** Token数据接口 */
export interface TokenData {
  /** 访问令牌 */
  accessToken: string
  /** 刷新令牌 */
  refreshToken?: string
  /** 过期时间戳 */
  expiresAt: number
  /** Token类型 */
  tokenType?: string
  /** 创建时间戳 */
  createdAt: number
  /** 最后刷新时间戳 */
  lastRefreshAt?: number
}

/** Token状态接口 */
export interface TokenStatus {
  /** 是否有效 */
  isValid: boolean
  /** 是否即将过期 */
  isExpiringSoon: boolean
  /** 剩余时间（毫秒） */
  remainingTime: number
  /** 过期时间戳 */
  expiresAt: number
  /** 状态描述 */
  status: 'valid' | 'expired' | 'expiring_soon' | 'invalid'
}

/** Token配置接口 */
export interface TokenConfig {
  /** 刷新阈值（分钟），默认5分钟 */
  refreshThresholdMinutes?: number
  /** 是否自动刷新，默认true */
  autoRefresh?: boolean
  /** 存储模块，默认AUTH */
  storageModule?: StorageModule
  /** Token类型，默认Bearer */
  tokenType?: string
}

// ==================== 常量定义 ====================

/** Token存储键 */
const TOKEN_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  TOKEN_DATA: 'token_data',
  TOKEN_EXPIRES_AT: 'token_expires_at',
  TOKEN_CREATED_AT: 'token_created_at',
  TOKEN_LAST_REFRESH: 'token_last_refresh'
} as const

// ==================== TokenUtil类 ====================

class TokenUtil {
  private readonly defaultConfig: Required<TokenConfig> = {
    refreshThresholdMinutes: 5,
    autoRefresh: true,
    storageModule: StorageModule.AUTH,
    tokenType: 'Bearer'
  }

  private config: Required<TokenConfig>
  private refreshTimer: NodeJS.Timeout | null = null

  constructor(config?: TokenConfig) {
    this.config = { ...this.defaultConfig, ...config }
    this.setupAutoRefresh()
  }

  // ==================== 核心Token管理方法 ====================

  /**
   * 保存Token数据
   */
  async saveToken(tokenData: Partial<TokenData> & { accessToken: string }): Promise<boolean> {
    try {
      const now = Date.now()
      const fullTokenData: TokenData = {
        accessToken: tokenData.accessToken,
        refreshToken: tokenData.refreshToken,
        expiresAt: tokenData.expiresAt || (now + 7200000), // 默认2小时
        tokenType: tokenData.tokenType || this.config.tokenType,
        createdAt: tokenData.createdAt || now,
        lastRefreshAt: tokenData.lastRefreshAt
      }

      // 保存access_token到普通存储
      const accessTokenResult = await StoreUtil.set(
        TOKEN_KEYS.ACCESS_TOKEN,
        fullTokenData.accessToken,
        this.config.storageModule
      )

      // 保存refresh_token到安全存储（如果存在）
      let refreshTokenResult = true
      if (fullTokenData.refreshToken) {
        refreshTokenResult = await secureStorage.setItem(
          TOKEN_KEYS.REFRESH_TOKEN,
          fullTokenData.refreshToken
        )
        // const logger = createContextLogger('TokenUtil')
        // logger.debug('refresh_token已保存到安全存储')
      }

      // 保存完整Token数据到普通存储（不包含refresh_token）
      const tokenDataForStorage = { ...fullTokenData }
      delete tokenDataForStorage.refreshToken // 从普通存储中移除refresh_token
      
      const saveResult = await StoreUtil.set(
        TOKEN_KEYS.TOKEN_DATA,
        tokenDataForStorage,
        this.config.storageModule
      )

      // 检查所有保存操作是否成功
      if (!accessTokenResult || !refreshTokenResult || !saveResult) {
        // const logger = createContextLogger('TokenUtil')
        // logger.error('Token保存失败')
        return false
      }

      const additionalSaves = await Promise.all([
        StoreUtil.set(TOKEN_KEYS.TOKEN_EXPIRES_AT, fullTokenData.expiresAt, this.config.storageModule),
        StoreUtil.set(TOKEN_KEYS.TOKEN_CREATED_AT, fullTokenData.createdAt, this.config.storageModule)
      ])

      if (additionalSaves.some(result => !result)) {
        // const logger = createContextLogger('TokenUtil')
        // logger.error('Token附加信息保存失败')
        return false
      }

      // const logger = createContextLogger('TokenUtil')
      // logger.debug('Token保存成功')
      return true
    } catch (_error) {
       // const logger = createContextLogger('TokenUtil')
       // logger.error('Token保存异常:', _error)
       return false
     }
  }

  /**
   * 获取访问令牌
   */
  async getAccessToken(): Promise<string | null> {
    try {
      return await StoreUtil.get(TOKEN_KEYS.ACCESS_TOKEN, this.config.storageModule)
    } catch (_error) {
       // const logger = createContextLogger('TokenUtil')
       // logger.error('获取访问令牌失败:', _error)
       return null
     }
  }

  /**
   * 获取刷新令牌
   */
  async getRefreshToken(): Promise<string | null> {
    try {
      // 从安全存储获取refresh_token
      const refreshToken = await secureStorage.getItem(TOKEN_KEYS.REFRESH_TOKEN)
      return refreshToken
    } catch (_error) {
       // const logger = createContextLogger('TokenUtil')
       // logger.error('获取刷新令牌失败:', _error)
       return null
     }
  }

  /**
   * 获取完整Token数据
   */
  async getTokenData(): Promise<TokenData | null> {
    try {
      const tokenData = await StoreUtil.get(TOKEN_KEYS.TOKEN_DATA, this.config.storageModule)
      if (tokenData) {
        // 从安全存储获取refresh_token并合并
        const refreshToken = await this.getRefreshToken()
        return { ...tokenData, refreshToken }
      }
      return null
    } catch (_error) {
       // const logger = createContextLogger('TokenUtil')
       // logger.error('获取Token数据失败:', _error)
       return null
     }
  }

  /**
   * 获取Token状态
   */
  async getTokenStatus(): Promise<TokenStatus> {
    try {
      const tokenData = await this.getTokenData()
      
      if (!tokenData || !tokenData.accessToken) {
        return {
          isValid: false,
          isExpiringSoon: false,
          remainingTime: 0,
          expiresAt: 0,
          status: 'invalid'
        }
      }

      const now = Date.now()
      const remainingTime = tokenData.expiresAt - now
      const thresholdTime = this.config.refreshThresholdMinutes * 60 * 1000

      if (remainingTime <= 0) {
        return {
          isValid: false,
          isExpiringSoon: false,
          remainingTime: 0,
          expiresAt: tokenData.expiresAt,
          status: 'expired'
        }
      }

      const isExpiringSoon = remainingTime <= thresholdTime

      return {
        isValid: true,
        isExpiringSoon,
        remainingTime,
        expiresAt: tokenData.expiresAt,
        status: isExpiringSoon ? 'expiring_soon' : 'valid'
      }
    } catch (_error) {
      // const logger = createContextLogger('TokenUtil')
      // logger.error('获取Token状态失败:', _error)
      return {
        isValid: false,
        isExpiringSoon: false,
        remainingTime: 0,
        expiresAt: 0,
        status: 'invalid'
      }
    }
  }

  /**
   * 检查Token是否有效
   */
  async isTokenValid(): Promise<boolean> {
    const status = await this.getTokenStatus()
    return status.isValid
  }

  /**
   * 检查Token是否即将过期
   */
  async isTokenExpiringSoon(): Promise<boolean> {
    const status = await this.getTokenStatus()
    return status.isExpiringSoon
  }

  /**
   * 刷新Token
   */
  async refreshToken(): Promise<boolean> {
    try {
      const refreshToken = await this.getRefreshToken()
      
      if (!refreshToken) {
        // const logger = createContextLogger('TokenUtil')
        // logger.warn('没有刷新令牌，无法刷新')
        return false
      }

      // const logger = createContextLogger('TokenUtil')
      // logger.debug('开始刷新Token')
      
      // 动态导入auth API模块，避免循环依赖
      const { refreshToken: refreshTokenApi } = await import('@/api/auth/auth')
      
      // 调用刷新Token的API
      const response = await refreshTokenApi({ refreshToken })
      
      if (response && response.data && response.data.access_token) {
        // 计算新的过期时间
        const expiresAt = Date.now() + (response.data.expire_in * 1000)
        
        await this.saveToken({
          accessToken: response.data.access_token,
          refreshToken: response.data.refresh_token || refreshToken,
          expiresAt
        })
        
        // logger.debug('Token刷新成功')
        return true
      }
      
      // logger.warn('Token刷新失败：响应数据无效')
      return false
    } catch (_error) {
      // const logger = createContextLogger('TokenUtil')
      // logger.error('Token刷新异常:', _error)
      return false
    }
  }

  /**
   * 清除Token
   */
  async clearToken(): Promise<boolean> {
    try {
      // 清除普通存储中的token数据
      const clearResults = await Promise.all([
        StoreUtil.remove(TOKEN_KEYS.ACCESS_TOKEN, this.config.storageModule),
        StoreUtil.remove(TOKEN_KEYS.TOKEN_DATA, this.config.storageModule),
        StoreUtil.remove(TOKEN_KEYS.TOKEN_EXPIRES_AT, this.config.storageModule),
        StoreUtil.remove(TOKEN_KEYS.TOKEN_CREATED_AT, this.config.storageModule),
        StoreUtil.remove(TOKEN_KEYS.TOKEN_LAST_REFRESH, this.config.storageModule)
      ])

      // 清除安全存储中的refresh_token
      const secureResult = await secureStorage.removeItem(TOKEN_KEYS.REFRESH_TOKEN)

      // 清除自动刷新定时器
      if (this.refreshTimer) {
        clearTimeout(this.refreshTimer)
        this.refreshTimer = null
      }

      const allSuccess = clearResults.every(result => result) && secureResult
      
      // const logger = createContextLogger('TokenUtil')
      // if (allSuccess) {
      //   logger.debug('Token清除成功')
      // } else {
      //   logger.warn('Token清除部分失败')
      // }
      
      return allSuccess
    } catch (_error) {
      // const logger = createContextLogger('TokenUtil')
      // logger.error('Token清除异常:', _error)
      return false
    }
  }

  // ==================== 自动刷新相关方法 ====================

  /**
   * 设置自动刷新
   */
  private setupAutoRefresh(): void {
    if (this.config.autoRefresh) {
      // 启动时检查并安排刷新
      this.checkAndScheduleRefresh()
    }
  }

  /**
   * 检查并安排刷新
   */
  private async checkAndScheduleRefresh(): Promise<void> {
    try {
      const tokenData = await this.getTokenData()
      if (tokenData && tokenData.expiresAt) {
        this.scheduleRefresh(tokenData.expiresAt)
      }
    } catch (_error) {
      // const logger = createContextLogger('TokenUtil')
      // logger.error('检查刷新安排失败:', _error)
    }
  }

  /**
   * 安排刷新
   */
  private scheduleRefresh(expiresAt: number): void {
    // 清除现有定时器
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer)
    }

    const now = Date.now()
    const thresholdTime = this.config.refreshThresholdMinutes * 60 * 1000
    const refreshTime = expiresAt - thresholdTime
    const delay = refreshTime - now

    if (delay > 0) {
      this.refreshTimer = setTimeout(async () => {
        // const logger = createContextLogger('TokenUtil')
        // logger.debug('自动刷新Token触发')
        const success = await this.refreshToken()
        
        if (success) {
          // 刷新成功，重新安排下次刷新
          const newTokenData = await this.getTokenData()
          if (newTokenData && newTokenData.expiresAt) {
            this.scheduleRefresh(newTokenData.expiresAt)
          }
        } else {
          // 刷新失败，执行失败处理
          this.onRefreshFailed()
        }
      }, delay)
      
      // const logger = createContextLogger('TokenUtil')
      // logger.debug(`已安排自动刷新，${Math.round(delay / 1000 / 60)}分钟后执行`)
    } else {
      // const logger = createContextLogger('TokenUtil')
      // logger.warn('Token已过期或即将过期，立即尝试刷新')
      this.refreshToken()
    }
  }

  /**
   * 刷新失败处理
   */
  private onRefreshFailed(): void {
    // const logger = createContextLogger('TokenUtil')
    // logger.debug('自动刷新失败，清除Token')
    this.clearToken()
    
    // 可以在这里触发登出事件或跳转到登录页
    // 例如：uni.navigateTo({ url: '/pages/auth/login' })
  }

  // ==================== 工具方法 ====================

  /**
   * 获取格式化的Token（带Bearer前缀）
   */
  async getFormattedToken(): Promise<string | null> {
    const token = await this.getAccessToken()
    return token ? `${this.config.tokenType} ${token}` : null
  }

  /**
   * 获取格式化的剩余时间
   */
  async getTokenRemainingTimeFormatted(): Promise<string> {
    const status = await this.getTokenStatus()
    
    if (!status.isValid) {
      return '已过期'
    }
    
    const minutes = Math.floor(status.remainingTime / 1000 / 60)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)
    
    if (days > 0) {
      return `${days}天${hours % 24}小时`
    } else if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟`
    } else {
      return `${minutes}分钟`
    }
  }

  // ==================== 配置管理 ====================

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<TokenConfig>): void {
    this.config = { ...this.config, ...newConfig }
    
    // 如果自动刷新配置改变，重新设置
    if ('autoRefresh' in newConfig) {
      if (newConfig.autoRefresh) {
        this.setupAutoRefresh()
      } else if (this.refreshTimer) {
        clearTimeout(this.refreshTimer)
        this.refreshTimer = null
      }
    }
  }

  /**
   * 销毁实例
   */
  destroy(): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer)
      this.refreshTimer = null
    }
  }
}

// ==================== 导出 ====================

/** 默认Token工具实例 */
export const tokenUtil = new TokenUtil()

/** TokenUtil类导出 */
export { TokenUtil }

/** 便捷方法导出 */
export const tokenHelpers = {
  /**
   * 保存Token的便捷方法
   */
  saveToken: (accessToken: string, refreshToken?: string, expiresInSeconds?: number) => {
    const expiresAt = expiresInSeconds ? Date.now() + (expiresInSeconds * 1000) : undefined
    return tokenUtil.saveToken({ accessToken, refreshToken, expiresAt })
  },

  /**
   * 获取Token的便捷方法
   */
  getToken: () => tokenUtil.getAccessToken(),

  /**
   * 获取格式化Token的便捷方法
   */
  getFormattedToken: () => tokenUtil.getFormattedToken(),

  /**
   * 检查Token有效性的便捷方法
   */
  isValid: () => tokenUtil.isTokenValid(),

  /**
   * 刷新Token的便捷方法
   */
  refresh: () => tokenUtil.refreshToken(),

  /**
   * 清除Token的便捷方法
   */
  clear: () => tokenUtil.clearToken()
}