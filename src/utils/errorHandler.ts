/**
 * 统一错误处理服务
 * 提供标准化的错误提示和日志记录功能
 */

import { createContextLogger } from './common/logger'
export class ErrorHandler {
  /**
   * 显示错误提示
   * @param message 错误消息
   * @param duration 显示时长（毫秒）
   * @param icon 图标类型
   */
  static showError(message: string, duration = 2000, icon: 'none' | 'error' = 'none') {
    uni.showToast({
      title: message,
      icon,
      duration,
      mask: false
    })
  }

  /**
   * 显示成功提示
   * @param message 成功消息
   * @param duration 显示时长（毫秒）
   */
  static showSuccess(message: string, duration = 2000) {
    uni.showToast({
      title: message,
      icon: 'success',
      duration,
      mask: false
    })
  }

  /**
   * 显示警告提示
   * @param message 警告消息
   * @param duration 显示时长（毫秒）
   */
  static showWarning(message: string, duration = 2000) {
    uni.showToast({
      title: message,
      icon: 'none',
      duration,
      mask: false
    })
  }

  /**
   * 记录错误日志
   * @param context 错误上下文
   * @param error 错误对象
   * @param extra 额外信息
   */
  static logError(context: string, error: any, extra?: any) {
    const logger = createContextLogger(context)
    logger.error(error, extra)
  }

  /**
   * 记录警告日志
   * @param context 警告上下文
   * @param message 警告消息
   * @param extra 额外信息
   */
  static logWarning(context: string, message: string, extra?: any) {
    const logger = createContextLogger(context)
    logger.warn(message, extra)
  }

  /**
   * 记录信息日志
   * @param context 信息上下文
   * @param message 信息消息
   * @param extra 额外信息
   */
  static logInfo(context: string, message: string, extra?: any) {
    const logger = createContextLogger(context)
    logger.info(message, extra)
  }

  /**
   * 处理API错误
   * @param error 错误对象
   * @param context 错误上下文
   * @param showToast 是否显示错误提示
   */
  static handleApiError(error: any, context: string, showToast = true) {
    let message = '操作失败，请稍后重试'
    
    if (error?.response?.data?.message) {
      message = error.response.data.message
    } else if (error?.message) {
      message = error.message
    } else if (typeof error === 'string') {
      message = error
    }

    this.logError(context, error)
    
    if (showToast) {
      this.showError(message)
    }

    return message
  }

  /**
   * 处理网络错误
   * @param error 错误对象
   * @param context 错误上下文
   */
  static handleNetworkError(error: any, context: string) {
    let message = '网络连接失败，请检查网络设置'
    
    if (error?.code === 'NETWORK_ERROR') {
      message = '网络连接超时，请稍后重试'
    } else if (error?.code === 'TIMEOUT') {
      message = '请求超时，请稍后重试'
    }

    this.logError(context, error)
    this.showError(message)

    return message
  }
}

// 导出便捷方法
export const { showError, showSuccess, showWarning, logError, logWarning, logInfo, handleApiError, handleNetworkError } = ErrorHandler