/**
 * 加密解密工具
 * 基于现有crypto-js实现的企业级加密工具
 */

import CryptoJS from 'crypto-js'

/**
 * AES加密 (ECB模式) - 兼容官方AJ-Captcha实现
 * @param data 待加密数据
 * @param key 密钥
 * @returns 加密后的字符串
 */
export function aesEncrypt(data: string, key: string): string {
  if (!key) {
    // console.warn('AES加密: 密钥为空，返回原始数据')
    return data
  }

  try {
    // 将密钥转换为WordArray
    const keyWordArray = CryptoJS.enc.Utf8.parse(key)
    
    // 将数据转换为WordArray
    const dataWordArray = CryptoJS.enc.Utf8.parse(data)
    
    // 使用AES ECB模式加密（官方标准）
    const encrypted = CryptoJS.AES.encrypt(dataWordArray, keyWordArray, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    })
    
    return encrypted.toString()
  } catch (_error) {
    // console.error('AES加密失败:', _error)
    return data
  }
}

/**
 * AES解密 (ECB模式)
 * @param encryptedData 加密数据
 * @param key 密钥
 * @returns 解密后的字符串
 */
export function aesDecrypt(encryptedData: string, key: string): string {
  if (!key) {
    // console.warn('AES解密: 密钥为空，返回原始数据')
    return encryptedData
  }

  try {
    // 将密钥转换为WordArray
    const keyWordArray = CryptoJS.enc.Utf8.parse(key)
    
    // 解密
    const decrypted = CryptoJS.AES.decrypt(encryptedData, keyWordArray, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    })
    
    return decrypted.toString(CryptoJS.enc.Utf8)
  } catch (_error) {
    // console.error('AES解密失败:', _error)
    return encryptedData
  }
}

/**
 * MD5哈希
 * @param data 待哈希数据
 * @returns MD5哈希值
 */
export function md5Hash(data: string): string {
  return CryptoJS.MD5(data).toString()
}

/**
 * SHA256哈希
 * @param data 待哈希数据
 * @returns SHA256哈希值
 */
export function sha256Hash(data: string): string {
  return CryptoJS.SHA256(data).toString()
}

/**
 * Base64编码
 * @param data 待编码数据
 * @returns Base64编码字符串
 */
export function base64Encode(data: string): string {
  const words = CryptoJS.enc.Utf8.parse(data)
  return CryptoJS.enc.Base64.stringify(words)
}

/**
 * Base64解码
 * @param encodedData Base64编码数据
 * @returns 解码后的字符串
 */
export function base64Decode(encodedData: string): string {
  const words = CryptoJS.enc.Base64.parse(encodedData)
  return words.toString(CryptoJS.enc.Utf8)
}

/**
 * 生成随机字符串
 * @param length 长度
 * @returns 随机字符串
 */
export function generateRandomString(length: number = 16): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}