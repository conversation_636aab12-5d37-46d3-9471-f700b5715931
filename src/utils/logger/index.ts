/**
 * 统一日志服务
 * 支持不同环境的日志级别控制和格式化输出
 */

/**
 * 日志级别枚举
 */
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4
}

/**
 * 日志配置接口
 */
export interface LoggerConfig {
  /** 当前日志级别 */
  level: LogLevel
  /** 是否启用控制台输出 */
  enableConsole: boolean
  /** 是否启用远程日志 */
  enableRemote: boolean
  /** 远程日志端点 */
  remoteEndpoint?: string
  /** 日志前缀 */
  prefix?: string
  /** 是否显示时间戳 */
  showTimestamp: boolean
}

/**
 * 日志条目接口
 */
export interface LogEntry {
  level: LogLevel
  message: string
  timestamp: string
  prefix?: string
  data?: any
  context?: Record<string, any>
}

/**
 * 环境配置映射
 */
const ENV_CONFIG_MAP: Record<string, Partial<LoggerConfig>> = {
  development: {
    level: LogLevel.DEBUG,
    enableConsole: true,
    enableRemote: false,
    showTimestamp: true
  },
  testing: {
    level: LogLevel.INFO,
    enableConsole: true,
    enableRemote: false,
    showTimestamp: true
  },
  staging: {
    level: LogLevel.WARN,
    enableConsole: false,
    enableRemote: true,
    showTimestamp: true
  },
  production: {
    level: LogLevel.ERROR,
    enableConsole: false,
    enableRemote: true,
    showTimestamp: true
  }
}

/**
 * 日志服务类
 */
export class Logger {
  private static instance: Logger
  private config: LoggerConfig
  private logBuffer: LogEntry[] = []
  private readonly MAX_BUFFER_SIZE = 100

  private constructor(config?: Partial<LoggerConfig>) {
    // 根据环境获取默认配置
    const env = (import.meta as any).env?.MODE || 'development'
    const envConfig = ENV_CONFIG_MAP[env] || ENV_CONFIG_MAP.development

    this.config = {
      level: LogLevel.INFO,
      enableConsole: true,
      enableRemote: false,
      showTimestamp: true,
      ...envConfig,
      ...config
    }
  }

  /**
   * 获取日志服务实例
   */
  public static getInstance(config?: Partial<LoggerConfig>): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger(config)
    }
    return Logger.instance
  }

  /**
   * 更新日志配置
   */
  public updateConfig(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config }
  }

  /**
   * 获取当前配置
   */
  public getConfig(): LoggerConfig {
    return { ...this.config }
  }

  /**
   * 调试日志
   */
  public debug(message: string, data?: any, context?: Record<string, any>): void {
    this.log(LogLevel.DEBUG, message, data, context)
  }

  /**
   * 信息日志
   */
  public info(message: string, data?: any, context?: Record<string, any>): void {
    this.log(LogLevel.INFO, message, data, context)
  }

  /**
   * 警告日志
   */
  public warn(message: string, data?: any, context?: Record<string, any>): void {
    this.log(LogLevel.WARN, message, data, context)
  }

  /**
   * 错误日志
   */
  public error(message: string, data?: any, context?: Record<string, any>): void {
    this.log(LogLevel.ERROR, message, data, context)
  }

  /**
   * 核心日志方法
   */
  private log(level: LogLevel, message: string, data?: any, context?: Record<string, any>): void {
    // 检查日志级别
    if (level < this.config.level) {
      return
    }

    const logEntry: LogEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      prefix: this.config.prefix,
      data,
      context
    }

    // 添加到缓冲区
    this.addToBuffer(logEntry)

    // 控制台输出
    if (this.config.enableConsole) {
      this.outputToConsole(logEntry)
    }

    // 远程日志
    if (this.config.enableRemote && this.config.remoteEndpoint) {
      this.sendToRemote(logEntry)
    }
  }

  /**
   * 输出到控制台
   */
  private outputToConsole(entry: LogEntry): void {
    const levelNames = ['DEBUG', 'INFO', 'WARN', 'ERROR']
    const levelColors = ['#888', '#007acc', '#ff8c00', '#ff4444']
    
    let prefix = ''
    if (this.config.showTimestamp) {
      prefix += `[${new Date(entry.timestamp).toLocaleTimeString()}] `
    }
    if (entry.prefix) {
      prefix += `[${entry.prefix}] `
    }
    prefix += `[${levelNames[entry.level]}] `

    const style = `color: ${levelColors[entry.level]}; font-weight: bold;`
    const fullMessage = `%c${prefix}${entry.message}`

    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(fullMessage, style, entry.data || '', entry.context || '')
        break
      case LogLevel.INFO:
        console.info(fullMessage, style, entry.data || '', entry.context || '')
        break
      case LogLevel.WARN:
        console.warn(fullMessage, style, entry.data || '', entry.context || '')
        break
      case LogLevel.ERROR:
        console.error(fullMessage, style, entry.data || '', entry.context || '')
        break
    }
  }

  /**
   * 发送到远程服务
   */
  private async sendToRemote(entry: LogEntry): Promise<void> {
    if (!this.config.remoteEndpoint || typeof window === 'undefined') {
      return
    }

    try {
      const response = await window.fetch(this.config.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...entry,
          url: window.location?.href || '',
          userAgent: window.navigator?.userAgent || '',
          sessionId: this.getSessionId()
        })
      })
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }
    } catch (error) {
      // 远程日志发送失败时，只在开发环境下输出错误
      if ((import.meta as any).env?.DEV) {
        console.error('Failed to send log to remote:', error)
      }
    }
  }

  /**
   * 添加到缓冲区
   */
  private addToBuffer(entry: LogEntry): void {
    this.logBuffer.push(entry)
    
    // 保持缓冲区大小
    if (this.logBuffer.length > this.MAX_BUFFER_SIZE) {
      this.logBuffer.shift()
    }
  }

  /**
   * 获取日志缓冲区
   */
  public getLogBuffer(): LogEntry[] {
    return [...this.logBuffer]
  }

  /**
   * 清空日志缓冲区
   */
  public clearBuffer(): void {
    this.logBuffer = []
  }

  /**
   * 获取会话ID
   */
  private getSessionId(): string {
    if (typeof window === 'undefined' || !window.sessionStorage) {
      return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }

    let sessionId = window.sessionStorage.getItem('logger_session_id')
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      window.sessionStorage.setItem('logger_session_id', sessionId)
    }
    return sessionId
  }

  /**
   * 创建子日志器
   */
  public createChild(prefix: string): Logger {
    const childLogger = new Logger({
      ...this.config,
      prefix: this.config.prefix ? `${this.config.prefix}:${prefix}` : prefix
    })
    return childLogger
  }

  /**
   * 批量导出日志
   */
  public exportLogs(): string {
    return JSON.stringify(this.logBuffer, null, 2)
  }

  /**
   * 设置远程日志端点
   */
  public setRemoteEndpoint(endpoint: string): void {
    this.config.remoteEndpoint = endpoint
    this.config.enableRemote = true
  }
}

// 创建默认日志实例
export const logger = Logger.getInstance()

// 创建特定模块的日志器
export const createLogger = (prefix: string, config?: Partial<LoggerConfig>): Logger => {
  return Logger.getInstance(config).createChild(prefix)
}

// 导出日志级别常量
export { LogLevel as LOG_LEVEL }

// 默认导出
export default logger