/**
 * 统一存储服务调用入口
 * 提供所有存储相关功能的统一访问接口
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

import { UnifiedStorageManager, StorageModule, StorageType } from './storageService'
import { formStorage } from './adapters/FormStorageAdapter'
import { authStorage } from "./adapters/AuthStorageAdapter";
import type { FormPersistenceConfig } from './adapters/FormStorageAdapter'
import { createContextLogger } from '../common/logger'

/**
 * StoreUtil - 统一存储服务调用入口
 * 
 * 使用示例:
 * ```typescript
 * import { StoreUtil } from '@/utils/storage/StoreUtil'
 * 
 * // 基础存储操作
 * await StoreUtil.set('user_info', { name: 'John' })
 * const userInfo = await StoreUtil.get('user_info')
 * 
 * // 表单数据持久化
 * await StoreUtil.Form.save('login_form', formData)
 * 
 * // 认证令牌管理
 * await StoreUtil.Auth.saveAccessToken('token123', 3600)
 * const token = await StoreUtil.Auth.getAccessToken()
 * ```
 */
export class StoreUtil {
  /**
   * 核心存储服务实例
   */
  private static readonly _storage = {
    core: UnifiedStorageManager.getInstance(),
    form: formStorage,
    auth: authStorage
  }

  // ==================== 基础存储操作 ====================

  /**
   * 设置存储数据
   * @param key 存储键名
   * @param value 存储值
   * @param module 存储模块 (默认: CONFIG)
   * @param storageType 存储类型 (默认: LOCAL)
   */
  static async set<T = any>(
    key: string,
    value: T,
    module: StorageModule = StorageModule.CONFIG,
    storageType: StorageType = StorageType.LOCAL
  ): Promise<boolean> {
    return await this._storage.core.set(module, key, value, { storageType })
  }

  /**
   * 获取存储数据
   * @param key 存储键名
   * @param module 存储模块 (默认: CONFIG)
   * @param storageType 存储类型 (默认: LOCAL)
   */
  static async get<T = any>(
    key: string,
    module: StorageModule = StorageModule.CONFIG,
    storageType: StorageType = StorageType.LOCAL
  ): Promise<T | null> {
    return await this._storage.core.get<T>(module, key, storageType)
  }

  /**
   * 删除存储数据
   * @param key 存储键名
   * @param module 存储模块 (默认: CONFIG)
   * @param storageType 存储类型 (默认: LOCAL)
   */
  static async remove(
    key: string,
    module: StorageModule = StorageModule.CONFIG,
    storageType: StorageType = StorageType.LOCAL
  ): Promise<boolean> {
    return await this._storage.core.remove(module, key, storageType)
  }

  /**
   * 清除指定模块的所有数据
   * @param module 存储模块
   */
  static async clearModule(
    module: StorageModule
  ): Promise<boolean> {
    return await this._storage.core.clearModule(module)
  }

  // ==================== 表单数据持久化 ====================

  /**
   * 表单数据操作
   */
  static readonly Form = {
    /**
     * 保存表单数据
     * @param formId 表单ID
     * @param data 表单数据
     * @param options 配置选项
     */
    async save<T extends Record<string, any>>(
      formId: string, 
      data: T, 
      options: Partial<FormPersistenceConfig> = {}
    ): Promise<boolean> {
      const config: FormPersistenceConfig = {
        key: formId,
        expireTime: 30 * 60 * 1000, // 30分钟
        autoSave: true,
        storageType: StorageType.CACHE,
        ...options
      }
      return await StoreUtil._storage.form.saveFormData(config, data)
    },

    /**
     * 恢复表单数据
     * @param formId 表单ID
     * @param storageType 存储类型
     */
    async restore<T extends Record<string, any>>(
      formId: string,
      storageType: StorageType = StorageType.CACHE
    ): Promise<T | null> {
      return await StoreUtil._storage.form.restoreFormData(formId, storageType) as T | null
    },

    /**
     * 清除表单数据
     * @param formId 表单ID
     * @param storageType 存储类型
     */
    async clear(
      formId: string,
      storageType: StorageType = StorageType.CACHE
    ): Promise<boolean> {
      return await StoreUtil._storage.form.clearFormData(formId, storageType)
    },

    /**
     * 清除所有表单数据
     */
    async clearAll(): Promise<boolean> {
      return await StoreUtil._storage.form.clearAllFormData()
    }
  }

  // ==================== 认证令牌管理 ====================

  /**
   * 认证相关操作
   */
  static readonly Auth = {
    /**
     * 保存访问令牌
     * @param token 访问令牌
     * @param expiresIn 过期时间(秒)
     */
    async saveAccessToken(token: string, expiresIn?: number): Promise<boolean> {
      return await StoreUtil._storage.auth.saveAccessToken(token, expiresIn)
    },

    /**
     * 获取访问令牌
     */
    async getAccessToken(): Promise<string | null> {
      return await StoreUtil._storage.auth.getAccessToken()
    },

    /**
     * 保存刷新令牌
     * @param token 刷新令牌
     */
    async saveRefreshToken(token: string): Promise<boolean> {
      return await StoreUtil._storage.auth.saveRefreshToken(token)
    },

    /**
     * 获取刷新令牌
     */
    async getRefreshToken(): Promise<string | null> {
      return await StoreUtil._storage.auth.getRefreshToken()
    },

    /**
     * 检查令牌是否即将过期
     * @param thresholdMinutes 阈值分钟数
     */
    async isTokenExpiringSoon(thresholdMinutes: number = 5): Promise<boolean> {
      return await StoreUtil._storage.auth.isTokenExpiringSoon(thresholdMinutes)
    },

    /**
     * 获取令牌剩余有效时间
     */
    async getTokenRemainingTime(): Promise<number> {
      return await StoreUtil._storage.auth.getTokenRemainingTime()
    }
  }

  // ==================== 系统管理 ====================

  /**
   * 系统管理操作
   */
  static readonly System = {
    /**
     * 健康检查
     */
    async healthCheck(): Promise<boolean> {
      try {
        const testKey = '__health_check__'
        const testValue = Date.now().toString()
        
        await StoreUtil._storage.core.set(StorageModule.CONFIG, testKey, testValue, { storageType: StorageType.MEMORY })
        const retrieved = await StoreUtil._storage.core.get(StorageModule.CONFIG, testKey, StorageType.MEMORY)
        await StoreUtil._storage.core.remove(StorageModule.CONFIG, testKey, StorageType.MEMORY)
        
        return retrieved === testValue
      } catch (error) {
        const logger = createContextLogger('StoreUtil')
        logger.error('健康检查失败:', error)
        return false
      }
    },

    /**
     * 获取存储统计信息
     */
    async getStorageStats() {
      return {
        unified: StoreUtil._storage.core.getStats(),
        form: { status: 'active' },
        auth: { status: 'active' }
      }
    },

    /**
     * 清除所有存储数据
     */
    async clearAll(): Promise<void> {
      try {
        await StoreUtil._storage.form.clearAllFormData()
        // const logger = createContextLogger('StoreUtil')
        // logger.info('所有存储数据已清除')
      } catch (error) {
        const logger = createContextLogger('StoreUtil')
        logger.error('清除存储数据失败:', error)
      }
    }
  }
}

// 导出默认实例
export default StoreUtil

// 导出枚举和类型
export { StorageModule, StorageType }