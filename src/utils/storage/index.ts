/**
 * 统一存储服务入口
 * 整合所有存储相关功能和适配器
 */

// 核心存储管理器
import { UnifiedStorageManager, StorageModule, StorageType } from './storageService'

// 存储适配器
import { formStorage } from './adapters/FormStorageAdapter'
import { httpCache } from './adapters/HttpCacheAdapter'
import { secureStorage } from './adapters/SecureStorageAdapter'

// 认证存储
import { authStorage } from './adapters/AuthStorageAdapter'

// 迁移工具已移除，迁移工作已完成

// 导出storageService和StoreUtil
export * from './storageService'
export { StoreUtil } from './StoreUtil'

// 类型定义
export type {
  StorageData,
  CacheItem,
  StorageConfig
} from './storageService'

// 迁移相关类型已移除

export {
  StorageModule,
  StorageType
} from './storageService'

/**
 * 集成存储管理器
 * 提供统一的存储服务接口
 */
class IntegratedStorageManager {
  private static instance: IntegratedStorageManager
  private initialized = false
  
  // 存储管理器实例
  public readonly unified: UnifiedStorageManager
  public readonly form: typeof formStorage
  public readonly http: typeof httpCache
  public readonly auth: typeof authStorage
  public readonly secure: typeof secureStorage
  // 迁移功能已移除
  
  private constructor() {
    this.unified = UnifiedStorageManager.getInstance()
    this.form = formStorage
    this.http = httpCache
    this.auth = authStorage
    this.secure = secureStorage
    // 迁移功能已移除
  }
  
  /**
   * 获取单例实例
   */
  static getInstance(): IntegratedStorageManager {
    if (!IntegratedStorageManager.instance) {
      IntegratedStorageManager.instance = new IntegratedStorageManager()
    }
    return IntegratedStorageManager.instance
  }

  /**
   * 初始化存储服务
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return
    }
    
    try {
      // 初始化各个存储适配器
      // console.log('🚀 [Storage] 初始化存储服务...')
      
      // 初始化安全存储
      await this.secure.initialize()
      
      // 检查并执行数据迁移
      await this.autoMigrateIfNeeded()
      
      this.initialized = true

    } catch (_error) {
      // console.error('❌ [Storage] 存储服务初始化失败:', _error)
      throw _error
    }
  }
  
  /**
   * 迁移功能已移除
   */
  private async autoMigrateIfNeeded(): Promise<void> {
    // 迁移工作已完成，无需执行
  }

  /**
   * 获取存储统计信息
   */
  async getStorageStats() {
    return {
      unified: this.unified.getStats(),
      form: { status: 'active' }, // FormStorageAdapter没有getStats方法
      http: { status: 'active' }, // HttpCacheAdapter没有getStats方法
      auth: { status: 'active' }  // AuthStorageAdapter没有getStats方法
    }
  }
  
  /**
   * 清除所有存储数据
   */
  async clearAll(): Promise<void> {
    try {
      await this.form.clearAllFormData()
      await this.http.clear()
      // auth.clearAll方法不存在，跳过
      // console.log('🧹 [Storage] 所有存储数据已清除')
    } catch (_error) {
        // console.error('❌ [Storage] 清除存储数据失败:', _error)
      }
  }
  
  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    try {
      // 测试统一存储管理器
      const testKey = '__health_check__'
      const testValue = Date.now().toString()
      
      await this.unified.set(StorageModule.CONFIG, testKey, testValue, { storageType: StorageType.MEMORY })
      const retrieved = await this.unified.get(StorageModule.CONFIG, testKey, StorageType.MEMORY)
      await this.unified.remove(StorageModule.CONFIG, testKey, StorageType.MEMORY)
      
      return retrieved !== null && (retrieved as any)?.data === testValue
    } catch (_error) {
        // console.error('❌ [Storage] 健康检查失败:', _error)
        return false
      }
  }
}

// 导出单例实例
export const storageManager = IntegratedStorageManager.getInstance()

// 导出各个存储适配器
export { formStorage, httpCache, authStorage, secureStorage }

// 导出核心管理器
export { UnifiedStorageManager }

// 迁移工具已移除

// 默认导出集成管理器
export default storageManager