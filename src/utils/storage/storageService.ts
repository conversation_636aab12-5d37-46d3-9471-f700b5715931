/**
 * 认证相关存储键枚举
 */
export enum AuthStorageKeys {
  // 保留用于类型定义，实际使用时按需添加
}

/**
 * 获取存储数据
 * @param key 键名
 * @returns 存储的数据
 */
export const getStorage = async <T>(key: string): Promise<T | null> => {
  try {
    const data = uni.getStorageSync(key);
    if (data) {
      return typeof data === 'string' ? JSON.parse(data) : data;
    }
    return null;
  } catch (_error) {
    // console.error('获取存储数据失败:', _error);
    return null;
  }
};

/**
 * 获取认证token
 * @deprecated 请使用 tokenUtil.getAccessToken() 替代
 * @returns 认证token或null
 */
export const getAuthToken = async (): Promise<string | null> => {
  // 为了向后兼容，暂时保留此方法，但建议使用 tokenUtil
  const { tokenUtil } = await import('../token/TokenUtil');
  return await tokenUtil.getAccessToken();
};

/**
 * 设置存储数据
 * @param key 键名
 * @param data 数据
 */
export const setStorage = async <T>(key: string, data: T): Promise<void> => {
  try {
    uni.setStorageSync(key, data);
  } catch (_error) {
    // console.error('设置存储数据失败:', _error);
  }
};

/**
 * 移除存储数据
 * @param key 键名
 */
export const removeStorage = async (key: string): Promise<void> => {
  try {
    uni.removeStorageSync(key);
  } catch (_error) {
    // console.error('移除存储数据失败:', _error);
  }
};

/**
 * 清除所有存储数据
 */
export const clearStorage = (): void => {
  uni.clearStorageSync();
};

/**
 * 存储类型枚举
 */
export enum StorageType {
  /** 内存存储 - 临时数据 */
  // eslint-disable-next-line no-unused-vars
  MEMORY = 'memory',
  /** 本地存储 - 持久化数据 */
  // eslint-disable-next-line no-unused-vars
  LOCAL = 'local',
  /** 多重存储 - 高可靠性数据 */
  // eslint-disable-next-line no-unused-vars
  MULTI = 'multi',
  /** 缓存存储 - 带过期时间的数据 */
  // eslint-disable-next-line no-unused-vars
  CACHE = 'cache'
}

/**
 * 存储模块枚举
 */
export enum StorageModule {
  /** 认证模块 */
  // eslint-disable-next-line no-unused-vars
  AUTH = 'auth',
  /** 应用配置模块 */
  // eslint-disable-next-line no-unused-vars
  CONFIG = 'config'
}

/**
 * 存储数据接口
 */
export interface StorageData<T = unknown> {
  /** 数据内容 */
  data: T
  /** 创建时间 */
  timestamp: number
  /** 过期时间（毫秒），0表示永不过期 */
  expireTime?: number
  /** 数据版本 */
  version?: string
  /** 所属模块 */
  module: StorageModule
  /** 数据类型标识 */
  type?: string
}

/**
 * 缓存项接口
 */
export interface CacheItem<T = unknown> {
  /** 缓存数据 */
  value: T
  /** 过期时间戳 */
  expireTime: number
  /** 访问次数 */
  accessCount: number
  /** 最后访问时间 */
  lastAccess: number
}

/**
 * 存储配置接口
 */
export interface StorageConfig {
  /** 默认过期时间（毫秒） */
  defaultTTL?: number
  /** 最大缓存大小 */
  maxCacheSize?: number
  /** 是否启用加密 */
  encryption?: boolean
  /** 是否启用压缩 */
  compression?: boolean
  /** 存储类型 */
  storageType: StorageType
}

/**
 * 统一数据持久化管理器
 */
export class UnifiedStorageManager {
  private static instance: UnifiedStorageManager
  private memoryCache: Map<string, CacheItem> = new Map()
  private readonly MAX_MEMORY_SIZE = 100 // 最大内存缓存条目数
  
  // 单例模式
  public static getInstance(): UnifiedStorageManager {
    if (!UnifiedStorageManager.instance) {
      UnifiedStorageManager.instance = new UnifiedStorageManager()
    }
    return UnifiedStorageManager.instance
  }

  private constructor() {
    // 初始化清理定时器
    this.setupCleanupTimer()
  }

  /**
   * 生成统一的存储键
   */
  private generateKey(module: StorageModule, key: string): string {
    return `oto_${module}_${key}`
  }

  /**
   * 设置数据（通用方法）
   */
  async set<T>(
    module: StorageModule,
    key: string,
    data: T,
    config: Partial<StorageConfig> = {}
  ): Promise<boolean> {
    try {
      const finalConfig: StorageConfig = {
        defaultTTL: 24 * 60 * 60 * 1000, // 默认24小时
        maxCacheSize: 50,
        encryption: false,
        compression: false,
        storageType: StorageType.LOCAL,
        ...config
      }

      const storageData: StorageData<T> = {
        data,
        timestamp: Date.now(),
        expireTime: finalConfig.defaultTTL ? Date.now() + finalConfig.defaultTTL : 0,
        version: '1.0',
        module,
        type: typeof data
      }

      const storageKey = this.generateKey(module, key)
      
      // 根据存储类型选择存储方式
      switch (finalConfig.storageType) {
        case StorageType.MEMORY:
          return this.setMemoryCache(storageKey, storageData, finalConfig.defaultTTL || 0)
        
        case StorageType.LOCAL:
          return await this.setLocalStorage(storageKey, storageData)
        
        case StorageType.MULTI: {
          const memoryResult = this.setMemoryCache(storageKey, storageData, finalConfig.defaultTTL || 0)
          const localResult = await this.setLocalStorage(storageKey, storageData)
          return memoryResult && localResult
        }
        
        case StorageType.CACHE:
          this.setMemoryCache(storageKey, storageData, finalConfig.defaultTTL || 0)
          await this.setLocalStorage(storageKey, storageData)
          return true
        
        default:
          return await this.setLocalStorage(storageKey, storageData)
      }
    } catch (_error) {
      // console.error(`[UnifiedStorage] 设置数据失败 ${module}.${key}:`, _error)
      return false
    }
  }

  /**
   * 获取数据（通用方法）
   */
  async get<T>(module: StorageModule, key: string, storageType: StorageType = StorageType.LOCAL): Promise<T | null> {
    try {
      const storageKey = this.generateKey(module, key)
      let storageData: StorageData<T> | null = null

      // 根据存储类型选择获取方式
      switch (storageType) {
        case StorageType.MEMORY:
          storageData = this.getMemoryCache<T>(storageKey)
          break
        
        case StorageType.LOCAL:
          storageData = await this.getLocalStorage<T>(storageKey)
          break
        
        case StorageType.MULTI:
        case StorageType.CACHE:
          // 优先从内存获取
          storageData = this.getMemoryCache<T>(storageKey)
          if (!storageData) {
            storageData = await this.getLocalStorage<T>(storageKey)
            // 如果从本地存储获取到数据，同步到内存缓存
            if (storageData && storageType === StorageType.CACHE) {
              this.setMemoryCache(storageKey, storageData, 0)
            }
          }
          break
        
        default:
          storageData = await this.getLocalStorage<T>(storageKey)
      }

      // 检查数据有效性
      if (!storageData) {
        return null
      }

      // 检查过期时间
      if (storageData.expireTime && storageData.expireTime > 0 && Date.now() > storageData.expireTime) {
        await this.remove(module, key, storageType)
        return null
      }

      return storageData.data
    } catch (_error) {
      // console.error(`[UnifiedStorage] 获取数据失败 ${module}.${key}:`, _error)
      return null
    }
  }

  /**
   * 删除数据
   */
  async remove(module: StorageModule, key: string, storageType: StorageType = StorageType.LOCAL): Promise<boolean> {
    try {
      const storageKey = this.generateKey(module, key)

      switch (storageType) {
        case StorageType.MEMORY:
          this.memoryCache.delete(storageKey)
          break
        
        case StorageType.LOCAL:
          uni.removeStorageSync(storageKey)
          break
        
        case StorageType.MULTI:
          this.memoryCache.delete(storageKey)
          uni.removeStorageSync(storageKey)
          break
        
        case StorageType.CACHE:
          this.memoryCache.delete(storageKey)
          uni.removeStorageSync(storageKey)
          break
      }

      return true
    } catch (_error) {
      // console.error(`[UnifiedStorage] 删除数据失败 ${module}.${key}:`, _error)
      return false
    }
  }

  /**
   * 内存缓存存储
   */
  private setMemoryCache<T>(key: string, data: StorageData<T>, ttl: number): boolean {
    try {
      const cacheItem: CacheItem<StorageData<T>> = {
        value: data,
        expireTime: ttl > 0 ? Date.now() + ttl : 0,
        accessCount: 1,
        lastAccess: Date.now()
      }

      this.memoryCache.set(key, cacheItem)
      return true
    } catch (_error) {
      // console.error('[UnifiedStorage] 内存缓存存储失败:', _error)
      return false
    }
  }

  /**
   * 内存缓存获取
   */
  private getMemoryCache<T>(key: string): StorageData<T> | null {
    try {
      const cacheItem = this.memoryCache.get(key)
      if (!cacheItem) {
        return null
      }

      // 检查过期时间
      if (cacheItem.expireTime > 0 && Date.now() > cacheItem.expireTime) {
        this.memoryCache.delete(key)
        return null
      }

      // 更新访问信息
      cacheItem.accessCount++
      cacheItem.lastAccess = Date.now()

      return cacheItem.value as StorageData<T>
    } catch (_error) {
      // console.error('[UnifiedStorage] 内存缓存获取失败:', _error)
      return null
    }
  }

  /**
   * 本地存储
   */
  private async setLocalStorage<T>(key: string, data: StorageData<T>): Promise<boolean> {
    try {
      uni.setStorageSync(key, JSON.stringify(data))
      return true
    } catch (_error) {
      // console.error('[UnifiedStorage] 本地存储失败:', _error)
      return false
    }
  }

  /**
   * 本地存储获取
   */
  private async getLocalStorage<T>(key: string): Promise<StorageData<T> | null> {
    try {
      const data = uni.getStorageSync(key)
      if (!data) {
        return null
      }
      return typeof data === 'string' ? JSON.parse(data) : data
    } catch (_error) {
      // console.error('[UnifiedStorage] 本地存储获取失败:', _error)
      return null
    }
  }

  /**
   * 设置清理定时器
   */
  private setupCleanupTimer(): void {
    // 每5分钟清理一次过期缓存
    setInterval(() => {
      this.cleanExpiredCache()
    }, 5 * 60 * 1000)
  }

  /**
   * 清理过期缓存
   */
  private cleanExpiredCache(): void {
    const now = Date.now()
    for (const [key, item] of this.memoryCache.entries()) {
      if (item.expireTime > 0 && now > item.expireTime) {
        this.memoryCache.delete(key)
      }
    }
  }

  /**
   * 清除指定模块的所有数据
   */
  async clearModule(module: StorageModule): Promise<boolean> {
    try {
      const prefix = `oto_${module}_`
      
      // 清除内存缓存中的模块数据
      for (const key of this.memoryCache.keys()) {
        if (key.startsWith(prefix)) {
          this.memoryCache.delete(key)
        }
      }
      
      // 清除本地存储中的模块数据
      const storageInfo = uni.getStorageInfoSync()
      const keysToRemove = storageInfo.keys.filter(key => key.startsWith(prefix))
      
      for (const key of keysToRemove) {
        uni.removeStorageSync(key)
      }
      
      // console.log(`[UnifiedStorage] 已清除模块 ${module} 的所有数据`)
      return true
    } catch (_error) {
      // console.error(`[UnifiedStorage] 清除模块 ${module} 数据失败:`, _error)
      return false
    }
  }

  /**
   * 获取存储统计信息
   */
  getStats() {
    try {
      const storageInfo = uni.getStorageInfoSync()
      const memoryStats = {
        size: this.memoryCache.size,
        maxSize: this.MAX_MEMORY_SIZE,
        usage: `${(this.memoryCache.size / this.MAX_MEMORY_SIZE * 100).toFixed(2)  }%`
      }
      
      return {
        memory: memoryStats,
        local: {
          keys: storageInfo.keys.length,
          currentSize: storageInfo.currentSize,
          limitSize: storageInfo.limitSize,
          usage: storageInfo.limitSize > 0 ? 
            `${(storageInfo.currentSize / storageInfo.limitSize * 100).toFixed(2)  }%` : 'N/A'
        },
        timestamp: Date.now()
      }
    } catch (_error) {
       // console.error('[UnifiedStorage] 获取统计信息失败:', _error)
       return {
         memory: { size: 0, maxSize: this.MAX_MEMORY_SIZE, usage: '0%' },
         local: { keys: 0, currentSize: 0, limitSize: 0, usage: '0%' },
         timestamp: Date.now(),
         error: _error instanceof Error ? _error.message : String(_error)
       }
     }
  }
}

// 导出单例实例
export const unifiedStorageManager = UnifiedStorageManager.getInstance()