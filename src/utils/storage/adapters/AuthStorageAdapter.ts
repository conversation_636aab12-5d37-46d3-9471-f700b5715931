/**
 * 认证存储适配器
 * 使用统一存储服务为认证模块提供专门的存储接口
 * 支持Token管理、用户信息、会话数据和偏好设置
 */

import { UnifiedStorageManager, StorageModule, StorageType } from '@/utils/storage/storageService'
import { secureStorage } from './SecureStorageAdapter'

// 获取统一存储管理器单例实例
const unifiedStorageManager = UnifiedStorageManager.getInstance()
import type { UserInfo } from '@/api/auth/auth'

// ==================== 类型定义 ====================

/** 用户偏好设置 */
export interface AuthPreferences {
  /** 记住登录状态 */
  rememberLogin: boolean
  /** 自动登录 */
  autoLogin: boolean
  /** 生物识别登录 */
  biometricLogin: boolean
  /** 信任设备 */
  trustedDevice: boolean
}

/** 会话数据 */
export interface SessionData {
  /** 设备ID */
  deviceId?: string
  /** 登录IP */
  loginIp?: string
  /** 登录时间 */
  loginTime: number
  /** 最后活跃时间 */
  lastActiveTime: number
  /** 会话ID */
  sessionId?: string
}

/** 完整认证状态 */
export interface AuthState {
  /** 是否有效 */
  isValid: boolean
  /** 访问令牌 */
  accessToken?: string
  /** 刷新令牌 */
  refreshToken?: string
  /** 用户信息 */
  userInfo?: UserInfo | null
  /** 会话数据 */
  sessionData?: SessionData
  /** 用户偏好设置 */
  preferences?: AuthPreferences
}

/** 保存认证状态的选项 */
export interface SaveAuthStateOptions {
  /** 刷新令牌 */
  refreshToken?: string
  /** 令牌过期时间（秒） */
  expiresInSeconds?: number
  /** 设备ID */
  deviceId?: string
  /** 登录IP */
  loginIp?: string
  /** 用户偏好设置 */
  preferences?: Partial<AuthPreferences>
}

// ==================== 存储键常量 ====================

const AUTH_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_INFO: 'user_info',
  SESSION_DATA: 'session_data',
  PREFERENCES: 'preferences',
  TOKEN_EXPIRES_AT: 'token_expires_at'
} as const

// ==================== 认证存储适配器类 ====================

class AuthStorageAdapter {
  private readonly storageModule = StorageModule.AUTH

  // ==================== Token 管理 ====================

  /**
   * 保存访问令牌
   */
  async saveAccessToken(token: string, expiresInSeconds?: number): Promise<boolean> {
    try {
      const success = await unifiedStorageManager.set(
        this.storageModule,
        AUTH_KEYS.ACCESS_TOKEN,
        token,
        { storageType: StorageType.MULTI }
      )

      if (success && expiresInSeconds) {
        const expiresAt = Date.now() + expiresInSeconds * 1000
        await unifiedStorageManager.set(
          this.storageModule,
          AUTH_KEYS.TOKEN_EXPIRES_AT,
          expiresAt,
          { storageType: StorageType.CACHE }
        )
      }

      return success
    } catch {
      return false
    }
  }

  /**
   * 获取访问令牌
   */
  async getAccessToken(): Promise<string | null> {
    try {
      return await unifiedStorageManager.get<string>(
        this.storageModule,
        AUTH_KEYS.ACCESS_TOKEN,
        StorageType.MULTI
      )
    } catch {
      return null
    }
  }

  /**
   * 保存刷新令牌（使用安全存储）
   */
  async saveRefreshToken(refreshToken: string): Promise<boolean> {
    try {
      // 使用安全存储保存refresh_token
      const secureResult = await secureStorage.setItem(AUTH_KEYS.REFRESH_TOKEN, refreshToken)
      
      // 同时保存到多重存储作为备份
      const multiResult = await unifiedStorageManager.set(
        this.storageModule,
        AUTH_KEYS.REFRESH_TOKEN,
        refreshToken,
        { storageType: StorageType.MULTI }
      )
      
      return secureResult && multiResult
    } catch {
      return false
    }
  }

  /**
   * 获取刷新令牌（优先从安全存储获取）
   */
  async getRefreshToken(): Promise<string | null> {
    try {
      // 优先从安全存储获取
      let refreshToken = await secureStorage.getItem(AUTH_KEYS.REFRESH_TOKEN)
      
      // 如果安全存储中没有，尝试从多重存储获取（兼容旧数据）
      if (!refreshToken) {
        refreshToken = await unifiedStorageManager.get<string>(
          this.storageModule,
          AUTH_KEYS.REFRESH_TOKEN,
          StorageType.MULTI
        )
        
        // 如果从多重存储获取到了，迁移到安全存储
        if (refreshToken) {
          await secureStorage.setItem(AUTH_KEYS.REFRESH_TOKEN, refreshToken)
        }
      }
      
      return refreshToken
    } catch {
      return null
    }
  }

  /**
   * 检查令牌是否即将过期
   */
  async isTokenExpiringSoon(thresholdMinutes: number = 5): Promise<boolean> {
    try {
      const expiresAt = await unifiedStorageManager.get<number>(
        this.storageModule,
        AUTH_KEYS.TOKEN_EXPIRES_AT,
        StorageType.CACHE
      )

      if (!expiresAt) {
        return false
      }

      const threshold = thresholdMinutes * 60 * 1000
      return Date.now() + threshold >= expiresAt
    } catch {
      return false
    }
  }

  /**
   * 获取令牌剩余有效时间（毫秒）
   */
  async getTokenRemainingTime(): Promise<number> {
    try {
      const expiresAt = await unifiedStorageManager.get<number>(
        this.storageModule,
        AUTH_KEYS.TOKEN_EXPIRES_AT,
        StorageType.CACHE
      )

      if (!expiresAt) {
        return 0
      }

      const remainingTime = expiresAt - Date.now()
      return Math.max(0, remainingTime)
    } catch {
      return 0
    }
  }

  // ==================== 用户信息管理 ====================

  /**
   * 保存用户信息
   */
  async saveUserInfo(userInfo: UserInfo): Promise<boolean> {
    try {
      return await unifiedStorageManager.set(
        this.storageModule,
        AUTH_KEYS.USER_INFO,
        userInfo,
        { storageType: StorageType.LOCAL }
      )
    } catch {
      return false
    }
  }

  /**
   * 获取用户信息
   */
  async getUserInfo(): Promise<UserInfo | null> {
    try {
      return await unifiedStorageManager.get<UserInfo>(
        this.storageModule,
        AUTH_KEYS.USER_INFO,
        StorageType.LOCAL
      )
    } catch {
      return null
    }
  }

  /**
   * 更新用户信息
   */
  async updateUserInfo(updates: Partial<UserInfo>): Promise<boolean> {
    try {
      const currentUserInfo = await this.getUserInfo()
      if (!currentUserInfo) {
        return false
      }

      const updatedUserInfo = { ...currentUserInfo, ...updates }
      return await this.saveUserInfo(updatedUserInfo)
    } catch {
      return false
    }
  }

  // ==================== 会话数据管理 ====================

  /**
   * 保存会话数据
   */
  async saveSessionData(sessionData: SessionData): Promise<boolean> {
    try {
      return await unifiedStorageManager.set(
        this.storageModule,
        AUTH_KEYS.SESSION_DATA,
        sessionData,
        { storageType: StorageType.CACHE }
      )
    } catch {
      return false
    }
  }

  /**
   * 获取会话数据
   */
  async getSessionData(): Promise<SessionData | null> {
    try {
      return await unifiedStorageManager.get<SessionData>(
        this.storageModule,
        AUTH_KEYS.SESSION_DATA,
        StorageType.CACHE
      )
    } catch {
      return null
    }
  }

  /**
   * 更新最后活跃时间
   */
  async updateLastActiveTime(): Promise<boolean> {
    try {
      const sessionData = await this.getSessionData()
      if (!sessionData) {
        return false
      }

      sessionData.lastActiveTime = Date.now()
      return await this.saveSessionData(sessionData)
    } catch {
      return false
    }
  }

  // ==================== 用户偏好设置管理 ====================

  /**
   * 保存用户偏好设置
   */
  async saveUserPreferences(preferences: AuthPreferences): Promise<boolean> {
    try {
      return await unifiedStorageManager.set(
        this.storageModule,
        AUTH_KEYS.PREFERENCES,
        preferences,
        { storageType: StorageType.LOCAL }
      )
    } catch {
      return false
    }
  }

  /**
   * 获取用户偏好设置
   */
  async getUserPreferences(): Promise<AuthPreferences | null> {
    try {
      return await unifiedStorageManager.get<AuthPreferences>(
        this.storageModule,
        AUTH_KEYS.PREFERENCES,
        StorageType.LOCAL
      )
    } catch {
      return null
    }
  }

  /**
   * 更新用户偏好设置
   */
  async updateUserPreferences(updates: Partial<AuthPreferences>): Promise<boolean> {
    try {
      const currentPreferences = await this.getUserPreferences() || {
        rememberLogin: false,
        autoLogin: false,
        biometricLogin: false,
        trustedDevice: false
      }

      const updatedPreferences = { ...currentPreferences, ...updates }
      return await this.saveUserPreferences(updatedPreferences)
    } catch {
      return false
    }
  }

  // ==================== 综合操作 ====================

  /**
   * 保存完整认证状态
   */
  async saveAuthState(
    accessToken: string,
    userInfo: UserInfo,
    options: SaveAuthStateOptions = {}
  ): Promise<boolean> {
    try {
      // 准备会话数据
      const sessionData: SessionData = {
        deviceId: options.deviceId,
        loginIp: options.loginIp,
        loginTime: Date.now(),
        lastActiveTime: Date.now(),
        sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      }

      // 原子操作：要么全部成功，要么全部失败
      const operations = [
        this.saveAccessToken(accessToken, options.expiresInSeconds),
        this.saveUserInfo(userInfo),
        this.saveSessionData(sessionData)
      ]

      if (options.refreshToken) {
        operations.push(this.saveRefreshToken(options.refreshToken))
      }

      if (options.preferences) {
        operations.push(this.updateUserPreferences(options.preferences))
      }

      const results = await Promise.all(operations)
      const allSuccess = results.every(result => result === true)

      return allSuccess
    } catch {
      return false
    }
  }

  /**
   * 获取完整认证状态
   */
  async getAuthState(): Promise<AuthState> {
    try {
      const [
        accessToken,
        refreshToken,
        userInfo,
        sessionData,
        preferences
      ] = await Promise.all([
        this.getAccessToken(),
        this.getRefreshToken(),
        this.getUserInfo(),
        this.getSessionData(),
        this.getUserPreferences()
      ])

      const isValid = !!(accessToken && userInfo)

      return {
        isValid,
        accessToken: accessToken || undefined,
        refreshToken: refreshToken || undefined,
        userInfo,
        sessionData: sessionData || undefined,
        preferences: preferences || undefined
      }
    } catch {
      return {
        isValid: false
      }
    }
  }

  /**
   * 清除认证状态
   */
  async clearAuthState(): Promise<boolean> {
    try {
      const operations = [
        unifiedStorageManager.remove(this.storageModule, AUTH_KEYS.ACCESS_TOKEN, StorageType.MULTI),
        unifiedStorageManager.remove(this.storageModule, AUTH_KEYS.REFRESH_TOKEN, StorageType.MULTI),
        unifiedStorageManager.remove(this.storageModule, AUTH_KEYS.USER_INFO, StorageType.LOCAL),
        unifiedStorageManager.remove(this.storageModule, AUTH_KEYS.SESSION_DATA, StorageType.CACHE),
        unifiedStorageManager.remove(this.storageModule, AUTH_KEYS.TOKEN_EXPIRES_AT, StorageType.CACHE),
        // 同时清除安全存储中的refresh_token
        secureStorage.removeItem(AUTH_KEYS.REFRESH_TOKEN)
        // 注意：不清除用户偏好设置，因为这些是用户个人选择
      ]

      const results = await Promise.all(operations)
      const allSuccess = results.every(result => result === true)

      return allSuccess
    } catch {
      return false
    }
  }

  // ==================== 数据迁移和兼容性 ====================

  /**
   * 从旧存储格式迁移数据
   * TODO: 重新实现数据迁移逻辑，避免循环依赖
   */
  async migrateFromLegacyStorage(): Promise<boolean> {
    try {
      return true
    } catch {
      return false
    }
  }

  /**
   * 验证存储数据完整性
   */
  async validateStorageIntegrity(): Promise<{
    isValid: boolean
    issues: string[]
  }> {
    const issues: string[] = []

    try {
      const authState = await this.getAuthState()

      // 检查基本完整性
      if (authState.accessToken && !authState.userInfo) {
        issues.push('存在访问令牌但缺少用户信息')
      }

      if (authState.userInfo && !authState.accessToken) {
        issues.push('存在用户信息但缺少访问令牌')
      }

      // 检查令牌过期
      if (authState.accessToken) {
        const remainingTime = await this.getTokenRemainingTime()
        if (remainingTime <= 0) {
          issues.push('访问令牌已过期')
        }
      }

      // 检查会话数据完整性
      if (authState.sessionData) {
        const { loginTime, lastActiveTime } = authState.sessionData
        if (lastActiveTime < loginTime) {
          issues.push('会话数据时间逻辑错误')
        }

        // 检查会话是否过期（超过30天）
        const sessionAge = Date.now() - loginTime
        if (sessionAge > 30 * 24 * 60 * 60 * 1000) {
          issues.push('会话已过期（超过30天）')
        }
      }

      return {
        isValid: issues.length === 0,
        issues
      }
    } catch (error) {
      return {
        isValid: false,
        issues: [`验证过程出错: ${error instanceof Error ? error.message : '未知错误'}`]
      }
    }
  }

  /**
   * 回滚迁移（恢复到旧格式）
   * TODO: 重新实现回滚逻辑，避免循环依赖
   */
  async rollbackMigration(): Promise<boolean> {
    try {
      return true
    } catch {
      return false
    }
  }
}

// ==================== 导出单例实例 ====================

/** 认证存储适配器单例 */
export const authStorage = new AuthStorageAdapter()

// ==================== 导出类型 ====================
// 注意：类型已经在上面通过 export interface 导出了，这里不需要重复导出