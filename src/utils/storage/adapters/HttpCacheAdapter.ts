/**
 * HTTP缓存适配器
 * 基于UnifiedStorageManager实现HTTP请求缓存
 * 支持多种缓存策略和智能失效机制
 */

import { UnifiedStorageManager, StorageModule, StorageType } from '@/utils/storage/storageService'

// 获取统一存储管理器实例
const unifiedStorage = UnifiedStorageManager.getInstance()

// ==================== 类型定义 ====================

/** HTTP缓存策略 */
export enum CacheStrategy {
  /** 缓存优先 - 有缓存就用缓存 */
  CACHE_FIRST = 'cache-first',
  /** 网络优先 - 网络失败才用缓存 */
  NETWORK_FIRST = 'network-first',
  /** 仅缓存 - 只使用缓存 */
  CACHE_ONLY = 'cache-only',
  /** 仅网络 - 不使用缓存 */
  NETWORK_ONLY = 'network-only',
  /** 先返回缓存，后台更新 */
  STALE_WHILE_REVALIDATE = 'stale-while-revalidate'
}

/** HTTP请求方法 */
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'

/** 缓存配置 */
export interface CacheConfig {
  /** 缓存策略 */
  strategy: CacheStrategy
  /** 缓存时间（毫秒），默认5分钟 */
  ttl?: number
  /** 存储类型，默认CACHE */
  storageType?: StorageType
  /** 是否启用压缩 */
  compression?: boolean
  /** 缓存键前缀 */
  keyPrefix?: string
  /** 是否忽略查询参数 */
  ignoreQuery?: boolean
  /** 自定义缓存键生成函数 */
  keyGenerator?: (_url: string, _options: RequestOptions) => string
}

/** 请求选项 */
export interface RequestOptions {
  method?: HttpMethod
  headers?: Record<string, string>
  body?: unknown
  params?: Record<string, unknown>
  timeout?: number
}

/** 缓存项元数据 */
export interface CacheMetadata {
  /** 请求URL */
  url: string
  /** 请求方法 */
  method: HttpMethod
  /** 缓存时间 */
  cachedAt: number
  /** 过期时间 */
  expiresAt: number
  /** 响应状态码 */
  status: number
  /** 响应头 */
  headers: Record<string, string>
  /** ETag */
  etag?: string
  /** Last-Modified */
  lastModified?: string
  /** 数据大小（字节） */
  size: number
  /** 命中次数 */
  hitCount: number
}

/** 缓存响应 */
export interface CacheResponse<T = unknown> {
  /** 响应数据 */
  data: T
  /** 元数据 */
  metadata: CacheMetadata
  /** 是否来自缓存 */
  fromCache: boolean
  /** 缓存键 */
  cacheKey: string
}

/** 缓存统计 */
export interface CacheStats {
  /** 总请求数 */
  totalRequests: number
  /** 缓存命中数 */
  cacheHits: number
  /** 缓存未命中数 */
  cacheMisses: number
  /** 命中率 */
  hitRate: number
  /** 缓存大小 */
  cacheSize: number
  /** 缓存项数量 */
  itemCount: number
}

// ==================== HTTP缓存适配器类 ====================

class HttpCacheAdapter {
  private readonly storageModule = StorageModule.CONFIG
  private readonly DEFAULT_TTL = 5 * 60 * 1000 // 5分钟
  private readonly CACHE_PREFIX = 'http_cache'
  private readonly STATS_KEY = 'cache_stats'
  
  // 统计数据
  private stats: CacheStats = {
    totalRequests: 0,
    cacheHits: 0,
    cacheMisses: 0,
    hitRate: 0,
    cacheSize: 0,
    itemCount: 0
  }

  constructor() {
    this.loadStats()
  }

  // ==================== 核心缓存方法 ====================

  /**
   * 获取缓存数据
   */
  async get<T = unknown>(url: string, options: RequestOptions = {}, config: CacheConfig): Promise<CacheResponse<T> | null> {
    try {
      this.stats.totalRequests++
      
      const cacheKey = this.generateCacheKey(url, options, config)
      const cachedItem = await unifiedStorage.get<{ data: T; metadata: CacheMetadata }>(
        this.storageModule,
        cacheKey,
        config.storageType || StorageType.CACHE
      )

      if (!cachedItem) {
        this.stats.cacheMisses++
        this.updateStats()
        return null
      }

      // 检查是否过期
      if (this.isExpired(cachedItem.metadata)) {
        await this.remove(cacheKey, config.storageType)
        this.stats.cacheMisses++
        this.updateStats()
        return null
      }

      // 更新命中统计
      cachedItem.metadata.hitCount++
      this.stats.cacheHits++
      this.updateStats()

      // 异步更新缓存项（更新命中次数）
      this.updateCacheItem(cacheKey, cachedItem, config.storageType)

      // console.log(`🎯 [HttpCache] 缓存命中: ${url}`, {
      //   age: `${Math.round((Date.now() - cachedItem.metadata.cachedAt) / 1000)  }s`,
      //   hitCount: cachedItem.metadata.hitCount,
      //   size: this.formatBytes(cachedItem.metadata.size)
      // })

      return {
        data: cachedItem.data,
        metadata: cachedItem.metadata,
        fromCache: true,
        cacheKey
      }
    } catch (_error) {
      // console.error('❌ [HttpCache] 获取缓存失败:', _error)
      this.stats.cacheMisses++
      this.updateStats()
      return null
    }
  }

  /**
   * 设置缓存数据
   */
  async set<T = unknown>(
    url: string,
    data: T,
    options: RequestOptions = {},
    config: CacheConfig,
    responseHeaders: Record<string, string> = {},
    status: number = 200
  ): Promise<boolean> {
    try {
      const now = Date.now()
      const ttl = config.ttl || this.DEFAULT_TTL
      const cacheKey = this.generateCacheKey(url, options, config)
      
      // 计算数据大小
      const dataSize = this.calculateSize(data)
      
      const metadata: CacheMetadata = {
        url,
        method: options.method || 'GET',
        cachedAt: now,
        expiresAt: now + ttl,
        status,
        headers: responseHeaders,
        etag: responseHeaders['etag'] || responseHeaders['ETag'],
        lastModified: responseHeaders['last-modified'] || responseHeaders['Last-Modified'],
        size: dataSize,
        hitCount: 0
      }

      const cacheItem = { data, metadata }
      
      const success = await unifiedStorage.set(
        this.storageModule,
        cacheKey,
        cacheItem,
        {
          storageType: config.storageType || StorageType.CACHE,
          defaultTTL: ttl,
          compression: config.compression
        }
      )

      if (success) {
        this.stats.cacheSize += dataSize
        this.stats.itemCount++
        this.updateStats()
        
        // console.log(`💾 [HttpCache] 数据已缓存: ${url}`, {
        //   method: metadata.method,
        //   size: this.formatBytes(dataSize),
        //   ttl: `${Math.round(ttl / 1000)  }s`,
        //   strategy: config.strategy
        // })
      }

      return success
    } catch (_error) {
      // console.error('❌ [HttpCache] 设置缓存失败:', _error)
      return false
    }
  }

  /**
   * 删除缓存数据
   */
  async remove(cacheKey: string, storageType: StorageType = StorageType.CACHE): Promise<boolean> {
    try {
      const success = await unifiedStorage.remove(
        this.storageModule,
        cacheKey,
        storageType
      )

      if (success) {
        this.stats.itemCount = Math.max(0, this.stats.itemCount - 1)
        this.updateStats()
        // console.log(`🗑️ [HttpCache] 缓存已删除: ${cacheKey}`)
      }

      return success
    } catch (_error) {
      // console.error('❌ [HttpCache] 删除缓存失败:', _error)
      return false
    }
  }

  /**
   * 清除所有缓存
   */
  async clear(): Promise<boolean> {
    try {
      const success = await unifiedStorage.clearModule(this.storageModule)
      
      if (success) {
        this.stats.cacheSize = 0
        this.stats.itemCount = 0
        this.updateStats()
        // console.log('🧹 [HttpCache] 所有缓存已清除')
      }

      return success
    } catch (_error) {
      // console.error('❌ [HttpCache] 清除缓存失败:', _error)
      return false
    }
  }

  // ==================== 高级功能 ====================

  /**
   * 批量预加载缓存
   */
  async preload(requests: Array<{ url: string; options?: RequestOptions; config: CacheConfig }>): Promise<void> {
    try {
      const promises = requests.map(async ({ url }) => {
        try {
          // 这里需要实际的HTTP请求实现
          // 暂时跳过，等待HTTP客户端集成
          // console.log(`🔄 [HttpCache] 预加载: ${url}`)
        } catch (_error) {
          // console.error(`❌ [HttpCache] 预加载失败: ${url}`, _error)
        }
      })

      await Promise.allSettled(promises)
      // console.log(`✅ [HttpCache] 批量预加载完成: ${requests.length}个请求`)
    } catch (_error) {
      // console.error('❌ [HttpCache] 批量预加载失败:', _error)
    }
  }

  /**
   * 缓存失效（基于URL模式）
   */
  async invalidateByPattern(_pattern: string | RegExp): Promise<number> {
    try {
      // 这里需要扩展UnifiedStorageManager来支持模式匹配删除
      // 暂时返回0，后续实现
      // console.log(`🗑️ [HttpCache] 按模式失效缓存: ${_pattern}`)
      return 0
    } catch (_error) {
      // console.error('❌ [HttpCache] 模式失效失败:', _error)
      return 0
    }
  }

  /**
   * 缓存失效（基于标签）
   */
  async invalidateByTags(_tags: string[]): Promise<number> {
    try {
      // 这里需要扩展缓存项来支持标签
      // 暂时返回0，后续实现
      // console.log(`🏷️ [HttpCache] 按标签失效缓存:`, _tags)
      return 0
    } catch (_error) {
      // console.error('❌ [HttpCache] 标签失效失败:', _error)
      return 0
    }
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): CacheStats {
    this.stats.hitRate = this.stats.totalRequests > 0 
      ? Math.round((this.stats.cacheHits / this.stats.totalRequests) * 100) / 100
      : 0
    
    return { ...this.stats }
  }

  /**
   * 重置统计数据
   */
  resetStats(): void {
    this.stats = {
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      hitRate: 0,
      cacheSize: 0,
      itemCount: 0
    }
    this.saveStats()
  }

  // ==================== 辅助方法 ====================

  /**
   * 生成缓存键
   */
  private generateCacheKey(url: string, options: RequestOptions, config: CacheConfig): string {
    if (config.keyGenerator) {
      return config.keyGenerator(url, options)
    }

    const method = options.method || 'GET'
    const prefix = config.keyPrefix || this.CACHE_PREFIX
    
    // 处理查询参数
    let finalUrl = url
    if (!config.ignoreQuery && options.params) {
      const stringParams = Object.entries(options.params).reduce((acc, [key, value]) => {
        acc[key] = String(value)
        return acc
      }, {} as Record<string, string>)
      const searchParams = new URLSearchParams(stringParams)
      const separator = url.includes('?') ? '&' : '?'
      finalUrl = `${url}${separator}${searchParams.toString()}`
    }

    // 处理请求体（POST等）
    let bodyHash = ''
    if (options.body && ['POST', 'PUT', 'PATCH'].includes(method)) {
      bodyHash = `_${  this.simpleHash(JSON.stringify(options.body))}`
    }

    return `${prefix}_${method}_${this.simpleHash(finalUrl)}${bodyHash}`
  }

  /**
   * 检查缓存是否过期
   */
  private isExpired(metadata: CacheMetadata): boolean {
    return Date.now() > metadata.expiresAt
  }

  /**
   * 更新缓存项
   */
  private async updateCacheItem(
    cacheKey: string,
    cacheItem: { data: unknown; metadata: CacheMetadata },
    storageType: StorageType = StorageType.CACHE
  ): Promise<void> {
    try {
      await unifiedStorage.set(
        this.storageModule,
        cacheKey,
        cacheItem,
        { storageType }
      )
    } catch (_error) {
      // console.error('❌ [HttpCache] 更新缓存项失败:', _error)
    }
  }

  /**
   * 计算数据大小
   */
  private calculateSize(data: unknown): number {
    try {
      return new Blob([JSON.stringify(data)]).size
    } catch {
      return JSON.stringify(data).length * 2 // 粗略估算
    }
  }

  /**
   * 格式化字节大小
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return `${Math.round(bytes / Math.pow(k, i) * 100) / 100  } ${  sizes[i]}`
  }

  /**
   * 简单哈希函数
   */
  private simpleHash(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash).toString(36)
  }

  /**
   * 加载统计数据
   */
  private async loadStats(): Promise<void> {
    try {
      const savedStats = await unifiedStorage.get<CacheStats>(
        this.storageModule,
        this.STATS_KEY,
        StorageType.LOCAL
      )
      
      if (savedStats) {
        this.stats = { ...this.stats, ...savedStats }
      }
    } catch (_error) {
      // console.error('❌ [HttpCache] 加载统计数据失败:', _error)
    }
  }

  /**
   * 保存统计数据
   */
  private async saveStats(): Promise<void> {
    try {
      await unifiedStorage.set(
        this.storageModule,
        this.STATS_KEY,
        this.stats,
        { storageType: StorageType.LOCAL }
      )
    } catch (_error) {
      // console.error('❌ [HttpCache] 保存统计数据失败:', _error)
    }
  }

  /**
   * 更新统计数据
   */
  private updateStats(): void {
    this.stats.hitRate = this.stats.totalRequests > 0 
      ? Math.round((this.stats.cacheHits / this.stats.totalRequests) * 100) / 100
      : 0
    
    // 异步保存，不阻塞主流程
    this.saveStats().catch(() => {})
  }
}

// ==================== 导出实例和工具函数 ====================

// 导出单例实例
export const httpCache = new HttpCacheAdapter()

// 预定义的缓存配置
export const CacheConfigs = {
  /** 快速缓存 - 30秒 */
  FAST: {
    strategy: CacheStrategy.CACHE_FIRST,
    ttl: 30 * 1000,
    storageType: StorageType.MEMORY
  } as CacheConfig,
  
  /** 标准缓存 - 5分钟 */
  STANDARD: {
    strategy: CacheStrategy.CACHE_FIRST,
    ttl: 5 * 60 * 1000,
    storageType: StorageType.CACHE
  } as CacheConfig,
  
  /** 长期缓存 - 1小时 */
  LONG: {
    strategy: CacheStrategy.CACHE_FIRST,
    ttl: 60 * 60 * 1000,
    storageType: StorageType.LOCAL
  } as CacheConfig,
  
  /** 网络优先 */
  NETWORK_FIRST: {
    strategy: CacheStrategy.NETWORK_FIRST,
    ttl: 5 * 60 * 1000,
    storageType: StorageType.CACHE
  } as CacheConfig,
  
  /** 仅缓存 */
  CACHE_ONLY: {
    strategy: CacheStrategy.CACHE_ONLY,
    ttl: 60 * 60 * 1000,
    storageType: StorageType.LOCAL
  } as CacheConfig,
  
  /** 仅网络 */
  NETWORK_ONLY: {
    strategy: CacheStrategy.NETWORK_ONLY,
    ttl: 0,
    storageType: StorageType.MEMORY
  } as CacheConfig,
  
  /** 后台更新 */
  STALE_WHILE_REVALIDATE: {
    strategy: CacheStrategy.STALE_WHILE_REVALIDATE,
    ttl: 10 * 60 * 1000,
    storageType: StorageType.CACHE
  } as CacheConfig
}

// Vue 3 Composition API Hook
export function useHttpCache() {
  return {
    httpCache,
    CacheStrategy,
    CacheConfigs,
    
    /**
     * 创建缓存配置
     */
    createCacheConfig: (overrides: Partial<CacheConfig> = {}): CacheConfig => ({
      strategy: CacheStrategy.CACHE_FIRST,
      ttl: 5 * 60 * 1000,
      storageType: StorageType.CACHE,
      compression: false,
      ignoreQuery: false,
      ...overrides
    }),
    
    /**
     * 获取缓存统计
     */
    getCacheStats: () => httpCache.getCacheStats(),
    
    /**
     * 清除缓存
     */
    clearCache: () => httpCache.clear(),
    
    /**
     * 重置统计
     */
    resetStats: () => httpCache.resetStats()
  }
}

// 兼容性导出
export default httpCache