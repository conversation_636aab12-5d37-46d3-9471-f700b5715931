/**
 * 表单存储适配器
 * 基于UnifiedStorageManager实现表单数据持久化
 * 替代原有的FormPersistenceManager
 */

import { UnifiedStorageManager, StorageModule, StorageType } from '@/utils/storage/storageService'

// 获取统一存储管理器实例
const unifiedStorage = UnifiedStorageManager.getInstance()

// ==================== 类型定义 ====================

/** 表单数据接口 */
export interface FormData {
  [key: string]: any
}

/** 表单持久化配置 */
export interface FormPersistenceConfig {
  /** 存储键名 */
  key: string
  /** 过期时间（毫秒），默认30分钟 */
  expireTime?: number
  /** 是否自动保存，默认true */
  autoSave?: boolean
  /** 存储类型，默认CACHE */
  storageType?: StorageType
  /** 是否启用压缩 */
  compression?: boolean
}

/** 表单元数据 */
export interface FormMetadata {
  /** 表单ID */
  formId: string
  /** 页面路径 */
  pagePath?: string
  /** 创建时间 */
  createdAt: number
  /** 最后更新时间 */
  updatedAt: number
  /** 数据版本 */
  version: string
  /** 字段数量 */
  fieldCount: number
}

/** 表单存储项 */
export interface FormStorageItem {
  /** 表单数据 */
  data: FormData
  /** 元数据 */
  metadata: FormMetadata
  /** 配置信息 */
  config: FormPersistenceConfig
}

// ==================== 表单存储适配器类 ====================

class FormStorageAdapter {
  private readonly storageModule = StorageModule.CONFIG
  private readonly DEFAULT_EXPIRE_TIME = 30 * 60 * 1000 // 30分钟
  private readonly FORM_PREFIX = 'form'
  
  // ==================== 核心存储方法 ====================

  /**
   * 保存表单数据
   */
  async saveFormData(config: FormPersistenceConfig, data: FormData): Promise<boolean> {
    try {
      const now = Date.now()
      const expireTime = config.expireTime || this.DEFAULT_EXPIRE_TIME
      
      // 构建表单存储项
      const formItem: FormStorageItem = {
        data,
        metadata: {
          formId: config.key,
          pagePath: this.getCurrentPagePath(),
          createdAt: now,
          updatedAt: now,
          version: '2.0', // 新版本标识
          fieldCount: Object.keys(data).length
        },
        config
      }

      const storageKey = this.getFormStorageKey(config.key)
      const storageType = config.storageType || StorageType.CACHE
      
      const success = await unifiedStorage.set(
        this.storageModule,
        storageKey,
        formItem,
        {
          storageType,
          defaultTTL: expireTime,
          compression: config.compression
        }
      )

      if (success) {
        // console.log(`📱 [FormStorage] 表单数据已保存: ${config.key}`, {
        //   fieldCount: Object.keys(data).length,
        //   storageType,
        //   expireTime: `${expireTime / 1000  }s`
        // })
      }

      return success
    } catch (_error) {
      // console.error('❌ [FormStorage] 保存表单数据失败:', _error)
      return false
    }
  }

  /**
   * 恢复表单数据
   */
  async restoreFormData(key: string, storageType: StorageType = StorageType.CACHE): Promise<FormData | null> {
    try {
      const storageKey = this.getFormStorageKey(key)
      
      const formItem = await unifiedStorage.get<FormStorageItem>(
        this.storageModule,
        storageKey,
        storageType
      )

      if (!formItem) {
        return null
      }

      // 验证数据完整性
      if (!this.validateFormItem(formItem)) {
        // console.warn(`⚠️ [FormStorage] 表单数据验证失败: ${key}`)
        await this.clearFormData(key, storageType)
        return null
      }

      // 更新最后访问时间
      formItem.metadata.updatedAt = Date.now()
      await unifiedStorage.set(
        this.storageModule,
        storageKey,
        formItem,
        { storageType }
      )

      // console.log(`📱 [FormStorage] 表单数据已恢复: ${key}`, {
      //   fieldCount: formItem.metadata.fieldCount,
      //   version: formItem.metadata.version,
      //   age: `${Math.round((Date.now() - formItem.metadata.createdAt) / 1000)  }s`
      // })

      return formItem.data
    } catch (_error) {
      // console.error('❌ [FormStorage] 恢复表单数据失败:', _error)
      return null
    }
  }

  /**
   * 清除指定表单数据
   */
  async clearFormData(key: string, storageType: StorageType = StorageType.CACHE): Promise<boolean> {
    try {
      const storageKey = this.getFormStorageKey(key)
      
      const success = await unifiedStorage.remove(
        this.storageModule,
        storageKey,
        storageType
      )

      if (success) {
        // console.log(`📱 [FormStorage] 表单数据已清除: ${key}`)
      }

      return success
    } catch (_error) {
      // console.error('❌ [FormStorage] 清除表单数据失败:', _error)
      return false
    }
  }

  /**
   * 清除所有表单数据
   */
  async clearAllFormData(): Promise<boolean> {
    try {
      const success = await unifiedStorage.clearModule(this.storageModule)
      
      if (success) {
        // console.log('📱 [FormStorage] 所有表单数据已清除')
      }

      return success
    } catch (_error) {
      // console.error('❌ [FormStorage] 清除所有表单数据失败:', _error)
      return false
    }
  }

  // ==================== 高级功能 ====================

  /**
   * 获取表单列表
   */
  async getFormList(): Promise<FormMetadata[]> {
    try {
      // 这里需要扩展UnifiedStorageManager来支持键列表查询
      // 暂时返回空数组，后续实现
      return []
    } catch (_error) {
      // console.error('❌ [FormStorage] 获取表单列表失败:', _error)
      return []
    }
  }

  /**
   * 获取表单元数据
   */
  async getFormMetadata(key: string, storageType: StorageType = StorageType.CACHE): Promise<FormMetadata | null> {
    try {
      const storageKey = this.getFormStorageKey(key)
      
      const formItem = await unifiedStorage.get<FormStorageItem>(
        this.storageModule,
        storageKey,
        storageType
      )

      return formItem?.metadata || null
    } catch (_error) {
      // console.error('❌ [FormStorage] 获取表单元数据失败:', _error)
      return null
    }
  }

  /**
   * 批量保存表单数据
   */
  async batchSaveFormData(forms: Array<{ config: FormPersistenceConfig; data: FormData }>): Promise<boolean[]> {
    try {
      const promises = forms.map(({ config, data }) => this.saveFormData(config, data))
      return await Promise.all(promises)
    } catch (_error) {
      // console.error('❌ [FormStorage] 批量保存表单数据失败:', _error)
      return forms.map(() => false)
    }
  }

  /**
   * 数据迁移：从旧的FormPersistenceManager迁移数据
   */
  async migrateFromLegacyStorage(): Promise<{ success: number; failed: number }> {
    let success = 0
    let failed = 0

    try {
      // 检查localStorage中的旧数据
      const legacyKeys = this.getLegacyFormKeys()
      
      for (const legacyKey of legacyKeys) {
        try {
          const legacyData = this.getLegacyFormData(legacyKey)
          if (legacyData) {
            const formKey = legacyKey.replace('oto_form_', '')
            const config: FormPersistenceConfig = {
              key: formKey,
              expireTime: legacyData.expireTime,
              autoSave: true
            }
            
            const migrated = await this.saveFormData(config, legacyData.data)
            if (migrated) {
              success++
              // 清除旧数据
              localStorage.removeItem(legacyKey)
            } else {
              failed++
            }
          }
        } catch (_error) {
          // console.error(`❌ [FormStorage] 迁移表单数据失败: ${legacyKey}`, _error)
          failed++
        }
      }

      // console.log(`📱 [FormStorage] 数据迁移完成: 成功${success}个, 失败${failed}个`)
    } catch (_error) {
      // console.error('❌ [FormStorage] 数据迁移失败:', _error)
    }

    return { success, failed }
  }

  // ==================== 辅助方法 ====================

  /**
   * 生成表单存储键
   */
  private getFormStorageKey(key: string): string {
    return `${this.FORM_PREFIX}_${key}`
  }

  /**
   * 获取当前页面路径
   */
  private getCurrentPagePath(): string {
    try {
      // #ifdef H5
      return window.location.pathname
      // #endif
      
      // #ifdef MP
      const mpPages = getCurrentPages()
      return mpPages[mpPages.length - 1]?.route || ''
      // #endif
      
      // #ifdef APP-PLUS
      const appPages = getCurrentPages()
      return appPages[appPages.length - 1]?.route || ''
      // #endif
      
      return ''
    } catch {
      return ''
    }
  }

  /**
   * 验证表单存储项
   */
  private validateFormItem(formItem: FormStorageItem): boolean {
    try {
      return !!(formItem.data && 
               formItem.metadata && 
               formItem.metadata.formId && 
               formItem.metadata.version)
    } catch {
      return false
    }
  }

  /**
   * 获取旧版本表单键列表
   */
  private getLegacyFormKeys(): string[] {
    try {
      // #ifdef H5
      const keys = Object.keys(localStorage)
      return keys.filter(key => key.startsWith('oto_form_'))
      // #endif
      
      return []
    } catch {
      return []
    }
  }

  /**
   * 获取旧版本表单数据
   */
  private getLegacyFormData(key: string): any {
    try {
      // #ifdef H5
      const data = localStorage.getItem(key)
      return data ? JSON.parse(data) : null
      // #endif
      
      return null
    } catch {
      return null
    }
  }
}

// ==================== 导出实例和Hook ====================

// 导出单例实例
export const formStorage = new FormStorageAdapter()

// Vue 3 Composition API Hook
export function useFormPersistence(key: string, config?: Partial<FormPersistenceConfig>) {
  const fullConfig: FormPersistenceConfig = {
    key,
    expireTime: config?.expireTime,
    autoSave: config?.autoSave ?? true,
    storageType: config?.storageType || StorageType.CACHE,
    compression: config?.compression
  }

  /**
   * 保存表单数据
   */
  const saveForm = (data: FormData) => {
    return formStorage.saveFormData(fullConfig, data)
  }

  /**
   * 恢复表单数据
   */
  const restoreForm = (): Promise<FormData | null> => {
    return formStorage.restoreFormData(key, fullConfig.storageType)
  }

  /**
   * 清除表单数据
   */
  const clearForm = () => {
    return formStorage.clearFormData(key, fullConfig.storageType)
  }

  /**
   * 获取表单元数据
   */
  const getFormMetadata = () => {
    return formStorage.getFormMetadata(key, fullConfig.storageType)
  }

  /**
   * 自动保存表单数据（在页面隐藏时）
   */
  const setupAutoSave = (getData: () => FormData) => {
    if (!fullConfig.autoSave) return

    const handleVisibilityChange = () => {
      if (document.hidden) {
        const data = getData()
        if (data && Object.keys(data).length > 0) {
          saveForm(data)
        }
      }
    }

    const handleBeforeUnload = () => {
      const data = getData()
      if (data && Object.keys(data).length > 0) {
        saveForm(data)
      }
    }

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    // 监听页面卸载
    window.addEventListener('beforeunload', handleBeforeUnload)

    // 返回清理函数
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }

  return {
    saveForm,
    restoreForm,
    clearForm,
    getFormMetadata,
    setupAutoSave
  }
}

// 兼容性导出（向后兼容）
export default formStorage