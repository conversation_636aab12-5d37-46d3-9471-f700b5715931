/**
 * 安全存储适配器
 * 提供跨平台的安全存储解决方案
 * 支持H5、iOS、Android等不同平台的安全存储机制
 */

// ==================== 类型定义 ====================

/** 安全存储配置 */
export interface SecureStorageConfig {
  /** 是否启用加密 */
  enableEncryption: boolean
  /** 加密密钥 */
  encryptionKey?: string
  /** 存储类型 */
  storageType: 'keychain' | 'keystore' | 'secure-local' | 'encrypted-local'
}

/** 安全存储项 */
export interface SecureStorageItem {
  /** 存储的值 */
  value: string
  /** 创建时间 */
  createdAt: number
  /** 最后访问时间 */
  lastAccessAt: number
  /** 过期时间（可选） */
  expiresAt?: number
}

/** 平台检测结果 */
interface PlatformInfo {
  platform: 'h5' | 'ios' | 'android' | 'mp-weixin' | 'unknown'
  supportsKeychain: boolean
  supportsKeystore: boolean
}

// ==================== 平台检测工具 ====================

class PlatformDetector {
  /**
   * 检测当前平台信息
   */
  static detect(): PlatformInfo {
    let platform: PlatformInfo['platform'] = 'unknown'
    let supportsKeychain = false
    let supportsKeystore = false

    // #ifdef H5
    platform = 'h5'
    supportsKeychain = false
    supportsKeystore = false
    // #endif

    // #ifdef APP-PLUS
    const systemInfo = uni.getSystemInfoSync()
    if (systemInfo.platform === 'ios') {
      platform = 'ios'
      supportsKeychain = true
    } else if (systemInfo.platform === 'android') {
      platform = 'android'
      supportsKeystore = true
    }
    // #endif

    // #ifdef MP-WEIXIN
    platform = 'mp-weixin'
    supportsKeychain = false
    supportsKeystore = false
    // #endif

    return {
      platform,
      supportsKeychain,
      supportsKeystore
    }
  }
}

// ==================== 加密工具 ====================

class EncryptionUtil {
  /**
   * 生成加密密钥
   */
  static async generateKey(): Promise<string> {
    // 简单的随机字符串生成
    return Array.from({ length: 32 }, () => 
      Math.floor(Math.random() * 16).toString(16)
    ).join('')
  }

  /**
   * 简单加密（XOR）
   */
  static encrypt(data: string, key: string): string {
    const result = []
    for (let i = 0; i < data.length; i++) {
      const keyChar = key.charCodeAt(i % key.length)
      const dataChar = data.charCodeAt(i)
      result.push((dataChar ^ keyChar).toString(16).padStart(2, '0'))
    }
    return result.join('')
  }

  /**
   * 简单解密（XOR）
   */
  static decrypt(encryptedData: string, key: string): string {
    const result = []
    const bytes = encryptedData.match(/.{2}/g) || []
    for (let i = 0; i < bytes.length; i++) {
      const keyChar = key.charCodeAt(i % key.length)
      const encryptedChar = parseInt(bytes[i], 16)
      result.push(String.fromCharCode(encryptedChar ^ keyChar))
    }
    return result.join('')
  }
}

// ==================== 安全存储适配器 ====================

export class SecureStorageAdapter {
  private static instance: SecureStorageAdapter
  private platformInfo: PlatformInfo
  private encryptionKey: string | null = null
  private initialized = false

  private constructor() {
    this.platformInfo = PlatformDetector.detect()
  }

  /**
   * 获取单例实例
   */
  static getInstance(): SecureStorageAdapter {
    if (!this.instance) {
      this.instance = new SecureStorageAdapter()
    }
    return this.instance
  }

  /**
   * 初始化安全存储
   */
  async initialize(config?: Partial<SecureStorageConfig>): Promise<boolean> {
    try {
      if (this.initialized) {
        return true
      }

      // 生成或获取加密密钥
      this.encryptionKey = config?.encryptionKey || await this.getOrCreateEncryptionKey()
      
      this.initialized = true
      return true
    } catch {
      return false
    }
  }

  /**
   * 安全存储数据
   */
  async setItem(key: string, value: string): Promise<boolean> {
    try {
      if (!this.initialized) {
        await this.initialize()
      }

      const item: SecureStorageItem = {
        value,
        createdAt: Date.now(),
        lastAccessAt: Date.now()
      }

      const serializedItem = JSON.stringify(item)
      const encryptedData = this.encryptData(serializedItem)
      
      return await this.platformSetItem(key, encryptedData)
    } catch {
       return false
     }
  }

  /**
   * 安全获取数据
   */
  async getItem(key: string): Promise<string | null> {
    try {
      if (!this.initialized) {
        await this.initialize()
      }

      const encryptedData = await this.platformGetItem(key)
      if (!encryptedData) {
        return null
      }

      const decryptedData = this.decryptData(encryptedData)
      const item: SecureStorageItem = JSON.parse(decryptedData)

      // 检查是否过期
      if (item.expiresAt && Date.now() > item.expiresAt) {
        await this.removeItem(key)
        return null
      }

      // 更新最后访问时间
      item.lastAccessAt = Date.now()
      const updatedData = this.encryptData(JSON.stringify(item))
      await this.platformSetItem(key, updatedData)

      return item.value
    } catch {
       return null
     }
  }

  /**
   * 移除安全存储的数据
   */
  async removeItem(key: string): Promise<boolean> {
    try {
      return await this.platformRemoveItem(key)
    } catch {
       return false
     }
  }

  /**
   * 清除所有安全存储数据
   */
  async clear(): Promise<boolean> {
    try {
      return await this.platformClear()
    } catch {
       return false
     }
  }

  /**
   * 获取平台信息
   */
  getPlatformInfo(): PlatformInfo {
    return { ...this.platformInfo }
  }

  // ==================== 私有方法 ====================

  /**
   * 获取或创建加密密钥
   */
  private async getOrCreateEncryptionKey(): Promise<string> {
    const keyStorageKey = '__secure_storage_key__'
    
    // 尝试从本地存储获取现有密钥
    let key = uni.getStorageSync(keyStorageKey)
    
    if (!key) {
      // 生成新密钥
      key = await EncryptionUtil.generateKey()
      uni.setStorageSync(keyStorageKey, key)
    }
    
    return key
  }

  /**
   * 加密数据
   */
  private encryptData(data: string): string {
    if (!this.encryptionKey) {
      throw new Error('加密密钥未初始化')
    }
    return EncryptionUtil.encrypt(data, this.encryptionKey)
  }

  /**
   * 解密数据
   */
  private decryptData(encryptedData: string): string {
    if (!this.encryptionKey) {
      throw new Error('加密密钥未初始化')
    }
    return EncryptionUtil.decrypt(encryptedData, this.encryptionKey)
  }

  /**
   * 平台特定的存储实现
   */
  private async platformSetItem(key: string, value: string): Promise<boolean> {
    try {
      switch (this.platformInfo.platform) {
        case 'ios':
        case 'android':
        case 'h5':
        case 'mp-weixin':
        default:
          // 统一使用 uni-app 的存储API，添加安全前缀
          uni.setStorageSync(`secure_${key}`, value)
          return true
      }
    } catch {
       return false
     }
  }

  /**
   * 平台特定的获取实现
   */
  private async platformGetItem(key: string): Promise<string | null> {
    try {
      switch (this.platformInfo.platform) {
        case 'ios':
        case 'android':
        case 'h5':
        case 'mp-weixin':
        default:
          // 统一使用 uni-app 的存储API
          return uni.getStorageSync(`secure_${key}`) || null
      }
    } catch {
       return null
     }
  }

  /**
   * 平台特定的移除实现
   */
  private async platformRemoveItem(key: string): Promise<boolean> {
    try {
      switch (this.platformInfo.platform) {
        case 'ios':
        case 'android':
        case 'h5':
        case 'mp-weixin':
        default:
          uni.removeStorageSync(`secure_${key}`)
          return true
      }
    } catch {
       return false
     }
  }

  /**
   * 平台特定的清除实现
   */
  private async platformClear(): Promise<boolean> {
    try {
      // 获取所有存储键，移除以 secure_ 开头的项
      const info = uni.getStorageInfoSync()
      const keysToRemove = info.keys.filter(key => key.startsWith('secure_'))
      
      keysToRemove.forEach(key => {
        try {
          uni.removeStorageSync(key)
        } catch {
          // 忽略单个键移除失败
        }
      })
      
      return true
    } catch {
       return false
     }
  }
}

// ==================== 导出 ====================

/** 安全存储实例 */
export const secureStorage = SecureStorageAdapter.getInstance()

/** 默认导出 */
export default secureStorage