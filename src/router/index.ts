/**
 * 路由管理入口
 * UniApp 使用 pages.json 配置路由，此文件用于路由相关的工具函数
 */

import { RouteManager, routerUtils } from './manager'
import { RouteGuard } from './guard'
import { ROUTES } from './routes'

// 导出路由管理器和工具
export { RouteManager, RouteGuard, routerUtils, ROUTES }

// 导出类型定义
export type {
  RouteConfig,
  NavigateOptions,
  PageStackInfo,
  RouteHistory,
  RouteGuardHook,
  RouteGuardConfig,
  RouteModule,
  RouteConstants
} from './types'

// 导出路由配置
export {
  authRoutes,
  mainRoutes,
  userRoutes,
  routeModules,
  getAllRoutes,
  getRouteByPath,
  getAuthRequiredPages,
  getGuestOnlyPages,
  getTabBarPages
} from './routes'

/**
 * 兼容旧版本的路由跳转封装
 * @deprecated 建议使用 RouteManager 替代
 */
export class Router {
  /**
   * 跳转到指定页面
   */
  static navigateTo(url: string, params?: Record<string, any>) {
    return RouteManager.navigateTo(url, params)
  }

  /**
   * 重定向到指定页面
   */
  static redirectTo(url: string, params?: Record<string, any>) {
    return RouteManager.redirectTo(url, params)
  }

  /**
   * 切换到 tabBar 页面
   */
  static switchTab(url: string) {
    return RouteManager.switchTab(url)
  }

  /**
   * 返回上一页
   */
  static navigateBack(delta: number = 1) {
    return RouteManager.navigateBack(delta)
  }

  /**
   * 构建查询字符串
   */
  private static buildQuery(params: Record<string, any>): string {
    return Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&')
  }
}

/**
 * 常用路由跳转方法
 */
export const routerHelpers = {
  // 跳转到登录页
  toLogin: () => Router.redirectTo(ROUTES.AUTH.LOGIN),
  
  // 跳转到首页
  toHome: () => Router.switchTab(ROUTES.HOME),
  
  // 跳转到个人中心
  toProfile: () => Router.navigateTo(ROUTES.USER.PROFILE),
  
  // 跳转到用户信息页
  toUserInfo: () => Router.navigateTo(ROUTES.USER.INFORMATION),
  
  // 跳转到我的页面
  toMine: () => Router.navigateTo(ROUTES.USER.MINE)
}