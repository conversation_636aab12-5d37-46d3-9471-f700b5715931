/**
 * 增强路由守卫 - 登录跳转机制核心实现
 * 基于现有架构增强，提供自动登录跳转和状态恢复功能
 */

import { useAuthStore } from '@/store/modules/auth'
import { ROUTES, getRouteByPath } from '../routes'
import { StoreUtil } from '@/utils/storage/StoreUtil'
import { StorageModule } from '@/utils/storage'

/**
 * 跳转信息接口
 */
interface RedirectInfo {
  /** 目标页面路径 */
  targetPath: string
  /** 页面参数 */
  params?: Record<string, any>
  /** 跳转类型 */
  navigateType: 'navigateTo' | 'redirectTo' | 'reLaunch' | 'switchTab'
  /** 保存时间戳 */
  timestamp: number
  /** 来源页面 */
  fromPath?: string
}

/**
 * 增强路由守卫类
 */
export class EnhancedRouteGuard {
  private static readonly REDIRECT_KEY = 'login_redirect_info'
  private static readonly MAX_REDIRECT_AGE = 30 * 60 * 1000 // 30分钟
  
  /**
   * 初始化增强路由守卫
   * 拦截所有uni路由API
   */
  static init() {
    this.interceptNavigationAPIs()
  }
  
  /**
   * 拦截uni导航API
   */
  private static interceptNavigationAPIs() {
    // 保存原始API
    const originalNavigateTo = uni.navigateTo
    const originalRedirectTo = uni.redirectTo
    const originalReLaunch = uni.reLaunch
    const originalSwitchTab = uni.switchTab
    
    // 拦截 uni.navigateTo
    uni.navigateTo = (options: any) => {
      return this.interceptNavigation('navigateTo', options, originalNavigateTo)
    }
    
    // 拦截 uni.redirectTo
    uni.redirectTo = (options: any) => {
      return this.interceptNavigation('redirectTo', options, originalRedirectTo)
    }
    
    // 拦截 uni.reLaunch
    uni.reLaunch = (options: any) => {
      return this.interceptNavigation('reLaunch', options, originalReLaunch)
    }
    
    // 拦截 uni.switchTab
    uni.switchTab = (options: any) => {
      return this.interceptNavigation('switchTab', options, originalSwitchTab)
    }
  }
  
  /**
   * 拦截导航操作
   */
  private static async interceptNavigation(
    type: 'navigateTo' | 'redirectTo' | 'reLaunch' | 'switchTab',
    options: any,
    originalMethod: Function
  ) {
    try {
      const { url } = options
      const [path, queryString] = url.split('?')
      const params = this.parseQueryString(queryString)
      
      // 检查权限
      const authCheck = await this.checkAuthentication(path)
      
      if (!authCheck.canAccess) {
        // 需要登录，保存跳转信息并跳转到登录页
        await this.saveRedirectInfo({
          targetPath: path,
          params,
          navigateType: type,
          timestamp: Date.now(),
          fromPath: this.getCurrentPath()
        })
        
        // 跳转到登录页
        return originalMethod.call(uni, {
          ...options,
          url: ROUTES.AUTH.LOGIN,
          success: (res: any) => {
            options.success?.(res)
          },
          fail: options.fail,
          complete: options.complete
        })
      }
      
      // 权限检查通过，执行原始导航
      return originalMethod.call(uni, options)
      
    } catch (error) {
      console.error(`❌ [EnhancedRouteGuard] 导航拦截失败:`, error)
      // 出错时执行原始导航
      return originalMethod.call(uni, options)
    }
  }
  
  /**
   * 检查认证状态
   */
  private static async checkAuthentication(
    path: string
  ): Promise<{ canAccess: boolean; reason?: string }> {
    const authStore = useAuthStore()
    const routeConfig = getRouteByPath(path)
    
    // 如果是登录页面，直接允许访问
    if (path === ROUTES.AUTH.LOGIN || path === ROUTES.AUTH.REGISTER) {
      return { canAccess: true }
    }
    
    // 检查是否需要登录
    if (routeConfig?.requiresAuth) {
      if (!authStore.isLoggedIn) {
        return {
          canAccess: false,
          reason: '页面需要登录访问'
        }
      }
    }
    
    // 检查已登录用户访问游客页面
    if (routeConfig?.guestOnly && authStore.isLoggedIn) {
      // 这里可以选择重定向到首页或阻止访问
      return { canAccess: true } // 暂时允许，可根据需求调整
    }
    
    return { canAccess: true }
  }
  
  /**
   * 保存跳转信息
   */
  static async saveRedirectInfo(redirectInfo: RedirectInfo): Promise<void> {
    try {
      await StoreUtil.set(this.REDIRECT_KEY, redirectInfo, StorageModule.CONFIG)
    } catch (error) {
      console.error(`❌ [EnhancedRouteGuard] 保存跳转信息失败:`, error)
    }
  }
  
  /**
   * 获取并清除跳转信息
   */
  static async getAndClearRedirectInfo(): Promise<RedirectInfo | null> {
    try {
      const redirectInfo = await StoreUtil.get(this.REDIRECT_KEY, StorageModule.CONFIG) as RedirectInfo
      
      if (!redirectInfo) {
        return null
      }
      
      // 检查是否过期
      if (Date.now() - redirectInfo.timestamp > this.MAX_REDIRECT_AGE) {
        await StoreUtil.remove(this.REDIRECT_KEY, StorageModule.CONFIG)
        return null
      }
      
      // 清除跳转信息
      await StoreUtil.remove(this.REDIRECT_KEY, StorageModule.CONFIG)
      
      return redirectInfo
      
    } catch {
        // 存储读取失败时返回 null，不影响正常流程
        return null
      }
    }
   
   /**
   * 执行登录后跳转
   */
  static async handleLoginSuccess(): Promise<void> {
    try {
      const redirectInfo = await this.getAndClearRedirectInfo()
      
      if (redirectInfo) {
        // 构建完整URL
        let targetUrl = redirectInfo.targetPath
        if (redirectInfo.params && Object.keys(redirectInfo.params).length > 0) {
          const queryString = Object.keys(redirectInfo.params)
            .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(redirectInfo.params![key])}`)
            .join('&')
          targetUrl = `${targetUrl}?${queryString}`
        }
        
        // console.log(`🎯 [EnhancedRouteGuard] 登录成功，跳转到目标页面: ${targetUrl}`)
        
        // 根据原始跳转类型执行跳转
        switch (redirectInfo.navigateType) {
          case 'navigateTo':
            uni.navigateTo({ url: targetUrl })
            break
          case 'redirectTo':
            uni.redirectTo({ url: targetUrl })
            break
          case 'reLaunch':
            uni.reLaunch({ url: targetUrl })
            break
          case 'switchTab':
            uni.switchTab({ url: targetUrl })
            break
          default:
            uni.navigateTo({ url: targetUrl })
        }
      } else {
        // 没有跳转信息，跳转到首页
        uni.switchTab({ url: ROUTES.HOME })
      }
    } catch {
        // 导航失败时跳转到首页作为兜底方案
        uni.switchTab({ url: ROUTES.HOME })
      }
    }
  
  /**
   * 解析查询字符串
   */
  private static parseQueryString(queryString?: string): Record<string, any> {
    if (!queryString) return {}
    
    const params: Record<string, any> = {}
    queryString.split('&').forEach(pair => {
      const [key, value] = pair.split('=')
      if (key) {
        params[decodeURIComponent(key)] = decodeURIComponent(value || '')
      }
    })
    
    return params
  }
  
  /**
   * 获取当前页面路径
   */
  private static getCurrentPath(): string {
    const pages = getCurrentPages()
    if (pages.length === 0) return ''
    
    const currentPage = pages[pages.length - 1]
    return `/${currentPage.route}`
  }
  
  /**
   * 清除所有跳转信息
   */
  static async clearAllRedirectInfo(): Promise<void> {
    try {
      await StoreUtil.remove(this.REDIRECT_KEY, StorageModule.CONFIG)
      // console.log(`🧹 [EnhancedRouteGuard] 已清除所有跳转信息`)
    } catch (_error) {
        console.error(`❌ [EnhancedRouteGuard] 清理跳转信息失败:`, _error)
      }
    }
 }