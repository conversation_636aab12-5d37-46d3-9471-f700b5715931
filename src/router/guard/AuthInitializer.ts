/**
 * 认证初始化器
 * 负责应用启动时的认证状态恢复和路由守卫初始化
 */

import { useAuthStore } from '@/store/modules/auth'
import { EnhancedRouteGuard } from './EnhancedRouteGuard'
import { ROUTES } from '../routes'

/**
 * 认证初始化器类
 */
export class AuthInitializer {
  private static isInitialized = false
  
  /**
   * 初始化认证系统
   */
  static async init(): Promise<void> {
    if (this.isInitialized) {
      // console.log('ℹ️ [AuthInitializer] 认证系统已初始化，跳过重复初始化')
      return
    }
    
    try {
      // console.log('🚀 [AuthInitializer] 开始初始化认证系统...')
      
      // 1. 初始化增强路由守卫
      EnhancedRouteGuard.init()
      
      // 2. 恢复认证状态
      await this.restoreAuthState()
      
      // 3. 处理应用启动场景
      await this.handleAppLaunch()
      
      this.isInitialized = true
      // console.log('✅ [AuthInitializer] 认证系统初始化完成')
      
    } catch (error) {
      console.error('❌ [AuthInitializer] 认证系统初始化失败:', error)
      // 初始化失败时仍然标记为已初始化，避免重复尝试
      this.isInitialized = true
    }
  }
  
  /**
   * 恢复认证状态
   */
  private static async restoreAuthState(): Promise<void> {
    try {
      // console.log('🔄 [AuthInitializer] 开始恢复认证状态...')
      
      const authStore = useAuthStore()
      await authStore.restoreAuth()
      
      if (authStore.isLoggedIn) {
        // console.log('✅ [AuthInitializer] 认证状态恢复成功，用户已登录')
      } else {
        // console.log('ℹ️ [AuthInitializer] 用户未登录或认证状态已过期')
      }
      
    } catch (error) {
      console.error('❌ [AuthInitializer] 恢复认证状态失败:', error)
    }
  }
  
  /**
   * 处理应用启动场景
   */
  private static async handleAppLaunch(): Promise<void> {
    try {
      // 获取启动页面信息
      const launchOptions = this.getLaunchOptions()
      
      if (launchOptions) {
        // console.log('📱 [AuthInitializer] 应用启动信息:', launchOptions)
        
        // 如果是从分享链接或其他外部链接启动
        if (launchOptions.path && launchOptions.path !== 'pages/home/<USER>/home') {
          await this.handleExternalLaunch(launchOptions)
        }
      }
      
    } catch (error) {
      console.error('❌ [AuthInitializer] 处理应用启动失败:', error)
    }
  }
  
  /**
   * 处理外部链接启动
   */
  private static async handleExternalLaunch(launchOptions: any): Promise<void> {
    try {
      const authStore = useAuthStore()
      const targetPath = `/${launchOptions.path}`
      
      // 检查目标页面是否需要登录
      const { getRouteByPath } = await import('../routes')
      const routeConfig = getRouteByPath(targetPath)
      
      if (routeConfig?.requiresAuth && !authStore.isLoggedIn) {
        // console.log('🔐 [AuthInitializer] 外部链接需要登录，保存跳转信息')
        
        // 保存跳转信息
        await EnhancedRouteGuard.saveRedirectInfo({
          targetPath,
          params: launchOptions.query || {},
          navigateType: 'reLaunch',
          timestamp: Date.now(),
          fromPath: 'external'
        })
        
        // 跳转到登录页
        setTimeout(() => {
          uni.reLaunch({
            url: ROUTES.AUTH.LOGIN
          })
        }, 100)
      }
      
    } catch (error) {
      console.error('❌ [AuthInitializer] 处理外部链接启动失败:', error)
    }
  }
  
  /**
   * 获取应用启动选项
   */
  private static getLaunchOptions(): any {
    try {
      // 尝试获取启动选项
      // 注意：这个API在不同平台可能有差异
      return (getApp() as any).globalData?.launchOptions || null
    } catch (_error) {
      // console.warn('⚠️ [AuthInitializer] 无法获取启动选项:', _error)
      return null
    }
  }
  
  /**
   * 重置初始化状态（用于测试或重新初始化）
   */
  static reset(): void {
    this.isInitialized = false
    // console.log('🔄 [AuthInitializer] 初始化状态已重置')
  }
  
  /**
   * 检查是否已初始化
   */
  static get initialized(): boolean {
    return this.isInitialized
  }
}

/**
 * 导出便捷的初始化函数
 */
export const initializeAuth = AuthInitializer.init.bind(AuthInitializer)