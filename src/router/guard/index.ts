/**
 * 路由守卫
 * UniApp 中通过页面生命周期实现路由守卫功能
 */

import { useAuthStore } from '@/store/modules/auth'
import { ROUTES, getRouteByPath, getAuthRequiredPages, getGuestOnlyPages } from '../routes'
import { Router } from '../index'
import { StoreUtil } from '@/utils/storage/StoreUtil'
import { StorageModule } from '@/utils/storage'
import type { RouteConfig, RouteGuardHook } from '../types'

/**
 * 需要登录的页面列表（动态获取）
 */
const getAuthRequiredPagesList = () => getAuthRequiredPages()

/**
 * 登录用户不能访问的页面列表（动态获取）
 */
const getGuestOnlyPagesList = () => getGuestOnlyPages()

/**
 * 路由守卫类
 */
export class RouteGuard {
  /**
   * 页面加载前的权限检查
   * 在页面的 onLoad 生命周期中调用
   */
  static async beforeEnter(currentPath: string) {
    const authStore = useAuthStore()
    const isLoggedIn = authStore.isLoggedIn
    
    // 检查是否需要登录
    if (this.requiresAuth(currentPath) && !isLoggedIn) {
      // 保存当前页面路径，登录后跳转回来
      await StoreUtil.set('redirect_after_login', currentPath, StorageModule.CONFIG)
      Router.redirectTo(ROUTES.AUTH.LOGIN)
      return false
    }
    
    // 检查已登录用户是否访问游客页面
    if (this.isGuestOnly(currentPath) && isLoggedIn) {
      Router.redirectTo(ROUTES.HOME)
      return false
    }
    
    return true
  }
  
  /**
   * 登录成功后的重定向处理
   */
  static async handleLoginSuccess() {
    const redirectPath = await StoreUtil.get('redirect_after_login', StorageModule.CONFIG)
    if (redirectPath) {
      await StoreUtil.remove('redirect_after_login', StorageModule.CONFIG)
      Router.redirectTo(redirectPath)
    } else {
      Router.redirectTo(ROUTES.HOME)
    }
  }
  
  /**
   * 检查页面是否需要登录
   */
  private static requiresAuth(path: string): boolean {
    const authRequiredPages = getAuthRequiredPagesList()
    return authRequiredPages.some((authPage: string) => path.includes(authPage))
  }
  
  /**
   * 检查页面是否只允许游客访问
   */
  private static isGuestOnly(path: string): boolean {
    const guestOnlyPages = getGuestOnlyPagesList()
    return guestOnlyPages.some((guestPage: string) => path.includes(guestPage))
  }
}

/**
 * 页面权限检查 Mixin
 * 在需要权限控制的页面中使用
 */
export const routeGuardMixin = {
  onLoad() {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentPath = `/${currentPage.route}`
    
    RouteGuard.beforeEnter(currentPath)
  }
}