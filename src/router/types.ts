/**
 * 路由相关类型定义
 */

/**
 * 路由配置接口
 */
export interface RouteConfig {
  /** 路由路径 */
  path: string
  /** 页面标题 */
  title?: string
  /** 是否需要登录 */
  requiresAuth?: boolean
  /** 是否只允许游客访问 */
  guestOnly?: boolean
  /** 导航栏样式 */
  navigationStyle?: 'default' | 'custom'
  /** 导航栏背景色 */
  navigationBarBackgroundColor?: string
  /** 导航栏文字样式 */
  navigationBarTextStyle?: 'black' | 'white'
  /** 页面元数据 */
  meta?: Record<string, any>
}

/**
 * 路由跳转参数
 */
export interface NavigateOptions {
  /** 跳转路径 */
  url: string
  /** 查询参数 */
  params?: Record<string, any>
  /** 跳转类型 */
  type?: 'navigateTo' | 'redirectTo' | 'switchTab' | 'reLaunch'
  /** 成功回调 */
  success?: () => void
  /** 失败回调 */
  fail?: (_error: any) => void
  /** 完成回调 */
  complete?: () => void
}

/**
 * 路由守卫钩子函数类型
 */
export type RouteGuardHook = (_to: RouteConfig, _from?: RouteConfig) => boolean | Promise<boolean>

/**
 * 路由守卫配置
 */
export interface RouteGuardConfig {
  /** 全局前置守卫 */
  beforeEach?: RouteGuardHook[]
  /** 全局后置守卫 */
  afterEach?: Array<(_to: RouteConfig, _from?: RouteConfig) => void>
  /** 需要登录的页面列表 */
  authRequiredPages?: string[]
  /** 只允许游客访问的页面列表 */
  guestOnlyPages?: string[]
}

/**
 * 路由模块配置
 */
export interface RouteModule {
  /** 模块名称 */
  name: string
  /** 模块路径前缀 */
  prefix: string
  /** 模块下的路由配置 */
  routes: Record<string, RouteConfig>
}

/**
 * 路由常量定义
 */
export interface RouteConstants {
  /** 认证相关路由 */
  AUTH: {
    LOGIN: string
    REGISTER: string
    REAL_AUTH: string
  }
  /** 主要功能路由 */
  HOME: string
  SERVICE: string
  PUBLISH: string
  MESSAGE: string
  /** 用户相关路由 */
  USER: {
    MINE: string
    PROFILE: string
    INFORMATION: string
    EXTEND_INFO: string
    SETUP_PROFILE: string
  }
}

/**
 * 页面栈信息
 */
export interface PageStackInfo {
  /** 当前页面路径 */
  currentPath: string
  /** 页面栈深度 */
  stackDepth: number
  /** 是否可以返回 */
  canGoBack: boolean
  /** 页面参数 */
  params?: Record<string, any>
}

/**
 * 路由历史记录
 */
export interface RouteHistory {
  /** 访问路径 */
  path: string
  /** 访问时间 */
  timestamp: number
  /** 页面参数 */
  params?: Record<string, any>
  /** 来源页面 */
  from?: string
}