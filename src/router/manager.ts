/**
 * 增强的路由管理器
 * 提供完整的路由管理功能
 */

import { ROUTES, getRouteByPath, getTabBarPages } from './routes'
import { RouteGuard } from './guard'
import type { NavigateOptions, PageStackInfo, RouteHistory, RouteConfig } from './types'
import { StoreUtil } from '@/utils/storage/StoreUtil'
import { StorageModule } from '@/utils/storage'

/**
 * 路由历史记录存储键
 */
const ROUTE_HISTORY_KEY = 'route_history'

/**
 * 增强的路由管理器
 */
export class RouteManager {
  private static history: RouteHistory[] = []
  private static maxHistoryLength = 50

  /**
   * 初始化路由管理器
   */
  static async init() {
    // 加载路由历史记录
    await this.loadHistory()
    
    // 监听页面显示事件
    this.setupPageListeners()
  }

  /**
   * 统一的路由跳转方法
   */
  static async navigate(options: NavigateOptions): Promise<boolean> {
    const { url, params, type = 'navigateTo', success, fail, complete } = options
    
    try {
      // 构建完整URL
      const fullUrl = this.buildUrl(url, params)
      
      // 获取路由配置
      const routeConfig = getRouteByPath(url)
      
      // 执行路由守卫检查
      if (routeConfig) {
        const canNavigate = await RouteGuard.beforeEnter(url)
        if (!canNavigate) {
          fail?.(new Error('路由守卫拦截'))
          complete?.()
          return false
        }
      }
      
      // 记录路由历史
      await this.addToHistory({
        path: url,
        timestamp: Date.now(),
        params,
        from: this.getCurrentPath()
      })
      
      // 执行跳转
      const navigatePromise = this.executeNavigation(type, fullUrl)
      
      await navigatePromise
      success?.()
      complete?.()
      return true
      
    } catch (_error) {
      // console.error('路由跳转失败:', _error)
      fail?.(_error)
      complete?.()
      return false
    }
  }

  /**
   * 跳转到指定页面
   */
  static navigateTo(url: string, params?: Record<string, any>) {
    return this.navigate({ url, params, type: 'navigateTo' })
  }

  /**
   * 重定向到指定页面
   */
  static redirectTo(url: string, params?: Record<string, any>) {
    return this.navigate({ url, params, type: 'redirectTo' })
  }

  /**
   * 切换到 tabBar 页面
   */
  static switchTab(url: string) {
    return this.navigate({ url, type: 'switchTab' })
  }

  /**
   * 关闭所有页面，打开到应用内的某个页面
   */
  static reLaunch(url: string, params?: Record<string, any>) {
    return this.navigate({ url, params, type: 'reLaunch' })
  }

  /**
   * 返回上一页
   */
  static navigateBack(delta: number = 1): Promise<boolean> {
    return new Promise((resolve) => {
      uni.navigateBack({
        delta,
        success: () => resolve(true),
        fail: (err) => {
          // console.error('返回上一页失败:', err)
          resolve(false)
        }
      })
    })
  }

  /**
   * 获取当前页面信息
   */
  static getCurrentPageInfo(): PageStackInfo {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    
    return {
      currentPath: `/${currentPage.route}`,
      stackDepth: pages.length,
      canGoBack: pages.length > 1,
      params: (currentPage as any).options || {}
    }
  }

  /**
   * 获取当前页面路径
   */
  static getCurrentPath(): string {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    return `/${currentPage.route}`
  }

  /**
   * 获取路由历史记录
   */
  static getHistory(): RouteHistory[] {
    return [...this.history]
  }

  /**
   * 清空路由历史记录
   */
  static async clearHistory() {
    this.history = []
    await StoreUtil.remove(ROUTE_HISTORY_KEY, StorageModule.CONFIG)
  }

  /**
   * 检查是否为TabBar页面
   */
  static isTabBarPage(path: string): boolean {
    const tabBarPages = getTabBarPages()
    return tabBarPages.some(page => page.path === path)
  }

  /**
   * 获取页面配置
   */
  static getPageConfig(path: string): RouteConfig | undefined {
    return getRouteByPath(path)
  }

  /**
   * 构建URL
   */
  private static buildUrl(url: string, params?: Record<string, any>): string {
    if (!params || Object.keys(params).length === 0) {
      return url
    }
    
    const query = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&')
    
    return `${url}?${query}`
  }

  /**
   * 执行具体的导航操作
   */
  private static executeNavigation(type: string, url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const options = {
        url,
        success: () => resolve(),
        fail: (err: any) => reject(err)
      }

      switch (type) {
        case 'navigateTo':
          uni.navigateTo(options)
          break
        case 'redirectTo':
          uni.redirectTo(options)
          break
        case 'switchTab':
          uni.switchTab(options)
          break
        case 'reLaunch':
          uni.reLaunch(options)
          break
        default:
          reject(new Error(`不支持的导航类型: ${type}`))
      }
    })
  }

  /**
   * 添加到历史记录
   */
  private static async addToHistory(record: RouteHistory) {
    this.history.unshift(record)
    
    // 限制历史记录长度
    if (this.history.length > this.maxHistoryLength) {
      this.history = this.history.slice(0, this.maxHistoryLength)
    }
    
    // 保存到本地存储
    await this.saveHistory()
  }

  /**
   * 加载历史记录
   */
  private static async loadHistory() {
    try {
      const history = await StoreUtil.get(ROUTE_HISTORY_KEY, StorageModule.CONFIG)
      if (Array.isArray(history)) {
        this.history = history
      }
    } catch (_error) {
      // console.warn('加载路由历史记录失败:', _error)
    }
  }

  /**
   * 保存历史记录
   */
  private static async saveHistory() {
    try {
      await StoreUtil.set(ROUTE_HISTORY_KEY, this.history, StorageModule.CONFIG)
    } catch (_error) {
      // console.warn('保存路由历史记录失败:', _error)
    }
  }

  /**
   * 设置页面监听器
   */
  private static setupPageListeners() {
    // 这里可以添加全局的页面监听逻辑
    // 由于UniApp的限制，主要通过页面生命周期来实现
  }
}

/**
 * 路由工具函数
 */
export const routerUtils = {
  // 跳转到登录页
  toLogin: () => RouteManager.redirectTo(ROUTES.AUTH.LOGIN),
  
  // 跳转到首页
  toHome: () => RouteManager.switchTab(ROUTES.HOME),
  
  // 跳转到个人中心
  toProfile: () => RouteManager.navigateTo(ROUTES.USER.MINE),
  
  // 跳转到用户信息页
  toUserInfo: () => RouteManager.navigateTo(ROUTES.USER.INFORMATION),
  
  // 跳转到我的页面
  toMine: () => RouteManager.switchTab(ROUTES.USER.MINE),
  
  // 跳转到服务页
  toService: () => RouteManager.switchTab(ROUTES.SERVICE),
  
  // 跳转到匹配页
  toMatch: () => RouteManager.switchTab(ROUTES.MATCH),
  
  // 跳转到消息页
  toMessage: () => RouteManager.switchTab(ROUTES.MESSAGE),
  
  // 跳转到实名认证
  toRealAuth: () => RouteManager.navigateTo(ROUTES.AUTH.REAL_AUTH),
  
  // 跳转到完善资料
  toSetupProfile: () => RouteManager.navigateTo(ROUTES.USER.SETUP_PROFILE),
  
  // 跳转到扩展信息
  toExtendInfo: () => RouteManager.navigateTo(ROUTES.USER.EXTEND_INFO)
}