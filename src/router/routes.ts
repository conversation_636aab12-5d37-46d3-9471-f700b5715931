/**
 * 路由配置管理
 */

import type { RouteConfig, RouteModule, RouteConstants } from './types'

/**
 * 认证模块路由
 */
export const authRoutes: RouteModule = {
  name: 'auth',
  prefix: '/pages/auth',
  routes: {
    LOGIN: {
      path: '/pages/auth/login',
      title: '登录',
      guestOnly: true,
      navigationStyle: 'custom',
      meta: {
        keepAlive: false,
        transition: 'slide-right'
      }
    },
    REGISTER: {
      path: '/pages/auth/register',
      title: '注册',
      guestOnly: true,
      navigationStyle: 'custom',
      meta: {
        keepAlive: false,
        transition: 'slide-left'
      }
    },
    REAL_AUTH: {
      path: '/pages/auth/realAuth',
      title: '实名认证',
      requiresAuth: true,
      navigationBarBackgroundColor: '#faf5f5',
      navigationBarTextStyle: 'black',
      meta: {
        keepAlive: true
      }
    }
  }
}

/**
 * 主要功能模块路由
 */
export const mainRoutes: RouteModule = {
  name: 'main',
  prefix: '/pages',
  routes: {
    HOME: {
      path: '/pages/home/<USER>/home',
      title: '首页',
      meta: {
        keepAlive: true,
        isTabBar: true,
        tabBarIndex: 0
      }
    },
    SERVICE: {
      path: '/pages/service/index/service',
      title: '服务',
      meta: {
        keepAlive: true,
        isTabBar: true,
        tabBarIndex: 1
      }
    },
    PUBLISH: {
      path: '/pages/publish/publish',
      title: '发布',
      meta: {
        keepAlive: true,
        isTabBar: true,
        tabBarIndex: 2
      }
    },
    MESSAGE: {
      path: '/pages/message/index/message',
      title: '消息',
      requiresAuth: true,
      meta: {
        keepAlive: true,
        isTabBar: true,
        tabBarIndex: 3
      }
    }
  }
}

/**
 * 用户模块路由
 */
export const userRoutes: RouteModule = {
  name: 'user',
  prefix: '/pages/user',
  routes: {
    MINE: {
      path: '/pages/user/mine/mine',
      title: '我的',
      meta: {
        keepAlive: true,
        isTabBar: true,
        tabBarIndex: 4
      }
    },
    INFORMATION: {
      path: '/pages/user/infomation/infomation',
      title: '基础资料',
      requiresAuth: true,
      meta: {
        keepAlive: true
      }
    },
    EXTEND_INFO: {
      path: '/pages/user/extendInfo/ExtendInfo',
      title: '扩展信息',
      requiresAuth: true,
      navigationBarBackgroundColor: '#faf5f5',
      navigationBarTextStyle: 'black',
      meta: {
        keepAlive: true
      }
    },
    SETUP_PROFILE: {
      path: '/pages/user/setupProfile/setupProfile',
      title: '完善资料',
      requiresAuth: true,
      navigationStyle: 'custom',
      meta: {
        keepAlive: false
      }
    }
  }
}

/**
 * 所有路由模块
 */
export const routeModules: RouteModule[] = [
  authRoutes,
  mainRoutes,
  userRoutes
]

/**
 * 路由常量定义
 */
export const ROUTES: RouteConstants = {
  AUTH: {
    LOGIN: authRoutes.routes.LOGIN.path,
    REGISTER: authRoutes.routes.REGISTER.path,
    REAL_AUTH: authRoutes.routes.REAL_AUTH.path
  },
  HOME: mainRoutes.routes.HOME.path,
  SERVICE: mainRoutes.routes.SERVICE.path,
  PUBLISH: mainRoutes.routes.PUBLISH.path,
  MESSAGE: mainRoutes.routes.MESSAGE.path,
  USER: {
    MINE: userRoutes.routes.MINE.path,
    PROFILE: userRoutes.routes.MINE.path, // 兼容旧版本
    INFORMATION: userRoutes.routes.INFORMATION.path,
    EXTEND_INFO: userRoutes.routes.EXTEND_INFO.path,
    SETUP_PROFILE: userRoutes.routes.SETUP_PROFILE.path
  }
}

/**
 * 获取所有路由配置
 */
export function getAllRoutes(): RouteConfig[] {
  const allRoutes: RouteConfig[] = []
  
  routeModules.forEach(module => {
    Object.values(module.routes).forEach(route => {
      allRoutes.push(route)
    })
  })
  
  return allRoutes
}

/**
 * 根据路径获取路由配置
 */
export function getRouteByPath(path: string): RouteConfig | undefined {
  const allRoutes = getAllRoutes()
  return allRoutes.find(route => route.path === path)
}

/**
 * 获取需要登录的页面列表
 */
export function getAuthRequiredPages(): string[] {
  const allRoutes = getAllRoutes()
  return allRoutes
    .filter(route => route.requiresAuth)
    .map(route => route.path)
}

/**
 * 获取只允许游客访问的页面列表
 */
export function getGuestOnlyPages(): string[] {
  const allRoutes = getAllRoutes()
  return allRoutes
    .filter(route => route.guestOnly)
    .map(route => route.path)
}

/**
 * 获取TabBar页面列表
 */
export function getTabBarPages(): RouteConfig[] {
  const allRoutes = getAllRoutes()
  return allRoutes
    .filter(route => route.meta?.isTabBar)
    .sort((a, b) => (a.meta?.tabBarIndex || 0) - (b.meta?.tabBarIndex || 0))
}