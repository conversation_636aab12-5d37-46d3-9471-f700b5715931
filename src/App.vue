<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";
import CustomTabBar from './custom-tab-bar/index.vue';
import { http } from '@/http/http';
import type { ApiResponse } from '@/api/types';
import { ROUTES, RouterService } from '@/config';
import { initChromeCompatibility, getBrowserInfo } from '@/utils/common/browserCompatibility';
import { StoreUtil } from '@/utils/storage/StoreUtil';
import { StorageModule } from '@/utils/storage';
import { initializeAuth } from '@/router/guard/AuthInitializer';
import { tokenUtil } from '@/utils/token/TokenUtil';
import { storageManager } from '@/utils/storage';

onLaunch(async () => {
  // console.log("App Launch");

  try {
    // 初始化Chrome兼容性设置
    initChromeCompatibility();

    // 显示浏览器信息
    const browserInfo = getBrowserInfo();
    // console.log('[App] 浏览器信息:', browserInfo);

    // 初始化存储系统
    await storageManager.initialize();
    // console.log('[App] ✅ 存储系统初始化完成');

    // 初始化认证系统和增强路由守卫
    await initializeAuth();

    // 检查Token状态并启动自动刷新机制
    const tokenStatus = await tokenUtil.getTokenStatus();
    // console.log('[App] Token状态:', tokenStatus);
    
    // TokenUtil会自动处理定时刷新，无需手动启动
    // console.log('[App] ✅ 应用启动完成，Token管理器已激活');
    
  } catch (error) {
    // console.error('[App] ❌ 应用启动失败:', error);
  }
});

// 检查登录状态
let lastCheckTime = 0;
const CHECK_INTERVAL = 5 * 60 * 1000; // 5分钟检查一次，避免过于频繁

// 获取当前页面路径
const getCurrentPageRoute = (): string => {
  const pages = getCurrentPages();
  if (pages.length > 0) {
    const currentPage = pages[pages.length - 1] as any;
    return currentPage.route || '';
  }
  return '';
};

// 检查是否为需要认证的页面
const isAuthRequiredPage = (route: string) => {
  const authRequiredPages = [
    'pages/modules/user/setupProfile/setupProfile',
    'pages/modules/user/infomation/information',
    'pages/modules/user/mine/mine',
    'pages/modules/home/<USER>/home',
    'pages/modules/service/index/service',
    'pages/modules/match/index/match',
    'pages/modules/message/index/message'
  ];
  return authRequiredPages.some(page => route.includes(page));
};

const checkLoginStatus = async () => {
  const currentRoute = getCurrentPageRoute();
  // console.log('[App] 当前页面:', currentRoute);

  // 如果当前在登录或注册页面，不进行检查
  if (currentRoute.includes('login') || currentRoute.includes('register')) {
    // console.log('[App] 当前在认证页面，跳过登录检查');
    return;
  }

  const token = await StoreUtil.get('token', StorageModule.AUTH);
  if (!token) {
    // console.log('[App] 无token，当前页面:', currentRoute);
    // 只有在需要认证的页面才跳转到登录页
    if (isAuthRequiredPage(currentRoute)) {
      // console.log('[App] 需要认证的页面，跳转到登录页');
      RouterService.reLaunch({ url: ROUTES.AUTH.LOGIN });
    }
    return;
  }

  // 添加时间检查，避免频繁调用
  const now = Date.now();
  if (now - lastCheckTime < CHECK_INTERVAL) {
    return;
  }
  lastCheckTime = now;

  // console.log('[App] Token验证功能已临时禁用');

  // ======== Token验证 (临时禁用) ========
  /*
  // console.log('[App] 开始验证token有效性')
  try {
    const result = await request.get<null>('/member/auth/verifyAccessToken')
    // console.log('[App] Token验证成功')
  } catch (error) {
    // console.error('[App] Token验证失败:', error)
  }
  */
};

onShow(async () => {
  // console.log("App Show");
  // 触发tabbar页面变化事件
  uni.$emit('tabBarPageChange');

  // 应用显示时，TokenUtil会自动检查并恢复定时器
  try {
    const tokenStatus = await tokenUtil.getTokenStatus();
    if (tokenStatus.isValid) {
      // console.log('[App] 应用显示，Token管理器已激活，剩余时间:', Math.round(tokenStatus.remainingTime / 1000 / 60), '分钟');
    }
  } catch (error) {
    // console.error('[App] Token状态检查失败:', error);
  }

  // 增强路由守卫会自动处理认证检查，这里保留原有逻辑作为备用
  await checkLoginStatus();
});

onHide(() => {
  // console.log("App Hide");
  
  // 应用隐藏时，TokenUtil会自动暂停定时器（节省资源）
  // console.log('[App] 应用隐藏，Token管理器已暂停');
});
</script>

<template>
  <view>
    <view class="content-container">
      <slot></slot>
    </view>
    <CustomTabBar />
  </view>
</template>

<style>
/* 为底部tabbar留出空间 */
.content-container {
  padding-bottom: 150rpx; /* 增加底部间距，适应tabbar高度 */
  padding-bottom: calc(150rpx + env(safe-area-inset-bottom)); /* 适配iPhone X及以上机型 */
}

/* 手机模式下的特殊处理 */
/* #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-QQ || MP-TOUTIAO || APP-PLUS */
.content-container {
  padding-bottom: 180rpx; /* 更大的内边距以适应更高的Tabbar */
  padding-bottom: calc(180rpx + env(safe-area-inset-bottom)); /* 适配iPhone X及以上机型 */
}
/* #endif */
</style>
