/**
 * 自定义指令集合
 */
import { ErrorHandler } from '../utils/errorHandler'

/**
 * 防抖指令
 * 用法: v-debounce="{ handler: clickHandler, delay: 300 }"
 */
export const debounce = {
  mounted(el: any, binding: any) {
    let timer: NodeJS.Timeout | null = null
    const { handler, delay = 300 } = binding.value
    
    el.addEventListener('click', () => {
      if (timer) {
        clearTimeout(timer)
      }
      timer = setTimeout(() => {
        handler()
      }, delay)
    })
  }
}

/**
 * 节流指令
 * 用法: v-throttle="{ handler: clickHandler, delay: 1000 }"
 */
export const throttle = {
  mounted(el: any, binding: any) {
    let lastTime = 0
    const { handler, delay = 1000 } = binding.value
    
    el.addEventListener('click', () => {
      const now = Date.now()
      if (now - lastTime >= delay) {
        handler()
        lastTime = now
      }
    })
  }
}

/**
 * 长按指令
 * 用法: v-longpress="{ handler: longPressHandler, duration: 800 }"
 */
export const longpress = {
  mounted(el: any, binding: any) {
    let timer: NodeJS.Timeout | null = null
    const { handler, duration = 800 } = binding.value
    
    const start = () => {
      timer = setTimeout(() => {
        handler()
      }, duration)
    }
    
    const cancel = () => {
      if (timer) {
        clearTimeout(timer)
        timer = null
      }
    }
    
    el.addEventListener('touchstart', start)
    el.addEventListener('touchend', cancel)
    el.addEventListener('touchcancel', cancel)
    el.addEventListener('mousedown', start)
    el.addEventListener('mouseup', cancel)
    el.addEventListener('mouseleave', cancel)
  }
}

/**
 * 复制文本指令
 * 用法: v-copy="textToCopy"
 */
export const copy = {
  mounted(el: any, binding: any) {
    el.addEventListener('click', () => {
      const text = binding.value
      if (text) {
        // UniApp 复制到剪贴板
        uni.setClipboardData({
          data: text,
          success: () => {
            ErrorHandler.showSuccess('复制成功')
          },
          fail: () => {
            ErrorHandler.showError('复制失败')
          }
        })
      }
    })
  }
}

/**
 * 图片懒加载指令
 * 用法: v-lazy="imageUrl"
 */
export const lazy = {
  mounted(el: any, binding: any) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement
          img.src = binding.value
          observer.unobserve(img)
        }
      })
    })
    
    observer.observe(el)
  }
}

/**
 * 权限控制指令
 * 用法: v-permission="'admin'"
 */
export const permission = {
  mounted(el: any, binding: any) {
    const requiredPermission = binding.value
    // 这里应该从用户权限中检查
    // const userPermissions = getUserPermissions()
    // const hasPermission = userPermissions.includes(requiredPermission)
    
    // 临时实现，实际应该根据用户权限判断
    const hasPermission = true
    
    if (!hasPermission) {
      el.style.display = 'none'
    }
  }
}

/**
 * 导出所有指令
 */
export default {
  debounce,
  throttle,
  longpress,
  copy,
  lazy,
  permission
}