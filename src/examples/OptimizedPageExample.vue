<!--
  优化后的页面示例
  展示如何使用统一的 loading 管理和错误处理
  替代项目中重复的 loading 和错误处理代码
-->
<template>
  <view class="optimized-page">
    <view class="header">
      <text class="title">优化后的页面示例</text>
      <text class="subtitle">使用统一的 loading 和错误处理</text>
    </view>

    <view class="content">
      <!-- 加载状态展示 -->
      <view class="section">
        <text class="section-title">加载状态管理</text>
        <view class="button-group">
          <button @click="testGlobalLoading" class="btn">全局 Loading</button>
          <button @click="testLocalLoading" class="btn">局部 Loading</button>
          <button @click="testAsyncOperation" class="btn">异步操作</button>
        </view>
        <view class="status">
          <text>全局加载状态: {{ globalLoading.isLoading ? '加载中' : '空闲' }}</text>
          <text>局部加载状态: {{ localLoading.loading ? '加载中' : '空闲' }}</text>
        </view>
      </view>

      <!-- 错误处理展示 -->
      <view class="section">
        <text class="section-title">错误处理</text>
        <view class="button-group">
          <button @click="testApiError" class="btn">API 错误</button>
          <button @click="testNetworkError" class="btn">网络错误</button>
          <button @click="testCustomError" class="btn">自定义错误</button>
        </view>
        <view class="error-status" v-if="errorHandler.hasError">
          <text class="error-text">错误: {{ errorHandler.error }}</text>
          <button @click="errorHandler.clearError" class="btn-clear">清除错误</button>
        </view>
      </view>

      <!-- 设备信息展示 -->
      <view class="section">
        <text class="section-title">设备信息</text>
        <view class="device-info">
          <text>平台: {{ deviceInfo.platform }}</text>
          <text>设备类型: {{ deviceInfo.deviceType }}</text>
          <text>屏幕尺寸: {{ deviceInfo.screenWidth }}x{{ deviceInfo.screenHeight }}</text>
          <text>是否移动设备: {{ deviceInfo.isMobile ? '是' : '否' }}</text>
        </view>
      </view>

      <!-- 统一API调用示例 -->
      <view class="section">
        <text class="section-title">API 调用示例</text>
        <view class="button-group">
          <button @click="fetchUserInfo" class="btn">获取用户信息</button>
          <button @click="updateProfile" class="btn">更新资料</button>
        </view>
        <view class="api-result" v-if="apiResult">
          <text>API 结果: {{ JSON.stringify(apiResult, null, 2) }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useGlobalLoading, useLocalLoading } from '@/hooks/useLoading'
import { useErrorHandler, useApiErrorHandler } from '@/hooks/useErrorHandler'
import { getSystemInfo, getPlatform, getDeviceType, getScreenInfo, isMobile } from '@/utils/common/deviceInfo'
import { http } from '@/http/http'

// 加载状态管理
const globalLoading = useGlobalLoading()
const localLoading = useLocalLoading('示例页面')

// 错误处理
const errorHandler = useErrorHandler({ context: 'OptimizedPageExample' })
const apiErrorHandler = useApiErrorHandler('API调用')

// 设备信息
const deviceInfo = reactive({
  platform: '',
  deviceType: '',
  screenWidth: 0,
  screenHeight: 0,
  isMobile: false
})

// API 结果
const apiResult = ref<any>(null)

// 初始化设备信息
onMounted(() => {
  const systemInfo = getSystemInfo()
  const screenInfo = getScreenInfo()
  
  deviceInfo.platform = getPlatform()
  deviceInfo.deviceType = getDeviceType()
  deviceInfo.screenWidth = screenInfo.screenWidth
  deviceInfo.screenHeight = screenInfo.screenHeight
  deviceInfo.isMobile = isMobile()
})

// 测试全局加载
const testGlobalLoading = () => {
  globalLoading.show('全局加载中...')
  setTimeout(() => {
    globalLoading.hide()
  }, 2000)
}

// 测试局部加载
const testLocalLoading = () => {
  localLoading.show('局部加载中...')
  setTimeout(() => {
    localLoading.hide()
  }, 2000)
}

// 测试异步操作
const testAsyncOperation = async () => {
  await localLoading.withLoading(async () => {
    // 模拟异步操作
    await new Promise(resolve => setTimeout(resolve, 1500))
    console.log('异步操作完成')
  }, '处理中...')
}

// 测试API错误
const testApiError = () => {
  const mockApiError = {
    code: 400,
    message: '请求参数错误',
    details: { field: 'username', reason: '用户名不能为空' }
  }
  errorHandler.handleApiError(mockApiError)
}

// 测试网络错误
const testNetworkError = () => {
  const mockNetworkError = new Error('网络连接超时')
  errorHandler.handleError(mockNetworkError, '网络请求失败')
}

// 测试自定义错误
const testCustomError = () => {
  errorHandler.handleError('这是一个自定义错误消息')
}

// 获取用户信息（模拟API调用）
const fetchUserInfo = async () => {
  const result = await apiErrorHandler.withErrorHandling(async () => {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    return {
      id: 1,
      name: '张三',
      email: '<EMAIL>',
      avatar: 'https://example.com/avatar.jpg'
    }
  }, '获取用户信息失败')
  
  if (result) {
    apiResult.value = result
    errorHandler.showSuccess('用户信息获取成功')
  }
}

// 更新资料（模拟API调用）
const updateProfile = async () => {
  await globalLoading.withLoading(async () => {
    try {
      // 模拟API调用
      await new Promise((resolve, reject) => {
        setTimeout(() => {
          // 模拟50%的失败率
          if (Math.random() > 0.5) {
            resolve('success')
          } else {
            reject(new Error('服务器内部错误'))
          }
        }, 1500)
      })
      
      errorHandler.showSuccess('资料更新成功')
      apiResult.value = { message: '资料更新成功', timestamp: Date.now() }
    } catch (error) {
      apiErrorHandler.handleError(error, '资料更新失败')
    }
  }, '更新资料中...')
}
</script>

<style scoped>
.optimized-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.subtitle {
  font-size: 14px;
  color: #666;
  display: block;
}

.content {
  max-width: 600px;
  margin: 0 auto;
}

.section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15px;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.btn {
  background: #007aff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 14px;
  cursor: pointer;
}

.btn:hover {
  background: #0056cc;
}

.btn-clear {
  background: #ff3b30;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  margin-left: 10px;
}

.status text,
.device-info text {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-size: 14px;
}

.error-status {
  background: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 4px;
  padding: 10px;
  margin-top: 10px;
}

.error-text {
  color: #d32f2f;
  font-size: 14px;
}

.api-result {
  background: #f0f0f0;
  border-radius: 4px;
  padding: 10px;
  margin-top: 10px;
}

.api-result text {
  font-family: monospace;
  font-size: 12px;
  color: #333;
  white-space: pre-wrap;
}
</style>
