<template>
  <view class="smart-loading-example">
    <view class="title">智能Loading示例</view>
    
    <view class="button-group">
      <!-- 默认loading（200ms延迟） -->
      <button @click="normalRequest" class="btn">
        普通请求（默认延迟200ms）
      </button>
      
      <!-- 立即显示loading -->
      <button @click="immediateLoadingRequest" class="btn">
        立即显示Loading
      </button>
      
      <!-- 延迟500ms显示loading -->
      <button @click="delayedLoadingRequest" class="btn">
        延迟500ms显示Loading
      </button>
      
      <!-- 禁用loading -->
      <button @click="noLoadingRequest" class="btn">
        禁用Loading
      </button>
      
      <!-- 自定义loading文本 -->
      <button @click="customTextRequest" class="btn">
        自定义Loading文本
      </button>
      
      <!-- 无遮罩loading -->
      <button @click="noMaskRequest" class="btn">
        无遮罩Loading
      </button>
    </view>
    
    <view class="result" v-if="result">
      <text>{{ result }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { http } from '@/http/http'

const result = ref('')

// 模拟API请求
const mockApiRequest = (delay: number = 1000) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        message: '请求成功',
        data: { timestamp: new Date().toLocaleTimeString() }
      })
    }, delay)
  })
}

// 普通请求（默认200ms延迟显示loading）
const normalRequest = async () => {
  try {
    const response = await http.post('/api/test', {}, {
      // 使用默认配置，200ms后显示loading
    } as any)
    result.value = `普通请求成功: ${JSON.stringify(response)}`
  } catch (error) {
    result.value = `请求失败: ${error}`
  }
}

// 立即显示loading
const immediateLoadingRequest = async () => {
  try {
    const response = await http.post('/api/test', {}, {
      loadingDelay: 0, // 立即显示
      loadingText: '处理中...'
    } as any)
    result.value = `立即Loading请求成功: ${JSON.stringify(response)}`
  } catch (error) {
    result.value = `请求失败: ${error}`
  }
}

// 延迟500ms显示loading
const delayedLoadingRequest = async () => {
  try {
    const response = await http.post('/api/test', {}, {
      loadingDelay: 500, // 500ms后显示
      loadingText: '正在加载...'
    } as any)
    result.value = `延迟Loading请求成功: ${JSON.stringify(response)}`
  } catch (error) {
    result.value = `请求失败: ${error}`
  }
}

// 禁用loading
const noLoadingRequest = async () => {
  try {
    const response = await http.post('/api/test', {}, {
      showLoading: false // 禁用loading
    } as any)
    result.value = `无Loading请求成功: ${JSON.stringify(response)}`
  } catch (error) {
    result.value = `请求失败: ${error}`
  }
}

// 自定义loading文本
const customTextRequest = async () => {
  try {
    const response = await http.post('/api/test', {}, {
      loadingText: '正在提交数据，请稍候...',
      loadingDelay: 100
    } as any)
    result.value = `自定义文本请求成功: ${JSON.stringify(response)}`
  } catch (error) {
    result.value = `请求失败: ${error}`
  }
}

// 无遮罩loading
const noMaskRequest = async () => {
  try {
    const response = await http.post('/api/test', {}, {
      loadingMask: false, // 不显示遮罩
      loadingText: '后台处理中...',
      loadingDelay: 0
    } as any)
    result.value = `无遮罩请求成功: ${JSON.stringify(response)}`
  } catch (error) {
    result.value = `请求失败: ${error}`
  }
}
</script>

<style scoped>
.smart-loading-example {
  padding: 20px;
}

.title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  text-align: center;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.btn {
  padding: 12px 20px;
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
}

.btn:active {
  background-color: #0056cc;
}

.result {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 6px;
  font-size: 12px;
  word-break: break-all;
}
</style>