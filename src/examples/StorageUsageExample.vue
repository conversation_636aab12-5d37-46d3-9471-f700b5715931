<template>
  <view class="storage-example">
    <view class="header">
      <text class="title">统一存储系统示例</text>
      <view class="status" :class="{ healthy: isHealthy, loading: !isReady }">
        {{ isReady ? (isHealthy ? '系统正常' : '系统异常') : '初始化中...' }}
      </view>
    </view>

    <!-- 核心存储示例 -->
    <view class="section">
      <text class="section-title">核心存储示例</text>
      <view class="form-group">
        <input 
          v-model="coreData.key" 
          placeholder="存储键名"
          class="input"
        />
        <input 
          v-model="coreData.value" 
          placeholder="存储值"
          class="input"
        />
        <view class="button-group">
          <button @click="saveCoreData" class="btn btn-primary">保存</button>
          <button @click="loadCoreData" class="btn btn-secondary">读取</button>
          <button @click="removeCoreData" class="btn btn-danger">删除</button>
        </view>
      </view>
      <view v-if="coreResult" class="result">
        <text>结果: {{ coreResult }}</text>
      </view>
    </view>

    <!-- 表单存储示例 -->
    <view class="section">
      <text class="section-title">表单存储示例</text>
      <view class="form-group">
        <input 
          v-model="formData.username" 
          placeholder="用户名"
          class="input"
        />
        <input 
          v-model="formData.email" 
          placeholder="邮箱"
          class="input"
        />
        <view class="button-group">
          <button @click="saveFormData" class="btn btn-primary">保存表单</button>
          <button @click="restoreFormData" class="btn btn-secondary">恢复表单</button>
          <button @click="clearFormData" class="btn btn-danger">清除表单</button>
        </view>
      </view>
      <view class="auto-save-status">
        <text>自动保存: {{ autoSaveEnabled ? '已启用' : '已禁用' }}</text>
        <button @click="toggleAutoSave" class="btn btn-small">
          {{ autoSaveEnabled ? '停止' : '启动' }}自动保存
        </button>
      </view>
    </view>

    <!-- 认证存储示例 -->
    <view class="section">
      <text class="section-title">认证存储示例</text>
      <view class="form-group">
        <input 
          v-model="authData.token" 
          placeholder="访问令牌"
          class="input"
        />
        <input 
          v-model="authData.refreshToken" 
          placeholder="刷新令牌"
          class="input"
        />
        <view class="button-group">
          <button @click="saveAuthData" class="btn btn-primary">保存认证</button>
          <button @click="loadAuthData" class="btn btn-secondary">读取认证</button>
          <button @click="clearAuthData" class="btn btn-danger">清除认证</button>
        </view>
      </view>
      <view v-if="authResult" class="result">
        <text>认证状态: {{ authResult }}</text>
      </view>
    </view>

    <!-- 系统操作 -->
    <view class="section">
      <text class="section-title">系统操作</text>
      <view class="stats">
        <view class="stat-item">
          <text class="stat-label">存储状态:</text>
          <text class="stat-value">{{ systemStatus }}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">操作计数:</text>
          <text class="stat-value">{{ operationCount }}</text>
        </view>
      </view>
      <view class="button-group">
        <button @click="runHealthCheck" class="btn btn-primary">健康检查</button>
        <button @click="clearAllData" class="btn btn-danger">清除所有数据</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { StoreUtil, StorageModule, StorageType } from '@/utils/storage/StoreUtil'
import { tokenUtil } from '@/utils/token'
import { ErrorHandler } from '../utils/errorHandler'

// 响应式状态
const isReady = ref(true)
const isHealthy = ref(true)
const autoSaveEnabled = ref(false)
const autoSaveCleanup = ref<(() => void) | null>(null)
const systemStatus = ref('正常')
const operationCount = ref(0)

// 响应式数据
const coreData = reactive({
  key: 'example_key',
  value: 'example_value'
})

const formData = reactive({
  username: '',
  email: ''
})

const authData = reactive({
  token: '',
  refreshToken: ''
})

const coreResult = ref('')
const authResult = ref('')

// 核心存储操作
const saveCoreData = async () => {
  try {
    const success = await StoreUtil.set(
      coreData.key, 
      coreData.value,
      StorageModule.CONFIG
    )
    coreResult.value = success ? '保存成功' : '保存失败'
    operationCount.value++
  } catch (error: any) {
    coreResult.value = `保存失败: ${error?.message || '未知错误'}`
  }
}

const loadCoreData = async () => {
  try {
    const result = await StoreUtil.get(coreData.key, StorageModule.CONFIG)
    coreResult.value = result ? `读取成功: ${result}` : '数据不存在'
    operationCount.value++
  } catch (error: any) {
    coreResult.value = `读取失败: ${error?.message || '未知错误'}`
  }
}

const removeCoreData = async () => {
  try {
    const success = await StoreUtil.remove(coreData.key, StorageModule.CONFIG)
    coreResult.value = success ? '删除成功' : '删除失败'
    operationCount.value++
  } catch (error: any) {
    coreResult.value = `删除失败: ${error?.message || '未知错误'}`
  }
}

// 表单存储操作
const saveFormData = async () => {
  try {
    await StoreUtil.Form.save('example_form', formData)
    ErrorHandler.showSuccess('表单保存成功')
    operationCount.value++
  } catch (error) {
    ErrorHandler.showError('表单保存失败')
  }
}

const restoreFormData = async () => {
  try {
    const saved = await StoreUtil.Form.restore('example_form')
    if (saved) {
      Object.assign(formData, saved)
      ErrorHandler.showSuccess('表单恢复成功')
    } else {
      ErrorHandler.showError('没有保存的表单数据')
    }
    operationCount.value++
  } catch (error) {
    ErrorHandler.showError('表单恢复失败')
  }
}

const clearFormData = async () => {
  try {
    await StoreUtil.Form.clear('example_form')
    Object.assign(formData, { username: '', email: '' })
    ErrorHandler.showSuccess('表单清除成功')
    operationCount.value++
  } catch (error) {
    ErrorHandler.showError('表单清除失败')
  }
}

const toggleAutoSave = () => {
  if (autoSaveEnabled.value) {
    if (autoSaveCleanup.value) {
      autoSaveCleanup.value()
      autoSaveCleanup.value = null
    }
    autoSaveEnabled.value = false
    ErrorHandler.showError('自动保存已停止')
  } else {
    // 简化的自动保存实现，定期保存表单数据
    const interval = setInterval(async () => {
      if (formData.username || formData.email) {
        await StoreUtil.Form.save('example_form', formData)
      }
    }, 5000) // 每5秒保存一次
    
    autoSaveCleanup.value = () => clearInterval(interval)
    autoSaveEnabled.value = true
    ErrorHandler.showSuccess('自动保存已启动')
  }
}

// 认证存储操作
const saveAuthData = async () => {
  try {
    const success = await tokenUtil.saveToken({
      accessToken: authData.token,
      refreshToken: authData.refreshToken,
      expiresAt: Date.now() + 3600 * 1000 // 1小时
    })
    
    authResult.value = success ? '认证数据保存成功' : '认证数据保存失败'
    success ? ErrorHandler.showSuccess('认证保存成功') : ErrorHandler.showError('认证保存失败')
    operationCount.value++
  } catch (error: any) {
    authResult.value = `认证保存失败: ${error?.message || '未知错误'}`
    ErrorHandler.showError('认证保存失败')
  }
}

const loadAuthData = async () => {
  try {
    const token = await tokenUtil.getAccessToken()
    const refreshToken = await tokenUtil.getRefreshToken()
    
    if (token || refreshToken) {
      authResult.value = `Token: ${token || '无'}, RefreshToken: ${refreshToken || '无'}`
    } else {
      authResult.value = '没有认证数据'
    }
    operationCount.value++
  } catch (error: any) {
    authResult.value = `认证读取失败: ${error?.message || '未知错误'}`
  }
}

const clearAuthData = async () => {
  try {
    const success = await tokenUtil.clearToken()
    authData.token = ''
    authData.refreshToken = ''
    authResult.value = success ? '认证数据已清除' : '认证清除失败'
    success ? ErrorHandler.showSuccess('认证清除成功') : ErrorHandler.showError('认证清除失败')
    operationCount.value++
  } catch (error: any) {
    authResult.value = `认证清除失败: ${error?.message || '未知错误'}`
    ErrorHandler.showError('认证清除失败')
  }
}

// 系统操作
const runHealthCheck = async () => {
  try {
    const healthResult = await StoreUtil.System.healthCheck()
    isHealthy.value = healthResult
    systemStatus.value = healthResult ? 'healthy' : 'error'
    
    const healthStatus = `状态: ${systemStatus.value}`
    
    uni.showModal({
      title: '健康检查结果',
      content: healthStatus,
      showCancel: false
    })
    operationCount.value++
  } catch (error) {
    isHealthy.value = false
    systemStatus.value = '错误'
    ErrorHandler.showError('健康检查失败')
  }
}

const clearAllData = async () => {
  try {
    uni.showModal({
      title: '确认清除',
      content: '确定要清除所有存储数据吗？此操作不可恢复。',
      success: async (res) => {
        if (res.confirm) {
          try {
            await StoreUtil.System.clearAll()
            // 重置所有表单数据
            Object.assign(coreData, { key: 'example_key', value: 'example_value' })
            Object.assign(formData, { username: '', email: '' })
            Object.assign(authData, { token: '', refreshToken: '' })
            coreResult.value = ''
            authResult.value = ''
            operationCount.value = 0
            
            ErrorHandler.showSuccess('所有数据已清除')
          } catch (error) {
            ErrorHandler.showError('清除失败')
          }
        }
      }
    })
  } catch (error) {
    ErrorHandler.showError('清除操作失败')
  }
}

// 初始化
onMounted(async () => {
  try {
    // 尝试恢复表单数据
    await restoreFormData()
    
    // 运行健康检查
    await runHealthCheck()
  } catch (error) {
    console.error('初始化失败:', error)
  }
})
</script>

<style scoped>
.storage-example {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: white;
  border-radius: 10rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.status {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
  background-color: #999;
}

.status.healthy {
  background-color: #52c41a;
}

.status.loading {
  background-color: #1890ff;
}

.section {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: white;
  border-radius: 10rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.form-group {
  margin-bottom: 20rpx;
}

.input {
  width: 100%;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.button-group {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
}

.btn {
  padding: 20rpx 30rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  cursor: pointer;
  flex: 1;
  min-width: 120rpx;
}

.btn-primary {
  background-color: #1890ff;
  color: white;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #333;
}

.btn-danger {
  background-color: #ff4d4f;
  color: white;
}

.btn-small {
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  min-width: auto;
  flex: none;
}

.result {
  padding: 20rpx;
  background-color: #f6ffed;
  border: 2rpx solid #b7eb8f;
  border-radius: 8rpx;
  margin-top: 20rpx;
}

.auto-save-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  margin-top: 20rpx;
}

.stats {
  margin-bottom: 20rpx;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 15rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  color: #666;
  font-size: 28rpx;
}

.stat-value {
  color: #333;
  font-size: 28rpx;
  font-weight: bold;
}
</style>