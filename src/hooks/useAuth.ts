/**
 * 认证相关 Composable
 */
import { computed } from 'vue'
import { passwordLogin, smsLogin } from '@/api/auth/auth'
import { tokenUtil } from '@/utils/token/TokenUtil'
import { StoreUtil } from '@/utils/storage/StoreUtil'
import { useAuthStore } from '@/store/modules/auth'
import { createContextLogger } from '@/utils/common/logger'
import type { LoginParams } from '@/api/auth/auth'

const logger = createContextLogger('useAuth')

export const useAuth = () => {
  const authStore = useAuthStore()
  const isLoggedIn = computed(() => !!authStore.userInfo)

  /**
   * 直接解析JWT token
   */
  const parseTokenDirectly = (token: string) => {
    try {
      const parts = token.split('.')
      if (parts.length !== 3) return null
      
      // 解码payload部分
      const payload = parts[1]
      // 将Base64URL转换为标准Base64
      const base64 = payload.replace(/-/g, '+').replace(/_/g, '/')
      // 处理填充
      const pad = base64.length % 4
      const paddedBase64 = pad ? base64 + '='.repeat(4 - pad) : base64
      
      // 解码
      const decodedPayload = decodeURIComponent(
        atob(paddedBase64)
          .split('')
          .map(c => `%${(`00${  c.charCodeAt(0).toString(16)}`).slice(-2)}`)
          .join('')
      )
      
      return JSON.parse(decodedPayload)
    } catch (error) {
      logger.error('直接解析JWT失败:', error)
      return null
    }
  }

  /**
   * 构造用户信息对象
   */
  const buildUserInfo = (params: LoginParams, accessToken: string) => {
    // 直接解析传入的token
    const payload = parseTokenDirectly(accessToken)
    
    if (!payload) {
      throw new Error('无法解析JWT token')
    }
    
    // 处理嵌套的claimsJson结构
    let claims = payload
    if (payload.claimsJson) {
      if (typeof payload.claimsJson === 'string') {
        try {
          claims = JSON.parse(payload.claimsJson)
        } catch (e) {
          logger.error('解析claimsJson字符串失败:', e)
        }
      } else {
        claims = payload.claimsJson
      }
    }
    
    const userId = claims.userId || payload.userId
    const userName = claims.userName || payload.userName
    
    // 验证关键字段
    if (!userId) {
      logger.error('无法从JWT中获取用户ID', { payload, claims })
      throw new Error('JWT中缺少用户ID信息')
    }
    
    // 构造用户信息对象（从JWT中获取真实数据）
    const userInfo = {
      id: String(userId),  // 使用JWT中的真实用户ID
      username: userName || 'user',
      phonenumber: params.phonenumber || '',
      avatar: '',
      nickname: userName || 'user',
      email: '',
      gender: 0,
      birthdate: '',
      createTime: new Date().toISOString()
    }
    
    return userInfo
  }

  /**
   * 密码登录
   */
  const loginWithPassword = async (params: LoginParams) => {
    const response = await passwordLogin(params)
    
    if (response.code === 200 && response.data) {
      // 验证access_token
      if (!response.data.access_token) {
        throw new Error('API响应缺少access_token')
      }
      
      // 构造用户信息（在保存token之前解析）
      const userInfo = buildUserInfo(params, response.data.access_token)
      
      // 保存token到存储中
      await tokenUtil.saveToken({
        accessToken: response.data.access_token,
        refreshToken: response.data.refresh_token || '',
        expiresAt: Date.now() + (response.data.expire_in || 7200) * 1000
      })
      
      // 保存用户信息
      await StoreUtil.set('USER_INFO', userInfo)
      
      // 更新AuthStore状态
      await authStore.setAuth({
        token: response.data.access_token,
        refreshToken: response.data.refresh_token || undefined,
        userInfo
      })
      
      return { success: true, data: { ...response.data, userInfo } }
    } else {
      throw new Error(response.message || '登录失败')
    }
  }

  /**
   * 短信登录
   */
  const loginWithSms = async (params: LoginParams) => {
    const response = await smsLogin(params)
    
    if (response.code === 200 && response.data) {
      // 验证access_token
      if (!response.data.access_token) {
        throw new Error('API响应缺少access_token')
      }
      
      // 构造用户信息（在保存token之前解析）
      const userInfo = buildUserInfo(params, response.data.access_token)
      
      // 保存token到存储中
      await tokenUtil.saveToken({
        accessToken: response.data.access_token,
        refreshToken: response.data.refresh_token || '',
        expiresAt: Date.now() + (response.data.expire_in || 7200) * 1000
      })
      
      // 保存用户信息
      await StoreUtil.set('USER_INFO', userInfo)
      
      // 更新AuthStore状态
      await authStore.setAuth({
        token: response.data.access_token,
        refreshToken: response.data.refresh_token || undefined,
        userInfo
      })
      
      return { success: true, data: { ...response.data, userInfo } }
    } else {
      throw new Error(response.message || '登录失败')
    }
  }

  return {
    isLoggedIn,
    loginWithPassword,
    loginWithSms
  }
}