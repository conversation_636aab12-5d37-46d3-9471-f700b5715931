/**
 * 统一表单验证 Hook
 * 提供常用的表单验证规则和方法
 * <AUTHOR>
 * @since 2024-01-01
 */

import { ref, computed, reactive } from 'vue'
import { useErrorHandler } from './useErrorHandler'
import { createContextLogger } from '@/utils/common/logger'

const logger = createContextLogger('FormValidation')

// 验证规则类型
export interface ValidationRule {
  required?: boolean
  pattern?: RegExp
  min?: number
  max?: number
  validator?: (value: any) => boolean | string
  message?: string
}

// 字段验证配置
export interface FieldConfig {
  label: string
  rules: ValidationRule[]
}

// 表单验证状态
export interface FormValidationState {
  isValid: boolean
  errors: Record<string, string>
  touched: Record<string, boolean>
}

// 常用验证规则
export const VALIDATION_PATTERNS = {
  phone: /^1[3-9]\d{9}$/,
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  idCard: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
  password: /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,20}$/,
  chinese: /^[\u4e00-\u9fa5]+$/,
  number: /^\d+$/,
  decimal: /^\d+(\.\d+)?$/
}

// 常用字段名称
export const FIELD_LABELS = {
  phonenumber: '手机号',
  phone: '手机号',
  password: '密码',
  confirmPassword: '确认密码',
  smsCode: '验证码',
  realName: '真实姓名',
  idCard: '身份证号',
  nickname: '昵称',
  email: '邮箱',
  birthday: '生日',
  height: '身高',
  weight: '体重',
  education: '学历',
  career: '职业',
  marriage: '婚姻状况',
  income: '收入',
  nativePlace: '籍贯',
  currentResidence: '现居地',
  hobby: '兴趣爱好'
}

// 预定义验证规则
export const COMMON_RULES = {
  required: (message?: string): ValidationRule => ({
    required: true,
    message: message || '此字段为必填项'
  }),
  
  phone: (message?: string): ValidationRule => ({
    pattern: VALIDATION_PATTERNS.phone,
    message: message || '请输入有效的手机号'
  }),
  
  email: (message?: string): ValidationRule => ({
    pattern: VALIDATION_PATTERNS.email,
    message: message || '请输入有效的邮箱地址'
  }),
  
  idCard: (message?: string): ValidationRule => ({
    pattern: VALIDATION_PATTERNS.idCard,
    message: message || '请输入有效的身份证号'
  }),
  
  password: (message?: string): ValidationRule => ({
    pattern: VALIDATION_PATTERNS.password,
    message: message || '密码需包含字母和数字，长度6-20位'
  }),
  
  chinese: (message?: string): ValidationRule => ({
    pattern: VALIDATION_PATTERNS.chinese,
    message: message || '请输入中文字符'
  }),
  
  minLength: (min: number, message?: string): ValidationRule => ({
    min,
    message: message || `最少输入${min}个字符`
  }),
  
  maxLength: (max: number, message?: string): ValidationRule => ({
    max,
    message: message || `最多输入${max}个字符`
  }),
  
  range: (min: number, max: number, message?: string): ValidationRule => ({
    min,
    max,
    message: message || `长度应在${min}-${max}个字符之间`
  }),
  
  custom: (validator: (value: any) => boolean | string, message?: string): ValidationRule => ({
    validator,
    message: message || '验证失败'
  })
}

/**
 * 表单验证 Hook
 */
export function useFormValidation() {
  const { handleError } = useErrorHandler({ context: 'FormValidation' })
  
  // 表单字段配置
  const fields = ref<Record<string, FieldConfig>>({})
  
  // 验证状态
  const validationState = reactive<FormValidationState>({
    isValid: true,
    errors: {},
    touched: {}
  })
  
  // 添加字段配置
  const addField = (name: string, config: FieldConfig) => {
    fields.value[name] = config
  }
  
  // 批量添加字段配置
  const addFields = (fieldsConfig: Record<string, FieldConfig>) => {
    Object.assign(fields.value, fieldsConfig)
  }
  
  // 验证单个字段
  const validateField = (name: string, fieldValue: any): string | null => {
    const fieldConfig = fields.value[name]
    if (!fieldConfig) {
      logger.warn(`字段 ${name} 没有配置`)
      return null
    }
    
    logger.debug(`开始验证字段 [${name}]`, {
      字段值: fieldValue,
      字段值类型: typeof fieldValue,
      字段值长度: typeof fieldValue === 'string' ? fieldValue.length : 'N/A',
      规则数量: fieldConfig.rules?.length || 0
    })
    
    const { rules } = fieldConfig
    
    for (let i = 0; i < rules.length; i++) {
      const rule = rules[i]
      logger.debug(`执行规则 ${i + 1}/${rules.length}`, {
        规则类型: {
          必填: !!rule.required,
          正则: !!rule.pattern,
          最小长度: rule.min !== undefined,
          最大长度: rule.max !== undefined,
          自定义验证: !!rule.validator
        }
      })
      
      // 必填验证
      if (rule.required) {
        const isEmpty = !fieldValue || (typeof fieldValue === 'string' && !fieldValue.trim())
        logger.debug(`必填验证`, {
          字段值: fieldValue,
          是否为空: isEmpty,
          验证结果: !isEmpty ? '通过' : '失败'
        })
        
        if (isEmpty) {
          const errorMsg = rule.message || `${fieldConfig.label}为必填项`
          logger.debug(`必填验证失败，返回错误: ${errorMsg}`)
          return errorMsg
        }
      }
      
      // 如果值为空且不是必填，跳过其他验证
      if (!fieldValue || (typeof fieldValue === 'string' && !fieldValue.trim())) {
        logger.debug(`字段值为空且非必填，跳过后续验证`)
        continue
      }
      
      // 正则验证
      if (rule.pattern) {
        const patternTest = rule.pattern.test(String(fieldValue))
        logger.debug('正则验证', {
          field: name,
          pattern: rule.pattern.toString(),
          value: String(fieldValue),
          result: patternTest
        })
        
        if (!patternTest) {
          const errorMsg = rule.message || `${fieldConfig.label}格式不正确`
          logger.warn('正则验证失败', { field: name, error: errorMsg })
          return errorMsg
        }
      }
      
      // 最小长度验证
      if (rule.min !== undefined) {
        const valueLength = String(fieldValue).length
        const minValid = valueLength >= rule.min
        logger.debug('最小长度验证', {
          field: name,
          minRequired: rule.min,
          actualLength: valueLength,
          result: minValid
        })
        
        if (!minValid) {
          const errorMsg = rule.message || `${fieldConfig.label}最少${rule.min}个字符`
          logger.warn('最小长度验证失败', { field: name, error: errorMsg })
          return errorMsg
        }
      }
      
      // 最大长度验证
      if (rule.max !== undefined) {
        const valueLength = String(fieldValue).length
        const maxValid = valueLength <= rule.max
        logger.debug('最大长度验证', {
          field: name,
          maxAllowed: rule.max,
          actualLength: valueLength,
          result: maxValid
        })
        
        if (!maxValid) {
          const errorMsg = rule.message || `${fieldConfig.label}最多${rule.max}个字符`
          logger.warn('最大长度验证失败', { field: name, error: errorMsg })
          return errorMsg
        }
      }
      
      // 自定义验证
      if (rule.validator) {
        logger.debug('执行自定义验证器', { field: name })
        const result = rule.validator(fieldValue)
        const customValid = result === true
        logger.debug('自定义验证结果', {
          field: name,
          validatorResult: result,
          isValid: customValid
        })
        
        if (!customValid) {
          const errorMsg = typeof result === 'string' ? result : (rule.message || `${fieldConfig.label}验证失败`)
          logger.warn('自定义验证失败', { field: name, error: errorMsg })
          return errorMsg
        }
      }
    }
    
    logger.debug('字段验证通过', { field: name })
    return null
  }
  
  // 验证表单数据
  const validateForm = (formData: Record<string, any>): boolean => {
    const errors: Record<string, string> = {}
    let isValid = true
    
    // 验证所有配置的字段
    Object.keys(fields.value).forEach(fieldName => {
      const error = validateField(fieldName, formData[fieldName])
      if (error) {
        errors[fieldName] = error
        isValid = false
      }
    })
    
    // 更新验证状态
    validationState.errors = errors
    validationState.isValid = isValid
    
    return isValid
  }
  
  // 验证并显示错误
  const validateAndShowError = (formData: Record<string, any>): boolean => {
    logger.debug('开始表单验证', {
      formData: Object.keys(formData),
      fieldsCount: Object.keys(fields.value).length
    })
    
    // 检查字段数据
    Object.keys(formData).forEach(key => {
      const value = formData[key]
      logger.debug('字段数据分析', {
        field: key,
        type: typeof value,
        hasValue: !!value,
        hasConfig: !!fields.value[key]
      })
    })
    
    const isValid = validateForm(formData)
    
    logger.debug('表单验证结果', {
      isValid,
      errorCount: Object.keys(validationState.errors).length
    })
    
    if (!isValid) {
      const firstErrorField = Object.keys(validationState.errors)[0]
      const firstError = Object.values(validationState.errors)[0]
      logger.warn('表单验证失败', {
        firstErrorField,
        firstError,
        fieldValue: formData[firstErrorField]
      })
      
      if (firstError) {
        handleError(new Error(firstError))
      }
    }
    
    return isValid
  }
  
  // 标记字段为已触摸
  const touchField = (name: string) => {
    validationState.touched[name] = true
  }
  
  // 清除字段错误
  const clearFieldError = (name: string) => {
    delete validationState.errors[name]
    updateValidState()
  }
  
  // 清除所有错误
  const clearAllErrors = () => {
    validationState.errors = {}
    validationState.isValid = true
  }
  
  // 重置验证状态
  const resetValidation = () => {
    validationState.errors = {}
    validationState.touched = {}
    validationState.isValid = true
  }
  
  // 更新整体验证状态
  const updateValidState = () => {
    validationState.isValid = Object.keys(validationState.errors).length === 0
  }
  
  // 获取字段错误
  const getFieldError = (name: string): string => {
    return validationState.errors[name] || ''
  }
  
  // 检查字段是否有错误
  const hasFieldError = (name: string): boolean => {
    return !!validationState.errors[name]
  }
  
  // 检查字段是否已触摸
  const isFieldTouched = (name: string): boolean => {
    return !!validationState.touched[name]
  }
  
  // 计算属性：错误数量
  const errorCount = computed(() => Object.keys(validationState.errors).length)
  
  // 计算属性：是否有错误
  const hasErrors = computed(() => errorCount.value > 0)
  
  return {
    // 状态
    validationState,
    fields,
    
    // 计算属性
    errorCount,
    hasErrors,
    
    // 字段管理
    addField,
    addFields,
    
    // 验证方法
    validateField,
    validateForm,
    validateAndShowError,
    
    // 状态管理
    touchField,
    clearFieldError,
    clearAllErrors,
    resetValidation,
    
    // 查询方法
    getFieldError,
    hasFieldError,
    isFieldTouched
  }
}

/**
 * 快速验证工具函数
 */
export const quickValidate = {
  phone: (value: string): boolean => VALIDATION_PATTERNS.phone.test(value),
  email: (value: string): boolean => VALIDATION_PATTERNS.email.test(value),
  idCard: (value: string): boolean => VALIDATION_PATTERNS.idCard.test(value),
  password: (value: string): boolean => VALIDATION_PATTERNS.password.test(value),
  chinese: (value: string): boolean => VALIDATION_PATTERNS.chinese.test(value),
  required: (value: any): boolean => !!value && String(value).trim() !== ''
}

/**
 * 常用表单配置预设
 */
export const formPresets = {
  // 登录表单
  login: {
    phonenumber: {
      label: FIELD_LABELS.phonenumber,
      rules: [COMMON_RULES.required(), COMMON_RULES.phone()]
    },
    password: {
      label: FIELD_LABELS.password,
      rules: [COMMON_RULES.required()]
    },
    smsCode: {
      label: FIELD_LABELS.smsCode,
      rules: [COMMON_RULES.required(), COMMON_RULES.range(4, 6, '验证码长度为4-6位')]
    }
  },
  
  // 注册表单
  register: {
    phonenumber: {
      label: FIELD_LABELS.phonenumber,
      rules: [COMMON_RULES.required(), COMMON_RULES.phone()]
    },
    password: {
      label: FIELD_LABELS.password,
      rules: [COMMON_RULES.required(), COMMON_RULES.password()]
    },
    smsCode: {
      label: FIELD_LABELS.smsCode,
      rules: [COMMON_RULES.required(), COMMON_RULES.range(4, 6, '验证码长度为4-6位')]
    }
  },
  
  // 实名认证表单
  realAuth: {
    realName: {
      label: FIELD_LABELS.realName,
      rules: [COMMON_RULES.required(), COMMON_RULES.chinese(), COMMON_RULES.range(2, 10)]
    },
    idCard: {
      label: FIELD_LABELS.idCard,
      rules: [COMMON_RULES.required(), COMMON_RULES.idCard()]
    }
  },
  
  // 个人资料表单
  profile: {
    nickname: {
      label: FIELD_LABELS.nickname,
      rules: [COMMON_RULES.required(), COMMON_RULES.range(2, 20)]
    },
    birthday: {
      label: FIELD_LABELS.birthday,
      rules: [COMMON_RULES.required()]
    },
    height: {
      label: FIELD_LABELS.height,
      rules: [COMMON_RULES.custom((value) => {
        const num = Number(value)
        return num >= 100 && num <= 250
      }, '身高应在100-250cm之间')]
    },
    weight: {
      label: FIELD_LABELS.weight,
      rules: [COMMON_RULES.custom((value) => {
        const num = Number(value)
        return num >= 30 && num <= 200
      }, '体重应在30-200kg之间')]
    }
  }
}