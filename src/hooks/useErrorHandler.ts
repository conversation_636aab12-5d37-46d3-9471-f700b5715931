/**
 * 统一错误处理 Composable
 * 解决项目中 50+ 个文件的重复错误处理模式问题
 * 
 * <AUTHOR> Assistant
 * @date 2024-12-28
 */

import { ref, computed } from 'vue'
import { apiErrorHandler } from '@/api/errorHandler'

// 全局错误状态
const globalError = ref<string | null>(null)
const globalErrorCode = ref<string | number | null>(null)

/**
 * 错误处理配置接口
 */
export interface ErrorHandlerOptions {
  /** 是否显示错误提示 */
  showToast?: boolean
  /** 是否记录错误日志 */
  logError?: boolean
  /** 是否使用全局错误状态 */
  global?: boolean
  /** 自定义错误提示持续时间 */
  duration?: number
  /** 错误提示图标 */
  icon?: 'none' | 'success' | 'error' | 'loading'
  /** 上下文信息，用于日志记录 */
  context?: string
}

/**
 * 错误信息接口
 */
export interface ErrorInfo {
  message: string
  code?: string | number
  details?: any
  timestamp?: number
}

/**
 * 错误处理 Hook
 * @param options 配置选项
 */
export function useErrorHandler(options: ErrorHandlerOptions = {}) {
  const {
    showToast = true,
    logError = true,
    global = false,
    duration = 2000,
    icon = 'none',
    context = 'Unknown'
  } = options

  // 局部错误状态
  const localError = ref<string | null>(null)
  const localErrorCode = ref<string | number | null>(null)

  // 根据配置选择使用全局或局部状态
  const error = global ? globalError : localError
  const errorCode = global ? globalErrorCode : localErrorCode

  /**
   * 显示错误提示
   * @param message 错误消息
   * @param customDuration 自定义持续时间
   */
  const showErrorToast = (message: string, customDuration?: number) => {
    if (!showToast) return

    uni.showToast({
      title: message,
      icon,
      duration: customDuration || duration,
      mask: false
    })
  }

  /**
   * 显示成功提示
   * @param message 成功消息
   * @param customDuration 自定义持续时间
   */
  const showSuccess = (message: string, customDuration?: number) => {
    uni.showToast({
      title: message,
      icon: 'success',
      duration: customDuration || duration,
      mask: false
    })
  }

  /**
   * 记录错误日志
   * @param errorInfo 错误信息
   */
  const logErrorInfo = (errorInfo: ErrorInfo) => {
    if (!logError) return

    // 使用统一的日志服务记录错误
    const _errorMessage = `[${context}] ${errorInfo.message}`
    
    if (errorInfo.code) {
      // console.error(`${_errorMessage} (Code: ${errorInfo.code})`, errorInfo.details)
    } else {
      // console.error(${_errorMessage}, errorInfo.details)
    }
  }

  /**
   * 处理错误
   * @param err 错误对象
   * @param customMessage 自定义错误消息
   */
  const handleError = (err: any, customMessage?: string) => {
    let errorInfo: ErrorInfo

    // 解析错误信息
    if (typeof err === 'string') {
      errorInfo = {
        message: customMessage || err,
        timestamp: Date.now()
      }
    } else if (err && typeof err === 'object') {
      errorInfo = {
        message: customMessage || err.message || err.msg || '操作失败',
        code: err.code || err.errorCode || err.status,
        details: err,
        timestamp: Date.now()
      }
    } else {
      errorInfo = {
        message: customMessage || '未知错误',
        timestamp: Date.now()
      }
    }

    // 更新错误状态
    error.value = errorInfo.message
    errorCode.value = errorInfo.code || null

    // 显示错误提示
    showErrorToast(errorInfo.message)

    // 记录错误日志
    logErrorInfo(errorInfo)

    return errorInfo
  }

  /**
   * 处理 API 错误
   * 使用项目现有的 apiErrorHandler
   * @param err API 错误对象
   * @param config 错误处理配置
   */
  const handleApiError = (err: any, config?: any) => {
    try {
      // 使用现有的 API 错误处理器
      const errorInfo = apiErrorHandler.handleError(err, {
        showToast,
        context,
        ...config
      })

      // 更新本地状态
      error.value = errorInfo.message || '请求失败'
      errorCode.value = errorInfo.code || null

      return errorInfo
    } catch {
      // 如果 API 错误处理器失败，使用基础错误处理
      return handleError(err, '请求处理失败')
    }
  }

  /**
   * 清除错误状态
   */
  const clearError = () => {
    error.value = null
    errorCode.value = null
  }

  /**
   * 异步操作错误包装器
   * 自动处理异步操作中的错误
   * @param asyncFn 异步函数
   * @param errorMessage 自定义错误消息
   */
  const withErrorHandling = async <T>(
    asyncFn: () => Promise<T>,
    errorMessage?: string
  ): Promise<T | null> => {
    try {
      clearError()
      const result = await asyncFn()
      return result
    } catch (err) {
      handleError(err, errorMessage)
      return null
    }
  }

  /**
   * Try-Catch 包装器
   * 简化同步操作的错误处理
   * @param fn 同步函数
   * @param errorMessage 自定义错误消息
   */
  const withTryCatch = <T>(
    fn: () => T,
    errorMessage?: string
  ): T | null => {
    try {
      clearError()
      return fn()
    } catch (err) {
      handleError(err, errorMessage)
      return null
    }
  }

  /**
   * 验证操作结果
   * 用于处理可能失败的操作
   * @param result 操作结果
   * @param errorMessage 失败时的错误消息
   */
  const validateResult = (result: any, errorMessage: string = '操作失败') => {
    if (!result || (typeof result === 'object' && result.success === false)) {
      handleError(result, errorMessage)
      return false
    }
    return true
  }

  return {
    // 状态
    error,
    errorCode,
    hasError: computed(() => !!error.value),
    
    // 方法
    handleError,
    handleApiError,
    clearError,
    showErrorToast,
    showSuccess,
    logErrorInfo,
    withErrorHandling,
    withTryCatch,
    validateResult
  }
}

/**
 * 全局错误处理
 * 用于需要全局统一管理的错误
 */
export function useGlobalErrorHandler(context?: string) {
  return useErrorHandler({ 
    global: true, 
    context: context || 'Global'
  })
}

/**
 * 静默错误处理
 * 不显示错误提示，仅记录日志和状态
 */
export function useSilentErrorHandler(context?: string) {
  return useErrorHandler({ 
    showToast: false, 
    context: context || 'Silent'
  })
}

/**
 * API 错误处理
 * 专门用于 API 请求的错误处理
 */
export function useApiErrorHandler(context?: string) {
  return useErrorHandler({ 
    context: context || 'API',
    icon: 'none',
    duration: 3000
  })
}

// 导出全局错误状态供外部使用
export const globalErrorState = {
  error: globalError,
  errorCode: globalErrorCode,
  hasError: computed(() => !!globalError.value)
}

// 导出便捷的错误处理函数
export const quickError = {
  /**
   * 快速显示错误提示
   */
  show: (message: string, duration?: number) => {
    uni.showToast({
      title: message,
      icon: 'none',
      duration: duration || 2000
    })
  },
  
  /**
   * 快速记录错误日志
   */
  log: (_context: string, _error: any) => {
    // console.error(`[${_context}]`, _error)
  },
  
  /**
   * 快速处理常见错误
   */
  handle: (error: any, context: string = 'Unknown') => {
    const message = error?.message || error?.msg || error || '操作失败'
    quickError.show(message)
    quickError.log(context, error)
  }
}