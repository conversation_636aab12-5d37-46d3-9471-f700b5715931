/**
 * 增强版表单页面 Hook
 * 整合表单状态管理、验证和提交逻辑
 * <AUTHOR>
 * @since 2024-01-01
 */

import { ref, reactive, computed, watch } from 'vue'
import { useLoading } from './useLoading'
import { useErrorHandler } from './useErrorHandler'
import { useFormValidation, type FieldConfig, formPresets } from './useFormValidation'

// 表单页面配置
export interface FormPageConfig {
  // 表单预设类型
  preset?: keyof typeof formPresets
  // 自定义字段配置
  fields?: Record<string, FieldConfig>
  // 是否自动验证
  autoValidate?: boolean
  // 是否在输入时验证
  validateOnInput?: boolean
  // 是否在失焦时验证
  validateOnBlur?: boolean
  // 提交前是否显示确认对话框
  confirmBeforeSubmit?: boolean
  // 确认对话框文本
  confirmText?: string
}

// 表单提交函数类型
export type FormSubmitFunction<T = Record<string, unknown>> = (_data: T) => Promise<unknown>

/**
 * 增强版表单页面 Hook
 */
export function useEnhancedFormPage<T extends Record<string, unknown> = Record<string, unknown>>(
  initialData: T,
  config: FormPageConfig = {}
) {
  const {
    preset,
    fields: customFields,
    autoValidate = true,
    validateOnInput = false,
    validateOnBlur = true,
    confirmBeforeSubmit = false,
    confirmText = '确认提交表单？'
  } = config

  // 核心 Hooks
  const { loading, setLoading } = useLoading()
  const { handleError, clearError } = useErrorHandler({ context: 'FormPage' })
  const formValidation = useFormValidation()

  // 表单数据
  const formData = reactive<T>({ ...initialData })
  const originalData = ref<T>({ ...initialData })

  // 表单状态
  const formState = reactive({
    isSubmitting: false,
    hasChanges: false,
    lastSubmitTime: null as Date | null,
    submitCount: 0
  })

  // 提交函数
  const submitFunction = ref<FormSubmitFunction<T> | null>(null)

  // 初始化表单验证配置
  const initializeValidation = () => {
    if (preset && formPresets[preset]) {
      formValidation.addFields(formPresets[preset])
    }
    if (customFields) {
      formValidation.addFields(customFields)
    }
  }

  // 计算属性
  const isValid = computed(() => formValidation.validationState.isValid)
  const hasErrors = computed(() => formValidation.hasErrors.value)
  const canSubmit = computed(() => {
    return !formState.isSubmitting && 
           !loading.value && 
           isValid.value && 
           !!submitFunction.value
  })

  // 检查表单是否有变化
  const checkFormChanges = () => {
    formState.hasChanges = JSON.stringify(formData) !== JSON.stringify(originalData.value)
  }

  // 监听表单数据变化
  watch(
    () => formData,
    () => {
      checkFormChanges()
      
      if (autoValidate && validateOnInput) {
        formValidation.validateForm(formData)
      }
    },
    { deep: true }
  )

  // 设置表单数据
  const setFormData = (data: Partial<T>) => {
    Object.assign(formData, data)
  }

  // 重置表单
  const resetForm = () => {
    Object.assign(formData, originalData.value)
    formValidation.resetValidation()
    formState.hasChanges = false
    clearError()
  }

  // 更新原始数据（用于重置基准）
  const updateOriginalData = (data?: Partial<T>) => {
    if (data) {
      Object.assign(originalData.value, data)
    } else {
      originalData.value = { ...formData }
    }
    formState.hasChanges = false
  }

  // 设置提交函数
  const setSubmitFunction = (fn: FormSubmitFunction<T>) => {
    submitFunction.value = fn
  }

  // 验证表单
  const validateForm = (): boolean => {
    if (!autoValidate) return true
    return formValidation.validateAndShowError(formData)
  }

  // 验证单个字段
  const validateField = (fieldName: keyof T, showError = false): boolean => {
    const error = formValidation.validateField(String(fieldName), (formData as Record<string, unknown>)[fieldName as string])
    
    if (error) {
      formValidation.validationState.errors[String(fieldName)] = error
      if (showError) {
        handleError(new Error(error))
      }
      return false
    } else {
      formValidation.clearFieldError(String(fieldName))
      return true
    }
  }

  // 字段失焦处理
  const handleFieldBlur = (fieldName: keyof T) => {
    formValidation.touchField(String(fieldName))
    
    if (validateOnBlur) {
      validateField(fieldName)
    }
  }

  // 字段输入处理
  const handleFieldInput = (fieldName: keyof T, value: unknown) => {
    (formData as Record<string, unknown>)[fieldName as string] = value
    
    if (validateOnInput) {
      validateField(fieldName)
    }
  }

  // 提交表单
  const submitForm = async (): Promise<boolean> => {
    if (!submitFunction.value) {
      handleError(new Error('未设置提交函数'))
      return false
    }

    if (!canSubmit.value) {
      return false
    }

    // 验证表单
    if (!validateForm()) {
      return false
    }

    // 确认对话框
    if (confirmBeforeSubmit) {
      const confirmed = window.confirm(confirmText)
      if (!confirmed) {
        return false
      }
    }

    formState.isSubmitting = true
    setLoading(true)
    clearError()

    try {
      const result = await submitFunction.value({ ...formData } as T)
      
      // 更新状态
      formState.lastSubmitTime = new Date()
      formState.submitCount++
      updateOriginalData()
      
      return result
    } catch (error) {
      handleError(error as Error)
      return false
    } finally {
      formState.isSubmitting = false
      setLoading(false)
    }
  }

  // 获取字段错误
  const getFieldError = (fieldName: keyof T): string => {
    return formValidation.getFieldError(String(fieldName))
  }

  // 检查字段是否有错误
  const hasFieldError = (fieldName: keyof T): boolean => {
    return formValidation.hasFieldError(String(fieldName))
  }

  // 检查字段是否已触摸
  const isFieldTouched = (fieldName: keyof T): boolean => {
    return formValidation.isFieldTouched(String(fieldName))
  }

  // 设置字段错误
  const setFieldError = (fieldName: keyof T, error: string) => {
    formValidation.validationState.errors[String(fieldName)] = error
  }

  // 清除字段错误
  const clearFieldError = (fieldName: keyof T) => {
    formValidation.clearFieldError(String(fieldName))
  }

  // 清除所有错误
  const clearAllErrors = () => {
    formValidation.clearAllErrors()
    clearError()
  }

  // 添加字段配置
  const addField = (name: string, config: FieldConfig) => {
    formValidation.addField(name, config)
  }

  // 批量添加字段配置
  const addFields = (fieldsConfig: Record<string, FieldConfig>) => {
    formValidation.addFields(fieldsConfig)
  }

  // 初始化
  initializeValidation()

  return {
    // 表单数据
    formData,
    originalData,
    
    // 表单状态
    formState,
    loading,
    
    // 计算属性
    isValid,
    hasErrors,
    canSubmit,
    
    // 验证状态
    validationState: formValidation.validationState,
    errorCount: formValidation.errorCount,
    
    // 数据操作
    setFormData,
    resetForm,
    updateOriginalData,
    
    // 提交相关
    setSubmitFunction,
    submitForm,
    
    // 验证相关
    validateForm,
    validateField,
    
    // 字段操作
    handleFieldBlur,
    handleFieldInput,
    
    // 错误处理
    getFieldError,
    hasFieldError,
    isFieldTouched,
    setFieldError,
    clearFieldError,
    clearAllErrors,
    
    // 字段配置
    addField,
    addFields,
    
    // 工具方法
    checkFormChanges
  }
}

/**
 * 快速创建表单页面 Hook 的工厂函数
 */
export const createFormPage = {
  // 登录表单
  login: (initialData = { phonenumber: '', password: '', smsCode: '' }) => 
    useEnhancedFormPage(initialData, { preset: 'login' }),
  
  // 注册表单
  register: (initialData = { phonenumber: '', password: '', confirmPassword: '', smsCode: '' }) => 
    useEnhancedFormPage(initialData, { preset: 'register' }),
  
  // 实名认证表单
  realAuth: (initialData = { realName: '', idCard: '' }) => 
    useEnhancedFormPage(initialData, { preset: 'realAuth' }),
  
  // 个人资料表单
  profile: (initialData = { nickname: '', birthday: '', height: '', weight: '' }) => 
    useEnhancedFormPage(initialData, { preset: 'profile' })
}