/**
 * 页面通用组合式函数
 * 提供页面的通用状态管理和逻辑处理
 */

import { ref, reactive, computed, onMounted } from 'vue'

// 页面状态接口
export interface PageState {
  loading: boolean
  error: Error | null
  empty: boolean
  refreshing: boolean
  loadingMore: boolean
}

// 页面配置接口
export interface PageConfig {
  title?: string
  showHeader?: boolean
  showFooter?: boolean
  showBack?: boolean
  enablePullRefresh?: boolean
  enableLoadMore?: boolean
  autoLoad?: boolean
}

// 页面数据接口
export interface PageData<T = any> {
  list: T[]
  total: number
  current: number
  size: number
  hasMore: boolean
}

/**
 * 基础页面Hook
 */
export function usePage(config: PageConfig = {}) {
  // 页面状态
  const state = reactive<PageState>({
    loading: false,
    error: null,
    empty: false,
    refreshing: false,
    loadingMore: false
  })

  // 页面配置
  const pageConfig = reactive<PageConfig>({
    title: '',
    showHeader: true,
    showFooter: false,
    showBack: true,
    enablePullRefresh: true,
    enableLoadMore: true,
    autoLoad: true,
    ...config
  })

  // 设置加载状态
  const setLoading = (loading: boolean) => {
    state.loading = loading
  }

  // 设置错误状态
  const setError = (error: Error | null) => {
    state.error = error
    state.loading = false
  }

  // 设置空状态
  const setEmpty = (empty: boolean) => {
    state.empty = empty
  }

  // 清除状态
  const clearState = () => {
    state.loading = false
    state.error = null
    state.empty = false
    state.refreshing = false
    state.loadingMore = false
  }

  // 页面标题设置
  const setTitle = (title: string) => {
    pageConfig.title = title
    uni.setNavigationBarTitle({ title })
  }

  return {
    state,
    pageConfig,
    setLoading,
    setError,
    setEmpty,
    clearState,
    setTitle
  }
}

/**
 * 列表页面Hook
 */
export function useListPage<T = any>(config: PageConfig = {}) {
  const { state, pageConfig, setLoading, setError, setEmpty, clearState, setTitle } = usePage(config)

  // 列表数据
  const data = reactive<PageData<T>>({
    list: [],
    total: 0,
    current: 1,
    size: 20,
    hasMore: true
  })

  // 加载函数引用
  const loadFunction = ref<(() => Promise<any>) | null>(null)

  // 计算属性
  const isEmpty = computed(() => {
    return !state.loading && !state.error && data.list.length === 0
  })

  // 设置加载函数
  const setLoadFunction = (fn: () => Promise<any>) => {
    loadFunction.value = fn
  }

  // 加载数据
  const loadData = async (reset = false) => {
    if (!loadFunction.value) return

    try {
      if (reset) {
        data.current = 1
        data.list = []
        data.hasMore = true
        clearState()
      }

      if (reset) {
        setLoading(true)
      } else {
        state.loadingMore = true
      }

      const result = await loadFunction.value()
      
      if (result) {
        const { list = [], total = 0, current = 1, hasMore = false } = result
        
        if (reset) {
          data.list = list
        } else {
          data.list.push(...list)
        }
        
        data.total = total
        data.current = current
        data.hasMore = hasMore
        
        setEmpty(data.list.length === 0)
      }
    } catch (error) {
      // console.error('Load data error:', error)
      setError(error as Error)
    } finally {
      setLoading(false)
      state.loadingMore = false
      state.refreshing = false
    }
  }

  // 刷新数据
  const refresh = async () => {
    state.refreshing = true
    await loadData(true)
  }

  // 加载更多
  const loadMore = async () => {
    if (state.loadingMore || !data.hasMore) return
    
    data.current += 1
    await loadData(false)
  }

  // 重试
  const retry = async () => {
    clearState()
    await loadData(true)
  }

  // 添加项目
  const addItem = (item: T, index?: number) => {
    if (typeof index === 'number') {
      data.list.splice(index, 0, item as any)
    } else {
      data.list.unshift(item as any)
    }
    data.total += 1
    setEmpty(false)
  }

  // 更新项目
  const updateItem = (index: number, item: Partial<T>) => {
    if (index >= 0 && index < data.list.length) {
      data.list[index] = { ...data.list[index], ...item }
    }
  }

  // 删除项目
  const removeItem = (index: number) => {
    if (index >= 0 && index < data.list.length) {
      data.list.splice(index, 1)
      data.total -= 1
      setEmpty(data.list.length === 0)
    }
  }

  // 自动加载
  onMounted(() => {
    if (pageConfig.autoLoad) {
      loadData(true)
    }
  })

  return {
    // 基础页面功能
    state,
    pageConfig,
    setLoading,
    setError,
    setEmpty,
    clearState,
    setTitle,
    
    // 列表数据
    data,
    isEmpty,
    
    // 列表操作
    setLoadFunction,
    loadData,
    refresh,
    loadMore,
    retry,
    
    // 数据操作
    addItem,
    updateItem,
    removeItem
  }
}

/**
 * 表单页面Hook
 */
export function useFormPage(config: PageConfig = {}) {
  const { state, pageConfig, setLoading, setError, clearState, setTitle } = usePage(config)

  // 表单数据
  const formData = ref<Record<string, any>>({})
  
  // 表单验证状态
  const formErrors = ref<Record<string, string>>({})
  
  // 表单是否已修改
  const formChanged = ref(false)

  // 提交函数引用
  const submitFunction = ref<((data: any) => Promise<any>) | null>(null)

  // 设置表单数据
  const setFormData = (data: Record<string, any>) => {
    formData.value = { ...formData.value, ...data }
    formChanged.value = true
  }

  // 重置表单
  const resetForm = (data?: Record<string, any>) => {
    formData.value = data ? { ...data } : {}
    formErrors.value = {}
    formChanged.value = false
    clearState()
  }

  // 设置提交函数
  const setSubmitFunction = (fn: (data: any) => Promise<any>) => {
    submitFunction.value = fn
  }

  // 设置字段错误
  const setFieldError = (field: string, error: string) => {
    formErrors.value[field] = error
  }

  // 清除字段错误
  const clearFieldError = (field: string) => {
    delete formErrors.value[field]
  }

  // 清除所有错误
  const clearAllErrors = () => {
    formErrors.value = {}
  }

  // 验证表单
  const validateForm = () => {
    return Object.keys(formErrors.value).length === 0
  }

  // 提交表单
  const submitForm = async () => {
    if (!submitFunction.value) return false

    try {
      clearAllErrors()
      
      if (!validateForm()) {
        return false
      }

      setLoading(true)
      const result = await submitFunction.value(formData.value)
      formChanged.value = false
      return result
    } catch (error) {
      // console.error('Submit form error:', error)
      setError(error as Error)
      return false
    } finally {
      setLoading(false)
    }
  }

  return {
    // 基础页面功能
    state,
    pageConfig,
    setLoading,
    setError,
    clearState,
    setTitle,
    
    // 表单数据
    formData,
    formErrors,
    formChanged,
    
    // 表单操作
    setFormData,
    resetForm,
    setSubmitFunction,
    setFieldError,
    clearFieldError,
    clearAllErrors,
    validateForm,
    submitForm
  }
}