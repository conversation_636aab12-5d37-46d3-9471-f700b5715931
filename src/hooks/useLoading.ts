/**
 * 统一加载状态管理 Composable
 * 解决项目中 30+ 个文件的重复 loading 状态管理问题
 * 
 * <AUTHOR> Assistant
 * @date 2024-12-28
 */

import { ref, computed } from 'vue'
import { showLoading, hideLoading, hideAllLoading } from '@/utils/common/loadingManager'

// 全局加载状态计数器
const globalLoadingCount = ref(0)
const globalLoadingText = ref('加载中...')

/**
 * 加载状态管理 Hook
 * @param options 配置选项
 */
export function useLoading(options: {
  /** 是否使用全局加载状态 */
  global?: boolean
  /** 默认加载文本 */
  defaultText?: string
  /** 是否自动显示 uni.showLoading */
  autoShow?: boolean
} = {}) {
  const {
    global = false,
    defaultText = '加载中...',
    autoShow = true
  } = options

  // 局部加载状态
  const localLoading = ref(false)
  const localText = ref(defaultText)
  const currentTaskId = ref<string | null>(null)

  // 根据配置选择使用全局或局部状态
  const loading = computed(() => {
    return global ? globalLoadingCount.value > 0 : localLoading.value
  })

  const loadingText = computed(() => {
    return global ? globalLoadingText.value : localText.value
  })

  /**
   * 显示加载状态
   * @param text 加载文本
   */
  const show = (text: string = defaultText) => {
    if (global) {
      globalLoadingCount.value++
      globalLoadingText.value = text
      
      // 只在第一次显示时调用 showLoading
      if (globalLoadingCount.value === 1 && autoShow) {
        currentTaskId.value = showLoading(text)
      }
    } else {
      localLoading.value = true
      localText.value = text
      
      if (autoShow) {
        currentTaskId.value = showLoading(text)
      }
    }
  }

  /**
   * 隐藏加载状态
   */
  const hide = () => {
    if (global) {
      globalLoadingCount.value = Math.max(0, globalLoadingCount.value - 1)
      
      // 只在计数器归零时隐藏
      if (globalLoadingCount.value === 0 && autoShow && currentTaskId.value) {
        hideLoading(currentTaskId.value)
        currentTaskId.value = null
      }
    } else {
      localLoading.value = false
      
      if (autoShow && currentTaskId.value) {
        hideLoading(currentTaskId.value)
        currentTaskId.value = null
      }
    }
  }

  /**
   * 切换加载状态
   * @param text 加载文本
   */
  const toggle = (text?: string) => {
    if (loading.value) {
      hide()
    } else {
      show(text)
    }
  }

  /**
   * 设置加载状态
   * @param isLoading 是否加载中
   * @param text 加载文本
   */
  const setLoading = (isLoading: boolean, text?: string) => {
    if (isLoading) {
      show(text)
    } else {
      hide()
    }
  }

  /**
   * 异步操作包装器
   * 自动管理加载状态
   * @param asyncFn 异步函数
   * @param text 加载文本
   */
  const withLoading = async <T>(
    asyncFn: () => Promise<T>,
    text?: string
  ): Promise<T> => {
    try {
      show(text)
      const result = await asyncFn()
      return result
    } finally {
      hide()
    }
  }

  /**
   * 重置加载状态（仅全局模式）
   */
  const reset = () => {
    if (global) {
      globalLoadingCount.value = 0
      globalLoadingText.value = defaultText
      if (autoShow) {
        hideAllLoading()
      }
    } else {
      localLoading.value = false
      localText.value = defaultText
      if (autoShow && currentTaskId.value) {
        hideLoading(currentTaskId.value)
        currentTaskId.value = null
      }
    }
  }

  return {
    // 状态
    loading,
    loadingText,
    
    // 方法
    show,
    hide,
    toggle,
    setLoading,
    withLoading,
    reset
  }
}

/**
 * 全局加载状态管理
 * 用于需要全局统一管理的场景
 */
export function useGlobalLoading() {
  return useLoading({ global: true })
}

/**
 * 局部加载状态管理
 * 用于组件内部的加载状态
 */
export function useLocalLoading(defaultText?: string) {
  return useLoading({ global: false, defaultText })
}

/**
 * 静默加载状态管理
 * 不自动显示 uni.showLoading，仅管理状态
 */
export function useSilentLoading() {
  return useLoading({ autoShow: false })
}

// 导出全局状态供外部使用
export const globalLoadingState = {
  count: globalLoadingCount,
  text: globalLoadingText,
  isLoading: computed(() => globalLoadingCount.value > 0)
}