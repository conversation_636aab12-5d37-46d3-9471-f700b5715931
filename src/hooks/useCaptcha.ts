/**
 * 验证码相关 Composable
 * 将验证码逻辑抽象为可复用的组合式API
 */

import { ref, reactive, computed } from 'vue'
import { 
  getCaptcha, 
  checkCaptcha, 
  type CaptchaType, 
  type CaptchaResponse, 
  type CaptchaCheckParams 
} from '@/api/modules/captcha'
import { aesEncrypt } from '@/utils/helpers/crypto'

// 验证码状态
export interface CaptchaState {
  loading: boolean
  captchaData: CaptchaResponse | null
  isVerified: boolean
  errorMessage: string
}

// 滑块验证码Hook
export const useSliderCaptcha = () => {
  const state = reactive<CaptchaState>({
    loading: false,
    captchaData: null,
    isVerified: false,
    errorMessage: ''
  })

  // 图片尺寸配置
  const imgSize = computed(() => ({
    width: '310px',
    height: '155px'
  }))

  /**
   * 获取滑块验证码
   */
  const getCaptchaData = async () => {
    try {
      state.loading = true
      state.errorMessage = ''
      
      const response = await getCaptcha('blockPuzzle')
      if (response.code === 0 || response.code === 200) {
        state.captchaData = response.data
      } else {
        state.errorMessage = response.message || '获取验证码失败'
      }
    } catch (error: any) {
      state.errorMessage = error.message || '网络错误'
      // console.error('获取验证码失败:', error)
    } finally {
      state.loading = false
    }
  }

  /**
   * 验证滑块
   * @param moveDistance 移动距离
   */
  const verifySlider = async (moveDistance: number) => {
    if (!state.captchaData) {
      throw new Error('验证码数据不存在')
    }

    try {
      state.loading = true
      
      // 按比例转换坐标（官方标准310宽度）
      const scaledDistance = moveDistance * 310 / 310
      const pointData = { x: scaledDistance, y: 5.0 }
      
      // 加密坐标数据
      const pointJson = state.captchaData.secretKey 
        ? aesEncrypt(JSON.stringify(pointData), state.captchaData.secretKey)
        : JSON.stringify(pointData)

      const params: CaptchaCheckParams = {
        captchaType: 'blockPuzzle',
        pointJson,
        token: state.captchaData.token
      }

      const response = await checkCaptcha(params)
      
      if (response.code === 0 || response.code === 200) {
        if (response.data.result) {
          state.isVerified = true
          
          // 生成二次验证参数
          const captchaVerification = state.captchaData.secretKey
            ? aesEncrypt(`${state.captchaData.token}---${JSON.stringify(pointData)}`, state.captchaData.secretKey)
            : `${state.captchaData.token}---${JSON.stringify(pointData)}`

          return {
            success: true,
            captchaVerification,
            message: '验证成功'
          }
        } else {
          state.errorMessage = '验证失败，请重试'
          return {
            success: false,
            message: '验证失败，请重试'
          }
        }
      } else {
        state.errorMessage = response.message || '验证失败'
        return {
          success: false,
          message: response.message || '验证失败'
        }
      }
    } catch (error: any) {
      state.errorMessage = error.message || '验证异常'
      return {
        success: false,
        message: error.message || '验证异常'
      }
    } finally {
      state.loading = false
    }
  }

  /**
   * 重置验证码状态
   */
  const resetCaptcha = () => {
    state.isVerified = false
    state.errorMessage = ''
    state.captchaData = null
    getCaptchaData()
  }

  return {
    state,
    imgSize,
    getCaptchaData,
    verifySlider,
    resetCaptcha
  }
}

// 点选验证码Hook
export const useClickCaptcha = () => {
  const state = reactive<CaptchaState>({
    loading: false,
    captchaData: null,
    isVerified: false,
    errorMessage: ''
  })

  const clickPoints = ref<Array<{ x: number; y: number }>>([])

  /**
   * 获取点选验证码
   */
  const getCaptchaData = async () => {
    try {
      state.loading = true
      state.errorMessage = ''
      
      const response = await getCaptcha('clickWord')
      if (response.code === 0 || response.code === 200) {
        state.captchaData = response.data
      } else {
        state.errorMessage = response.message || '获取验证码失败'
      }
    } catch (error: any) {
      state.errorMessage = error.message || '网络错误'
      console.error('获取验证码失败:', error)
    } finally {
      state.loading = false
    }
  }

  /**
   * 添加点击点
   * @param point 点击坐标
   */
  const addClickPoint = (point: { x: number; y: number }) => {
    if (clickPoints.value.length < 4) {
      clickPoints.value.push(point)
    }
  }

  /**
   * 验证点选
   */
  const verifyClick = async () => {
    if (!state.captchaData || clickPoints.value.length === 0) {
      throw new Error('验证数据不完整')
    }

    try {
      state.loading = true
      
      // 按比例转换坐标（官方标准310x155）
      const scaledPoints = clickPoints.value.map(p => ({
        x: Math.round(310 * p.x / 310),
        y: Math.round(155 * p.y / 155)
      }))
      
      // 加密坐标数据
      const pointJson = state.captchaData.secretKey 
        ? aesEncrypt(JSON.stringify(clickPoints.value), state.captchaData.secretKey)
        : JSON.stringify(clickPoints.value)

      const params: CaptchaCheckParams = {
        captchaType: 'clickWord',
        pointJson,
        token: state.captchaData.token
      }

      const response = await checkCaptcha(params)
      
      if (response.code === 0 || response.code === 200) {
        if (response.data.result) {
          state.isVerified = true
          
          // 生成二次验证参数
          const captchaVerification = state.captchaData.secretKey
            ? aesEncrypt(`${state.captchaData.token}---${JSON.stringify(scaledPoints)}`, state.captchaData.secretKey)
            : `${state.captchaData.token}---${JSON.stringify(scaledPoints)}`

          return {
            success: true,
            captchaVerification,
            message: '验证成功'
          }
        } else {
          state.errorMessage = '验证失败，请重试'
          return {
            success: false,
            message: '验证失败，请重试'
          }
        }
      } else {
        state.errorMessage = response.message || '验证失败'
        return {
          success: false,
          message: response.message || '验证失败'
        }
      }
    } catch (error: any) {
      state.errorMessage = error.message || '验证异常'
      return {
        success: false,
        message: error.message || '验证异常'
      }
    } finally {
      state.loading = false
    }
  }

  /**
   * 重置验证码状态
   */
  const resetCaptcha = () => {
    state.isVerified = false
    state.errorMessage = ''
    state.captchaData = null
    clickPoints.value = []
    getCaptchaData()
  }

  return {
    state,
    clickPoints,
    getCaptchaData,
    addClickPoint,
    verifyClick,
    resetCaptcha
  }
}