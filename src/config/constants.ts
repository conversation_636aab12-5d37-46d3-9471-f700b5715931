/**
 * 常量配置文件
 * 统一管理应用中使用的常量
 */

// HTTP 状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500
} as const

// 业务状态码
export const BUSINESS_CODE = {
  SUCCESS: 0,
  FAIL: -1,
  TOKEN_EXPIRED: 401,
  PERMISSION_DENIED: 403
} as const

// 存储键名
export const STORAGE_KEYS = {
  TOKEN: 'oto_token',
  USER_INFO: 'oto_user_info',
  SETTINGS: 'oto_settings',
  THEME: 'oto_theme'
} as const

// 页面路径
export const PAGE_PATHS = {
  HOME: '/pages/index/index',
  LOGIN: '/pages/login/index',
  REGISTER: '/pages/register/index',
  PROFILE: '/pages/profile/index',
  SETTINGS: '/pages/settings/index'
} as const

// 事件名称
export const EVENT_NAMES = {
  LOGIN_SUCCESS: 'loginSuccess',
  LOGOUT: 'logout',
  USER_INFO_UPDATE: 'userInfoUpdate',
  THEME_CHANGE: 'themeChange'
} as const

// 正则表达式
export const REGEX_PATTERNS = {
  // 手机号
  PHONE: /^1[3-9]\d{9}$/,
  
  // 邮箱
  EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  
  // 身份证号
  ID_CARD: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
  
  // 密码（8-20位，包含字母和数字）
  PASSWORD: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,20}$/
} as const

// 默认配置
export const DEFAULT_CONFIG = {
  // 分页大小
  PAGE_SIZE: 20,
  
  // 请求超时时间
  REQUEST_TIMEOUT: 10000,
  
  // Loading 延迟时间
  LOADING_DELAY: 300,
  
  // 防抖延迟时间
  DEBOUNCE_DELAY: 300,
  
  // 节流延迟时间
  THROTTLE_DELAY: 1000
} as const