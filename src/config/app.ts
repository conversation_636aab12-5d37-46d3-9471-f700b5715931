/**
 * 应用配置文件
 * 统一管理应用级别的配置信息
 */

// 应用基础配置
export const APP_CONFIG = {
  // 应用名称
  name: 'OTO-UI',
  
  // 版本信息
  version: '1.0.0',
  
  // 环境配置
  env: process.env.NODE_ENV || 'development',
  
  // 是否为开发环境
  isDev: process.env.NODE_ENV === 'development',
  
  // 是否为生产环境
  isProd: process.env.NODE_ENV === 'production',
  
  // 调试模式
  debug: process.env.NODE_ENV === 'development'
}

// API 配置
export const API_CONFIG = {
  // 基础 URL
  baseURL: process.env.NODE_ENV === 'development' 
    ? 'http://localhost:3000/api' 
    : 'https://api.oto-ui.com',
  
  // 请求超时时间
  timeout: 10000,
  
  // 重试次数
  retryCount: 3,
  
  // 重试延迟
  retryDelay: 1000
}

// 存储配置
export const STORAGE_CONFIG = {
  // Token 存储键
  tokenKey: 'oto_token',
  
  // 用户信息存储键
  userInfoKey: 'oto_user_info',
  
  // 设置存储键
  settingsKey: 'oto_settings'
}

// 页面配置
export const PAGE_CONFIG = {
  // 默认页面大小
  defaultPageSize: 20,
  
  // 最大页面大小
  maxPageSize: 100
}