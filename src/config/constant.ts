/**
 * 应用常量配置
 */

// 存储键名常量
export const STORAGE_KEYS = {
  TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_INFO: 'user_info',
  DEVICE_ID: 'device_id',
  LANGUAGE: 'language'
} as const

// 页面路径常量已迁移到 @/config/router.ts 中的 ROUTES
// 请使用 import { ROUTES } from '@/config/router' 导入

// API 接口常量
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    REFRESH: '/auth/refresh',
    LOGOUT: '/auth/logout'
  },
  USER: {
    PROFILE: '/user/profile',
    UPDATE: '/user/update'
  },
  COURSE: {
    LIST: '/course/list',
    DETAIL: '/course/detail',
    BOOK: '/course/book'
  }
} as const

// 状态码常量
export const STATUS_CODES = {
  SUCCESS: 200,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500
} as const

// 设备类型常量
export const DEVICE_TYPES = {
  ANDROID: 'android',
  IOS: 'ios',
  WECHAT: 'wechat'
} as const

// 课程状态常量
export const COURSE_STATUS = {
  AVAILABLE: 'available',
  FULL: 'full',
  CANCELLED: 'cancelled',
  COMPLETED: 'completed'
} as const