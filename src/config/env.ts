/**
 * 环境变量配置管理
 * @description 类型安全的环境变量访问和配置管理
 * <AUTHOR>
 * @since 2024-01-01
 */

// 环境类型定义
export type AppEnvironment = 'development' | 'staging' | 'production' | 'test'

// 日志级别类型
export type LogLevel = 'debug' | 'info' | 'warn' | 'error'

// 应用配置接口
export interface AppConfig {
  // 基础信息
  APP_NAME: string
  APP_VERSION: string
  APP_DESCRIPTION: string
  APP_ENV: AppEnvironment
  
  // API配置
  API_BASE_URL: string
  REQUEST_TIMEOUT: number
  REQUEST_RETRY_COUNT: number
  
  // 日志配置
  LOG_LEVEL: LogLevel
  ENABLE_CONSOLE_LOG: boolean
  
  // 功能开关
  ENABLE_MOCK: boolean
  ENABLE_DEBUG_TOOLS: boolean
  ENABLE_HOT_RELOAD: boolean
  ENABLE_SOURCE_MAP: boolean
  ENABLE_PERFORMANCE_MONITOR: boolean
  
  // 安全配置
  ENABLE_HTTPS_ONLY: boolean
  ENABLE_STRICT_MODE: boolean
  ENABLE_CSRF_PROTECTION: boolean
  
  // 验证码配置
  CAPTCHA_ENABLED: boolean
  CAPTCHA_DEBUG: boolean
  
  // 设备指纹配置

  
  // CDN配置
  CDN_BASE_URL?: string
  STATIC_RESOURCE_VERSION?: string
}

/**
 * 检测运行环境并获取环境变量
 */
const getEnvironmentVariable = (key: string): string | undefined => {
  // 检测是否在Node.js环境
  if (typeof process !== 'undefined' && process.env) {
    return process.env[key]
  }
  
  // 检测是否在Vite/浏览器环境
  try {
    // @ts-ignore
    if (typeof import.meta !== 'undefined' && import.meta.env) {
      // @ts-ignore
      return import.meta.env[key]
    }
  } catch {
    // 忽略错误，继续尝试其他方式
  }
  
  return undefined
}

/**
 * 获取环境变量值（带类型转换）
 */
const getEnvValue = {
  string: (key: string, defaultValue: string = ''): string => {
    try {
      const value = getEnvironmentVariable(key)
      return value || defaultValue
    } catch {
      return defaultValue
    }
  },
  
  number: (key: string, defaultValue: number = 0): number => {
    try {
      const value = getEnvironmentVariable(key)
      return value ? parseInt(value, 10) : defaultValue
    } catch {
      return defaultValue
    }
  },
  
  boolean: (key: string, defaultValue: boolean = false): boolean => {
    try {
      const value = getEnvironmentVariable(key)
      return value === 'true' || (value === undefined && defaultValue)
    } catch {
      return defaultValue
    }
  },
  
  logLevel: (key: string, defaultValue: LogLevel = 'info'): LogLevel => {
    try {
      const value = getEnvironmentVariable(key) as LogLevel
      return ['debug', 'info', 'warn', 'error'].includes(value) ? value : defaultValue
    } catch {
      return defaultValue
    }
  },
  
  environment: (key: string, defaultValue: AppEnvironment = 'development'): AppEnvironment => {
    try {
      const value = getEnvironmentVariable(key) as AppEnvironment
      return ['development', 'staging', 'production', 'test'].includes(value) ? value : defaultValue
    } catch {
      return defaultValue
    }
  }
}

/**
 * 应用配置实例
 */
export const appConfig: AppConfig = {
  // 基础信息
  APP_NAME: getEnvValue.string('VITE_APP_NAME', 'OTO健身课程预约系统'),
  APP_VERSION: getEnvValue.string('VITE_APP_VERSION', '1.0.0'),
  APP_DESCRIPTION: getEnvValue.string('VITE_APP_DESCRIPTION', '专业的健身课程预约平台'),
  APP_ENV: getEnvValue.environment('VITE_APP_ENV', 'development'),
  
  // API配置
  API_BASE_URL: getEnvValue.string('VITE_API_BASE_URL', ''),
  REQUEST_TIMEOUT: getEnvValue.number('VITE_REQUEST_TIMEOUT', 10000),
  REQUEST_RETRY_COUNT: getEnvValue.number('VITE_REQUEST_RETRY_COUNT', 3),
  
  // 日志配置
  LOG_LEVEL: getEnvValue.logLevel('VITE_LOG_LEVEL', 'info'),
  ENABLE_CONSOLE_LOG: getEnvValue.boolean('VITE_ENABLE_CONSOLE_LOG', true),
  
  // 功能开关
  ENABLE_MOCK: getEnvValue.boolean('VITE_ENABLE_MOCK', false),
  ENABLE_DEBUG_TOOLS: getEnvValue.boolean('VITE_ENABLE_DEBUG_TOOLS', false),
  ENABLE_HOT_RELOAD: getEnvValue.boolean('VITE_ENABLE_HOT_RELOAD', true),
  ENABLE_SOURCE_MAP: getEnvValue.boolean('VITE_ENABLE_SOURCE_MAP', false),
  ENABLE_PERFORMANCE_MONITOR: getEnvValue.boolean('VITE_ENABLE_PERFORMANCE_MONITOR', false),
  
  // 安全配置
  ENABLE_HTTPS_ONLY: getEnvValue.boolean('VITE_ENABLE_HTTPS_ONLY', false),
  ENABLE_STRICT_MODE: getEnvValue.boolean('VITE_ENABLE_STRICT_MODE', false),
  ENABLE_CSRF_PROTECTION: getEnvValue.boolean('VITE_ENABLE_CSRF_PROTECTION', true),
  
  // 验证码配置
  CAPTCHA_ENABLED: getEnvValue.boolean('VITE_CAPTCHA_ENABLED', true),
  CAPTCHA_DEBUG: getEnvValue.boolean('VITE_CAPTCHA_DEBUG', false),
  
  // 设备指纹配置

  
  // CDN配置
  CDN_BASE_URL: getEnvValue.string('VITE_CDN_BASE_URL'),
  STATIC_RESOURCE_VERSION: getEnvValue.string('VITE_STATIC_RESOURCE_VERSION')
}

/**
 * 环境检查工具
 */
export const envUtils = {
  isDevelopment: () => appConfig.APP_ENV === 'development',
  isStaging: () => appConfig.APP_ENV === 'staging',
  isProduction: () => appConfig.APP_ENV === 'production',
  isTest: () => appConfig.APP_ENV === 'test',
  
  // 是否为开发相关环境
  isDevMode: () => ['development', 'test'].includes(appConfig.APP_ENV),
  
  // 是否为生产相关环境
  isProdMode: () => ['staging', 'production'].includes(appConfig.APP_ENV)
}

/**
 * 配置验证
 */
export const validateConfig = (): void => {
  const errors: string[] = []
  
  // 验证必需配置
  if (!appConfig.API_BASE_URL) {
    errors.push('API_BASE_URL is required')
  }
  
  if (!appConfig.APP_NAME) {
    errors.push('APP_NAME is required')
  }
  
  // 验证URL格式
  try {
    new URL(appConfig.API_BASE_URL)
  } catch {
    errors.push('API_BASE_URL must be a valid URL')
  }
  
  // 生产环境额外验证
  if (envUtils.isProduction()) {
    if (appConfig.API_BASE_URL.includes('localhost')) {
      errors.push('Production environment should not use localhost')
    }
    
    if (appConfig.ENABLE_DEBUG_TOOLS) {
      errors.push('Debug tools should be disabled in production')
    }
  }
  
  if (errors.length > 0) {
    console.error('Configuration validation failed:', errors)
    throw new Error(`Configuration validation failed: ${errors.join(', ')}`)
  }
}

// 开发环境下打印配置信息
if (envUtils.isDevelopment() && appConfig.ENABLE_CONSOLE_LOG) {
  // console.group('🔧 Application Configuration')
  // console.log('Environment:', appConfig.APP_ENV)
  // console.log('API Base URL:', appConfig.API_BASE_URL)
  // console.log('App Version:', appConfig.APP_VERSION)
  // console.log('Debug Mode:', appConfig.ENABLE_DEBUG_TOOLS)
  // console.groupEnd()
}

// 验证配置
validateConfig()

// 兼容性导出
export default appConfig