/**
 * 配置模块统一导出
 * 提供所有配置的统一入口
 */

// 应用配置
export * from './app'

// 常量配置
export * from './constants'

// 导出路由配置
export * from './router'
export { default as RouterService } from './router'

// 导出其他配置
// 这里可以添加其他配置文件的导出

// API配置已迁移到 @/api/http/config
// @deprecated 请使用 @/api/http/config 中的配置
import { getBaseURL, REQUEST_CONFIG } from '@/http/config'
export { getBaseURL, REQUEST_CONFIG }

// 兼容性导出（已废弃）
export const baseURL = getBaseURL();

// 应用配置
export const config = {
  // 应用配置
  app: {
    name: '乐享生活',
    version: '1.0.0'
  },

  // API配置已迁移，请使用 @/api/http/config
  // @deprecated 请使用 REQUEST_CONFIG
  api: {
    timeout: REQUEST_CONFIG.timeout,
    retry: 3
  }
};

// 废弃提示
console.warn('[config/index] API配置已迁移到 @/api/http/config，请更新导入路径');
