/**
 * 路由配置文件
 * @description 定义应用的路由规则和导航方法
 * <AUTHOR>
 * @since 2024-01-01
 */

/**
 * 路由路径常量
 */
export const ROUTES = {
  // 认证模块
  AUTH: {
    LOGIN: '/pages/auth/login',
  REGISTER: '/pages/auth/register',
    REAL_AUTH: '/pages/auth/realAuth'
  },
  
  // 用户模块
  USER: {
    SETUP_PROFILE: '/pages/user/setupProfile/setupProfile',
  MINE: '/pages/user/mine/mine',
  INFORMATION: '/pages/user/infomation/infomation',
  PROFILE: '/pages/user/profile/index',
  SETTINGS: '/pages/user/settings/index'
  },
  
  // 主要功能模块
  HOME: '/pages/home/<USER>/home',
  SERVICE: '/pages/service/index/service',
  MATCH: '/pages/match/index/match',
  MESSAGE: '/pages/message/index/message'
} as const

/**
 * 路由类型定义
 */
export type RouteType = typeof ROUTES[keyof typeof ROUTES] | string

/**
 * 导航方法配置
 */
export interface NavigationOptions {
  /** 路径 */
  url: string
  /** 是否关闭所有页面，打开到应用内的某个页面 */
  reLaunch?: boolean
  /** 是否关闭当前页面，跳转到应用内的某个页面 */
  redirectTo?: boolean
  /** 是否跳转到 tabBar 页面 */
  switchTab?: boolean
  /** 传递的参数 */
  params?: Record<string, any>
  /** 成功回调 */
  success?: () => void
  /** 失败回调 */
  fail?: (error: any) => void
}

/**
 * 路由导航工具类
 */
export class RouterService {
  /**
   * 构建完整的路由URL
   * @param path 路由路径
   * @param params 路由参数
   * @returns 完整的URL
   */
  static buildUrl(path: string, params?: Record<string, any>): string {
    if (!params || Object.keys(params).length === 0) {
      return path
    }
    
    const queryString = Object.entries(params)
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
      .join('&')
    
    return `${path}?${queryString}`
  }
  
  /**
   * 页面跳转
   * @param options 导航选项
   */
  static navigateTo(options: NavigationOptions): Promise<void> {
    return new Promise((resolve, reject) => {
      const { url, params, success, fail, ...restOptions } = options
      const finalUrl = this.buildUrl(url, params)
      
      uni.navigateTo({
        url: finalUrl,
        success: (res) => {
          success?.()
          resolve()
        },
        fail: (error) => {
          // console.error('页面跳转失败:', error)
          fail?.(error)
          reject(error)
        },
        ...restOptions
      })
    })
  }
  
  /**
   * 重定向到页面
   * @param options 导航选项
   */
  static redirectTo(options: NavigationOptions): Promise<void> {
    return new Promise((resolve, reject) => {
      const { url, params, success, fail } = options
      const finalUrl = this.buildUrl(url, params)
      
      uni.redirectTo({
        url: finalUrl,
        success: (res) => {
          success?.()
          resolve()
        },
        fail: (error) => {
          // console.error('页面重定向失败:', error)
          fail?.(error)
          reject(error)
        }
      })
    })
  }
  
  /**
   * 重新启动应用
   * @param options 导航选项
   */
  static reLaunch(options: NavigationOptions): Promise<void> {
    return new Promise((resolve, reject) => {
      const { url, params, success, fail } = options
      const finalUrl = this.buildUrl(url, params)
      
      uni.reLaunch({
        url: finalUrl,
        success: (res) => {
          success?.()
          resolve()
        },
        fail: (error) => {
          // console.error('重启应用失败:', error)
          fail?.(error)
          reject(error)
        }
      })
    })
  }
  
  /**
   * 切换到TabBar页面
   * @param options 导航选项
   */
  static switchTab(options: NavigationOptions): Promise<void> {
    return new Promise((resolve, reject) => {
      const { url, success, fail } = options
      
      uni.switchTab({
        url,
        success: (res) => {
          success?.()
          resolve()
        },
        fail: (error) => {
          // console.error('切换TabBar失败:', error)
          fail?.(error)
          reject(error)
        }
      })
    })
  }
  
  /**
   * 返回上一页
   * @param delta 返回的页面数，如果 delta 大于现有页面数，则返回到首页
   */
  static navigateBack(delta: number = 1): Promise<void> {
    return new Promise((resolve, reject) => {
      uni.navigateBack({
        delta,
        success: () => resolve(),
        fail: (error) => {
          // console.error('返回页面失败:', error)
          reject(error)
        }
      })
    })
  }
  
  /**
   * 获取当前页面路径
   */
  static getCurrentPath(): string {
    const pages = getCurrentPages()
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1]
      // @ts-ignore
      return currentPage.route || ''
    }
    return ''
  }
  
  /**
   * 获取当前页面完整URL（包含参数）
   */
  static getCurrentUrl(): string {
    const pages = getCurrentPages()
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1]
      // @ts-ignore
      const route = currentPage.route || ''
      // @ts-ignore
      const options = currentPage.options || {}
      
      if (Object.keys(options).length === 0) {
        return route
      }
      
      const queryString = Object.entries(options)
        .map(([key, value]) => `${key}=${value}`)
        .join('&')
      
      return `${route}?${queryString}`
    }
    return ''
  }
  
  /**
   * 判断是否是TabBar页面
   * @param path 页面路径
   */
  static isTabBarPage(path: string): boolean {
    const tabBarPages = [
      ROUTES.HOME,
      ROUTES.SERVICE,
      ROUTES.MATCH,
      ROUTES.MESSAGE,
      ROUTES.USER.MINE
    ]
    
    return tabBarPages.includes(path as any)
  }
}

/**
 * 路由导航Composable
 */
export const useRouter = () => {
  /**
   * 跳转到登录页
   */
  const goToLogin = () => {
    return RouterService.reLaunch({ url: ROUTES.AUTH.LOGIN })
  }
  
  /**
   * 跳转到注册页
   */
  const goToRegister = () => {
    return RouterService.navigateTo({ url: ROUTES.AUTH.REGISTER })
  }
  
  /**
   * 跳转到首页
   */
  const goToHome = () => {
    return RouterService.switchTab({ url: ROUTES.HOME })
  }
  
  /**
   * 跳转到用户中心
   */
  const goToMine = () => {
    return RouterService.switchTab({ url: ROUTES.USER.MINE })
  }
  
  /**
   * 返回上一页
   */
  const goBack = (delta: number = 1) => {
    return RouterService.navigateBack(delta)
  }
  
  return {
    // 路由常量
    ROUTES,
    
    // 导航方法
    navigateTo: RouterService.navigateTo,
    redirectTo: RouterService.redirectTo,
    reLaunch: RouterService.reLaunch,
    switchTab: RouterService.switchTab,
    navigateBack: RouterService.navigateBack,
    
    // 快捷导航
    goToLogin,
    goToRegister,
    goToHome,
    goToMine,
    goBack,
    
    // 工具方法
    getCurrentPath: RouterService.getCurrentPath,
    getCurrentUrl: RouterService.getCurrentUrl,
    isTabBarPage: RouterService.isTabBarPage,
    buildUrl: RouterService.buildUrl
  }
}

/**
 * 导出默认的路由服务实例
 */
export default RouterService