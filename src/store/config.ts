/**
 * Store 配置文件
 * 定义状态管理的配置选项和工具函数
 */

import type { 
  StoreModuleConfig, 
  PersistConfig, 
  LoadingState, 
  ErrorState 
} from './types'
import { StorageModule, StorageType } from '@/utils/storage/storageService'

/**
 * 默认加载状态
 */
export const DEFAULT_LOADING_STATE: LoadingState = {
  loading: false,
  loadingText: '加载中...',
  loadingType: 'default'
}

/**
 * 默认错误状态
 */
export const DEFAULT_ERROR_STATE: ErrorState = {
  error: '',
  errorCode: '',
  errorType: 'system',
  errorTime: 0
}

/**
 * Store 模块配置
 */
export const STORE_MODULE_CONFIGS: Record<string, StoreModuleConfig> = {
  auth: {
    name: 'auth',
    persist: true,
    persistConfig: {
      key: 'auth',
      storage: 'uni-storage',
      paths: ['token', 'refreshToken', 'userInfo']
    },
    devtools: true
  },
  
  app: {
    name: 'app',
    persist: true,
    persistConfig: {
      key: 'app',
      storage: 'uni-storage',
      paths: ['theme', 'networkType', 'deviceInfo']
    },
    devtools: true
  },
  
  user: {
    name: 'user',
    persist: true,
    persistConfig: {
      key: 'user',
      storage: 'uni-storage',
      paths: ['profileCompleteness', 'realAuthStatus', 'vipStatus']
    },
    devtools: true
  },
  
  chat: {
    name: 'chat',
    persist: true,
    persistConfig: {
      key: 'chat',
      storage: 'uni-storage',
      paths: ['chatList', 'currentChatId']
    },
    devtools: true
  }
}

/**
 * 获取模块配置
 */
export function getModuleConfig(moduleName: string): StoreModuleConfig | null {
  return STORE_MODULE_CONFIGS[moduleName] || null
}

/**
 * 获取持久化配置
 */
export function getPersistConfig(moduleName: string): PersistConfig | null {
  const config = getModuleConfig(moduleName)
  return config?.persistConfig || null
}

/**
 * 检查是否启用持久化
 */
export function isPersistEnabled(moduleName: string): boolean {
  const config = getModuleConfig(moduleName)
  return config?.persist || false
}

/**
 * 获取存储键列表
 */
export function getStorageKeys(moduleName: string): string[] {
  const config = getPersistConfig(moduleName)
  return config?.paths || []
}

/**
 * 获取存储键名
 */
export function getStorageKey(moduleName: string): string {
  const config = getPersistConfig(moduleName)
  return config?.key || moduleName
}

/**
 * Store 工具函数
 */
export class StoreConfigUtils {
  /**
   * 创建标准的异步操作状态
   */
  static createAsyncState<T>(initialData?: T) {
    return {
      data: initialData || null,
      loading: { ...DEFAULT_LOADING_STATE },
      error: { ...DEFAULT_ERROR_STATE },
      lastUpdated: null as number | null
    }
  }
  
  /**
   * 创建分页状态
   */
  static createPaginationState() {
    return {
      page: 1,
      pageSize: 20,
      total: 0,
      hasMore: true,
      loading: false
    }
  }
  
  /**
   * 重置加载状态
   */
  static resetLoadingState(): LoadingState {
    return { ...DEFAULT_LOADING_STATE }
  }
  
  /**
   * 重置错误状态
   */
  static resetErrorState(): ErrorState {
    return { ...DEFAULT_ERROR_STATE }
  }
  
  /**
   * 设置加载状态
   */
  static setLoadingState(loading: boolean, text?: string, type?: 'default' | 'skeleton' | 'spinner'): LoadingState {
    return {
      loading,
      loadingText: text || '加载中...',
      loadingType: type || 'default'
    }
  }
  
  /**
   * 设置错误状态
   */
  static setErrorState(
    error: string, 
    code?: string | number, 
    type?: 'network' | 'auth' | 'validation' | 'system'
  ): ErrorState {
    return {
      error,
      errorCode: code,
      errorType: type || 'system',
      errorTime: Date.now()
    }
  }
  
  /**
   * 验证状态数据完整性
   */
  static validateStateData<T>(data: T, requiredFields: string[]): boolean {
    if (!data || typeof data !== 'object') {
      return false
    }
    
    return requiredFields.every(field => {
      return Object.prototype.hasOwnProperty.call(data, field)
    })
  }
  
  /**
   * 合并状态数据
   */
  static mergeStateData<T extends Record<string, any>>(
    currentState: T, 
    newData: Partial<T>
  ): T {
    return {
      ...currentState,
      ...newData,
      lastUpdated: Date.now()
    }
  }
}

/**
 * Store 常量
 */
export const STORE_CONSTANTS = {
  // 默认超时时间
  DEFAULT_TIMEOUT: 10000,
  
  // 重试次数
  MAX_RETRY_COUNT: 3,
  
  // 缓存时间
  CACHE_TTL: {
    SHORT: 5 * 60 * 1000,      // 5分钟
    MEDIUM: 30 * 60 * 1000,    // 30分钟
    LONG: 2 * 60 * 60 * 1000,  // 2小时
    VERY_LONG: 24 * 60 * 60 * 1000 // 24小时
  },
  
  // 存储键前缀
  STORAGE_PREFIX: 'oto_store_',
  
  // 版本号
  VERSION: '1.0.0'
} as const