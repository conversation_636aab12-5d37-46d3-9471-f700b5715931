/**
 * 状态管理类型定义
 */
import type { UserInfo } from '@/api/auth/auth'
import type { AuthPreferences } from '@/utils/storage/adapters/AuthStorageAdapter'

// ==================== 基础状态类型 ====================

/** 加载状态 */
export interface LoadingState {
  /** 是否正在加载 */
  loading: boolean
  /** 加载文本 */
  loadingText?: string
  /** 加载类型 */
  loadingType?: 'default' | 'skeleton' | 'spinner'
}

/** 错误状态 */
export interface ErrorState {
  /** 错误信息 */
  error: string
  /** 错误代码 */
  errorCode?: string | number
  /** 错误类型 */
  errorType?: 'network' | 'auth' | 'validation' | 'system'
  /** 错误时间戳 */
  errorTime?: number
}

/** 分页状态 */
export interface PaginationState {
  /** 当前页码 */
  current: number
  /** 每页大小 */
  pageSize: number
  /** 总数量 */
  total: number
  /** 是否有更多数据 */
  hasMore: boolean
  /** 是否正在加载更多 */
  loadingMore: boolean
}

// ==================== 认证状态类型 ====================

/** 认证状态 */
export interface AuthState {
  /** 访问令牌 */
  token: string
  /** 刷新令牌 */
  refreshToken: string
  /** 用户信息 */
  userInfo: UserInfo | null
  /** 用户偏好设置 */
  preferences: AuthPreferences
  /** 是否已登录 */
  isLoggedIn: boolean
  /** 用户头像URL */
  userAvatar: string
  /** 用户昵称 */
  userNickname: string
  /** 用户ID */
  userId: string
  /** 是否是VIP用户 */
  isVip: boolean
}

/** 认证操作选项 */
export interface AuthOptions {
  /** 过期时间（秒） */
  expiresInSeconds?: number
  /** 设备ID */
  deviceId?: string
  /** 登录IP */
  loginIp?: string
  /** 记住登录 */
  rememberLogin?: boolean
}

/** 令牌验证结果 */
export interface TokenValidation {
  /** 是否有效 */
  isValid: boolean
  /** 剩余时间（秒） */
  remainingTime: number
  /** 是否即将过期 */
  isExpiringSoon: boolean
  /** 过期时间戳 */
  expiresAt?: number
}

// ==================== 应用状态类型 ====================

/** 主题类型 */
export type ThemeType = 'light' | 'dark' | 'auto'

/** 网络类型 */
export type NetworkType = 'wifi' | '2g' | '3g' | '4g' | '5g' | 'ethernet' | 'unknown' | 'none'

/** 应用状态 */
export interface AppState {
  /** 全局加载状态 */
  loading: boolean
  /** 主题模式 */
  theme: ThemeType
  /** 网络类型 */
  networkType: NetworkType
  /** 应用版本 */
  version: string
  /** 是否首次启动 */
  isFirstLaunch: boolean
  /** 语言设置 */
  locale: string
  /** 设备信息 */
  deviceInfo: DeviceInfo
}

/** 设备信息 */
export interface DeviceInfo {
  /** 设备型号 */
  model: string
  /** 操作系统 */
  platform: string
  /** 系统版本 */
  system: string
  /** 屏幕宽度 */
  screenWidth: number
  /** 屏幕高度 */
  screenHeight: number
  /** 状态栏高度 */
  statusBarHeight: number
  /** 安全区域 */
  safeArea: SafeArea
}

/** 安全区域 */
export interface SafeArea {
  /** 顶部安全距离 */
  top: number
  /** 底部安全距离 */
  bottom: number
  /** 左侧安全距离 */
  left: number
  /** 右侧安全距离 */
  right: number
}

// ==================== 业务状态类型 ====================

/** 用户状态 */
export interface UserState {
  /** 用户资料完整度 */
  profileCompleteness: number
  /** 实名认证状态 */
  realAuthStatus: 'none' | 'pending' | 'approved' | 'rejected'
  /** VIP状态 */
  vipStatus: VipStatus
  /** 消息未读数 */
  unreadCount: number
  /** 最后活跃时间 */
  lastActiveTime: number
}

/** VIP状态 */
export interface VipStatus {
  /** 是否是VIP */
  isVip: boolean
  /** VIP等级 */
  level: number
  /** VIP过期时间 */
  expiresAt?: number
  /** VIP权益列表 */
  benefits: string[]
}

/** 聊天状态 */
export interface ChatState {
  /** 当前聊天对象ID */
  currentChatId: string
  /** 聊天列表 */
  chatList: ChatItem[]
  /** 消息列表 */
  messageList: Message[]
  /** 正在输入状态 */
  isTyping: boolean
  /** 连接状态 */
  connectionStatus: 'connected' | 'connecting' | 'disconnected'
}

/** 聊天项 */
export interface ChatItem {
  /** 聊天ID */
  id: string
  /** 对方用户ID */
  userId: string
  /** 对方昵称 */
  nickname: string
  /** 对方头像 */
  avatar: string
  /** 最后一条消息 */
  lastMessage: string
  /** 最后消息时间 */
  lastMessageTime: number
  /** 未读数量 */
  unreadCount: number
  /** 是否置顶 */
  isPinned: boolean
}

/** 消息 */
export interface Message {
  /** 消息ID */
  id: string
  /** 发送者ID */
  senderId: string
  /** 接收者ID */
  receiverId: string
  /** 消息内容 */
  content: string
  /** 消息类型 */
  type: 'text' | 'image' | 'voice' | 'video' | 'file' | 'system'
  /** 发送时间 */
  timestamp: number
  /** 消息状态 */
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed'
  /** 是否撤回 */
  isRecalled: boolean
}

// ==================== Store 配置类型 ====================

/** Store 模块配置 */
export interface StoreModuleConfig {
  /** 模块名称 */
  name: string
  /** 是否持久化 */
  persist?: boolean
  /** 持久化配置 */
  persistConfig?: PersistConfig
  /** 是否启用开发工具 */
  devtools?: boolean
}

/** 持久化配置 */
export interface PersistConfig {
  /** 存储键名 */
  key?: string
  /** 存储类型 */
  storage?: 'localStorage' | 'sessionStorage' | 'uni-storage'
  /** 需要持久化的状态路径 */
  paths?: string[]
  /** 序列化函数 */
  serializer?: {
    serialize: (value: any) => string
    deserialize: (value: string) => any
  }
}

// ==================== Store 操作类型 ====================

/** Store 操作结果 */
export interface StoreActionResult<T = any> {
  /** 是否成功 */
  success: boolean
  /** 返回数据 */
  data?: T
  /** 错误信息 */
  error?: string
  /** 错误代码 */
  errorCode?: string | number
}

/** 异步操作状态 */
export interface AsyncActionState {
  /** 是否正在执行 */
  pending: boolean
  /** 执行结果 */
  fulfilled: boolean
  /** 是否被拒绝 */
  rejected: boolean
  /** 错误信息 */
  error?: string
}

// ==================== 导出所有类型 ====================
// 所有类型已通过 export interface 直接导出