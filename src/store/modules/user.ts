/**
 * 用户状态管理
 * 管理用户相关的业务状态和操作
 */
import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import type { VipStatus } from '../types'
import { StoreUtil } from '@/utils/storage/StoreUtil'
import { StorageModule } from '@/utils/storage'

export const useUserStore = defineStore('user', () => {
  // ==================== 响应式状态 ====================
  
  /** 用户资料完整度 */
  const profileCompleteness = ref<number>(0)
  
  /** 实名认证状态 */
  const realAuthStatus = ref<'none' | 'pending' | 'approved' | 'rejected'>('none')
  
  /** VIP状态 */
  const vipStatus = ref<VipStatus>({
    isVip: false,
    level: 0,
    benefits: []
  })
  
  /** 消息未读数 */
  const unreadCount = ref<number>(0)
  
  /** 最后活跃时间 */
  const lastActiveTime = ref<number>(Date.now())
  
  /** 加载状态 */
  const loading = ref<boolean>(false)
  
  /** 错误信息 */
  const error = ref<string>('')
  
  /** 用户偏好设置 */
  const preferences = ref({
    /** 推送通知 */
    pushNotification: true,
    /** 声音提醒 */
    soundAlert: true,
    /** 震动提醒 */
    vibrationAlert: true,
    /** 隐私模式 */
    privateMode: false,
    /** 自动播放视频 */
    autoPlayVideo: true,
    /** 流量保护 */
    dataProtection: false
  })

  // ==================== 计算属性 ====================

  /** 是否已实名认证 */
  const isRealAuthenticated = computed(() => realAuthStatus.value === 'approved')
  
  /** 是否VIP用户 */
  const isVip = computed(() => vipStatus.value.isVip)
  
  /** VIP是否过期 */
  const isVipExpired = computed(() => {
    if (!vipStatus.value.isVip || !vipStatus.value.expiresAt) return false
    return Date.now() > vipStatus.value.expiresAt
  })
  
  /** 资料是否完整 */
  const isProfileComplete = computed(() => profileCompleteness.value >= 80)
  
  /** 是否有未读消息 */
  const hasUnreadMessages = computed(() => unreadCount.value > 0)
  
  /** 用户状态摘要 */
  const userStatusSummary = computed(() => ({
    profileCompleteness: profileCompleteness.value,
    realAuthStatus: realAuthStatus.value,
    isVip: isVip.value,
    vipLevel: vipStatus.value.level,
    unreadCount: unreadCount.value,
    isProfileComplete: isProfileComplete.value,
    isRealAuthenticated: isRealAuthenticated.value
  }))

  // ==================== 内部工具方法 ====================

  /**
   * 清除错误状态
   */
  const clearError = () => {
    error.value = ''
  }

  /**
   * 设置加载状态
   */
  const setLoading = (isLoading: boolean) => {
    loading.value = isLoading
  }

  /**
   * 设置错误信息
   */
  const setError = (errorMessage: string) => {
    error.value = errorMessage
    // console.error('❌ [UserStore] 错误:', errorMessage)
  }

  // ==================== 用户资料管理 ====================

  /**
   * 更新用户资料完整度
   */
  const updateProfileCompleteness = async (completeness: number) => {
    try {
      if (completeness < 0 || completeness > 100) {
        throw new Error('资料完整度必须在0-100之间')
      }
      
      profileCompleteness.value = completeness
      
      // 持久化到本地存储
      await StoreUtil.set('profileCompleteness', completeness, StorageModule.AUTH)
      

    } catch (err: any) {
      setError(`更新资料完整度失败: ${err.message}`)
      throw err
    }
  }

  /**
   * 更新实名认证状态
   */
  const updateRealAuthStatus = async (status: 'none' | 'pending' | 'approved' | 'rejected') => {
    try {
      realAuthStatus.value = status
      
      // 持久化到本地存储
      await StoreUtil.set('realAuthStatus', status, StorageModule.AUTH)
      
      // console.log('✅ [UserStore] 实名认证状态更新成功:', status)
    } catch (err: any) {
      setError(`更新实名认证状态失败: ${err.message}`)
      throw err
    }
  }

  // ==================== VIP状态管理 ====================

  /**
   * 更新VIP状态
   */
  const updateVipStatus = async (newVipStatus: Partial<VipStatus>) => {
    try {
      vipStatus.value = { ...vipStatus.value, ...newVipStatus }
      
      // 持久化到本地存储
      await StoreUtil.set('vipStatus', vipStatus.value, StorageModule.AUTH)
      
      // console.log('✅ [UserStore] VIP状态更新成功:', vipStatus.value)
    } catch (err: any) {
      setError(`更新VIP状态失败: ${err.message}`)
      throw err
    }
  }

  /**
   * 检查VIP状态
   */
  const checkVipStatus = () => {
    if (vipStatus.value.isVip && vipStatus.value.expiresAt) {
      const now = Date.now()
      if (now > vipStatus.value.expiresAt) {
        // VIP已过期，更新状态
        updateVipStatus({
          isVip: false,
          level: 0,
          expiresAt: undefined,
          benefits: []
        })
        // console.log('⚠️ [UserStore] VIP已过期，状态已更新')
      }
    }
  }

  // ==================== 消息管理 ====================

  /**
   * 更新未读消息数
   */
  const updateUnreadCount = async (count: number) => {
    try {
      if (count < 0) {
        throw new Error('未读消息数不能为负数')
      }
      
      unreadCount.value = count
      
      // 持久化到本地存储
      await StoreUtil.set('unreadCount', count, StorageModule.AUTH)
      
      // console.log('✅ [UserStore] 未读消息数更新成功:', count)
    } catch (err: any) {
      setError(`更新未读消息数失败: ${err.message}`)
      throw err
    }
  }

  /**
   * 增加未读消息数
   */
  const incrementUnreadCount = async (increment: number = 1) => {
    await updateUnreadCount(unreadCount.value + increment)
  }

  /**
   * 清除未读消息数
   */
  const clearUnreadCount = async () => {
    await updateUnreadCount(0)
  }

  // ==================== 活跃时间管理 ====================

  /**
   * 更新最后活跃时间
   */
  const updateLastActiveTime = async (timestamp?: number) => {
    try {
      const activeTime = timestamp || Date.now()
      lastActiveTime.value = activeTime
      
      // 持久化到本地存储
      await StoreUtil.set('lastActiveTime', activeTime, StorageModule.AUTH)
      
      // console.log('✅ [UserStore] 最后活跃时间更新成功:', new Date(activeTime).toLocaleString())
    } catch (err: any) {
      setError(`更新最后活跃时间失败: ${err.message}`)
      throw err
    }
  }

  /**
   * 获取离线时长（分钟）
   */
  const getOfflineDuration = (): number => {
    const now = Date.now()
    const duration = now - lastActiveTime.value
    return Math.floor(duration / (1000 * 60)) // 转换为分钟
  }

  // ==================== 用户偏好设置 ====================

  /**
   * 更新用户偏好设置
   */
  const updatePreferences = async (newPreferences: Partial<typeof preferences.value>) => {
    try {
      setLoading(true)
      clearError()
      
      preferences.value = { ...preferences.value, ...newPreferences }
      
      // 持久化到本地存储
      await StoreUtil.set('userPreferences', preferences.value, StorageModule.AUTH)
      
      // console.log('✅ [UserStore] 用户偏好设置更新成功:', newPreferences)
    } catch (err: any) {
      setError(`更新用户偏好设置失败: ${err.message}`)
      throw err
    } finally {
      setLoading(false)
    }
  }

  /**
   * 重置用户偏好设置
   */
  const resetPreferences = async () => {
    const defaultPreferences = {
      pushNotification: true,
      soundAlert: true,
      vibrationAlert: true,
      privateMode: false,
      autoPlayVideo: true,
      dataProtection: false
    }
    
    await updatePreferences(defaultPreferences)
  }

  // ==================== 数据恢复和初始化 ====================

  /**
   * 从本地存储恢复用户状态
   */
  const restoreUserState = async () => {
    try {
      setLoading(true)
      clearError()
      
      // 恢复各项状态
      const storedCompleteness = await StoreUtil.get('profileCompleteness', StorageModule.AUTH)
      if (storedCompleteness !== null) {
        profileCompleteness.value = storedCompleteness
      }
      
      const storedAuthStatus = await StoreUtil.get('realAuthStatus', StorageModule.AUTH)
      if (storedAuthStatus) {
        realAuthStatus.value = storedAuthStatus
      }
      
      const storedVipStatus = await StoreUtil.get('vipStatus', StorageModule.AUTH)
      if (storedVipStatus) {
        vipStatus.value = storedVipStatus
        // 检查VIP是否过期
        checkVipStatus()
      }
      
      const storedUnreadCount = await StoreUtil.get('unreadCount', StorageModule.AUTH)
      if (storedUnreadCount !== null) {
        unreadCount.value = storedUnreadCount
      }
      
      const storedActiveTime = await StoreUtil.get('lastActiveTime', StorageModule.AUTH)
      if (storedActiveTime) {
        lastActiveTime.value = storedActiveTime
      }
      
      const storedPreferences = await StoreUtil.get('userPreferences', StorageModule.AUTH)
      if (storedPreferences) {
        preferences.value = { ...preferences.value, ...storedPreferences }
      }
      
      // console.log('✅ [UserStore] 用户状态恢复成功')
    } catch (err: any) {
      setError(`恢复用户状态失败: ${err.message}`)
      // console.error('❌ [UserStore] 恢复用户状态失败:', err)
    } finally {
      setLoading(false)
    }
  }

  /**
   * 清除所有用户状态
   */
  const clearUserState = async () => {
    try {
      setLoading(true)
      
      // 重置所有状态
      profileCompleteness.value = 0
      realAuthStatus.value = 'none'
      vipStatus.value = {
        isVip: false,
        level: 0,
        benefits: []
      }
      unreadCount.value = 0
      lastActiveTime.value = Date.now()
      
      // 清除本地存储
      await StoreUtil.remove('profileCompleteness', StorageModule.AUTH)
      await StoreUtil.remove('realAuthStatus', StorageModule.AUTH)
      await StoreUtil.remove('vipStatus', StorageModule.AUTH)
      await StoreUtil.remove('unreadCount', StorageModule.AUTH)
      await StoreUtil.remove('lastActiveTime', StorageModule.AUTH)
      await StoreUtil.remove('userPreferences', StorageModule.AUTH)
      
      // console.log('✅ [UserStore] 用户状态清除成功')
    } catch (err: any) {
      setError(`清除用户状态失败: ${err.message}`)
      throw err
    } finally {
      setLoading(false)
    }
  }

  /**
   * 初始化用户状态
   */
  const initUserState = async () => {
    await restoreUserState()
    await updateLastActiveTime()
  }

  // ==================== 存储导出 ====================

  return {
    // ==================== 状态 ====================
    profileCompleteness: readonly(profileCompleteness),
    realAuthStatus: readonly(realAuthStatus),
    vipStatus: readonly(vipStatus),
    unreadCount: readonly(unreadCount),
    lastActiveTime: readonly(lastActiveTime),
    loading: readonly(loading),
    error: readonly(error),
    preferences: readonly(preferences),
    
    // ==================== 计算属性 ====================
    isRealAuthenticated,
    isVip,
    isVipExpired,
    isProfileComplete,
    hasUnreadMessages,
    userStatusSummary,
    
    // ==================== 用户资料管理 ====================
    updateProfileCompleteness,
    updateRealAuthStatus,
    
    // ==================== VIP状态管理 ====================
    updateVipStatus,
    checkVipStatus,
    
    // ==================== 消息管理 ====================
    updateUnreadCount,
    incrementUnreadCount,
    clearUnreadCount,
    
    // ==================== 活跃时间管理 ====================
    updateLastActiveTime,
    getOfflineDuration,
    
    // ==================== 偏好设置 ====================
    updatePreferences,
    resetPreferences,
    
    // ==================== 数据管理 ====================
    restoreUserState,
    clearUserState,
    initUserState,
    clearError
  }
})