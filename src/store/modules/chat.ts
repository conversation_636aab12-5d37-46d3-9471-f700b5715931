/**
 * 聊天状态管理
 * 管理聊天列表、消息列表和聊天相关状态
 */
import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import type { ChatItem, Message } from '../types'
import { StoreUtil } from '@/utils/storage/StoreUtil'
import { StorageModule } from '@/utils/storage'

export const useChatStore = defineStore('chat', () => {
  // ==================== 响应式状态 ====================
  
  /** 当前聊天对象ID */
  const currentChatId = ref<string>('')
  
  /** 聊天列表 */
  const chatList = ref<ChatItem[]>([])
  
  /** 当前聊天的消息列表 */
  const messageList = ref<Message[]>([])
  
  /** 正在输入状态 */
  const isTyping = ref<boolean>(false)
  
  /** 连接状态 */
  const connectionStatus = ref<'connected' | 'connecting' | 'disconnected'>('disconnected')
  
  /** 加载状态 */
  const loading = ref<boolean>(false)
  
  /** 错误信息 */
  const error = ref<string>('')
  
  /** 消息发送状态 */
  const sendingMessages = ref<Set<string>>(new Set())
  
  /** 消息分页状态 */
  const messagePagination = ref({
    hasMore: true,
    loading: false,
    page: 1,
    pageSize: 20
  })

  // ==================== 计算属性 ====================

  /** 当前聊天信息 */
  const currentChat = computed(() => {
    return chatList.value.find(chat => chat.id === currentChatId.value) || null
  })
  
  /** 总未读消息数 */
  const totalUnreadCount = computed(() => {
    return chatList.value.reduce((total, chat) => total + chat.unreadCount, 0)
  })
  
  /** 置顶聊天列表 */
  const pinnedChats = computed(() => {
    return chatList.value.filter(chat => chat.isPinned)
  })
  
  /** 普通聊天列表 */
  const normalChats = computed(() => {
    return chatList.value.filter(chat => !chat.isPinned)
  })
  
  /** 排序后的聊天列表 */
  const sortedChatList = computed(() => {
    const pinned = pinnedChats.value.sort((a, b) => b.lastMessageTime - a.lastMessageTime)
    const normal = normalChats.value.sort((a, b) => b.lastMessageTime - a.lastMessageTime)
    return [...pinned, ...normal]
  })
  
  /** 是否已连接 */
  const isConnected = computed(() => connectionStatus.value === 'connected')
  
  /** 当前聊天的未读消息数 */
  const currentChatUnreadCount = computed(() => {
    return currentChat.value?.unreadCount || 0
  })

  // ==================== 内部工具方法 ====================

  /**
   * 清除错误状态
   */
  const clearError = () => {
    error.value = ''
  }

  /**
   * 设置加载状态
   */
  const setLoading = (isLoading: boolean) => {
    loading.value = isLoading
  }

  /**
   * 设置错误信息
   */
  const setError = (errorMessage: string) => {
    error.value = errorMessage
    // console.error('❌ [ChatStore] 错误:', errorMessage)
  }

  /**
   * 生成消息ID
   */
  const generateMessageId = (): string => {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 生成聊天ID
   */
  const generateChatId = (userId: string): string => {
    return `chat_${userId}_${Date.now()}`
  }

  // ==================== 聊天列表管理 ====================

  /**
   * 设置聊天列表
   */
  const setChatList = async (chats: ChatItem[]) => {
    try {
      chatList.value = chats
      
      // 持久化到本地存储
      await StoreUtil.set('chatList', chats, StorageModule.AUTH)
      
      // console.log('✅ [ChatStore] 聊天列表设置成功，共', chats.length, '个聊天')
    } catch (err: any) {
      setError(`设置聊天列表失败: ${err.message}`)
      throw err
    }
  }

  /**
   * 添加或更新聊天项
   */
  const upsertChatItem = async (chatItem: ChatItem) => {
    try {
      const existingIndex = chatList.value.findIndex(chat => chat.id === chatItem.id)
      
      if (existingIndex >= 0) {
        // 更新现有聊天
        chatList.value[existingIndex] = { ...chatList.value[existingIndex], ...chatItem }
      } else {
        // 添加新聊天
        chatList.value.unshift(chatItem)
      }
      
      // 持久化到本地存储
      await StoreUtil.set('chatList', chatList.value, StorageModule.AUTH)
      
      // console.log('✅ [ChatStore] 聊天项更新成功:', chatItem.id)
    } catch (err: any) {
      setError(`更新聊天项失败: ${err.message}`)
      throw err
    }
  }

  /**
   * 删除聊天项
   */
  const removeChatItem = async (chatId: string) => {
    try {
      const index = chatList.value.findIndex(chat => chat.id === chatId)
      
      if (index >= 0) {
        chatList.value.splice(index, 1)
        
        // 如果删除的是当前聊天，清空当前聊天
        if (currentChatId.value === chatId) {
          currentChatId.value = ''
          messageList.value = []
        }
        
        // 持久化到本地存储
        await StoreUtil.set('chatList', chatList.value, StorageModule.AUTH)
        
        // console.log('✅ [ChatStore] 聊天项删除成功:', chatId)
      }
    } catch (err: any) {
      setError(`删除聊天项失败: ${err.message}`)
      throw err
    }
  }

  /**
   * 置顶/取消置顶聊天
   */
  const toggleChatPin = async (chatId: string) => {
    try {
      const chat = chatList.value.find(c => c.id === chatId)
      
      if (chat) {
        chat.isPinned = !chat.isPinned
        
        // 持久化到本地存储
        await StoreUtil.set('chatList', chatList.value, StorageModule.AUTH)
        
        // console.log('✅ [ChatStore] 聊天置顶状态切换成功:', chatId, chat.isPinned)
      }
    } catch (err: any) {
      setError(`切换聊天置顶状态失败: ${err.message}`)
      throw err
    }
  }

  /**
   * 清除聊天未读数
   */
  const clearChatUnreadCount = async (chatId: string) => {
    try {
      const chat = chatList.value.find(c => c.id === chatId)
      
      if (chat && chat.unreadCount > 0) {
        chat.unreadCount = 0
        
        // 持久化到本地存储
        await StoreUtil.set('chatList', chatList.value, StorageModule.AUTH)
        
        // console.log('✅ [ChatStore] 聊天未读数清除成功:', chatId)
      }
    } catch (err: any) {
      setError(`清除聊天未读数失败: ${err.message}`)
      throw err
    }
  }

  // ==================== 当前聊天管理 ====================

  /**
   * 设置当前聊天
   */
  const setCurrentChat = async (chatId: string) => {
    try {
      currentChatId.value = chatId
      
      // 清除当前聊天的未读数
      if (chatId) {
        await clearChatUnreadCount(chatId)
      }
      
      // 清空消息列表，准备加载新的消息
      messageList.value = []
      messagePagination.value = {
        hasMore: true,
        loading: false,
        page: 1,
        pageSize: 20
      }
      
      // console.log('✅ [ChatStore] 当前聊天设置成功:', chatId)
    } catch (err: any) {
      setError(`设置当前聊天失败: ${err.message}`)
      throw err
    }
  }

  /**
   * 清除当前聊天
   */
  const clearCurrentChat = () => {
    currentChatId.value = ''
    messageList.value = []
    messagePagination.value = {
      hasMore: true,
      loading: false,
      page: 1,
      pageSize: 20
    }
  }

  // ==================== 消息管理 ====================

  /**
   * 设置消息列表
   */
  const setMessageList = (messages: Message[]) => {
    messageList.value = messages
    // console.log('✅ [ChatStore] 消息列表设置成功，共', messages.length, '条消息')
  }

  /**
   * 添加消息到列表
   */
  const addMessage = (message: Message) => {
    // 检查消息是否已存在
    const existingIndex = messageList.value.findIndex(msg => msg.id === message.id)
    
    if (existingIndex >= 0) {
      // 更新现有消息
      messageList.value[existingIndex] = message
    } else {
      // 添加新消息
      messageList.value.push(message)
    }
    
    // console.log('✅ [ChatStore] 消息添加成功:', message.id)
  }

  /**
   * 批量添加消息（用于分页加载）
   */
  const addMessages = (messages: Message[], prepend: boolean = false) => {
    if (prepend) {
      // 添加到列表开头（历史消息）
      messageList.value.unshift(...messages)
    } else {
      // 添加到列表末尾（新消息）
      messageList.value.push(...messages)
    }
    
    // console.log('✅ [ChatStore] 批量消息添加成功，共', messages.length, '条消息')
  }

  /**
   * 发送消息
   */
  const sendMessage = async (content: string, type: Message['type'] = 'text') => {
    try {
      if (!currentChatId.value) {
        throw new Error('未选择聊天对象')
      }
      
      if (!content.trim()) {
        throw new Error('消息内容不能为空')
      }
      
      const messageId = generateMessageId()
      const currentChat = chatList.value.find(chat => chat.id === currentChatId.value)
      
      if (!currentChat) {
        throw new Error('聊天对象不存在')
      }
      
      // 创建消息对象
      const message: Message = {
        id: messageId,
        senderId: '', // 需要从用户状态获取
        receiverId: currentChat.userId,
        content,
        type,
        timestamp: Date.now(),
        status: 'sending',
        isRecalled: false
      }
      
      // 添加到发送中列表
      sendingMessages.value.add(messageId)
      
      // 添加到消息列表
      addMessage(message)
      
      // 更新聊天列表中的最后消息
      currentChat.lastMessage = content
      currentChat.lastMessageTime = message.timestamp
      
      // console.log('✅ [ChatStore] 消息发送中:', messageId)
      
      // 这里应该调用API发送消息
      // 模拟发送成功
      setTimeout(() => {
        updateMessageStatus(messageId, 'sent')
        sendingMessages.value.delete(messageId)
      }, 1000)
      
      return messageId
    } catch (err: any) {
      setError(`发送消息失败: ${err.message}`)
      throw err
    }
  }

  /**
   * 更新消息状态
   */
  const updateMessageStatus = (messageId: string, status: Message['status']) => {
    const message = messageList.value.find(msg => msg.id === messageId)
    
    if (message) {
      message.status = status
      // console.log('✅ [ChatStore] 消息状态更新成功:', messageId, status)
    }
  }

  /**
   * 撤回消息
   */
  const recallMessage = async (messageId: string) => {
    try {
      const message = messageList.value.find(msg => msg.id === messageId)
      
      if (!message) {
        throw new Error('消息不存在')
      }
      
      if (message.isRecalled) {
        throw new Error('消息已被撤回')
      }
      
      // 检查撤回时间限制（2分钟内）
      const now = Date.now()
      const timeDiff = now - message.timestamp
      if (timeDiff > 2 * 60 * 1000) {
        throw new Error('超过撤回时间限制')
      }
      
      message.isRecalled = true
      message.content = '[消息已撤回]'
      
      // console.log('✅ [ChatStore] 消息撤回成功:', messageId)
    } catch (err: any) {
      setError(`撤回消息失败: ${err.message}`)
      throw err
    }
  }

  // ==================== 连接状态管理 ====================

  /**
   * 设置连接状态
   */
  const setConnectionStatus = (status: 'connected' | 'connecting' | 'disconnected') => {
    connectionStatus.value = status
    // console.log('✅ [ChatStore] 连接状态更新:', status)
  }

  /**
   * 设置输入状态
   */
  const setTypingStatus = (typing: boolean) => {
    isTyping.value = typing
  }

  // ==================== 数据恢复和初始化 ====================

  /**
   * 从本地存储恢复聊天状态
   */
  const restoreChatState = async () => {
    try {
      setLoading(true)
      clearError()
      
      // 恢复聊天列表
      const storedChatList = await StoreUtil.get('chatList', StorageModule.AUTH)
      if (storedChatList && Array.isArray(storedChatList)) {
        chatList.value = storedChatList
      }
      
      // console.log('✅ [ChatStore] 聊天状态恢复成功')
    } catch (err: any) {
      setError(`恢复聊天状态失败: ${err.message}`)
      // console.error('❌ [ChatStore] 恢复聊天状态失败:', err)
    } finally {
      setLoading(false)
    }
  }

  /**
   * 清除所有聊天状态
   */
  const clearChatState = async () => {
    try {
      setLoading(true)
      
      // 重置所有状态
      currentChatId.value = ''
      chatList.value = []
      messageList.value = []
      isTyping.value = false
      connectionStatus.value = 'disconnected'
      sendingMessages.value.clear()
      messagePagination.value = {
        hasMore: true,
        loading: false,
        page: 1,
        pageSize: 20
      }
      
      // 清除本地存储
      await StoreUtil.remove('chatList', StorageModule.AUTH)
      
      // console.log('✅ [ChatStore] 聊天状态清除成功')
    } catch (err: any) {
      setError(`清除聊天状态失败: ${err.message}`)
      throw err
    } finally {
      setLoading(false)
    }
  }

  /**
   * 初始化聊天状态
   */
  const initChatState = async () => {
    await restoreChatState()
  }

  // ==================== 存储导出 ====================

  return {
    // ==================== 状态 ====================
    currentChatId: readonly(currentChatId),
    chatList: readonly(chatList),
    messageList: readonly(messageList),
    isTyping: readonly(isTyping),
    connectionStatus: readonly(connectionStatus),
    loading: readonly(loading),
    error: readonly(error),
    sendingMessages: readonly(sendingMessages),
    messagePagination: readonly(messagePagination),
    
    // ==================== 计算属性 ====================
    currentChat,
    totalUnreadCount,
    pinnedChats,
    normalChats,
    sortedChatList,
    isConnected,
    currentChatUnreadCount,
    
    // ==================== 聊天列表管理 ====================
    setChatList,
    upsertChatItem,
    removeChatItem,
    toggleChatPin,
    clearChatUnreadCount,
    
    // ==================== 当前聊天管理 ====================
    setCurrentChat,
    clearCurrentChat,
    
    // ==================== 消息管理 ====================
    setMessageList,
    addMessage,
    addMessages,
    sendMessage,
    updateMessageStatus,
    recallMessage,
    
    // ==================== 连接状态管理 ====================
    setConnectionStatus,
    setTypingStatus,
    
    // ==================== 数据管理 ====================
    restoreChatState,
    clearChatState,
    initChatState,
    clearError
  }
})