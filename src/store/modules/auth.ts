/**
 * 认证状态管理 - 统一存储版本
 * 使用统一存储适配器确保数据安全和性能
 */
import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { authStorage, type AuthPreferences } from '@/utils/storage/adapters/AuthStorageAdapter'
import type { UserInfo } from '@/api/auth/auth'
import { EnhancedRouteGuard } from '@/router/guard/EnhancedRouteGuard'

export const useAuthStore = defineStore('auth', () => {
  // ==================== 响应式状态 ====================
  
  /** 访问令牌 */
  const token = ref<string>('')
  
  /** 刷新令牌 */
  const refreshToken = ref<string>('')
  
  /** 用户信息 */
  const userInfo = ref<UserInfo | null>(null)
  
  /** 加载状态 */
  const loading = ref<boolean>(false)
  
  /** 错误信息 */
  const error = ref<string>('')
  
  /** 用户偏好设置 */
  const preferences = ref<AuthPreferences>({
    rememberLogin: false,
    autoLogin: false,
    biometricLogin: false,
    trustedDevice: false
  })

  // ==================== 计算属性 ====================

  /** 是否已登录 */
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)
  
  /** 用户头像URL */
  const userAvatar = computed(() => userInfo.value?.avatar || '')
  
  /** 用户昵称 */
  const userNickname = computed(() => {
    if (!userInfo.value) return '未知用户'
    return userInfo.value.nickname || userInfo.value.phonenumber || '未知用户'
  })
  
  /** 用户ID */
  const userId = computed(() => userInfo.value?.id || '')
  
  /** 是否是VIP用户 */
  const isVip = computed(() => {
    // 如果UserInfo类型中有isVip字段则使用，否则返回false
    return (userInfo.value as any)?.isVip || false
  })

  // ==================== 内部工具方法 ====================

  /**
   * 清除错误状态
   */
  const clearError = () => {
    error.value = ''
  }

  /**
   * 设置加载状态
   */
  const setLoading = (isLoading: boolean) => {
    loading.value = isLoading
  }

  /**
   * 设置错误信息
   */
  const setError = (errorMessage: string) => {
    error.value = errorMessage
    // console.error('❌ [AuthStore] 错误:', errorMessage)
  }

  // ==================== 核心认证方法 ====================

  /**
   * 设置认证信息
   * @param authData 认证数据
   * @param options 可选配置
   */
  const setAuth = async (
    authData: {
      token: string
      refreshToken?: string
      userInfo: UserInfo
    },
    options: {
      expiresInSeconds?: number
      deviceId?: string
      loginIp?: string
      rememberLogin?: boolean
    } = {}
  ) => {
    try {
      setLoading(true)
      clearError()

      // 验证必需的认证数据
      if (!authData.token) {
        throw new Error('token不能为空')
      }
      
      if (!authData.userInfo) {
        throw new Error('userInfo不能为空')
      }
      
      if (!authData.userInfo.id) {
        throw new Error('userInfo.id不能为空')
      }
      
      // console.log('🔍 [AuthStore] 接收到的认证数据:', {
        //   hasToken: !!authData.token,
        //   hasRefreshToken: !!authData.refreshToken,
        //   hasUserInfo: !!authData.userInfo,
        //   userId: authData.userInfo?.id,
        //   userInfo: authData.userInfo
        // })

      // 更新本地状态
      token.value = authData.token
      refreshToken.value = authData.refreshToken || ''
      userInfo.value = authData.userInfo

      // 准备用户偏好设置
      const userPreferences: Partial<AuthPreferences> = {}
      if (options.rememberLogin !== undefined) {
        userPreferences.rememberLogin = options.rememberLogin
      }

      // 使用统一存储适配器保存完整认证状态
      const success = await authStorage.saveAuthState(
        authData.token,
        authData.userInfo,
        {
          refreshToken: authData.refreshToken,
          expiresInSeconds: options.expiresInSeconds,
          deviceId: options.deviceId,
          loginIp: options.loginIp,
          preferences: userPreferences
        }
      )

      if (success) {
        // 更新本地偏好设置
        if (Object.keys(userPreferences).length > 0) {
          preferences.value = { ...preferences.value, ...userPreferences }
        }

        // console.log('✅ [AuthStore] 认证信息设置成功', {
        //   userId: authData.userInfo.id,
        //   hasRefreshToken: !!authData.refreshToken,
        //   rememberLogin: options.rememberLogin
        // })
        
        // 处理登录成功后的跳转
        await handleLoginSuccess()
      } else {
        throw new Error('保存认证状态失败')
      }
    } catch (err: any) {
      setError(`设置认证信息失败: ${err.message}`)
      console.error('❌ [AuthStore] setAuth详细错误信息:', {
        error: err,
        authData,
        options,
        stack: err.stack
      })
      throw err
    } finally {
      setLoading(false)
    }
  }

  /**
   * 清除认证信息
   */
  const clearAuth = async () => {
    try {
      setLoading(true)
      clearError()

      // 清除本地状态
      token.value = ''
      refreshToken.value = ''
      userInfo.value = null

      // 使用统一存储适配器清除认证状态
      const success = await authStorage.clearAuthState()

      if (success) {
        // console.log('✅ [AuthStore] 认证信息清除成功')
      } else {
        // console.warn('⚠️ [AuthStore] 清除认证状态时遇到问题')
      }
    } catch (err: any) {
      setError(`清除认证信息失败: ${err.message}`)
      console.error('❌ [AuthStore] 清除认证信息时出错:', err)
    } finally {
      setLoading(false)
    }
  }

  /**
   * 从存储中恢复认证信息
   */
  const restoreAuth = async () => {
    try {
      setLoading(true)
      clearError()

      // 先尝试迁移旧数据
      await authStorage.migrateFromLegacyStorage()

      // 获取完整认证状态
      const authState = await authStorage.getAuthState()

      if (authState.isValid && authState.accessToken && authState.userInfo) {
        // 恢复本地状态
        token.value = authState.accessToken
        refreshToken.value = authState.refreshToken || ''
        userInfo.value = authState.userInfo || null

        // 恢复用户偏好设置
        if (authState.preferences) {
          preferences.value = { ...preferences.value, ...authState.preferences }
        }

        // console.log('✅ [AuthStore] 认证信息恢复成功', {
        //   userId: authState.userInfo.id,
        //   hasRefreshToken: !!authState.refreshToken,
        //   hasSession: !!authState.sessionData
        // })

        // 验证数据完整性
        const validation = await authStorage.validateStorageIntegrity()
        if (!validation.isValid) {
          console.warn('⚠️ [AuthStore] 存储数据完整性检查发现问题:', validation.issues)
        }
      } else {
        // console.log('ℹ️ [AuthStore] 未找到有效的认证信息')
      }
    } catch (err: any) {
      setError(`恢复认证信息失败: ${err.message}`)
      console.error('❌ [AuthStore] 恢复认证信息时出错:', err)
    } finally {
      setLoading(false)
    }
  }

  /**
   * 更新用户信息
   */
  const updateUserInfo = async (newUserInfo: Partial<UserInfo>) => {
    try {
      if (!userInfo.value) {
        throw new Error('用户未登录，无法更新用户信息')
      }

      setLoading(true)
      clearError()

      // 更新本地状态
      userInfo.value = { ...userInfo.value, ...newUserInfo }

      // 使用统一存储适配器更新用户信息
      const success = await authStorage.updateUserInfo(newUserInfo)

      if (success) {
        // console.log('✅ [AuthStore] 用户信息更新成功', {
        //   userId: userInfo.value.id,
        //   updatedFields: Object.keys(newUserInfo)
        // })
      } else {
        throw new Error('更新用户信息到存储失败')
      }
    } catch (err: any) {
      setError(`更新用户信息失败: ${err.message}`)
      
      // 回滚本地状态
      await restoreAuth()
      throw err
    } finally {
      setLoading(false)
    }
  }

  // ==================== Token 管理方法 ====================

  /**
   * 更新访问令牌
   */
  const updateAccessToken = async (newToken: string, expiresInSeconds?: number) => {
    try {
      setLoading(true)
      clearError()

      // 更新本地状态
      token.value = newToken

      // 使用统一存储适配器保存新令牌
      const success = await authStorage.saveAccessToken(newToken, expiresInSeconds)

      if (success) {
        // console.log('✅ [AuthStore] 访问令牌更新成功')
      } else {
        throw new Error('保存新访问令牌失败')
      }
    } catch (err: any) {
      setError(`更新访问令牌失败: ${err.message}`)
      throw err
    } finally {
      setLoading(false)
    }
  }

  /**
   * 更新刷新令牌
   */
  const updateRefreshToken = async (newRefreshToken: string) => {
    try {
      setLoading(true)
      clearError()

      // 更新本地状态
      refreshToken.value = newRefreshToken

      // 使用统一存储适配器保存新刷新令牌
      const success = await authStorage.saveRefreshToken(newRefreshToken)

      if (success) {
        // console.log('✅ [AuthStore] 刷新令牌更新成功')
      } else {
        throw new Error('保存新刷新令牌失败')
      }
    } catch (err: any) {
      setError(`更新刷新令牌失败: ${err.message}`)
      throw err
    } finally {
      setLoading(false)
    }
  }

  /**
   * 检查令牌是否即将过期
   */
  const isTokenExpiringSoon = async (thresholdMinutes: number = 5): Promise<boolean> => {
    try {
      return await authStorage.isTokenExpiringSoon(thresholdMinutes)
    } catch (err: any) {
      console.error('❌ [AuthStore] 检查令牌过期状态失败:', err)
      return false
    }
  }

  /**
   * 获取令牌剩余有效时间
   */
  const getTokenRemainingTime = async (): Promise<number> => {
    try {
      return await authStorage.getTokenRemainingTime()
    } catch (err: any) {
      console.error('❌ [AuthStore] 获取令牌剩余时间失败:', err)
      return 0
    }
  }

  // ==================== 用户偏好设置管理 ====================

  /**
   * 更新用户偏好设置
   */
  const updatePreferences = async (newPreferences: Partial<AuthPreferences>) => {
    try {
      setLoading(true)
      clearError()

      // 更新本地状态
      preferences.value = { ...preferences.value, ...newPreferences }

      // 使用统一存储适配器更新偏好设置
      const success = await authStorage.updateUserPreferences(newPreferences)

      if (success) {
        // console.log('✅ [AuthStore] 用户偏好设置更新成功', newPreferences)
      } else {
        throw new Error('保存用户偏好设置失败')
      }
    } catch (err: any) {
      setError(`更新用户偏好设置失败: ${err.message}`)
      
      // 回滚本地状态
      await loadPreferences()
      throw err
    } finally {
      setLoading(false)
    }
  }

  /**
   * 加载用户偏好设置
   */
  const loadPreferences = async () => {
    try {
      const storedPreferences = await authStorage.getUserPreferences()
      
      if (storedPreferences) {
        preferences.value = { ...preferences.value, ...storedPreferences }
      }
    } catch (err: any) {
      console.error('❌ [AuthStore] 加载用户偏好设置失败:', err)
    }
  }

  // ==================== 登录成功处理 ====================

  /**
   * 处理登录成功后的跳转
   */
  const handleLoginSuccess = async (): Promise<void> => {
    try {
      await EnhancedRouteGuard.handleLoginSuccess()
    } catch (error) {
      console.error('❌ [AuthStore] 处理登录成功跳转失败:', error)
    }
  }

  // ==================== 工具方法 ====================

  /**
   * 验证认证状态完整性
   */
  const validateAuthState = async (): Promise<{
    isValid: boolean
    issues: string[]
  }> => {
    try {
      return await authStorage.validateStorageIntegrity()
    } catch (err: any) {
      console.error('❌ [AuthStore] 验证认证状态完整性失败:', err)
      return {
        isValid: false,
        issues: [`验证过程出错: ${err.message}`]
      }
    }
  }

  /**
   * 强制同步存储状态到本地状态
   */
  const syncFromStorage = async () => {
    try {
      setLoading(true)
      
      const authState = await authStorage.getAuthState()
      
      // 同步所有状态
      token.value = authState.accessToken || ''
      refreshToken.value = authState.refreshToken || ''
      userInfo.value = authState.userInfo || null
      
      if (authState.preferences) {
        preferences.value = { ...preferences.value, ...authState.preferences }
      }
      
      // console.log('✅ [AuthStore] 存储状态同步完成')
    } catch (err: any) {
      setError(`同步存储状态失败: ${err.message}`)
      console.error('❌ [AuthStore] 同步存储状态失败:', err)
    } finally {
      setLoading(false)
    }
  }

  // ==================== 存储导出 ====================

  return {
    // ==================== 状态 ====================
    token: readonly(token),
    refreshToken: readonly(refreshToken),
    userInfo: readonly(userInfo),
    loading: readonly(loading),
    error: readonly(error),
    preferences: readonly(preferences),
    
    // ==================== 计算属性 ====================
    isLoggedIn,
    userAvatar,
    userNickname,
    userId,
    isVip,
    
    // ==================== 核心方法 ====================
    setAuth,
    clearAuth,
    restoreAuth,
    updateUserInfo,
    
    // ==================== Token 管理 ====================
    updateAccessToken,
    updateRefreshToken,
    isTokenExpiringSoon,
    getTokenRemainingTime,
    
    // ==================== 偏好设置 ====================
    updatePreferences,
    loadPreferences,
    
    // ==================== 登录处理 ====================
    handleLoginSuccess,
    
    // ==================== 工具方法 ====================
    validateAuthState,
    syncFromStorage,
    clearError
  }
})