/**
 * 应用状态管理
 */
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { StoreUtil } from '@/utils/storage/StoreUtil'
import { StorageModule } from '@/utils/storage'

export const useAppStore = defineStore('app', () => {
  // 应用状态
  const loading = ref(false)
  const theme = ref<'light' | 'dark'>('light')
  const networkType = ref<string>('unknown')

  /**
   * 设置全局加载状态
   */
  const setLoading = (isLoading: boolean) => {
    loading.value = isLoading
  }

  /**
   * 切换主题
   */
  const toggleTheme = async () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
    await StoreUtil.set('theme', theme.value, StorageModule.CONFIG)
  }

  /**
   * 设置网络类型
   */
  const setNetworkType = (type: string) => {
    networkType.value = type
  }

  /**
   * 初始化应用状态
   */
  const initApp = async () => {
    // 恢复主题设置
    const storedTheme = await StoreUtil.get('theme', StorageModule.CONFIG)
    if (storedTheme) {
      theme.value = storedTheme
    }

    // 获取网络类型
    uni.getNetworkType({
      success: (res) => {
        setNetworkType(res.networkType)
      }
    })

    // 监听网络状态变化
    uni.onNetworkStatusChange((res) => {
      setNetworkType(res.networkType)
    })
  }

  return {
    // 状态
    loading,
    theme,
    networkType,
    
    // 操作方法
    setLoading,
    toggleTheme,
    setNetworkType,
    initApp
  }
})