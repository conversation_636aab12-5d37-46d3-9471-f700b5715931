/**
 * 服务相关状态管理
 */
import { defineStore } from 'pinia'
import type {
  ServiceDetail,
  ServiceType,
  SearchParams,
  HomeConfig
} from '@/types/business'

export interface ServiceState {
  // 首页配置
  homeConfig: HomeConfig | null
  // 服务列表
  services: ServiceDetail[]
  // 当前查看的服务详情
  currentService: ServiceDetail | null
  // 搜索参数
  searchParams: SearchParams
  // 加载状态
  loading: boolean
  // 错误信息
  error: string | null
}

export const useServiceStore = defineStore('service', {
  state: (): ServiceState => ({
    homeConfig: null,
    services: [],
    currentService: null,
    searchParams: {
      page: 1,
      pageSize: 10,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    },
    loading: false,
    error: null
  }),

  getters: {
    // 获取指定类型的服务
    getServicesByType: (state) => (type: ServiceType) => {
      return state.services.filter(service => service.type === type)
    },

    // 获取推荐服务
    getRecommendedServices: (state) => {
      return state.services
        .filter(service => service.rating >= 4.5)
        .slice(0, 6)
    },

    // 获取热门服务
    getHotServices: (state) => {
      return state.services
        .filter(service => service.orderCount > 100)
        .sort((a, b) => b.orderCount - a.orderCount)
        .slice(0, 8)
    },

    // 获取金刚位配置
    getGridItems: (state) => {
      return state.homeConfig?.gridItems || []
    },

    // 获取核心服务
    getCoreServices: (state) => {
      const coreTypes = ['yoga', 'fitness', 'game_companion', 'dining_companion']
      return state.homeConfig?.gridItems?.filter(item => coreTypes.includes(item.type)) || []
    },

    // 获取上门服务
    getHomeServices: (state) => {
      const homeTypes = ['massage', 'foot_therapy', 'housekeeping', 'beauty']
      return state.homeConfig?.gridItems?.filter(item => homeTypes.includes(item.type)) || []
    },

    // 获取扩展服务
    getExtendServices: (state) => {
      const extendTypes = ['tutoring', 'music', 'pet_care', 'other']
      return state.homeConfig?.gridItems?.filter(item => extendTypes.includes(item.type)) || []
    },

    // 动态金刚位数据 - 统一的金刚位入口
    dynamicGridItems: (state) => {
      return state.homeConfig?.gridItems || []
    }
  },

  actions: {
    // 设置加载状态
    setLoading(loading: boolean) {
      this.loading = loading
    },

    // 设置错误信息
    setError(error: string | null) {
      this.error = error
    },

    // 获取首页配置
    async fetchHomeConfig() {
      try {
        this.setLoading(true)
        this.setError(null)
        
        // TODO: 调用API获取首页配置
        // const response = await api.getHomeConfig()
        
        // 临时模拟数据
        const mockConfig: HomeConfig = {
          banners: [
            {
              id: '1',
              title: '专业按摩师上门服务',
              image: '/static/banner1.jpg',
              link: '/pages/service/detail?id=1',
              order: 1,
              isActive: true
            }
          ],
          gridItems: [
            // 核心服务
            {
              id: '1',
              title: '瑜伽',
              icon: '🧘',
              gradient: 'linear-gradient(135deg, #667eea, #764ba2)',
              route: '/pages/service/category?type=yoga',
              type: 'yoga' as ServiceType,
              isHot: true,
              order: 1
            },
            {
              id: '2',
              title: '健身',
              icon: '💪',
              gradient: 'linear-gradient(135deg, #ff9a56, #ff6b6b)',
              route: '/pages/service/category?type=fitness',
              type: 'fitness' as ServiceType,
              order: 2
            },
            {
              id: '3',
              title: '游戏陪玩',
              icon: '🎮',
              gradient: 'linear-gradient(135deg, #4ecdc4, #44a08d)',
              route: '/pages/service/category?type=game_companion',
              type: 'game_companion' as ServiceType,
              order: 3
            },
            {
              id: '4',
              title: '美食分享',
              icon: '🍽️',
              gradient: 'linear-gradient(135deg, #ffd93d, #ff9a56)',
              route: '/pages/service/category?type=dining_companion',
              type: 'dining_companion' as ServiceType,
              order: 4
            },
            // 上门服务
            {
              id: '5',
              title: '上门按摩',
              icon: '💆',
              gradient: 'linear-gradient(135deg, #a8e6cf, #88d8a3)',
              route: '/pages/service/category?type=massage',
              type: 'massage' as ServiceType,
              isHot: true,
              order: 5
            },
            {
              id: '6',
              title: '足疗服务',
              icon: '🦶',
              gradient: 'linear-gradient(135deg, #ff9a9e, #fecfef)',
              route: '/pages/service/category?type=foot_therapy',
              type: 'foot_therapy' as ServiceType,
              order: 6
            },
            {
              id: '7',
              title: '家政服务',
              icon: '🧹',
              gradient: 'linear-gradient(135deg, #a8edea, #fed6e3)',
              route: '/pages/service/category?type=housekeeping',
              type: 'housekeeping' as ServiceType,
              order: 7
            },
            {
              id: '8',
              title: '美容美发',
              icon: '💇',
              gradient: 'linear-gradient(135deg, #fbc2eb, #a6c1ee)',
              route: '/pages/service/category?type=beauty',
              type: 'beauty' as ServiceType,
              order: 8
            },
            // 扩展服务
            {
              id: '9',
              title: '学习辅导',
              icon: '📚',
              gradient: 'linear-gradient(135deg, #667eea, #764ba2)',
              route: '/pages/service/category?type=tutoring',
              type: 'tutoring' as ServiceType,
              order: 9
            },
            {
              id: '10',
              title: '音乐教学',
              icon: '🎵',
              gradient: 'linear-gradient(135deg, #ff9a56, #ff6b6b)',
              route: '/pages/service/category?type=music',
              type: 'music' as ServiceType,
              order: 10
            },
            {
              id: '11',
              title: '宠物服务',
              icon: '🐕',
              gradient: 'linear-gradient(135deg, #4ecdc4, #44a08d)',
              route: '/pages/service/category?type=pet_care',
              type: 'pet_care' as ServiceType,
              order: 11
            },
            {
              id: '12',
              title: '更多服务',
              icon: '➕',
              gradient: 'linear-gradient(135deg, #a8e6cf, #88d8a3)',
              route: '/pages/service/all',
              type: 'other' as ServiceType,
              order: 12
            }
          ],
          recommendedServices: [],
          hotCategories: ['massage', 'foot_therapy', 'fitness', 'game_companion'] as ServiceType[]
        }
        
        this.homeConfig = mockConfig
      } catch (error) {
        this.setError('获取首页配置失败')
        console.error('获取首页配置失败:', error)
      } finally {
        this.setLoading(false)
      }
    },

    // 搜索服务
    async searchServices(params: Partial<SearchParams>) {
      try {
        this.setLoading(true)
        this.setError(null)
        
        // 更新搜索参数
        this.searchParams = { ...this.searchParams, ...params }
        
        // TODO: 调用API搜索服务
        // const response = await api.searchServices(this.searchParams)
        // this.services = response.data
        
        console.log('搜索参数:', this.searchParams)
      } catch (error) {
        this.setError('搜索服务失败')
        console.error('搜索服务失败:', error)
      } finally {
        this.setLoading(false)
      }
    },

    // 获取服务详情
    async fetchServiceDetail(serviceId: string) {
      try {
        this.setLoading(true)
        this.setError(null)
        
        // TODO: 调用API获取服务详情
        // const response = await api.getServiceDetail(serviceId)
        // this.currentService = response.data
        
        console.log('获取服务详情:', serviceId)
      } catch (error) {
        this.setError('获取服务详情失败')
        console.error('获取服务详情失败:', error)
      } finally {
        this.setLoading(false)
      }
    },

    // 清空当前服务
    clearCurrentService() {
      this.currentService = null
    },

    // 重置搜索参数
    resetSearchParams() {
      this.searchParams = {
        page: 1,
        pageSize: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      }
    }
  }
})