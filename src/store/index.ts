/**
 * Pinia Store 入口
 */
import { createPinia } from 'pinia'
import { useAuthStore } from './modules/auth'
import { useAppStore } from './modules/app'
import { useUserStore } from './modules/user'
import { useChatStore } from './modules/chat'

// 创建Pinia实例
export const store = createPinia()

// 导出Store
export {
  useAuthStore,
  useAppStore,
  useUserStore,
  useChatStore
}

// 导出类型定义
export * from './types'

/**
 * 初始化 Store
 * 恢复认证状态和初始化应用状态
 */
export async function initStore() {
  try {
    // console.log('🔄 [Store] 开始初始化状态管理...')
    
    // 恢复认证状态
    const authStore = useAuthStore()
    await authStore.restoreAuth()
    
    // 初始化应用状态
    const appStore = useAppStore()
    await appStore.initApp()
    
    // 初始化用户状态
    const userStore = useUserStore()
    await userStore.initUserState()
    
    // 恢复聊天状态
    const chatStore = useChatStore()
    await chatStore.restoreChatState()
    
    // console.log('✅ [Store] 状态管理初始化完成')
  } catch (_error) {
    // console.error('❌ [Store] 状态管理初始化失败:', _error)
  }
}