/**
 * Store 工具函数
 * 提供通用的状态操作和持久化功能
 */

import type { 
  LoadingState, 
  ErrorState, 
  PaginationState,
  StoreActionResult,
  AsyncActionState
} from './types'
import { StoreConfigUtils, STORE_CONSTANTS } from './config'
import { StoreUtil } from '@/utils/storage/StoreUtil'
import { StorageModule } from '@/utils/storage/storageService'

/**
 * 状态管理工具类
 */
export class StoreUtils {
  /**
   * 创建异步操作包装器
   */
  static createAsyncAction<T, P = any>(
    action: (params?: P) => Promise<T>,
    options: {
      loadingState?: { value: boolean }
      errorState?: { value: string }
      onSuccess?: (result: T) => void
      onError?: (error: Error) => void
      showLoading?: boolean
      showError?: boolean
    } = {}
  ) {
    return async (params?: P): Promise<StoreActionResult<T>> => {
      const { 
        loadingState, 
        errorState, 
        onSuccess, 
        onError, 
        showLoading = true, 
        showError = true 
      } = options

      try {
        // 设置加载状态
        if (showLoading && loadingState) {
          loadingState.value = true
        }

        // 清除错误状态
        if (errorState) {
          errorState.value = ''
        }

        // 执行异步操作
        const result = await action(params)

        // 成功回调
        if (onSuccess) {
          onSuccess(result)
        }

        return {
          success: true,
          data: result
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '操作失败'
        
        // 设置错误状态
        if (showError && errorState) {
          errorState.value = errorMessage
        }

        // 错误回调
        if (onError && error instanceof Error) {
          onError(error)
        }

        // console.error('[StoreUtils] 异步操作失败:', error)

        return {
          success: false,
          error: errorMessage
        }
      } finally {
        // 清除加载状态
        if (showLoading && loadingState) {
          loadingState.value = false
        }
      }
    }
  }

  /**
   * 创建分页操作包装器
   */
  static createPaginatedAction<T>(
    fetchData: (page: number, pageSize: number) => Promise<{ list: T[], total: number }>,
    paginationState: {
      current: { value: number }
      pageSize: { value: number }
      total: { value: number }
      hasMore: { value: boolean }
      loadingMore: { value: boolean }
    },
    dataList: { value: T[] },
    options: {
      reset?: boolean
      onSuccess?: (data: { list: T[], total: number }) => void
      onError?: (error: Error) => void
    } = {}
  ) {
    return async (): Promise<StoreActionResult<T[]>> => {
      const { reset = false, onSuccess, onError } = options
      const { current, pageSize, total, hasMore, loadingMore } = paginationState

      try {
        // 重置时清空数据和分页状态
        if (reset) {
          current.value = 1
          total.value = 0
          hasMore.value = true
          dataList.value = []
        }

        // 检查是否还有更多数据
        if (!reset && !hasMore.value) {
          return {
            success: true,
            data: dataList.value
          }
        }

        // 设置加载状态
        loadingMore.value = true

        // 获取数据
        const result = await fetchData(current.value, pageSize.value)
        
        // 更新数据
        if (reset || current.value === 1) {
          dataList.value = result.list
        } else {
          dataList.value.push(...result.list)
        }

        // 更新分页状态
        total.value = result.total
        hasMore.value = dataList.value.length < result.total
        current.value += 1

        // 成功回调
        if (onSuccess) {
          onSuccess(result)
        }

        return {
          success: true,
          data: dataList.value
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取数据失败'
        
        // 错误回调
        if (onError && error instanceof Error) {
          onError(error)
        }

        // console.error('[StoreUtils] 分页数据获取失败:', error)

        return {
          success: false,
          error: errorMessage
        }
      } finally {
        loadingMore.value = false
      }
    }
  }

  /**
   * 创建持久化状态管理器
   */
  static createPersistedState<T extends Record<string, any>>(
    moduleName: string,
    storageModule: StorageModule,
    initialState: T,
    options: {
      keys?: (keyof T)[]
      ttl?: number
      encryption?: boolean
      onRestore?: (data: Partial<T>) => void
      onSave?: (data: Partial<T>) => void
    } = {}
  ) {
    const { keys, ttl, encryption = false, onRestore, onSave } = options

    return {
      /**
       * 保存状态到存储
       */
      async saveState(state: T): Promise<boolean> {
        try {
          const dataToSave: Partial<T> = keys 
              ? Object.fromEntries(
                  keys.map(key => [key, state[key]])
                ) as Partial<T>
              : state

          await StoreUtil.set(
            moduleName,
            dataToSave,
            storageModule
          )

          if (onSave) {
            onSave(dataToSave)
          }

          return true
        } catch (error) {
          // console.error(`[StoreUtils] 保存状态失败 ${moduleName}:`, error)
          return false
        }
      },

      /**
       * 从存储恢复状态
       */
      async restoreState(): Promise<Partial<T>> {
        try {
          const savedData = await StoreUtil.get(moduleName, storageModule)
          
          if (savedData && typeof savedData === 'object') {
            const restoredData = { ...savedData } as Partial<T>
            
            if (onRestore) {
              onRestore(restoredData)
            }

            return restoredData
          }

          return {}
        } catch (error) {
          // console.error(`[StoreUtils] 恢复状态失败 ${moduleName}:`, error)
          return {}
        }
      },

      /**
       * 清除存储的状态
       */
      async clearState(): Promise<boolean> {
        try {
          await StoreUtil.remove(moduleName, storageModule)
          return true
        } catch (error) {
          // console.error(`[StoreUtils] 清除状态失败 ${moduleName}:`, error)
          return false
        }
      }
    }
  }

  /**
   * 创建状态验证器
   */
  static createStateValidator<T extends Record<string, any>>(
    schema: Record<keyof T, (value: any) => boolean>
  ) {
    return {
      /**
       * 验证状态数据
       */
      validate(data: Partial<T>): { isValid: boolean; errors: string[] } {
        const errors: string[] = []

        for (const [key, validator] of Object.entries(schema)) {
          const value = data[key as keyof T]
          
          if (value !== undefined && !validator(value)) {
            errors.push(`字段 ${key} 验证失败`)
          }
        }

        return {
          isValid: errors.length === 0,
          errors
        }
      },

      /**
       * 清理无效数据
       */
      sanitize(data: Partial<T>): Partial<T> {
        const sanitized: Partial<T> = {}

        for (const [key, validator] of Object.entries(schema)) {
          const value = data[key as keyof T]
          
          if (value !== undefined && validator(value)) {
            sanitized[key as keyof T] = value
          }
        }

        return sanitized
      }
    }
  }

  /**
   * 防抖函数
   */
  static debounce<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let timeoutId: NodeJS.Timeout
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => func(...args), delay)
    }
  }

  /**
   * 节流函数
   */
  static throttle<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let lastCall = 0
    
    return (...args: Parameters<T>) => {
      const now = Date.now()
      
      if (now - lastCall >= delay) {
        lastCall = now
        func(...args)
      }
    }
  }

  /**
   * 深度克隆对象
   */
  static deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') {
      return obj
    }

    if (obj instanceof Date) {
      return new Date(obj.getTime()) as unknown as T
    }

    if (obj instanceof Array) {
      return obj.map(item => this.deepClone(item)) as unknown as T
    }

    if (typeof obj === 'object') {
      const cloned = {} as T
      
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          cloned[key] = this.deepClone(obj[key])
        }
      }
      
      return cloned
    }

    return obj
  }

  /**
   * 合并对象
   */
  static mergeObjects<T extends Record<string, any>>(
    target: T,
    ...sources: Partial<T>[]
  ): T {
    const result = { ...target }

    for (const source of sources) {
      for (const key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          const value = source[key]
          
          if (value !== undefined) {
            if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
              result[key] = this.mergeObjects(
                result[key] || {} as any,
                value
              )
            } else {
              result[key] = value as T[Extract<keyof T, string>]
            }
          }
        }
      }
    }

    return result
  }

  /**
   * 格式化错误信息
   */
  static formatError(error: unknown): string {
    if (error instanceof Error) {
      return error.message
    }
    
    if (typeof error === 'string') {
      return error
    }
    
    if (typeof error === 'object' && error !== null) {
      return JSON.stringify(error)
    }
    
    return '未知错误'
  }

  /**
   * 生成唯一ID
   */
  static generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 检查是否为空值
   */
  static isEmpty(value: any): boolean {
    if (value === null || value === undefined) {
      return true
    }
    
    if (typeof value === 'string') {
      return value.trim().length === 0
    }
    
    if (Array.isArray(value)) {
      return value.length === 0
    }
    
    if (typeof value === 'object') {
      return Object.keys(value).length === 0
    }
    
    return false
  }
}

/**
 * 状态管理常用装饰器
 */
export class StoreDecorators {
  /**
   * 自动持久化装饰器
   */
  static autoPersist<T extends Record<string, any>>(
    moduleName: string,
    storageModule: StorageModule,
    keys?: (keyof T)[]
  ) {
    return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
      const originalMethod = descriptor.value
      
      descriptor.value = async function(this: any, ...args: any[]) {
        const result = await originalMethod.apply(this, args)
        
        // 自动保存状态
        try {
          const dataToSave = keys 
            ? Object.fromEntries(
                keys.map(key => [key, this[key]])
              )
            : this.$state || {}

          await StoreUtil.set(moduleName, dataToSave, storageModule)
        } catch (error) {
          // console.error(`[AutoPersist] 自动保存失败 ${moduleName}:`, error)
        }
        
        return result
      }
      
      return descriptor
    }
  }

  /**
   * 错误处理装饰器
   */
  static errorHandler(showError: boolean = true) {
    return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
      const originalMethod = descriptor.value
      
      descriptor.value = async function(this: any, ...args: any[]) {
        try {
          return await originalMethod.apply(this, args)
        } catch (error) {
          const errorMessage = StoreUtils.formatError(error)
          
          if (showError && this.error !== undefined) {
            this.error = errorMessage
          }
          
          // console.error(`[${target.constructor.name}] ${propertyKey} 执行失败:`, error)
          
          throw error
        }
      }
      
      return descriptor
    }
  }

  /**
   * 加载状态装饰器
   */
  static loading(loadingKey: string = 'loading') {
    return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
      const originalMethod = descriptor.value
      
      descriptor.value = async function(this: any, ...args: any[]) {
        try {
          if ((this as any)[loadingKey] !== undefined) {
            (this as any)[loadingKey] = true
          }
          
          return await originalMethod.apply(this, args)
        } finally {
          if ((this as any)[loadingKey] !== undefined) {
            (this as any)[loadingKey] = false
          }
        }
      }
      
      return descriptor
    }
  }
}

// 导出常用工具函数
export const {
  createAsyncAction,
  createPaginatedAction,
  createPersistedState,
  createStateValidator,
  debounce,
  throttle,
  deepClone,
  mergeObjects,
  formatError,
  generateId,
  isEmpty
} = StoreUtils

export const {
  autoPersist,
  errorHandler,
  loading
} = StoreDecorators