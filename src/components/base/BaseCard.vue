<template>
  <view class="base-card" :class="{
    'base-card--shadow': shadow,
    'base-card--bordered': bordered,
    'base-card--hoverable': hoverable
  }">
    <!-- 卡片头部 -->
    <view v-if="$slots.header || title" class="base-card__header">
      <slot name="header">
        <view class="base-card__title">
          <text class="base-card__title-text">{{ title }}</text>
          <text v-if="subtitle" class="base-card__subtitle">{{ subtitle }}</text>
        </view>
      </slot>
      
      <view v-if="$slots.extra" class="base-card__extra">
        <slot name="extra"></slot>
      </view>
    </view>
    
    <!-- 卡片内容 -->
    <view class="base-card__body" :style="bodyStyle">
      <slot></slot>
    </view>
    
    <!-- 卡片底部 -->
    <view v-if="$slots.footer" class="base-card__footer">
      <slot name="footer"></slot>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 组件属性
interface Props {
  title?: string
  subtitle?: string
  shadow?: boolean
  bordered?: boolean
  hoverable?: boolean
  padding?: string | number
  bodyStyle?: Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  subtitle: '',
  shadow: true,
  bordered: false,
  hoverable: false,
  padding: '32rpx',
  bodyStyle: () => ({})
})

// 计算属性
const computedBodyStyle = computed(() => {
  const style: Record<string, any> = {
    ...props.bodyStyle
  }
  
  if (props.padding) {
    style.padding = typeof props.padding === 'number' ? `${props.padding}rpx` : props.padding
  }
  
  return style
})
</script>

<style lang="scss" scoped>
.base-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  transition: all 0.3s;
  
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 32rpx 0;
    
    &:only-child {
      padding-bottom: 32rpx;
    }
  }
  
  &__title {
    flex: 1;
    
    &-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      line-height: 1.4;
    }
  }
  
  &__subtitle {
    display: block;
    margin-top: 8rpx;
    font-size: 24rpx;
    color: #666666;
    line-height: 1.4;
  }
  
  &__extra {
    margin-left: 24rpx;
  }
  
  &__body {
    padding: 32rpx;
    
    // 当有头部时，减少顶部内边距
    .base-card__header + & {
      padding-top: 24rpx;
    }
    
    // 当有底部时，减少底部内边距
    &:has(+ .base-card__footer) {
      padding-bottom: 24rpx;
    }
  }
  
  &__footer {
    padding: 0 32rpx 32rpx;
    border-top: 1px solid #f0f0f0;
    
    // 当有内容时，添加顶部内边距
    .base-card__body + & {
      padding-top: 24rpx;
    }
  }
  
  // 阴影样式
  &--shadow {
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  }
  
  // 边框样式
  &--bordered {
    border: 1px solid #e5e5e5;
  }
  
  // 悬浮效果
  &--hoverable {
    cursor: pointer;
    
    &:hover {
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
      transform: translateY(-2rpx);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .base-card {
    border-radius: 12rpx;
    
    &__header {
      padding: 24rpx 24rpx 0;
    }
    
    &__body {
      padding: 24rpx;
      
      .base-card__header + & {
        padding-top: 16rpx;
      }
    }
    
    &__footer {
      padding: 0 24rpx 24rpx;
      
      .base-card__body + & {
        padding-top: 16rpx;
      }
    }
    
    &__title {
      &-text {
        font-size: 28rpx;
      }
    }
    
    &__subtitle {
      font-size: 22rpx;
    }
  }
}
</style>