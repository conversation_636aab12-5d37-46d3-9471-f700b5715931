<template>
  <button 
    :class="buttonClass" 
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <text v-if="loading" class="loading-icon">⟳</text>
    <slot />
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// Props定义
interface Props {
  type?: 'primary' | 'secondary' | 'outline' | 'text'
  size?: 'small' | 'medium' | 'large'
  disabled?: boolean
  loading?: boolean
  block?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'primary',
  size: 'medium',
  disabled: false,
  loading: false,
  block: false
})

// Emits定义
const emit = defineEmits<{
  click: [event: Event]
}>()

// 按钮样式计算
const buttonClass = computed(() => [
  'base-button',
  `base-button--${props.type}`,
  `base-button--${props.size}`,
  {
    'base-button--disabled': props.disabled,
    'base-button--loading': props.loading,
    'base-button--block': props.block
  }
])

/**
 * 点击事件处理
 */
const handleClick = (event: Event) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style scoped>
.base-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  overflow: hidden;
}

/* 类型样式 */
.base-button--primary {
  background: #007aff;
  color: white;
}

.base-button--primary:hover {
  background: #0056cc;
}

.base-button--secondary {
  background: #f2f2f2;
  color: #333;
}

.base-button--outline {
  background: transparent;
  border: 1px solid #007aff;
  color: #007aff;
}

.base-button--text {
  background: transparent;
  color: #007aff;
}

/* 尺寸样式 */
.base-button--small {
  padding: 8px 16px;
  font-size: 14px;
  min-height: 32px;
}

.base-button--medium {
  padding: 12px 24px;
  font-size: 16px;
  min-height: 40px;
}

.base-button--large {
  padding: 16px 32px;
  font-size: 18px;
  min-height: 48px;
}

/* 状态样式 */
.base-button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.base-button--loading {
  cursor: wait;
}

.base-button--block {
  width: 100%;
  display: flex;
}

.loading-icon {
  margin-right: 8px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style> 