<template>
  <button
    class="base-button"
    :class="[
      `base-button--${type}`,
      `base-button--${size}`,
      {
        'base-button--disabled': disabled,
        'base-button--loading': loading,
        'base-button--block': block,
        'base-button--round': round,
        'base-button--plain': plain
      }
    ]"
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <view class="base-button__content">
      <view v-if="loading" class="base-button__loading">
        <text class="base-button__loading-icon">⟳</text>
      </view>
      <view v-if="icon && !loading" class="base-button__icon">
        <text :class="icon"></text>
      </view>
      <view v-if="$slots.default" class="base-button__text">
        <slot></slot>
      </view>
    </view>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 组件属性
interface Props {
  type?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info'
  size?: 'mini' | 'small' | 'medium' | 'large'
  disabled?: boolean
  loading?: boolean
  block?: boolean
  round?: boolean
  plain?: boolean
  icon?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'primary',
  size: 'medium',
  disabled: false,
  loading: false,
  block: false,
  round: false,
  plain: false,
  icon: ''
})

// 事件定义
const emit = defineEmits<{
  click: [event: Event]
}>()

// 点击处理
const handleClick = (event: Event) => {
  if (props.disabled || props.loading) {
    return
  }
  emit('click', event)
}
</script>

<style lang="scss" scoped>
.base-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 1;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  user-select: none;
  
  &__content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
  }
  
  &__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    
    &-icon {
      animation: rotate 1s linear infinite;
      font-size: 28rpx;
    }
  }
  
  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
  }
  
  &__text {
    display: flex;
    align-items: center;
  }
  
  // 尺寸
  &--mini {
    height: 48rpx;
    padding: 0 16rpx;
    font-size: 20rpx;
    border-radius: 6rpx;
  }
  
  &--small {
    height: 64rpx;
    padding: 0 24rpx;
    font-size: 24rpx;
    border-radius: 6rpx;
  }
  
  &--medium {
    height: 80rpx;
    padding: 0 32rpx;
    font-size: 28rpx;
    border-radius: 8rpx;
  }
  
  &--large {
    height: 96rpx;
    padding: 0 40rpx;
    font-size: 32rpx;
    border-radius: 10rpx;
  }
  
  // 类型
  &--primary {
    background-color: #007aff;
    border-color: #007aff;
    color: #ffffff;
    
    &:hover {
      background-color: #0056cc;
      border-color: #0056cc;
    }
    
    &:active {
      background-color: #004499;
      border-color: #004499;
    }
  }
  
  &--secondary {
    background-color: #f5f5f5;
    border-color: #e5e5e5;
    color: #333333;
    
    &:hover {
      background-color: #e5e5e5;
      border-color: #d5d5d5;
    }
    
    &:active {
      background-color: #d5d5d5;
      border-color: #c5c5c5;
    }
  }
  
  &--success {
    background-color: #34c759;
    border-color: #34c759;
    color: #ffffff;
    
    &:hover {
      background-color: #28a745;
      border-color: #28a745;
    }
    
    &:active {
      background-color: #1e7e34;
      border-color: #1e7e34;
    }
  }
  
  &--warning {
    background-color: #ff9500;
    border-color: #ff9500;
    color: #ffffff;
    
    &:hover {
      background-color: #e6851a;
      border-color: #e6851a;
    }
    
    &:active {
      background-color: #cc7a00;
      border-color: #cc7a00;
    }
  }
  
  &--danger {
    background-color: #ff3b30;
    border-color: #ff3b30;
    color: #ffffff;
    
    &:hover {
      background-color: #e6342a;
      border-color: #e6342a;
    }
    
    &:active {
      background-color: #cc2d24;
      border-color: #cc2d24;
    }
  }
  
  &--info {
    background-color: #5ac8fa;
    border-color: #5ac8fa;
    color: #ffffff;
    
    &:hover {
      background-color: #32ade6;
      border-color: #32ade6;
    }
    
    &:active {
      background-color: #1a9bcf;
      border-color: #1a9bcf;
    }
  }
  
  // 状态
  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  &--loading {
    cursor: not-allowed;
  }
  
  &--block {
    width: 100%;
    display: flex;
  }
  
  &--round {
    border-radius: 50rpx;
  }
  
  &--plain {
    background-color: transparent;
    
    &.base-button--primary {
      color: #007aff;
      border-color: #007aff;
      
      &:hover {
        background-color: rgba(0, 122, 255, 0.1);
      }
    }
    
    &.base-button--secondary {
      color: #333333;
      border-color: #e5e5e5;
      
      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
      }
    }
    
    &.base-button--success {
      color: #34c759;
      border-color: #34c759;
      
      &:hover {
        background-color: rgba(52, 199, 89, 0.1);
      }
    }
    
    &.base-button--warning {
      color: #ff9500;
      border-color: #ff9500;
      
      &:hover {
        background-color: rgba(255, 149, 0, 0.1);
      }
    }
    
    &.base-button--danger {
      color: #ff3b30;
      border-color: #ff3b30;
      
      &:hover {
        background-color: rgba(255, 59, 48, 0.1);
      }
    }
    
    &.base-button--info {
      color: #5ac8fa;
      border-color: #5ac8fa;
      
      &:hover {
        background-color: rgba(90, 200, 250, 0.1);
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>