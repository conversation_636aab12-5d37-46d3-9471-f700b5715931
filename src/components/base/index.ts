/**
 * 基础组件统一导出
 * 提供所有基础组件的统一入口
 */

export { default as BaseButton } from './BaseButton.vue'
export { default as BaseInput } from './BaseInput.vue'
export { default as BaseCard } from './BaseCard.vue'
export { default as BaseLoading } from './BaseLoading.vue'

// 基础组件类型定义
export interface BaseComponentProps {
  // 通用属性
  disabled?: boolean
  loading?: boolean
  size?: 'small' | 'medium' | 'large'
}

// 按钮组件属性
export interface BaseButtonProps extends BaseComponentProps {
  type?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info'
  variant?: 'filled' | 'outlined' | 'text'
  block?: boolean
  round?: boolean
  plain?: boolean
}

// 输入框组件属性
export interface BaseInputProps extends BaseComponentProps {
  modelValue?: string | number
  type?: 'text' | 'number' | 'password' | 'tel' | 'email'
  placeholder?: string
  clearable?: boolean
  maxlength?: number
  error?: string
}

// 卡片组件属性
export interface BaseCardProps {
  title?: string
  subtitle?: string
  shadow?: boolean
  bordered?: boolean
  hoverable?: boolean
  padding?: string | number
}

// 加载组件属性
export interface BaseLoadingProps {
  visible?: boolean
  type?: 'spinner' | 'circle' | 'dots' | 'wave' | 'pulse'
  text?: string
  overlay?: boolean
  fullscreen?: boolean
  size?: 'small' | 'medium' | 'large'
  color?: string
}