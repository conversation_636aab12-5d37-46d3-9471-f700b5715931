<template>
  <view class="base-input" :class="{
    'base-input--disabled': disabled,
    'base-input--error': error,
    'base-input--focused': focused
  }">
    <view v-if="label" class="base-input__label">
      <text class="base-input__label-text">{{ label }}</text>
      <text v-if="required" class="base-input__required">*</text>
    </view>
    
    <view class="base-input__wrapper">
      <view v-if="prefixIcon" class="base-input__prefix">
        <text :class="prefixIcon"></text>
      </view>
      
      <input
        class="base-input__field"
        :type="type"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :maxlength="maxlength"
        :focus="autoFocus"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @confirm="handleConfirm"
      />
      
      <view v-if="suffixIcon || clearable" class="base-input__suffix">
        <view v-if="clearable && modelValue && !disabled" 
              class="base-input__clear" 
              @click="handleClear">
          <text class="base-input__clear-icon">×</text>
        </view>
        <view v-if="suffixIcon" class="base-input__suffix-icon">
          <text :class="suffixIcon"></text>
        </view>
      </view>
    </view>
    
    <view v-if="error || helperText" class="base-input__helper">
      <text class="base-input__helper-text" :class="{
        'base-input__helper-text--error': error
      }">
        {{ error || helperText }}
      </text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 组件属性
interface Props {
  modelValue?: string | number
  type?: 'text' | 'number' | 'password' | 'tel' | 'email'
  label?: string
  placeholder?: string
  disabled?: boolean
  required?: boolean
  clearable?: boolean
  autoFocus?: boolean
  maxlength?: number
  prefixIcon?: string
  suffixIcon?: string
  error?: string
  helperText?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  type: 'text',
  label: '',
  placeholder: '',
  disabled: false,
  required: false,
  clearable: false,
  autoFocus: false,
  maxlength: -1,
  prefixIcon: '',
  suffixIcon: '',
  error: '',
  helperText: ''
})

// 事件定义
const emit = defineEmits<{
  'update:modelValue': [value: string | number]
  focus: [event: Event]
  blur: [event: Event]
  confirm: [event: Event]
  clear: []
}>()

// 响应式状态
const focused = ref(false)

// 输入处理
const handleInput = (event: any) => {
  const value = event.detail.value
  emit('update:modelValue', value)
}

// 聚焦处理
const handleFocus = (event: Event) => {
  focused.value = true
  emit('focus', event)
}

// 失焦处理
const handleBlur = (event: Event) => {
  focused.value = false
  emit('blur', event)
}

// 确认处理
const handleConfirm = (event: Event) => {
  emit('confirm', event)
}

// 清空处理
const handleClear = () => {
  emit('update:modelValue', '')
  emit('clear')
}
</script>

<style lang="scss" scoped>
.base-input {
  width: 100%;
  
  &__label {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
    
    &-text {
      font-size: 28rpx;
      font-weight: 500;
      color: #333333;
    }
  }
  
  &__required {
    margin-left: 8rpx;
    font-size: 28rpx;
    color: #ff3b30;
  }
  
  &__wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background-color: #ffffff;
    border: 1px solid #e5e5e5;
    border-radius: 8rpx;
    transition: all 0.3s;
    
    &:hover {
      border-color: #007aff;
    }
  }
  
  &__prefix {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80rpx;
    height: 80rpx;
    color: #999999;
    font-size: 32rpx;
  }
  
  &__field {
    flex: 1;
    height: 80rpx;
    padding: 0 24rpx;
    font-size: 28rpx;
    color: #333333;
    background-color: transparent;
    border: none;
    outline: none;
    
    &::placeholder {
      color: #999999;
    }
  }
  
  &__suffix {
    display: flex;
    align-items: center;
    padding-right: 24rpx;
    gap: 16rpx;
  }
  
  &__clear {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32rpx;
    height: 32rpx;
    border-radius: 50%;
    background-color: #cccccc;
    cursor: pointer;
    
    &-icon {
      font-size: 24rpx;
      color: #ffffff;
      line-height: 1;
    }
    
    &:hover {
      background-color: #999999;
    }
  }
  
  &__suffix-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999999;
    font-size: 32rpx;
  }
  
  &__helper {
    margin-top: 8rpx;
    
    &-text {
      font-size: 24rpx;
      color: #666666;
      
      &--error {
        color: #ff3b30;
      }
    }
  }
  
  // 状态
  &--focused {
    .base-input__wrapper {
      border-color: #007aff;
      box-shadow: 0 0 0 2rpx rgba(0, 122, 255, 0.2);
    }
  }
  
  &--error {
    .base-input__wrapper {
      border-color: #ff3b30;
      
      &:hover {
        border-color: #ff3b30;
      }
    }
    
    &.base-input--focused {
      .base-input__wrapper {
        border-color: #ff3b30;
        box-shadow: 0 0 0 2rpx rgba(255, 59, 48, 0.2);
      }
    }
  }
  
  &--disabled {
    .base-input__wrapper {
      background-color: #f5f5f5;
      border-color: #e5e5e5;
      cursor: not-allowed;
      
      &:hover {
        border-color: #e5e5e5;
      }
    }
    
    .base-input__field {
      color: #999999;
      cursor: not-allowed;
    }
  }
}
</style>