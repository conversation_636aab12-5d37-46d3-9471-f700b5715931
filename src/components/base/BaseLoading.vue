<template>
  <view class="base-loading" :class="{
    'base-loading--overlay': overlay,
    'base-loading--fullscreen': fullscreen
  }" v-if="visible">
    <view class="base-loading__content">
      <!-- 加载动画 -->
      <view class="base-loading__spinner" :class="`base-loading__spinner--${type}`">
        <!-- 圆形加载器 -->
        <view v-if="type === 'circle'" class="base-loading__circle">
          <view class="base-loading__circle-path"></view>
        </view>
        
        <!-- 点状加载器 -->
        <view v-else-if="type === 'dots'" class="base-loading__dots">
          <view class="base-loading__dot" v-for="i in 3" :key="i"></view>
        </view>
        
        <!-- 波浪加载器 -->
        <view v-else-if="type === 'wave'" class="base-loading__wave">
          <view class="base-loading__wave-bar" v-for="i in 5" :key="i"></view>
        </view>
        
        <!-- 脉冲加载器 -->
        <view v-else-if="type === 'pulse'" class="base-loading__pulse">
          <view class="base-loading__pulse-ring" v-for="i in 2" :key="i"></view>
        </view>
        
        <!-- 默认旋转加载器 -->
        <view v-else class="base-loading__spinner-default">
          <view class="base-loading__spinner-icon"></view>
        </view>
      </view>
      
      <!-- 加载文本 -->
      <view v-if="text" class="base-loading__text">
        <text>{{ text }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// 组件属性
interface Props {
  visible?: boolean
  type?: 'spinner' | 'circle' | 'dots' | 'wave' | 'pulse'
  text?: string
  overlay?: boolean
  fullscreen?: boolean
  size?: 'small' | 'medium' | 'large'
  color?: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: true,
  type: 'spinner',
  text: '',
  overlay: false,
  fullscreen: false,
  size: 'medium',
  color: '#007aff'
})
</script>

<style lang="scss" scoped>
.base-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  
  &__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16rpx;
  }
  
  &__text {
    font-size: 28rpx;
    color: #666666;
    text-align: center;
  }
  
  // 遮罩层
  &--overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 9999;
  }
  
  // 全屏
  &--fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    
    .base-loading__content {
      background-color: #ffffff;
      padding: 48rpx;
      border-radius: 16rpx;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
    }
  }
}

// 加载动画容器
.base-loading__spinner {
  width: 64rpx;
  height: 64rpx;
  position: relative;
}

// 默认旋转加载器
.base-loading__spinner-default {
  width: 100%;
  height: 100%;
  
  .base-loading__spinner-icon {
    width: 100%;
    height: 100%;
    border: 4rpx solid #e5e5e5;
    border-top-color: #007aff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

// 圆形加载器
.base-loading__circle {
  width: 100%;
  height: 100%;
  
  .base-loading__circle-path {
    width: 100%;
    height: 100%;
    border: 4rpx solid transparent;
    border-top-color: #007aff;
    border-radius: 50%;
    animation: spin 1.2s linear infinite;
    
    &::before {
      content: '';
      position: absolute;
      top: -4rpx;
      left: -4rpx;
      right: -4rpx;
      bottom: -4rpx;
      border: 4rpx solid #e5e5e5;
      border-radius: 50%;
      z-index: -1;
    }
  }
}

// 点状加载器
.base-loading__dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  width: 100%;
  height: 100%;
  
  .base-loading__dot {
    width: 12rpx;
    height: 12rpx;
    background-color: #007aff;
    border-radius: 50%;
    animation: dot-bounce 1.4s ease-in-out infinite both;
    
    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }
}

// 波浪加载器
.base-loading__wave {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4rpx;
  width: 100%;
  height: 100%;
  
  .base-loading__wave-bar {
    width: 6rpx;
    height: 40rpx;
    background-color: #007aff;
    border-radius: 3rpx;
    animation: wave-scale 1.2s ease-in-out infinite;
    
    &:nth-child(1) { animation-delay: -1.1s; }
    &:nth-child(2) { animation-delay: -1.0s; }
    &:nth-child(3) { animation-delay: -0.9s; }
    &:nth-child(4) { animation-delay: -0.8s; }
    &:nth-child(5) { animation-delay: -0.7s; }
  }
}

// 脉冲加载器
.base-loading__pulse {
  width: 100%;
  height: 100%;
  position: relative;
  
  .base-loading__pulse-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 4rpx solid #007aff;
    border-radius: 50%;
    opacity: 1;
    animation: pulse-scale 2s ease-in-out infinite;
    
    &:nth-child(2) {
      animation-delay: 1s;
    }
  }
}

// 动画定义
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes dot-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes wave-scale {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

@keyframes pulse-scale {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

// 尺寸变体
.base-loading__spinner--small {
  width: 32rpx;
  height: 32rpx;
}

.base-loading__spinner--large {
  width: 96rpx;
  height: 96rpx;
}
</style>