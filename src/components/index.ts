/**
 * 组件统一导出
 * 提供所有组件的统一入口
 */

import { App } from 'vue'

// 基础组件
export { default as BaseButton } from './base/BaseButton.vue'
export { default as BaseInput } from './base/BaseInput.vue'
export { default as BaseCard } from './base/BaseCard.vue'
export { default as BaseLoading } from './base/BaseLoading.vue'

// 业务组件
// 验证组件将在这里导出

// 自动导入所有组件
const modules = import.meta.glob('./**/*.vue', { eager: true })

// 组件注册函数
export function registerComponents(app: App) {
  Object.entries(modules).forEach(([path, module]) => {
    const componentName = path
      .split('/')
      .pop()
      ?.replace(/\.(vue|ts|js)$/, '')
    
    if (componentName && (module as any).default) {
      app.component(componentName, (module as any).default)
    }
  })
}

// 默认导出
export default {
  install: registerComponents
}