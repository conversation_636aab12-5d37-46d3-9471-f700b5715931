<template>
  <uni-popup ref="popup" type="center" background-color="transparent">
    <view class="captcha-modal">
      <view class="captcha-header">
        <text class="captcha-title">安全验证</text>
        <text class="captcha-close" @click="closeCaptcha">✕</text>
      </view>
      
      <view class="captcha-content">
        <verify
          ref="verifyRef"
          :captcha-type="captchaType"
          :img-size="responsiveImgSize"
          @success="handleSuccess"
          @error="handleError"
        />
      </view>
    </view>
  </uni-popup>
</template>

<script setup lang="ts">
import { ref, computed, defineExpose } from 'vue'
import Verify from '@/components/business/verification/Verify.vue'
import type { CaptchaType } from '@/api/modules/captcha'
import { getResponsiveSize } from '@/utils/common/deviceInfo'

// Props定义
interface Props {
  captchaType?: CaptchaType
}

const props = withDefaults(defineProps<Props>(), {
  captchaType: 'blockPuzzle'
})

// Emits定义
const emit = defineEmits<{
  success: [captchaVerification: string]
  error: [message: string]
}>()

// 组件引用
const popup = ref()
const verifyRef = ref()

// 响应式图片尺寸 - 解决窗口大小变化时图片不按比例缩放的问题
const responsiveImgSize = computed(() => {
  const size = getResponsiveSize(310, 155)
  return {
    width: size.width + 'px',
    height: size.height + 'px'
  }
})

/**
 * 显示验证码
 */
const show = () => {
  popup.value?.open()
}

/**
 * 关闭验证码
 */
const closeCaptcha = () => {
  popup.value?.close()
}

/**
 * 验证成功处理
 */
const handleSuccess = (params: { captchaVerification: string }) => {
  emit('success', params.captchaVerification)
  closeCaptcha()
}

/**
 * 验证失败处理
 */
const handleError = (message: string) => {
  emit('error', message)
}

// 暴露方法
defineExpose({
  show,
  close: closeCaptcha
})
</script>

<style scoped>
.captcha-modal {
  width: 90vw;
  max-width: 380px;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  /* 响应式宽度，适应不同屏幕尺寸 */
}

.captcha-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.captcha-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  /* 使用系统字体，避免自定义字体加载问题 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
}

.captcha-close {
  width: 24px;
  height: 24px;
  line-height: 22px;
  text-align: center;
  background: #e8e8e8;
  border-radius: 50%;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  transition: all 0.2s ease;
  /* 修复字体显示问题 */
  font-family: system-ui, -apple-system, sans-serif;
}

.captcha-close:hover {
  background: #ddd;
  color: #333;
}

.captcha-content {
  padding: 20px;
  /* 确保验证码组件在容器内正确显示 */
  overflow: hidden;
}

/* 响应式适配 */
@media (max-width: 480px) {
  .captcha-modal {
    width: 95vw;
    max-width: none;
  }
  
  .captcha-header {
    padding: 15px;
  }
  
  .captcha-content {
    padding: 15px;
  }
  
  .captcha-title {
    font-size: 16px;
  }
}

/* 修复在小屏幕设备上的显示问题 */
@media (max-width: 360px) {
  .captcha-modal {
    width: 100vw;
    margin: 0 10px;
    border-radius: 8px;
  }
  
  .captcha-header {
    padding: 12px;
  }
  
  .captcha-content {
    padding: 12px;
  }
}
</style>