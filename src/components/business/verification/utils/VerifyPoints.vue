<template>
  <div class="verify-points">
    <div class="verify-refresh" @click="refresh">
      <i class="iconfont-verify icon-refresh"></i>
    </div>
    <div class="verify-image-panel" :style="{width: imgSize.width, height: imgSize.height}" @click="clickImage">
      <img v-if="bgImg" ref="bgImg" :src="bgImg" alt="验证码背景" :style="{width: '100%', height: '100%'}" />
      <div class="verify-tips" v-if="tipWords">{{tipWords}}</div>
      <div v-for="(item, index) in pointsCoordinate" :key="index" 
           class="verify-point" 
           :style="{left: item.x + 'px', top: item.y + 'px'}"
           v-if="item.x && item.y">
        {{index + 1}}
      </div>
    </div>
    <div class="verify-bar-area" :style="{width: imgSize.width, height: '40px'}">
      <span class="verify-msg" v-text="explain"></span>
    </div>
  </div>
</template>

<script>
import { aesEncrypt } from '@/utils/helpers/crypto'
import { getWordCaptcha, checkWordCaptcha } from '@/api/modules/captcha'

export default {
  name: 'VerifyPoints',
  props: {
    captchaType: {
      type: String,
      default: 'clickWord'
    },
    isPassing: {
      type: Boolean,
      default: false
    },
    callback: {
      type: Function,
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      bgImg: '',
      tipWords: '',
      pointsCoordinate: [],
      originPointsCoordinate: [],
      clickWordSequence: [],
      captchaObj: null,
      secretKey: '', // 存储从接口获取的密钥
      explain: '请按语序依次点击下图中的文字',
      containerWidth: 310, // 容器宽度，响应式计算使用
      resizeObserver: null // resize观察器
    }
  },
  computed: {
    imgSize() {
      // 响应式尺寸计算，支持窗口大小变化
      const baseWidth = 310;
      const baseHeight = 155;
      
      // 获取容器宽度，如果没有则使用基础宽度
      const containerWidth = this.containerWidth || baseWidth;
      const scale = Math.min(1, containerWidth / baseWidth);
      
      return {
        width: Math.floor(baseWidth * scale) + 'px',
        height: Math.floor(baseHeight * scale) + 'px',
        scale: scale // 保存缩放比例供坐标计算使用
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        // 显示时初始化
        this.init()
      }
    }
  },
  mounted() {
    // 组件初始化
    if (this.visible) {
      this.init()
    }
    
    // 设置响应式容器尺寸监听
    this.setupResizeObserver()
  },
  
  beforeUnmount() {
    // 清理resize观察器
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
    }
  },
  methods: {
    init() {
      this.getImgData()
      this.initStyle()
    },
    // 初始化样式
    initStyle() {
      this.pointsCoordinate = []
      this.originPointsCoordinate = []
      this.clickWordSequence = []
    },
    // 获取验证码图片数据
    async getImgData() {
      try {
        const res = await getWordCaptcha()
        // 适配新的后端返回格式: { code: 200, msg: "操作成功", data: { repCode: "0000", repData: {...} } }
        if (res && res.code === 200 && res.data && res.data.repCode === '0000') {
          this.captchaObj = res.data.repData
          this.bgImg = 'data:image/png;base64,' + this.captchaObj.originalImageBase64
          this.tipWords = this.captchaObj.wordTips || '请按语序依次点击下图中的文字'
          // 获取密钥
          this.secretKey = res.data.repData.secretKey || ''
        } else {
          // console.error('获取验证码失败')
        }
      } catch (error) {
        // console.error('获取验证码请求失败', error)
      }
    },
    // 刷新验证码
    refresh() {
      this.init()
    },
    // 点击图片事件
    clickImage(e) {
      if (this.clickWordSequence.length >= 4) return
      
      // 计算点击坐标相对图片的位置
      const panel = this.$refs.bgImg
      const panelRect = panel.getBoundingClientRect()
      
      const x = (e.clientX || (e.touches && e.touches[0].clientX)) - panelRect.left
      const y = (e.clientY || (e.touches && e.touches[0].clientY)) - panelRect.top
      
      // 记录点击的位置
      this.clickWordSequence.push({x, y})
      this.originPointsCoordinate.push({x, y})
      
      // 显示点击的序号
      this.pointsCoordinate.push({
        x: x - 10, // 调整偏移，使点击标记居中
        y: y - 10
      })
      
      // 如果点击了4个点，自动提交验证
      if (this.clickWordSequence.length === 4) {
        this.checkWords()
      }
    },
    // 设置响应式容器尺寸监听
    setupResizeObserver() {
      this.$nextTick(() => {
        const panel = this.$refs.bgImg
        if (panel && window.ResizeObserver) {
          this.resizeObserver = new ResizeObserver(entries => {
            for (let entry of entries) {
              this.containerWidth = entry.contentRect.width
            }
          })
          this.resizeObserver.observe(panel.parentElement)
        }
      })
    },
    
    // 验证点击的文字
    async checkWords() {
      try {
        // 【响应式修复】按比例转换坐标，考虑响应式缩放
        // 使用当前实际显示尺寸计算比例，而不是固定310x155
        const currentScale = this.imgSize.scale || 1
        const scaledPoints = this.originPointsCoordinate.map(p => {
          // 将显示坐标转换为标准310x155尺寸的坐标
          let x = Math.round(p.x / currentScale)
          let y = Math.round(p.y / currentScale)
          return {x, y}
        })
        
        // 生成原始坐标数据（不加密）
        const pointDataJson = JSON.stringify(this.originPointsCoordinate)
        
        // 构建验证请求参数 - 只传递后端需要的3个字段，但包含secretKey供前端处理使用
        const data = {
          captchaType: this.captchaType,
          pointJson: this.secretKey ? aesEncrypt(pointDataJson, this.secretKey) : pointDataJson,
          token: this.captchaObj.token,
          secretKey: this.secretKey // 用于前端处理，不会传递给后端
        }
        

        
        // 请求验证
        const res = await checkWordCaptcha(data)
        // 适配新的后端返回格式: { code: 200, msg: "操作成功", data: { repCode: "0000", repData: { result: true/false } } }
        if (res && res.code === 200 && res.data && res.data.repCode === '0000') {
          if (res.data.repData.result) {
            // 验证成功
            this.$emit('update:isPassing', true)
            
            // 【官方逻辑】生成captchaVerification - 完全按照官方代码第145行
            // var captchaVerification = this.secretKey? 
            //   aesEncrypt(this.backToken+'---'+JSON.stringify(this.checkPosArr),this.secretKey) :
            //   this.backToken+'---'+JSON.stringify(this.checkPosArr)
            
            // 使用缩放后的坐标生成验证参数（按照官方逻辑）
            var captchaVerification = this.secretKey ?
              aesEncrypt(data.token + '---' + JSON.stringify(scaledPoints), this.secretKey) :
              data.token + '---' + JSON.stringify(scaledPoints)
            
            // 传递完整的验证参数给回调函数 - 移除多余字段
            const verifyResult = {
              captchaVerification: captchaVerification,
              captchaType: this.captchaType
            };
            
            this.callback(verifyResult);
            return;
          }
        }
        
        // 验证失败
        this.$emit('update:isPassing', false)
        this.init();
      } catch (error) {
        this.pointsCoordinate = []
        this.originPointsCoordinate = []
        this.clickWordSequence = []
      }
    }
  }
}
</script>

<style scoped>
.verify-points {
  position: relative;
  background-color: #fff;
  border-radius: 5px;
}

.verify-refresh {
  position: absolute;
  right: 5px;
  top: 5px;
  width: 30px;
  height: 30px;
  cursor: pointer;
  z-index: 2;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.verify-image-panel {
  position: relative;
  margin: 0 auto;
  border-radius: 5px;
  overflow: hidden;
  background-color: #e8e8e8;
  max-width: 100%;
  /* 响应式容器，支持自动缩放 */
}

.verify-image-panel img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
}

.verify-tips {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  text-align: center;
  padding: 5px 0;
  font-size: 12px;
}

.verify-point {
  position: absolute;
  width: 20px;
  height: 20px;
  background-color: #1890FF;
  color: #fff;
  border-radius: 50%;
  text-align: center;
  line-height: 20px;
  font-size: 12px;
  z-index: 9;
}

.verify-bar-area {
  position: relative;
  margin: 10px auto;
  background-color: #f5f5f5;
  border-radius: 20px;
  box-sizing: content-box;
  border: 1px solid #ddd;
}

.verify-msg {
  width: 100%;
  text-align: center;
  line-height: 40px;
  font-size: 14px;
  color: #666;
  user-select: none;
}
</style>