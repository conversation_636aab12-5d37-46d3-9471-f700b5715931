<template>
  <div class="verify-slide">
    <div class="verify-refresh" @click="refresh">
      <i class="iconfont-verify icon-refresh"></i>
    </div>
    <div class="verify-image-panel" ref="imagePanel" :style="{width: imgSize.width, height: imgSize.height}">
      <img v-if="blockImg" ref="blockImg" class="block-img" :src="blockImg" alt="验证码滑块" :style="blockImgStyle" />
      <img v-if="bgImg" ref="bgImg" :src="bgImg" alt="验证码背景" :style="{width: '100%', height: '100%'}" />
    </div>
    <div class="verify-bar-area" ref="barArea" :style="{width: imgSize.width, height: '40px'}">
      <span class="verify-msg" v-text="explain"></span>
      <div class="verify-left-bar" :style="leftBarStyle">
        <span class="verify-msg" v-text="finishText"></span>
      </div>
      <div class="verify-move-block" ref="moveBlock" @touchstart="start" @touchend="end" @mousedown="start" @mouseleave="handleMouseLeave" :style="moveBlockStyle">
        <i class="verify-icon iconfont-verify icon-right" v-if="isPassing"></i>
        <i class="verify-icon iconfont-verify icon-slider" v-else></i>
      </div>
    </div>
  </div>
</template>

<script>
import { aesEncrypt } from '@/utils/helpers/crypto'
import { getPuzzleCaptcha, checkPuzzleCaptcha } from '@/api/modules/captcha'
import { showError, logError } from '@/utils/common/errorHandler'
import { showLoading, hideLoading } from '@/utils/common/loadingManager'
import { getResponsiveSize } from '@/utils/common/deviceInfo'

export default {
  name: 'VerifySlide',
  props: {
    captchaType: {
      type: String,
      default: 'blockPuzzle'
    },
    isPassing: {
      type: Boolean,
      default: false
    },
    callback: {
      type: Function,
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      blockImg: '',
      bgImg: '',
      sliderLeft: 0, // 使用数值类型，更精确控制
      startLeft: 0,
      moveBlockLeft: 0,
      leftBarWidth: 0,
      status: false,
      isMoving: false,
      startMoveTime: null,
      endMoveTIme: null,
      captchaObj: null,
      secretKey: '',
      startX: 0,
      startY: 0,
      explain: '向右滑动完成验证',
      finishText: '',
      containerWidth: 310,
      currentScale: 1, // 当前缩放比例
      resizeObserver: null,
      // 拖拽优化
      isTouch: false,
      moveDistance: 0,
      maxDistance: 0
    }
  },
  computed: {
    imgSize() {
      const baseWidth = 310
      const baseHeight = 155
      
      // 使用统一的响应式尺寸服务
      const responsiveSize = getResponsiveSize(baseWidth, baseHeight)
      this.currentScale = responsiveSize.scale
      
      // 更新最大移动距离
      this.maxDistance = responsiveSize.width - 40
      
      return {
        width: responsiveSize.width + 'px',
        height: responsiveSize.height + 'px',
        scale: responsiveSize.scale
      }
    },
    // 滑块图片样式
    blockImgStyle() {
      return {
        left: this.sliderLeft + 'px',
        top: '0px',
        position: 'absolute',
        zIndex: 3,
        transition: this.isMoving ? 'none' : 'left 0.3s ease'
      }
    },
    // 左侧进度条样式
    leftBarStyle() {
      return {
        width: this.leftBarWidth + 'px',
        transition: this.isMoving ? 'none' : 'width 0.3s ease'
      }
    },
    // 滑块按钮样式
    moveBlockStyle() {
      return {
        left: this.sliderLeft + 'px',
        transition: this.isMoving ? 'none' : 'left 0.3s ease',
        backgroundColor: this.status ? '#5cb85c' : '#fff',
        boxShadow: this.isMoving ? '0 0 10px rgba(0, 0, 0, 0.4)' : '0 0 5px rgba(0, 0, 0, 0.2)'
      }
    }
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.resetPosition()
          this.$nextTick(() => {
            this.init()
          })
        }
      },
      immediate: true
    }
  },
  created() {
    this.resetPosition()
    
  },
  mounted() {
    if (this.visible) {
      this.init()
    }
    
    // 设置响应式监听
    this.setupResizeObserver()
    
    // 全局事件监听
    document.addEventListener('mousemove', this.handleGlobalMouseMove, { passive: false })
    document.addEventListener('mouseup', this.handleGlobalMouseUp, { passive: false })
    document.addEventListener('touchmove', this.handleGlobalTouchMove, { passive: false })
    document.addEventListener('touchend', this.handleGlobalTouchEnd, { passive: false })
  },
  beforeUnmount() {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
    }
    
    document.removeEventListener('mousemove', this.handleGlobalMouseMove)
    document.removeEventListener('mouseup', this.handleGlobalMouseUp)
    document.removeEventListener('touchmove', this.handleGlobalTouchMove)
    document.removeEventListener('touchend', this.handleGlobalTouchEnd)
  },
  methods: {
    init() {
      this.getImgData()
      this.resetPosition()
    },
    
    // 重置滑块位置
    resetPosition() {
      this.sliderLeft = 0
      this.moveBlockLeft = 0
      this.leftBarWidth = 0
      this.startLeft = 0
      this.status = false
      this.isMoving = false
      this.startX = 0
      this.startY = 0
      this.moveDistance = 0
      this.explain = '向右滑动完成验证'
      this.finishText = ''
      
      console.log('滑块位置已重置为初始状态')
    },
    
    // 获取验证码图片数据
    async getImgData() {
      try {
        const res = await getPuzzleCaptcha()
        // 适配新的后端返回格式: { code: 200, msg: "操作成功", data: { repCode: "0000", repData: {...} } }
        if (res && res.code === 200 && res.data && res.data.repCode === '0000') {
          this.captchaObj = res.data.repData
          this.blockImg = 'data:image/png;base64,' + this.captchaObj.jigsawImageBase64
          this.bgImg = 'data:image/png;base64,' + this.captchaObj.originalImageBase64
          this.secretKey = res.data.repData.secretKey || ''
          
          // 验证码数据加载成功
        } else {
          logError('获取验证码失败', { response: res })
          showError('获取验证码失败，请重试')
        }
      } catch (error) {
        logError('获取验证码请求失败', { error })
        showError('网络异常，请检查网络连接')
      }
    },
    
    // 刷新验证码
    refresh() {
      this.resetPosition()
      this.init()
    },
    
    // 设置响应式监听
    setupResizeObserver() {
      this.$nextTick(() => {
        const panel = this.$refs.imagePanel
        if (panel && window.ResizeObserver) {
          this.resizeObserver = new ResizeObserver(entries => {
            for (let entry of entries) {
              this.containerWidth = entry.contentRect.width
              // 窗口大小变化时重置位置
              if (!this.isMoving && !this.status) {
                this.resetPosition()
              }
            }
          })
          this.resizeObserver.observe(panel.parentElement || panel)
        }
      })
    },
    
    // 全局事件处理
    handleGlobalMouseMove(e) {
      if (this.isMoving && !this.isTouch) {
        requestAnimationFrame(() => this.move(e))
      }
    },
    
    handleGlobalMouseUp(e) {
      if (this.isMoving && !this.isTouch) {
        this.end(e)
      }
    },
    
    handleGlobalTouchMove(e) {
      if (this.isMoving && this.isTouch) {
        requestAnimationFrame(() => this.move(e))
      }
    },
    
    handleGlobalTouchEnd(e) {
      if (this.isMoving && this.isTouch) {
        this.end(e)
      }
    },
    
    handleMouseLeave(e) {
      // 不在这里结束拖拽，继续监听全局事件
    },
    
    // 开始拖拽
    start(e) {
      if (this.status || this.isMoving) return
      
      e.preventDefault()
      e.stopPropagation()
      
      this.isTouch = e.type.includes('touch')
      this.isMoving = true
      
      const eventX = this.isTouch ? e.touches[0].clientX : e.clientX
      const eventY = this.isTouch ? e.touches[0].clientY : e.clientY
      
      if (typeof eventX !== 'number' || typeof eventY !== 'number') {
        this.isMoving = false
        return
      }
      
      this.startX = eventX
      this.startY = eventY
      this.startLeft = this.sliderLeft
      this.startMoveTime = Date.now()
      
      document.body.style.userSelect = 'none'
      document.body.style.webkitUserSelect = 'none'
      
      // console.log('开始拖拽:', { startX: this.startX, startLeft: this.startLeft })
    },
    
    // 移动过程
    move(e) {
      if (!this.isMoving) return
      
      e.preventDefault()
      e.stopPropagation()
      
      const eventX = this.isTouch ? e.touches[0].clientX : e.clientX
      
      if (typeof eventX !== 'number') return
      
      let moveX = eventX - this.startX
      
      // 边界限制
      if (moveX < 0) {
        moveX = 0
      } else if (moveX > this.maxDistance) {
        moveX = this.maxDistance
      }
      
      const newPosition = this.startLeft + moveX
      
      // 更新位置
      this.sliderLeft = newPosition
      this.moveBlockLeft = newPosition
      this.leftBarWidth = newPosition
      this.moveDistance = moveX
    },
    
    // 结束拖拽
    async end(e) {
      if (!this.isMoving) return
      
      this.isMoving = false
      this.endMoveTIme = Date.now()
      
      document.body.style.userSelect = ''
      document.body.style.webkitUserSelect = ''
      
      // console.log('拖拽结束:', { 
      //   moveDistance: this.moveDistance, 
      //   sliderLeft: this.sliderLeft,
      //   maxDistance: this.maxDistance,
      //   scale: this.currentScale
      // })
      
      // 检查移动距离
      if (this.moveDistance < 10) {
        this.resetPosition()
        this.explain = '请拖动滑块'
        return
      }
      
      try {
        // 根据AJ-Captcha官方文档，坐标需要按照原始图片尺寸计算
        // 原始图片尺寸是310x155，需要将当前移动距离转换为原始尺寸的坐标
        const originalMoveDistance = Math.round(this.sliderLeft / this.currentScale)
        
        const pointData = { x: originalMoveDistance, y: 5.0 }
        const pointDataJson = JSON.stringify(pointData)
        
        const data = {
          captchaType: this.captchaType,
          pointJson: this.secretKey ? aesEncrypt(pointDataJson, this.secretKey) : pointDataJson,
          token: this.captchaObj.token
        }
        
        // console.log('发送验证请求:', {
        //   原始移动距离: this.sliderLeft,
        //   缩放比例: this.currentScale,
        //   转换后距离: originalMoveDistance,
        //   坐标数据: pointData,
        //   是否加密: !!this.secretKey
        // })
        
        const res = await checkPuzzleCaptcha(data)
        
        // 适配新的后端返回格式: { code: 200, msg: "操作成功", data: { repCode: "0000", repData: { result: true/false } } }
        if (res && res.code === 200 && res.data && res.data.repCode === '0000' && res.data.repData && res.data.repData.result) {
          // 验证成功
          this.status = true
          this.$emit('update:isPassing', true)
          this.explain = '验证通过'
          this.finishText = '验证通过'
          
          // 生成captchaVerification，遵循AJ-Captcha官方格式
          const captchaVerification = this.secretKey ?
            aesEncrypt(this.captchaObj.token + '---' + JSON.stringify(pointData), this.secretKey) :
            this.captchaObj.token + '---' + JSON.stringify(pointData)
          
          const verifyResult = {
            captchaVerification: captchaVerification,
            captchaType: this.captchaType
          }
          
          // console.log('验证成功，返回结果:', verifyResult)
          this.callback(verifyResult)
          
        } else {
          // 验证失败
          this.handleVerifyFail()
        }
        
      } catch (error) {
        logError('验证请求失败', { error })
        this.handleVerifyFail()
      }
    },
    
    // 处理验证失败
    handleVerifyFail() {
      this.status = false
      this.$emit('update:isPassing', false)
      this.explain = '验证失败，请重试'
      this.finishText = ''
      
      // 1秒后重置并刷新
      setTimeout(() => {
        this.resetPosition()
        this.init()
      }, 1000)
    }
  }
}
</script>

<style scoped>
.verify-slide {
  position: relative;
  background-color: #fff;
  border-radius: 5px;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.verify-slide * {
  box-sizing: border-box;
}

.verify-refresh {
  position: absolute;
  right: 5px;
  top: 5px;
  width: 30px;
  height: 30px;
  cursor: pointer;
  z-index: 4;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.verify-image-panel {
  position: relative;
  margin: 0 auto;
  border-radius: 5px;
  overflow: hidden;
  background-color: #e8e8e8;
  max-width: 100%;
  width: 100%;
}

.verify-image-panel img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
  max-width: 100%;
}

.block-img {
  position: absolute !important;
  top: 0 !important;
  z-index: 3 !important;
  width: auto !important;
  height: auto !important;
  max-width: none !important;
  object-fit: none !important;
}

.verify-bar-area {
  position: relative;
  margin: 10px auto;
  background-color: #f5f5f5;
  border-radius: 20px;
  border: 1px solid #ddd;
  overflow: hidden;
  max-width: 100%;
  width: 100%;
  height: 40px !important;
  line-height: 40px;
}

.verify-left-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  border-radius: 20px;
  background-color: #007AFF;
  min-width: 0;
  max-width: 100%;
}

.verify-move-block {
  position: absolute;
  top: 0;
  left: 0;
  width: 40px;
  height: 40px;
  background-color: #fff;
  border-radius: 50%;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  touch-action: none;
}

.verify-move-block:hover {
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
}

.verify-move-block:active {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.4);
  transform: scale(1.05);
}

.verify-icon {
  font-size: 20px;
  color: #007AFF;
}

.verify-icon.icon-right {
  color: #4cae4c;
}

.verify-icon.icon-slider {
  color: #007AFF;
}

.verify-msg {
  width: 100%;
  text-align: center;
  line-height: 40px;
  font-size: 14px;
  color: #666;
  user-select: none;
  position: relative;
  z-index: 1;
}

.verify-left-bar .verify-msg {
  color: #fff;
}

/* 使用Unicode字符替代自定义图标字体 */
.iconfont-verify {
  font-family: system-ui, -apple-system, sans-serif;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-refresh:before {
  content: "↻";
}

.icon-right:before {
  content: "✓";
}

.icon-slider:before {
  content: "→";
}

/* 响应式媒体查询 */
@media (max-width: 768px) {
  .verify-slide {
    width: 100%;
    max-width: calc(100vw - 40px);
  }
  
  .verify-image-panel, 
  .verify-bar-area {
    width: 100%;
    max-width: calc(100vw - 40px);
  }
}

@media (max-width: 480px) {
  .verify-slide {
    width: 100%;
    max-width: calc(100vw - 20px);
  }
  
  .verify-image-panel, 
  .verify-bar-area {
    width: 100%;
    max-width: calc(100vw - 20px);
  }
  
  .verify-msg {
    font-size: 12px;
  }
}
</style>