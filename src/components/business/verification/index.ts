/**
 * 验证组件统一导出
 */

export { default as Verify } from './Verify.vue'
export { default as VerifyPoints } from './utils/VerifyPoints.vue'
export { default as VerifySlide } from './utils/VerifySlide.vue'

// 验证组件类型定义
export interface VerifyProps {
  // 验证组件属性
  type?: 'slide' | 'point' | 'rotate'
  width?: number
  height?: number
  refreshOnFail?: boolean
  onSuccess?: (data: any) => void
  onFail?: (error: any) => void
}

export interface VerifyPointsProps {
  // 点选验证属性
  pointCount?: number
  tolerance?: number
}

export interface VerifySlideProps {
  // 滑动验证属性
  tolerance?: number
  showRefresh?: boolean
}