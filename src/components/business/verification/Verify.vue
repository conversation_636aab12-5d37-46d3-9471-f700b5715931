<template>
  <view>
    <view v-if="mode === 'pop'" class="modal-verify" v-show="visible">
      <view class="verify-mask" @click="close"></view>
      <view class="verify-panel">
        <text class="verify-title">安全验证</text>
        <view class="verify-close" @click="close"><text>×</text></view>
        <view class="verify-body">
          <view class="verify-tips" v-if="tipWords">{{tipWords}}</view>
          <view class="verify-content">
            <VerifySlide v-if="captchaType === 'blockPuzzle'" :captchaType="captchaType" v-model:isPassing="isPassing" :callback="success" :visible="visible"></VerifySlide>
            <VerifyPoints v-if="captchaType === 'clickWord'" :captchaType="captchaType" v-model:isPassing="isPassing" :callback="success" :visible="visible"></VerifyPoints>
          </view>
        </view>
      </view>
    </view>
    <view v-else class="verify-fixed">
      <view class="verify-tips" v-if="tipWords">{{tipWords}}</view>
      <view class="verify-content">
        <VerifySlide v-if="captchaType === 'blockPuzzle'" :captchaType="captchaType" v-model:isPassing="isPassing" :callback="success" :visible="visible"></VerifySlide>
        <VerifyPoints v-if="captchaType === 'clickWord'" :captchaType="captchaType" v-model:isPassing="isPassing" :callback="success" :visible="visible"></VerifyPoints>
      </view>
    </view>
  </view>
</template>

<script>
import VerifySlide from './utils/VerifySlide.vue'
import VerifyPoints from './utils/VerifyPoints.vue'
import { StoreUtil, StorageModule } from '@/utils/storage'

export default {
  name: 'Verify',
  components: {
    VerifySlide,
    VerifyPoints
  },
  props: {
    // 验证码类型：blockPuzzle-滑块拼图, clickWord-文字点选
    captchaType: {
      type: String,
      default: 'blockPuzzle'
    },
    // 验证码显示模式：pop-弹出式, fixed-固定式
    mode: {
      type: String,
      default: 'pop'
    },
    // 滑动条内提示文字
    explain: {
      type: String,
      default: '向右滑动完成验证'
    },
    // 图片宽度
    imgSize: {
      type: Object,
      default: () => ({
        width: '330px',
        height: '155px'
      })
    },
    // 验证码和滑动条之间的间距
    vSpace: {
      type: Number,
      default: 5
    }
  },
  data() {
    return {
      // 验证码是否通过
      isPassing: false,
      // 气泡提示文字
      tipWords: '',
      // 弹出式组件是否可见
      visible: false,
      // 组件状态标识
      stateKey: `verify_${this.captchaType}_${Date.now()}`
    }
  },
  methods: {
    // 保存组件状态
    async saveState() {
      try {
        const state = {
          isVerified: this.isPassing,
          verifyData: null,
          countdownEndTime: null,
          countdownKey: this.stateKey,
          componentState: {
            visible: this.visible,
            isPassing: this.isPassing,
            tipWords: this.tipWords
          },
          savedAt: Date.now()
        }
        

        await StoreUtil.set(`captcha_state_${this.stateKey}`, JSON.stringify(state), StorageModule.CONFIG)
      } catch (error) {

      }
    },
    
    // 恢复组件状态
    async restoreState() {
      try {
        const stored = await StoreUtil.get(`captcha_state_${this.stateKey}`, StorageModule.CONFIG)
        if (!stored) return
        
        const savedState = JSON.parse(stored)

        
        // 检查状态是否过期（24小时）
        const now = Date.now()
        if (savedState.savedAt && (now - savedState.savedAt > 24 * 60 * 60 * 1000)) {

          await this.clearState()
          return
        }
        
        if (savedState && savedState.componentState) {
          this.isPassing = savedState.componentState.isPassing
          this.tipWords = savedState.componentState.tipWords
          // 不恢复visible状态，避免自动弹出

        }
      } catch (error) {

      }
    },
    
    // 验证成功的回调
    success(params) {
      this.isPassing = true
      this.visible = false
      this.saveState() // 保存状态
      this.$emit('success', params)
    },
    
    // 弹出式调用方法
    show() {
      this.visible = true
      this.isPassing = false
      this.saveState() // 保存状态
    },
    
    // 关闭弹出层
    close() {
      this.visible = false
      this.saveState() // 保存状态
      this.$emit('error')
    },
    
    // 清除验证码状态
    async clearState() {
      try {

        await StoreUtil.remove(`captcha_state_${this.stateKey}`, StorageModule.CONFIG)
      } catch (error) {

      }
    },
    
    // 重置组件状态
    reset() {
      this.isPassing = false
      this.visible = false
      this.tipWords = ''
      this.clearState()
    }
  },
  mounted() {
    // 生成唯一状态标识
    this.stateKey = `verify_${this.captchaType}_${this._uid || Math.random()}`
    
    // 恢复状态
    this.restoreState()
    
    // 如果是fixed模式，初始就显示
    if (this.mode === 'fixed') {
      this.visible = true
      this.saveState()
    }
    
    this.$emit('ready')
  },
  
  beforeUnmount() {
    // 组件销毁前清理状态（根据需要）
    // clearCaptchaState(this.stateKey)
  }
}
</script>

<style scoped>
/* 弹出式 */
.modal-verify {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.verify-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: -1;
}

.verify-panel {
  position: relative;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  max-width: 90%;
  width: 350px;
  box-sizing: border-box;
  padding: 15px;
}

.verify-title {
  text-align: center;
  font-size: 16px;
  margin: 0 0 15px 0;
  padding: 0;
  color: #333;
}

.verify-close {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 24px;
  height: 24px;
  text-align: center;
  line-height: 24px;
  cursor: pointer;
  color: #999;
}

.verify-close i {
  font-style: normal;
  font-size: 16px;
}

.verify-body {
  padding: 0;
}

.verify-tips {
  padding: 5px;
  margin: 5px 0;
  text-align: center;
  background-color: #f8f8f8;
  color: #999;
  font-size: 12px;
  border-radius: 3px;
}

.verify-content {
  position: relative;
}

.verify-fixed {
  padding: 15px;
}
</style>