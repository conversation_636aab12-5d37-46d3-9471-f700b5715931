/**
 * 业务组件统一导出
 * 提供所有业务组件的统一入口
 */

// 验证组件
export * from './verification'

// 业务组件类型定义
export interface BusinessComponentProps {
  // 通用业务属性
  loading?: boolean
  disabled?: boolean
}

// 验证组件属性
export interface VerificationProps extends BusinessComponentProps {
  // 验证码相关属性
  length?: number
  type?: 'number' | 'text' | 'mixed'
  autoFocus?: boolean
  secure?: boolean
}

// 图形验证码属性
export interface CaptchaProps extends BusinessComponentProps {
  // 图形验证码属性
  width?: number
  height?: number
  refreshable?: boolean
  placeholder?: string
}