import type { RealAuthFormData, AuthRequestParams } from '@/api/auth/types'
import { submitRealAuth } from '@/api/auth/auth'

/**
 * 实名认证组合式函数
 */
export function useRealAuth() {
  /**
   * 提交实名认证
   * @param formData 认证表单数据
   */
  const submitAuth = async (formData: RealAuthFormData) => {
    // 转换表单数据为API所需的格式
    const authParams: AuthRequestParams = {
      name: formData.name,
      idCard: formData.idCard,
      timestamp: Date.now()
    }

    const res = await submitRealAuth(authParams)

    if (res.code === 200) {
      return {
        success: true,
        message: '认证成功'
      }
    } else {
      return {
        success: false,
        message: res.message || '认证失败'
      }
    }
  }

  return {
    submitAuth
  }
}
