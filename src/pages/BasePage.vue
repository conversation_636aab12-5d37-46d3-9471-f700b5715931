<template>
  <view class="base-page" :class="{
    'base-page--loading': loading,
    'base-page--no-header': !showHeader,
    'base-page--no-footer': !showFooter
  }" :style="pageStyle">
    <!-- 页面头部 -->
    <view v-if="showHeader" class="base-page__header">
      <slot name="header">
        <view class="base-page__nav">
          <view v-if="showBack" class="base-page__back" @click="handleBack">
            <text class="base-page__back-icon">‹</text>
          </view>
          
          <view class="base-page__title">
            <text class="base-page__title-text">{{ title }}</text>
          </view>
          
          <view class="base-page__actions">
            <slot name="headerActions"></slot>
          </view>
        </view>
      </slot>
    </view>
    
    <!-- 页面内容 -->
    <view class="base-page__content" :class="{
      'base-page__content--scrollable': scrollable,
      'base-page__content--safe-area': safeArea
    }">
      <!-- 加载状态 -->
      <BaseLoading 
        v-if="loading" 
        :visible="loading" 
        :text="loadingText"
        overlay
      />
      
      <!-- 错误状态 -->
      <view v-else-if="error" class="base-page__error">
        <slot name="error" :error="error" :retry="handleRetry">
          <view class="base-page__error-content">
            <text class="base-page__error-icon">⚠</text>
            <text class="base-page__error-message">{{ error.message || '页面加载失败' }}</text>
            <view class="base-page__error-actions">
              <BaseButton 
                type="primary" 
                size="small" 
                @click="handleRetry"
              >
                重试
              </BaseButton>
            </view>
          </view>
        </slot>
      </view>
      
      <!-- 空状态 -->
      <view v-else-if="empty" class="base-page__empty">
        <slot name="empty" :refresh="handleRefresh">
          <view class="base-page__empty-content">
            <text class="base-page__empty-icon">📭</text>
            <text class="base-page__empty-message">{{ emptyText || '暂无数据' }}</text>
            <view v-if="showRefresh" class="base-page__empty-actions">
              <BaseButton 
                type="primary" 
                size="small" 
                @click="handleRefresh"
              >
                刷新
              </BaseButton>
            </view>
          </view>
        </slot>
      </view>
      
      <!-- 正常内容 -->
      <view v-else class="base-page__main">
        <slot></slot>
      </view>
    </view>
    
    <!-- 页面底部 -->
    <view v-if="showFooter" class="base-page__footer">
      <slot name="footer"></slot>
    </view>
    
    <!-- 浮动操作按钮 -->
    <view v-if="$slots.fab" class="base-page__fab">
      <slot name="fab"></slot>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { BaseLoading, BaseButton } from '@/components/base'

// 组件属性
interface Props {
  title?: string
  loading?: boolean
  loadingText?: string
  error?: Error | null
  empty?: boolean
  emptyText?: string
  showHeader?: boolean
  showFooter?: boolean
  showBack?: boolean
  showRefresh?: boolean
  scrollable?: boolean
  safeArea?: boolean
  backgroundColor?: string
  padding?: string | number
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  loading: false,
  loadingText: '加载中...',
  error: null,
  empty: false,
  emptyText: '',
  showHeader: true,
  showFooter: false,
  showBack: true,
  showRefresh: true,
  scrollable: true,
  safeArea: true,
  backgroundColor: '#f5f5f5',
  padding: '0'
})

// 事件定义
const emit = defineEmits<{
  back: []
  retry: []
  refresh: []
}>()

// 计算属性
const pageStyle = computed(() => {
  const style: Record<string, any> = {}
  
  if (props.backgroundColor) {
    style.backgroundColor = props.backgroundColor
  }
  
  if (props.padding) {
    style.padding = typeof props.padding === 'number' ? `${props.padding}rpx` : props.padding
  }
  
  return style
})

// 事件处理
const handleBack = () => {
  emit('back')
  // 默认返回行为
  uni.navigateBack({
    delta: 1
  })
}

const handleRetry = () => {
  emit('retry')
}

const handleRefresh = () => {
  emit('refresh')
}
</script>

<style lang="scss" scoped>
.base-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  
  &__header {
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: #ffffff;
    border-bottom: 1px solid #e5e5e5;
  }
  
  &__nav {
    display: flex;
    align-items: center;
    height: 88rpx;
    padding: 0 32rpx;
    
    // 状态栏适配
    padding-top: env(safe-area-inset-top);
  }
  
  &__back {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 64rpx;
    height: 64rpx;
    margin-right: 16rpx;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #f0f0f0;
    }
    
    &:active {
      background-color: #e0e0e0;
    }
    
    &-icon {
      font-size: 48rpx;
      font-weight: bold;
      color: #333333;
      line-height: 1;
    }
  }
  
  &__title {
    flex: 1;
    text-align: center;
    
    &-text {
      font-size: 36rpx;
      font-weight: 600;
      color: #333333;
      line-height: 1.4;
    }
  }
  
  &__actions {
    min-width: 80rpx;
    display: flex;
    justify-content: flex-end;
  }
  
  &__content {
    flex: 1;
    position: relative;
    
    &--scrollable {
      overflow-y: auto;
    }
    
    &--safe-area {
      padding-bottom: env(safe-area-inset-bottom);
    }
  }
  
  &__main {
    min-height: 100%;
  }
  
  &__error,
  &__empty {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    padding: 64rpx 32rpx;
    
    &-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      max-width: 480rpx;
    }
    
    &-icon {
      font-size: 96rpx;
      margin-bottom: 32rpx;
      opacity: 0.6;
    }
    
    &-message {
      font-size: 28rpx;
      color: #666666;
      line-height: 1.6;
      margin-bottom: 32rpx;
    }
    
    &-actions {
      display: flex;
      gap: 16rpx;
    }
  }
  
  &__footer {
    background-color: #ffffff;
    border-top: 1px solid #e5e5e5;
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  &__fab {
    position: fixed;
    right: 32rpx;
    bottom: 32rpx;
    z-index: 200;
    
    // 安全区域适配
    bottom: calc(32rpx + env(safe-area-inset-bottom));
  }
  
  // 状态样式
  &--loading {
    .base-page__content {
      pointer-events: none;
    }
  }
  
  &--no-header {
    .base-page__content {
      padding-top: env(safe-area-inset-top);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .base-page {
    &__nav {
      height: 80rpx;
      padding: 0 24rpx;
    }
    
    &__back {
      width: 56rpx;
      height: 56rpx;
      margin-right: 12rpx;
      
      &-icon {
        font-size: 40rpx;
      }
    }
    
    &__title {
      &-text {
        font-size: 32rpx;
      }
    }
    
    &__error,
    &__empty {
      padding: 48rpx 24rpx;
      
      &-icon {
        font-size: 80rpx;
        margin-bottom: 24rpx;
      }
      
      &-message {
        font-size: 26rpx;
        margin-bottom: 24rpx;
      }
    }
  }
}
</style>