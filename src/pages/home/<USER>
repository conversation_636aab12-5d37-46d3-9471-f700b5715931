<template>
  <view class="home-container">
    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-bar">
        <view class="search-input" @click="handleSearch">
          <uni-icons type="search" size="18" color="#999"></uni-icons>
          <text class="search-placeholder">搜索服务、技能、需求</text>
        </view>
        <view class="location-btn" @click="handleLocation">
          <uni-icons type="location" size="16" color="#fff" class="location-icon"></uni-icons>
          <text class="location-text">{{ currentLocation }}</text>
        </view>
      </view>
    </view>

    <!-- 轮播图 -->
    <view class="banner-section" v-if="homeConfig?.banners?.length">
      <swiper class="banner-swiper" indicator-dots circular autoplay>
        <swiper-item v-for="banner in homeConfig.banners" :key="banner.id" class="banner-item">
          <image class="banner-image" :src="banner.image" mode="aspectFill" @click="handleBannerClick(banner)"></image>
          <view class="banner-overlay">
            <text class="banner-title">{{ banner.title }}</text>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 动态金刚位 -->
    <view class="grid-section">
      <view class="grid-container">
        <view 
          class="grid-item" 
          v-for="item in dynamicGridItems" 
          :key="item.id"
          @click="handleGridClick(item)"
        >
          <view class="grid-icon" :style="{ background: item.gradient || item.color }">
            <text class="icon-emoji">{{ item.icon }}</text>
          </view>
          <text class="grid-title">{{ item.title }}</text>
          <view class="hot-badge" v-if="item.isHot">
            <text>热</text>
          </view>
          <view class="new-badge" v-if="item.isNew">
            <text>新</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 推荐服务 -->
    <view class="recommend-section">
      <view class="section-header">
        <text class="section-title">热门推荐</text>
        <view class="more-btn" @click="handleViewMore">
          <text class="more-text">查看更多</text>
          <uni-icons type="right" size="12" color="#999" class="more-icon"></uni-icons>
        </view>
      </view>
      <view class="service-list">
        <view class="service-item" v-for="service in recommendedServices" :key="service.id" @click="handleServiceClick(service)">
          <image class="service-image" :src="service.images[0]" mode="aspectFill"></image>
          <view class="service-info">
            <text class="service-title">{{ service.title }}</text>
            <text class="service-desc">{{ service.description }}</text>
            <view class="service-meta">
              <text class="service-price">¥{{ service.price }}<text class="price-unit">/{{ service.unit }}</text></text>
              <view class="service-rating">
                <uni-icons type="star-filled" size="12" color="#ffd700" class="rating-star"></uni-icons>
                <text class="rating-text">{{ service.rating }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 快速发布 -->
    <view class="publish-section">
      <text class="section-title">快速发布</text>
      <view class="publish-buttons">
        <view class="publish-btn service-btn" @click="handlePublishService">
          <uni-icons type="plus" size="24" color="#fff" class="publish-icon"></uni-icons>
          <text class="publish-text">发布服务</text>
        </view>
        <view class="publish-btn demand-btn" @click="handlePublishDemand">
          <uni-icons type="help" size="24" color="#fff" class="publish-icon"></uni-icons>
          <text class="publish-text">发布需求</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useServiceStore } from '@/store/modules/service'
import type { ServiceDetail } from '@/types/business'

const serviceStore = useServiceStore()

// 响应式数据
const currentLocation = ref('定位中...')

// 计算属性
const homeConfig = computed(() => serviceStore.homeConfig)
const coreServices = computed(() => serviceStore.getCoreServices)
const homeServices = computed(() => serviceStore.getHomeServices)
const extendServices = computed(() => serviceStore.getExtendServices)
const recommendedServices = computed(() => serviceStore.getRecommendedServices)
const dynamicGridItems = computed(() => serviceStore.dynamicGridItems)

// 生命周期
onMounted(async () => {
  // 设置导航栏标题
  uni.setNavigationBarTitle({
    title: '乐享生活'
  })
  
  await serviceStore.fetchHomeConfig()
  getCurrentLocation()
})

// 方法
const getCurrentLocation = () => {
  uni.getLocation({
    type: 'wgs84',
    success: (res) => {
      // TODO: 根据经纬度获取地址信息
      currentLocation.value = '当前位置'
    },
    fail: () => {
      currentLocation.value = '定位失败'
    }
  })
}

const handleSearch = () => {
  uni.navigateTo({
    url: '/pages/service/search'
  })
}

const handleLocation = () => {
  uni.chooseLocation({
    success: (res) => {
      currentLocation.value = res.name || res.address
    }
  })
}

const handleBannerClick = (banner: any) => {
  if (banner.link) {
    uni.navigateTo({
      url: banner.link
    })
  }
}

const handleGridClick = (item: any) => {
  uni.navigateTo({
    url: item.route
  })
}

const handleServiceClick = (service: ServiceDetail) => {
  uni.navigateTo({
    url: `/pages/service/detail?id=${service.id}`
  })
}

const handleViewMore = () => {
  uni.navigateTo({
    url: '/pages/service/list'
  })
}

const handlePublishService = () => {
  uni.navigateTo({
    url: '/pages/publish/service'
  })
}

const handlePublishDemand = () => {
  uni.navigateTo({
    url: '/pages/publish/demand'
  })
}
</script>

<style lang="scss">
.home-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9ff 0%, #ffffff 100%);
  
  // 搜索栏样式
  .search-section {
    padding: 30rpx 30rpx 20rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    
    .search-bar {
      display: flex;
      align-items: center;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 50rpx;
      padding: 20rpx 30rpx;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
      
      .search-icon {
        font-size: 32rpx;
        color: #999;
        margin-right: 20rpx;
      }
      
      .search-input {
        flex: 1;
        font-size: 28rpx;
        color: #333;
        
        &::placeholder {
          color: #999;
        }
      }
      
      .location-btn {
        display: flex;
        align-items: center;
        padding: 10rpx 20rpx;
        background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        border-radius: 30rpx;
        margin-left: 20rpx;
        
        .location-icon {
          font-size: 24rpx;
          color: white;
          margin-right: 8rpx;
        }
        
        .location-text {
          font-size: 24rpx;
          color: white;
        }
      }
    }
  }
  
  // 轮播图样式
  .banner-section {
    padding: 30rpx;
    
    .banner-swiper {
      height: 300rpx;
      border-radius: 20rpx;
      overflow: hidden;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
      
      .banner-item {
        position: relative;
        width: 100%;
        height: 100%;
        
        .banner-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        
        .banner-overlay {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
          padding: 40rpx 30rpx 20rpx;
          
          .banner-title {
            font-size: 32rpx;
            font-weight: bold;
            color: white;
            text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
          }
        }
      }
    }
  }
  
  // 金刚位网格样式
  .grid-section {
    padding: 0 30rpx 30rpx;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30rpx;
      
      .section-title {
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
        position: relative;
        
        &::after {
          content: '';
          position: absolute;
          bottom: -8rpx;
          left: 0;
          width: 60rpx;
          height: 6rpx;
          background: linear-gradient(135deg, #667eea, #764ba2);
          border-radius: 3rpx;
        }
      }
      
      .more-btn {
        display: flex;
        align-items: center;
        color: #999;
        font-size: 26rpx;
        
        .more-text {
          margin-right: 8rpx;
        }
        
        .more-icon {
          font-size: 20rpx;
        }
      }
    }
    
    .grid-container {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 30rpx;
      
      .grid-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 30rpx 20rpx;
        background: white;
        border-radius: 20rpx;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        position: relative;
        
        &:active {
          transform: scale(0.95);
        }
        
        .grid-icon {
          width: 80rpx;
          height: 80rpx;
          border-radius: 20rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 36rpx;
          color: white;
          margin-bottom: 16rpx;
          position: relative;
          overflow: hidden;
          
          .icon-emoji {
            font-size: 40rpx;
            line-height: 1;
          }
          
          &::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
          }
          
          &:hover::before {
            opacity: 1;
            animation: shine 0.6s ease;
          }
        }
        
        .grid-title {
          font-size: 24rpx;
          color: #333;
          text-align: center;
          font-weight: 500;
        }
        
        .hot-badge {
          position: absolute;
          top: -8rpx;
          right: -8rpx;
          background: linear-gradient(135deg, #ff6b6b, #ee5a24);
          color: white;
          font-size: 20rpx;
          padding: 4rpx 12rpx;
          border-radius: 20rpx;
          transform: scale(0.8);
        }
        
        .new-badge {
          position: absolute;
          top: -8rpx;
          right: -8rpx;
          background: linear-gradient(135deg, #4ecdc4, #44a08d);
          color: white;
          font-size: 20rpx;
          padding: 4rpx 12rpx;
          border-radius: 20rpx;
          transform: scale(0.8);
        }
      }
    }
  }
  
  // 推荐服务样式
  .recommend-section {
    padding: 0 30rpx 30rpx;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30rpx;
      
      .section-title {
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
        position: relative;
        
        &::after {
          content: '';
          position: absolute;
          bottom: -8rpx;
          left: 0;
          width: 60rpx;
          height: 6rpx;
          background: linear-gradient(135deg, #ff6b6b, #ee5a24);
          border-radius: 3rpx;
        }
      }
      
      .more-btn {
        display: flex;
        align-items: center;
        color: #999;
        font-size: 26rpx;
        
        .more-text {
          margin-right: 8rpx;
        }
        
        .more-icon {
          font-size: 20rpx;
        }
      }
    }
    
    .service-list {
      display: flex;
      flex-direction: column;
      gap: 20rpx;
      
      .service-item {
        display: flex;
        background: white;
        border-radius: 20rpx;
        padding: 30rpx;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        
        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
        }
        
        .service-image {
          width: 120rpx;
          height: 120rpx;
          border-radius: 16rpx;
          margin-right: 24rpx;
          object-fit: cover;
        }
        
        .service-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          
          .service-title {
            font-size: 32rpx;
            font-weight: bold;
            color: #333;
            margin-bottom: 8rpx;
          }
          
          .service-desc {
            font-size: 26rpx;
            color: #666;
            margin-bottom: 16rpx;
            line-height: 1.4;
          }
          
          .service-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            
            .service-price {
              font-size: 32rpx;
              font-weight: bold;
              color: #ff6b6b;
              
              .price-unit {
                font-size: 24rpx;
                color: #999;
              }
            }
            
            .service-rating {
              display: flex;
              align-items: center;
              
              .rating-star {
                font-size: 24rpx;
                color: #ffd700;
                margin-right: 8rpx;
              }
              
              .rating-text {
                font-size: 24rpx;
                color: #999;
              }
            }
          }
        }
      }
    }
  }
  
  // 快速发布样式
  .publish-section {
    padding: 0 30rpx 60rpx;
    
    .section-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 30rpx;
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        bottom: -8rpx;
        left: 0;
        width: 60rpx;
        height: 6rpx;
        background: linear-gradient(135deg, #4ecdc4, #44a08d);
        border-radius: 3rpx;
      }
    }
    
    .publish-buttons {
      display: flex;
      gap: 20rpx;
      
      .publish-btn {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 40rpx 30rpx;
        background: white;
        border-radius: 20rpx;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        
        &:active {
          transform: scale(0.98);
        }
        
        &.service-btn {
          background: linear-gradient(135deg, #667eea, #764ba2);
          
          .publish-icon,
          .publish-text {
            color: white;
          }
        }
        
        &.demand-btn {
          background: linear-gradient(135deg, #ff6b6b, #ee5a24);
          
          .publish-icon,
          .publish-text {
            color: white;
          }
        }
        
        .publish-icon {
          font-size: 48rpx;
          margin-bottom: 16rpx;
        }
        
        .publish-text {
          font-size: 28rpx;
          font-weight: 500;
        }
      }
    }
  }
}

// 动画定义
@keyframes shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}
</style>