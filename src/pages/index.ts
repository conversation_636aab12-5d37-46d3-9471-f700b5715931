/**
 * 页面组件统一导出
 * 提供所有页面组件的统一入口
 */

// 认证相关页面
export { default as LoginPage } from './auth/login.vue'
export { default as RegisterPage } from './auth/register.vue'
export { default as RealAuthPage } from './auth/realAuth.vue'

// 主要功能页面
export { default as HomePage } from './home/<USER>/home.vue'
export { default as MatchPage } from './match/index/match.vue'
export { default as MessagePage } from './message/index/message.vue'
export { default as ServicePage } from './service/index/service.vue'

// 用户相关页面
export { default as MinePage } from './user/mine/Mine.vue'
export { default as InfomationPage } from './user/infomation/infomation.vue'
export { default as ExtendInfoPage } from './user/extendInfo/ExtendInfo.vue'
export { default as SetupProfilePage } from './user/setupProfile/SetupProfile.vue'

// 页面组件类型定义
export interface PageComponentProps {
  // 通用页面属性
  title?: string
  loading?: boolean
  showHeader?: boolean
  showFooter?: boolean
  backgroundColor?: string
}

// 认证页面属性
export interface AuthPageProps extends PageComponentProps {
  // 认证相关属性
  redirectUrl?: string
  autoLogin?: boolean
}

// 用户页面属性
export interface UserPageProps extends PageComponentProps {
  // 用户相关属性
  userId?: string
  editable?: boolean
}

// 列表页面属性
export interface ListPageProps extends PageComponentProps {
  // 列表相关属性
  pageSize?: number
  enablePullRefresh?: boolean
  enableLoadMore?: boolean
}