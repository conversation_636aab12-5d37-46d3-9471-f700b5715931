<template>
  <view class="publish-page">
    <!-- 页面头部 -->
    <view class="header">
      <view class="title">发布服务</view>
      <view class="subtitle">分享你的技能，连接更多可能</view>
    </view>

    <!-- 快速发布选项 -->
    <view class="quick-publish">
      <view class="section-title">快速发布</view>
      <view class="publish-grid">
        <view 
          class="publish-item" 
          v-for="item in quickPublishItems" 
          :key="item.id"
          @click="handleQuickPublish(item)"
        >
          <view class="item-icon">
            <uni-icons :type="item.icon" size="24" color="#007AFF"></uni-icons>
          </view>
          <view class="item-title">{{ item.title }}</view>
          <view class="item-desc">{{ item.desc }}</view>
        </view>
      </view>
    </view>

    <!-- 服务分类 -->
    <view class="service-categories">
      <view class="section-title">选择服务类型</view>
      <view class="category-list">
        <view 
          class="category-item" 
          v-for="category in serviceCategories" 
          :key="category.id"
          @click="handleCategorySelect(category)"
        >
          <view class="category-icon">
            <uni-icons :type="category.icon" size="20" color="#666"></uni-icons>
          </view>
          <view class="category-name">{{ category.name }}</view>
          <view class="category-arrow">
            <uni-icons type="right" size="16" color="#ccc"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <!-- 我的发布 -->
    <view class="my-publish">
      <view class="section-title">
        <text>我的发布</text>
        <text class="view-all" @click="viewAllPublish">查看全部</text>
      </view>
      <view class="publish-list" v-if="myPublishList.length > 0">
        <view 
          class="publish-card" 
          v-for="item in myPublishList" 
          :key="item.id"
          @click="handlePublishEdit(item)"
        >
          <view class="card-header">
            <view class="service-title">{{ item.title }}</view>
            <view class="status" :class="item.status">{{ getStatusText(item.status) }}</view>
          </view>
          <view class="card-content">
            <view class="service-desc">{{ item.description }}</view>
            <view class="service-meta">
              <text class="price">¥{{ item.price }}/次</text>
              <text class="views">{{ item.views }}次浏览</text>
            </view>
          </view>
        </view>
      </view>
      <view class="empty-state" v-else>
        <uni-icons type="info" size="48" color="#ccc"></uni-icons>
        <view class="empty-text">暂无发布内容</view>
        <view class="empty-desc">快来发布你的第一个服务吧</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { usePage } from '@/pages/composables/usePage'

const { setTitle } = usePage()

// 快速发布选项
const quickPublishItems = ref([
  {
    id: 1,
    title: '技能服务',
    desc: '分享你的专业技能',
    icon: 'star',
    type: 'skill'
  },
  {
    id: 2,
    title: '陪伴服务',
    desc: '提供陪伴和社交',
    icon: 'heart',
    type: 'companion'
  },
  {
    id: 3,
    title: '生活服务',
    desc: '日常生活帮助',
    icon: 'home',
    type: 'life'
  },
  {
    id: 4,
    title: '学习服务',
    desc: '知识分享和教学',
    icon: 'book',
    type: 'education'
  }
])

// 服务分类
const serviceCategories = ref([
  { id: 1, name: '运动健身', icon: 'fire' },
  { id: 2, name: '美食餐饮', icon: 'gift' },
  { id: 3, name: '学习教育', icon: 'book' },
  { id: 4, name: '生活服务', icon: 'home' },
  { id: 5, name: '娱乐休闲', icon: 'videocam' },
  { id: 6, name: '技能分享', icon: 'star' }
])

// 我的发布列表
const myPublishList = ref([
  {
    id: 1,
    title: '瑜伽私教课程',
    description: '专业瑜伽教练，提供一对一瑜伽指导',
    price: 80,
    views: 156,
    status: 'active'
  },
  {
    id: 2,
    title: '英语口语陪练',
    description: '海外留学经验，帮助提升英语口语水平',
    price: 60,
    views: 89,
    status: 'pending'
  }
])

// 快速发布处理
const handleQuickPublish = (item: any) => {
  uni.navigateTo({
    url: `/pages/publish/create?type=${item.type}`
  })
}

// 分类选择处理
const handleCategorySelect = (category: any) => {
  uni.navigateTo({
    url: `/pages/publish/create?category=${category.id}`
  })
}

// 查看全部发布
const viewAllPublish = () => {
  uni.navigateTo({
    url: '/pages/publish/list'
  })
}

// 编辑发布
const handlePublishEdit = (item: any) => {
  uni.navigateTo({
    url: `/pages/publish/edit?id=${item.id}`
  })
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    active: '已发布',
    pending: '审核中',
    rejected: '已拒绝',
    offline: '已下线'
  }
  return statusMap[status] || '未知'
}

onMounted(() => {
  setTitle('发布')
})
</script>

<style lang="scss" scoped>
.publish-page {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 100rpx;
}

.header {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  padding: 60rpx 40rpx 40rpx;
  color: white;
  
  .title {
    font-size: 48rpx;
    font-weight: bold;
    margin-bottom: 16rpx;
  }
  
  .subtitle {
    font-size: 28rpx;
    opacity: 0.9;
  }
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .view-all {
    font-size: 26rpx;
    color: #007AFF;
    font-weight: normal;
  }
}

.quick-publish {
  padding: 40rpx;
  background: white;
  margin-bottom: 20rpx;
  
  .publish-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24rpx;
  }
  
  .publish-item {
    background: #f8f9fa;
    border-radius: 16rpx;
    padding: 32rpx 24rpx;
    text-align: center;
    transition: all 0.3s ease;
    
    &:active {
      transform: scale(0.98);
      background: #e9ecef;
    }
    
    .item-icon {
      margin-bottom: 16rpx;
    }
    
    .item-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 8rpx;
    }
    
    .item-desc {
      font-size: 24rpx;
      color: #666;
    }
  }
}

.service-categories {
  background: white;
  padding: 40rpx;
  margin-bottom: 20rpx;
  
  .category-list {
    .category-item {
      display: flex;
      align-items: center;
      padding: 24rpx 0;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      &:active {
        background: #f8f9fa;
      }
      
      .category-icon {
        margin-right: 24rpx;
      }
      
      .category-name {
        flex: 1;
        font-size: 28rpx;
        color: #333;
      }
      
      .category-arrow {
        margin-left: 16rpx;
      }
    }
  }
}

.my-publish {
  background: white;
  padding: 40rpx;
  
  .publish-list {
    .publish-card {
      background: #f8f9fa;
      border-radius: 16rpx;
      padding: 32rpx;
      margin-bottom: 24rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      &:active {
        background: #e9ecef;
      }
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16rpx;
        
        .service-title {
          font-size: 30rpx;
          font-weight: 600;
          color: #333;
          flex: 1;
        }
        
        .status {
          font-size: 24rpx;
          padding: 8rpx 16rpx;
          border-radius: 20rpx;
          
          &.active {
            background: #d4edda;
            color: #155724;
          }
          
          &.pending {
            background: #fff3cd;
            color: #856404;
          }
          
          &.rejected {
            background: #f8d7da;
            color: #721c24;
          }
          
          &.offline {
            background: #e2e3e5;
            color: #383d41;
          }
        }
      }
      
      .card-content {
        .service-desc {
          font-size: 26rpx;
          color: #666;
          margin-bottom: 16rpx;
          line-height: 1.5;
        }
        
        .service-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .price {
            font-size: 28rpx;
            font-weight: 600;
            color: #007AFF;
          }
          
          .views {
            font-size: 24rpx;
            color: #999;
          }
        }
      }
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 80rpx 40rpx;
    
    .empty-text {
      font-size: 28rpx;
      color: #666;
      margin: 24rpx 0 16rpx;
    }
    
    .empty-desc {
      font-size: 24rpx;
      color: #999;
    }
  }
}
</style>