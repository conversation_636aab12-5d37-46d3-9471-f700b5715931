<template>
	<view class="auth-container">
		<!-- 背景装饰 -->
		<view class="bg-decoration">
			<!-- 几何装饰元素 -->
			<view class="bg-shape bg-shape-1"></view>
			<view class="bg-shape bg-shape-2"></view>
			<view class="bg-shape bg-shape-3"></view>
			<view class="bg-shape bg-shape-4"></view>
			
			<!-- 年轻清新生活主题装饰 -->
			<view class="life-decoration life-1">🌟</view>
			<view class="life-decoration life-2">🎯</view>
			<view class="life-decoration life-3">🚀</view>
			<view class="life-decoration life-4">🎨</view>
			<view class="life-decoration life-5">🌈</view>
			<view class="life-decoration life-6">⚡</view>
			<view class="life-decoration life-7">🎪</view>
			<view class="life-decoration life-8">🎭</view>
			
			<!-- 动态光效 -->
			<view class="light-effect light-1"></view>
			<view class="light-effect light-2"></view>
		</view>
		
		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- Logo区域 -->
			<view class="logo-section" :class="{ 'animate-in': isAnimated }">
				<view class="logo-wrapper">
			<image class="logo" src="@/assets/images/logo.png" mode="aspectFit"></image>
				</view>
				<text class="brand-title">乐享生活</text>
				<text class="brand-subtitle">运动玩乐，精彩每一天</text>
		</view>

			<!-- 认证卡片 -->
			<view class="auth-card" :class="{ 'animate-in': isAnimated }">
		<!-- 选项卡切换 -->
				<view class="tab-switcher">
					<view class="tab-slider" :style="{ transform: `translateX(${activeTab === 'login' ? '0%' : '100%'})` }"></view>
			<view 
						:class="['tab-option', { active: activeTab === 'login' }]" 
				@click="switchTab('login')"
			>
						<text class="tab-text">登录</text>
			</view>
			<view 
						:class="['tab-option', { active: activeTab === 'register' }]" 
				@click="switchTab('register')"
			>
						<text class="tab-text">注册</text>
			</view>
		</view>

		<!-- 登录表单 -->
				<view v-if="activeTab === 'login'" class="form-section">
			<!-- 登录方式选择 -->
					<view class="login-method-selector">
				<view 
					:class="['method-item', { active: loginType === 'password' }]" 
			@click="loginType = 'password'"
			>
					<view class="method-icon">🏃‍♂️</view>
					<text class="method-text">密码登录</text>
			</view>
			<view 
					:class="['method-item', { active: loginType === 'sms' }]" 
			@click="loginType = 'sms'"
			>
					<view class="method-icon">⚡</view>
					<text class="method-text">验证码登录</text>
			</view>
			</view>

			<!-- 登录表单内容 -->
			<view class="form-content">
						<!-- 手机号输入 -->
						<view class="input-group">
							<view class="input-wrapper">
								<view class="input-icon">🏃‍♀️</view>
					<input 
									class="modern-input" 
						type="number" 
						v-model="loginForm.phonenumber" 
						placeholder="请输入手机号" 
						maxlength="11"
					/>
							</view>
				</view>

						<!-- 密码输入 -->
						<view v-if="loginType === 'password'" class="input-group">
							<view class="input-wrapper">
								<view class="input-icon">🎯</view>
					<input 
									class="modern-input" 
						:type="showPassword ? 'text' : 'password'" 
						v-model="loginForm.password" 
				placeholder="请输入密码"
					/>
					<view 
									class="eye-toggle"
									@click="showPassword = !showPassword"
					>
									<text class="eye-icon">{{ showPassword ? '🎯' : '🔍' }}</text>
						</view>
					</view>
				</view>

						<!-- 验证码输入 -->
						<view v-if="loginType === 'sms'" class="input-group">
							<view class="input-wrapper">
								<view class="input-icon">🔢</view>
						<input 
									class="modern-input verification-input" 
							type="number" 
							v-model="loginForm.smsCode" 
							placeholder="请输入验证码" 
				maxlength="6"
						/>
						<button 
									class="code-btn" 
							:disabled="countdown > 0 || !loginForm.phonenumber || loginForm.phonenumber.length !== 11" 
							@click="handleShowVerifyAndGetSmsCode('login')"
						>
									<text class="code-btn-text">{{ countdown > 0 ? `${countdown}s` : '获取验证码' }}</text>
						</button>
					</view>
				</view>
				
						<!-- 安全验证 -->
						<view v-if="loginType === 'password'" class="input-group captcha-group">
					<Verify
						ref="passwordLoginVerify"
						@success="handlePasswordLoginSuccess"
						@error="handlePasswordLoginError"
						:mode="'pop'"
						:captchaType="'blockPuzzle'"
						:imgSize="{ width: '330px', height: '155px' }"
					></Verify>
							<view class="verify-card" @click="showPasswordLoginCaptcha">
								<view class="verify-icon">
									<text class="shield-icon" :class="{'verified': passwordLoginCaptcha?.captchaVerified?.value}">🏆</text>
								</view>
								<text class="verify-text" :class="{'verified': passwordLoginCaptcha?.captchaVerified?.value}">
									{{ passwordLoginCaptcha?.captchaVerified?.value ? '验证通过' : '点击进行安全验证' }}
						</text>
								<view class="verify-arrow">→</view>
					</view>
				</view>
				
						<!-- 短信登录验证码 -->
				<Verify
					ref="smsLoginVerify"
					@success="handleSmsLoginSuccess"
					@error="handleSmsLoginError"
					:mode="'pop'"
					:captchaType="'blockPuzzle'"
					:imgSize="{ width: '330px', height: '155px' }"
				></Verify>

				<!-- 🛡️ 设备安全状态 - 已注释 -->
				<!-- <view class="device-security-status">
					<view v-if="fingerprintLoading" class="security-item loading">
						<view class="security-icon">⏳</view>
						<text class="security-text">正在生成设备指纹...</text>
					</view>
					
					<view v-if="fingerprintVerifying" class="security-item verifying">
						<view class="security-icon">🔍</view>
						<text class="security-text">正在验证设备安全...</text>
					</view>
					
					<view v-if="securityCheckResult && hasFingerprint" class="security-item result" 
						  :class="{
							'passed': deviceCheckPassed,
							'failed': !deviceCheckPassed,
							'high-risk': isHighRisk,
							'requires-auth': requiresAdditionalAuth
						  }">
						<view class="security-icon">
							<text v-if="deviceCheckPassed">✅</text>
							<text v-else-if="isHighRisk">⚠️</text>
							<text v-else>🔒</text>
						</view>
						<view class="security-content">
							<text class="security-text">
								{{ deviceCheckPassed ? '设备安全检查通过' : securityCheckResult.message }}
							</text>
							<text v-if="riskLevel && riskLevel !== 'low'" class="risk-level">
								风险等级: {{ riskLevel === 'medium' ? '中等' : '高' }}
							</text>
						</view>
						
						<view v-if="!deviceCheckPassed && securityCheckResult" class="retry-btn" @click="retryDeviceCheck">
							<text class="retry-text">重试</text>
						</view>
					</view>
					
					<view v-if="fingerprintError" class="security-item error">
						<view class="security-icon">❌</view>
						<view class="security-content">
							<text class="security-text">{{ fingerprintError }}</text>
						</view>
						<view class="retry-btn" @click="retryDeviceCheck">
							<text class="retry-text">重试</text>
						</view>
					</view>
				</view> -->

				<!-- 登录按钮 -->
				<button 
							class="primary-btn login-btn" 
					:disabled="loginBtnDisabled || isLogging" 
					@click="handleLogin"
				>
							<text class="btn-text">
								{{ isLogging ? '登录中...' : '登录' }}
							</text>
							<view v-if="!isLogging" class="btn-icon">🚀</view>
				</button>
			</view>
		</view>

				<!-- 注册表单 -->
				<view v-if="activeTab === 'register'" class="form-section">
			<view class="form-content">
						<!-- 手机号输入 -->
						<view class="input-group">
							<view class="input-wrapper">
								<view class="input-icon">🏃‍♀️</view>
					<input 
									class="modern-input" 
						type="number" 
						v-model="registerForm.phonenumber" 
						placeholder="请输入手机号" 
					maxlength="11"
					/>
							</view>
				</view>

						<!-- 密码输入 -->
						<view class="input-group">
							<view class="input-wrapper">
								<view class="input-icon">🎯</view>
				<input 
								class="modern-input" 
					type="password" 
					v-model="registerForm.password" 
			placeholder="请设置登录密码"
					/>
							</view>
				</view>

						<!-- 确认密码输入 -->
						<view class="input-group">
							<view class="input-wrapper">
								<view class="input-icon">🔐</view>
				<input 
								class="modern-input" 
					type="password"
					v-model="confirmPassword" 
			placeholder="请再次输入密码"
								/>
								<view v-if="confirmPassword && registerForm.password" class="password-match">
									<text class="match-icon" :class="{ 'match': registerForm.password === confirmPassword }">
										{{ registerForm.password === confirmPassword ? '✅' : '❌' }}
									</text>
								</view>
							</view>
				</view>

						<!-- 验证码输入 -->
						<view class="input-group">
							<view class="input-wrapper">
								<view class="input-icon">📱</view>
						<input 
									class="modern-input verification-input" 
							type="number" 
							v-model="registerForm.smsCode" 
							placeholder="请输入验证码" 
			maxlength="6"
						/>
						<button 
									class="code-btn" 
							:disabled="countdown > 0 || !registerForm.phonenumber || registerForm.phonenumber.length !== 11 || !registerForm.password || !confirmPassword || registerForm.password !== confirmPassword" 
							@click="handleShowVerifyAndGetSmsCode('register')"
						>
									<text class="code-btn-text">{{ countdown > 0 ? `${countdown}s` : '获取验证码' }}</text>
						</button>
					</view>
				</view>

						<!-- 注册验证码 -->
				<Verify
					ref="registerVerify"
					@success="handleRegisterSuccess"
					@error="handleRegisterError"
					:mode="'pop'"
					:captchaType="'blockPuzzle'"
					:imgSize="{ width: '330px', height: '155px' }"
				></Verify>

				<!-- 注册按钮 -->
				<button 
							class="primary-btn register-btn" 
					:disabled="registerBtnDisabled" 
					@click="handleRegister"
				>
							<text class="btn-text">{{ isRegistering ? '注册中...' : '立即注册' }}</text>
							<view v-if="!isRegistering" class="btn-icon">🚀</view>
				</button>
					</view>
				</view>
			</view>

			<!-- 底部链接 -->
			<view class="footer-links">
				<text class="link-text">遇到问题？</text>
				<text class="link-action">联系客服</text>
			</view>


		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick, onUnmounted } from 'vue';
import { passwordLogin, smsLogin, register, getSmsCode } from '@/api/auth/auth';
import { tokenUtil } from '@/utils/token/TokenUtil';
import { StoreUtil } from '@/utils/storage/StoreUtil';
import { ErrorHandler } from '../../utils/errorHandler';
import Verify from '@/components/business/verification/Verify.vue';
import { useCaptchaService, type CaptchaVerifyData } from '@/api/modules/captcha';
import { useEnhancedFormPage } from '@/hooks/useEnhancedFormPage';
import { useErrorHandler } from '@/hooks/useErrorHandler';
import { COMMON_RULES } from '@/hooks/useFormValidation';
import { createLogger } from '@/utils/logger';
// import { useFormPersistence, startCountdownWithPersistence, restoreCountdownState } from '@/utils/formPersistence';
// 已迁移到新的存储服务

// 创建日志服务实例
const logger = createLogger('LoginPage')

// ==================== 🛡️ 设备指纹相关导入 - 已注释 ====================
// import { useLogin } from './composables/useLogin';

// ==================== 表单数据 ====================
const activeTab = ref<'login' | 'register'>('login');
const loginType = ref<'password' | 'sms'>('password');
const showPassword = ref(false);
const countdown = ref(0);
const { handleError, handleApiError, showErrorToast } = useErrorHandler();
const isLogging = ref(false);
const isRegistering = ref(false);

// 使用增强版表单页面 Hook
const {
	formData: loginForm,
	formState: loginFormState,
	validateForm: validateLoginForm,
	validateField: validateLoginField,
	submitForm: submitLoginForm,
	setSubmitFunction: setLoginSubmitFunction,
	addField: addLoginField
} = useEnhancedFormPage(
	{ phonenumber: '', password: '', smsCode: '' },
	{
		preset: 'login',
		fields: {
			smsCode: {
				label: '短信验证码',
				rules: [] // 动态设置，在登录类型变化时更新
			}
		}
	}
);

// 注册表单使用增强版 Hook
const {
	formData: registerForm,
	formState: registerFormState,
	validateForm: validateRegisterForm,
	validateField: validateRegisterField,
	submitForm: submitRegisterForm,
	setSubmitFunction: setRegisterSubmitFunction
} = useEnhancedFormPage(
	{ phonenumber: '', password: '', smsCode: '' },
	{
		preset: 'register',
		fields: {
			smsCode: {
				label: '短信验证码',
				rules: [COMMON_RULES.required('短信验证码')]
			}
		}
	}
);



// ==================== 🛡️ 设备指纹和安全检查 - 已注释 ====================
// const {
//   // 状态
//   loginLoading: deviceLoginLoading,
//   fingerprintLoading,
//   fingerprintVerifying,
//   hasFingerprint,
//   isHighRisk,
//   requiresAdditionalAuth,
//   deviceStatus,
//   riskLevel,
//   fingerprintError,
//   deviceCheckPassed,
//   securityCheckResult,
//   // 方法
//   handleLogin: deviceHandleLogin,
//   performDeviceSecurityCheck,
//   retryDeviceCheck
// } = useLogin();

// 确认密码
const confirmPassword = ref('');

// 动画状态
const isAnimated = ref(false);

// 🔧 表单持久化服务 - 已迁移到新存储服务
// 临时创建空的持久化对象以避免类型错误
const loginFormPersistence = {
	clearForm: () => {
		// 登录表单数据已清除
	}
};
const registerFormPersistence = {
	clearForm: () => {
		// 注册表单数据已清除
	}
};

// 验证码组件引用
const passwordLoginVerify = ref<any>(null);
const smsLoginVerify = ref<any>(null);
const registerVerify = ref<any>(null);

// 创建验证码服务实例
const passwordLoginCaptcha = useCaptchaService({
	captchaType: 'blockPuzzle',
	onSuccess: (data) => {
		// 密码登录验证成功
	}
});

// 创建短信登录验证码服务实例
const smsLoginCaptcha = useCaptchaService({
	captchaType: 'blockPuzzle',
	onSuccess: (data) => {
		// 验证成功后自动获取短信验证码
		handleGetSmsCodeAfterVerify('login', data);
	}
});

// 创建注册验证码服务实例
const registerCaptcha = useCaptchaService({
	captchaType: 'blockPuzzle',
	onSuccess: (data) => {
		// 验证成功后自动获取短信验证码
		handleGetSmsCodeAfterVerify('register', data);
	}
});

// 计算属性：登录按钮是否禁用
const loginBtnDisabled = computed(() => {
	if (!loginForm.phonenumber || loginForm.phonenumber.length !== 11) {
		return true;
	}
	
	if (loginType.value === 'password') {
		// 密码登录需要验证密码和验证码
		return !loginForm.password || !passwordLoginCaptcha?.captchaVerified?.value;
	} else {
		// 验证码登录只需要验证短信验证码
		return !loginForm.smsCode;
	}
});

// 计算属性：注册按钮是否禁用
const registerBtnDisabled = computed(() => {
	if (!registerForm.phonenumber || registerForm.phonenumber.length !== 11) {
		return true;
	}
	
	if (!registerForm.smsCode) {
		return true;
	}
	
	if (!registerForm.password || !confirmPassword.value) {
		return true;
	}
	
	// 密码和确认密码不一致
	if (registerForm.password !== confirmPassword.value) {
		return true;
	}
	
	return false;
});

// 切换登录/注册
const switchTab = (tab: 'login' | 'register') => {
	activeTab.value = tab;
	
	// 切换时重置验证码状态
	passwordLoginCaptcha.resetCaptcha();
	smsLoginCaptcha.resetCaptcha();
	registerCaptcha.resetCaptcha();
	
	// 如果切换到注册，跳转到注册页面
	if (tab === 'register') {
		uni.navigateTo({ url: '/pages/auth/register' });
	}
};

// 新增：处理显示验证码并获取短信验证码的方法
const handleShowVerifyAndGetSmsCode = (type: 'login' | 'register') => {
	if (countdown.value > 0) {
		return;
	}
	
	const phonenumber = type === 'login' ? loginForm.phonenumber : registerForm.phonenumber;
	
	if (!phonenumber || phonenumber.length !== 11) {
		ErrorHandler.showError('请输入有效的手机号');
		return;
	}
	
	// 如果是注册，还需要验证密码
	if (type === 'register') {
		if (!registerForm.password) {
			ErrorHandler.showError('请输入密码');
			return;
		}
		
		if (!confirmPassword.value) {
			ErrorHandler.showError('请确认密码');
			return;
		}
		
		if (registerForm.password !== confirmPassword.value) {
			ErrorHandler.showError('两次输入的密码不一致');
			return;
		}
	}
	
	// 显示验证码
	if (type === 'login') {
		showSmsLoginCaptcha();
	} else {
		showRegisterCaptcha();
	}
};

// 添加请求状态管理
const isGettingSmsCode = ref(false);

// 验证码验证成功后获取短信验证码
// 注意：移除了try-catch，让HTTP错误交给统一拦截器处理
const handleGetSmsCodeAfterVerify = async (type: 'login' | 'register', captchaData: CaptchaVerifyData) => {
	// 防止重复请求
	if (isGettingSmsCode.value) {
		// logger.debug('正在获取验证码，请勿重复点击');
		return;
	}
	
	const phonenumber = type === 'login' ? loginForm.phonenumber : registerForm.phonenumber;
	
	// 确保验证码数据完整
	if (!captchaData.captchaVerification) {
		// logger.warn('验证码数据不完整，可能验证组件未正确初始化', { 
		//	captchaData,
		//	type,
		//	hasToken: !!captchaData.token 
		// });
		ErrorHandler.showError('验证失败，请重试');
		
		// 重置验证码状态
		if (type === 'login') {
			smsLoginCaptcha.resetCaptcha();
		} else {
			registerCaptcha.resetCaptcha();
		}
		return;
	}
	
	// logger.debug('获取短信验证码，携带验证数据：', captchaData);
	
	try {
		isGettingSmsCode.value = true;
		
		// 调用获取验证码API，传入验证码相关数据
		// 已配置禁用自动错误处理，手动处理业务错误
		const res = await getSmsCode({
			phonenumber,
			type,
			captchaType: captchaData?.captchaType,
			captchaVerification: captchaData?.captchaVerification || undefined
		});
		
		// 增强错误处理日志
		// logger.debug('getSmsCode响应', {
		//	code: res.code,
		//	msg: res.msg,
		//	message: res.message
		// });
		
		if (res.code === 200) {
			// 成功发送，开始倒计时
			startCountdown();
			ErrorHandler.showSuccess('验证码已发送');
		} else {
			// 处理各种错误情况，包括频率限制
			let errorMessage = '获取验证码失败';
			if (res.code === 500) {
				// 服务器错误，显示具体错误信息
				errorMessage = res.msg || res.message || '服务器繁忙，请稍后重试';
			} else {
				errorMessage = res.msg || res.message || '获取验证码失败';
			}
			
			ErrorHandler.showError(errorMessage);
			
			// 🔧 清除倒计时状态，确保按钮可以再次点击 - 已迁移到新的存储服务
			// if (countdownCleanup) {
			// 	countdownCleanup();
			// 	countdownCleanup = null;
			// }
			countdown.value = 0;
			
			// 重置验证码状态，让用户重新验证
			if (type === 'login') {
				smsLoginCaptcha.resetCaptcha();
			} else {
				registerCaptcha.resetCaptcha();
			}
		}
	} catch (error) {
		ErrorHandler.showError('网络异常，请重试');
		
		// 🔧 清除倒计时状态，确保按钮可以再次点击 - 已迁移到新的存储服务
		// if (countdownCleanup) {
		// 	countdownCleanup();
		// 	countdownCleanup = null;
		// }
		countdown.value = 0;
		
		// 重置验证码状态
		if (type === 'login') {
			smsLoginCaptcha.resetCaptcha();
		} else {
			registerCaptcha.resetCaptcha();
		}
	} finally {
		isGettingSmsCode.value = false;
	}
};



// 🛡️ 集成设备指纹的登录函数
const handleLogin = async () => {
	// 简化验证，直接调用表单提交
	await submitLoginForm();
};

// 注册
const handleRegister = async () => {
	// 验证确认密码
	if (registerForm.password !== confirmPassword.value) {
		ErrorHandler.showError('两次输入的密码不一致');
		return;
	}
	
	// 使用新的表单提交方法
	await submitRegisterForm();
};

// 倒计时清理函数 - 已迁移到新的存储服务
// let countdownCleanup: (() => void) | null = null;

// 启动倒计时（带持久化）
const startCountdown = async () => {
	// 倒计时功能已迁移到新的存储服务
	// if (countdownCleanup) {
	// 	countdownCleanup();
	// }
	// 
	// const countdownKey = 'sms_countdown';
	// 
	// countdownCleanup = await startCountdownWithPersistence(
	// 	countdownKey,
	// 	60, // 60秒倒计时
	// 	(remaining) => {
	// 		countdown.value = remaining;
	// 	},
	// 	() => {
	// 		countdown.value = 0;
	// 		countdownCleanup = null;
	// 	}
	// );
	
	// 简化的倒计时实现
	countdown.value = 60;
	const timer = setInterval(() => {
		countdown.value--;
		if (countdown.value <= 0) {
			clearInterval(timer);
		}
	}, 1000);
};

// 观察手机号变化
watch(
	() => loginForm.phonenumber,
	(newVal) => {
		smsLoginCaptcha.updateOptions({
			phoneNumber: newVal
		});
	}
);

watch(
	() => registerForm.phonenumber,
	(newVal) => {
		registerCaptcha.updateOptions({
			phoneNumber: newVal
		});
	}
);

// 观察登录类型变化
watch(
	() => loginType.value,
	() => {
		// 切换登录类型时重置验证码状态
		passwordLoginCaptcha.resetCaptcha();
		smsLoginCaptcha.resetCaptcha();
		
		// 动态更新短信验证码字段的验证规则
		if (loginType.value === 'sms') {
			// 短信登录模式下，短信验证码为必填
			addLoginField('smsCode', {
				label: '短信验证码',
				rules: [COMMON_RULES.required('短信验证码')]
			});
		} else {
			// 密码登录模式下，短信验证码不验证
			addLoginField('smsCode', {
				label: '短信验证码',
				rules: []
			});
		}
	}
);

// 组件挂载时初始化
onMounted(() => {
	// 设置登录表单提交函数
	setLoginSubmitFunction(async () => {
		// 获取验证码服务实例
		const captchaService = loginType.value === 'password' ? passwordLoginCaptcha : smsLoginCaptcha;
		// 获取验证数据
		const captchaData = captchaService.getCaptchaFormData();
		
		// 密码登录模式下必须有验证数据
		if (loginType.value === 'password' && (!captchaData || !captchaData.captchaVerification)) {
			ErrorHandler.showError('请完成安全验证');
			showPasswordLoginCaptcha();
			return;
		}
		
		// 组装登录基础参数
		const loginParams: any = {
			loginGrantType: loginType.value,
			phonenumber: loginForm.phonenumber,
			...(loginType.value === 'password' 
				? { password: loginForm.password } 
				: { smsCode: loginForm.smsCode })
		};
		
		// 根据登录类型添加验证参数
		if (captchaData) {
			loginParams.captchaType = captchaData.captchaType;
			loginParams.captchaVerification = captchaData.captchaVerification || '';
		}
		
		// 调用登录API
		const res = loginType.value === 'password' ? await passwordLogin(loginParams) : await smsLogin(loginParams);
		if (res.code === 200 && res.data) {
			// 保存token和用户信息
			await tokenUtil.saveToken({
				accessToken: res.data.access_token,
				refreshToken: res.data.refresh_token || '',
				expiresAt: Date.now() + (res.data.expire_in || 7200) * 1000
			});
			StoreUtil.set('USER_INFO', res.data.userInfo);
			
			// 清除验证码缓存数据
			captchaService.clearCaptchaData();
			
			ErrorHandler.showSuccess('登录成功');
			
			// 跳转到首页
			setTimeout(() => {
				uni.reLaunch({
					url: '/pages/home/<USER>'
				})
			}, 1500)
		} else {
			throw new Error(res.msg || res.message || '登录失败');
		}
	});
	
	// 设置注册表单提交函数
	setRegisterSubmitFunction(async () => {
		// 注册逻辑
		const res = await register({
			phonenumber: registerForm.phonenumber,
			password: registerForm.password,
			confirmPassword: confirmPassword.value,
			smsCode: registerForm.smsCode
		});
		
		if (res.code === 200) {
			ErrorHandler.showSuccess('注册成功');
			// 清除表单数据
			registerFormPersistence.clearForm();
			// 跳转到登录
			switchTab('login');
		} else {
			throw new Error(res.msg || res.message || '注册失败');
		}
	});
});

// 处理验证码组件事件
const handlePasswordLoginSuccess = (params: any) => {
	passwordLoginCaptcha.verifyCaptchaSuccess(params);
};

const handlePasswordLoginError = () => {
	passwordLoginCaptcha.verifyCaptchaError();
};

const handleSmsLoginSuccess = (params: any) => {
	smsLoginCaptcha.verifyCaptchaSuccess(params);
};

const handleSmsLoginError = () => {
	smsLoginCaptcha.verifyCaptchaError();
};

const handleRegisterSuccess = (params: any) => {
	registerCaptcha.verifyCaptchaSuccess(params);
};

const handleRegisterError = () => {
	registerCaptcha.verifyCaptchaError();
};

// 显示验证码组件
const showPasswordLoginCaptcha = () => {
	if (passwordLoginVerify.value) {
		passwordLoginVerify.value.show();
	}
};

const showSmsLoginCaptcha = () => {
	if (smsLoginVerify.value) {
		smsLoginVerify.value.show();
	}
};

const showRegisterCaptcha = () => {
	if (registerVerify.value) {
		registerVerify.value.show();
	}
};



// 组件挂载时开始动画
onMounted(async () => {
	setTimeout(() => {
		isAnimated.value = true;
	}, 100);
	
	// 🔧 表单数据恢复和自动保存功能已迁移到新的存储服务
	// const savedLoginData = loginFormPersistence.restoreForm();
	// const savedRegisterData = registerFormPersistence.restoreForm();
	// 
	// if (savedLoginData) {
	// 	loginForm.value = { ...loginForm.value, ...savedLoginData };
	// 	console.log('📱 已恢复登录表单数据');
	// }
	// 
	// if (savedRegisterData) {
	// 	registerForm.value = { ...registerForm.value, ...savedRegisterData };
	// 	if (savedRegisterData.confirmPassword) {
	// 		confirmPassword.value = savedRegisterData.confirmPassword;
	// 	}
	// 	console.log('📱 已恢复注册表单数据');
	// }
	// 
	// // 🔧 恢复倒计时状态
	// const countdownKey = 'sms_countdown';
	// countdownCleanup = await restoreCountdownState(
	// 	countdownKey,
	// 	(remaining) => {
	// 		countdown.value = remaining;
	// 		console.log(`[倒计时恢复] 当前剩余: ${remaining}秒`);
	// 	},
	// 	() => {
	// 		countdown.value = 0;
	// 		countdownCleanup = null;
	// 		console.log('[倒计时恢复] 倒计时已结束');
	// 	}
	// );
	// 
	// // 🔧 设置自动保存
	// const loginCleanup = loginFormPersistence.setupAutoSave(() => ({
	// 	phonenumber: loginForm.value.phonenumber,
	// 	password: loginForm.value.password,
	// 	loginType: loginType.value
	// }));
	// 
	// const registerCleanup = registerFormPersistence.setupAutoSave(() => ({
	// 	phonenumber: registerForm.value.phonenumber,
	// 	password: registerForm.value.password,
	// 	confirmPassword: confirmPassword.value
	// }));
	
	// 使用 nextTick 确保组件完全渲染后再设置引用
	nextTick(() => {
		// 初始化验证码引用
		if (passwordLoginVerify.value) {
			passwordLoginCaptcha.verifyCaptcha = passwordLoginVerify;
		}
		
		if (smsLoginVerify.value) {
			smsLoginCaptcha.verifyCaptcha = smsLoginVerify;
		}
		
		if (registerVerify.value) {
			registerCaptcha.verifyCaptcha = registerVerify;
		}
	});
});

// 组件卸载时清理验证码服务
onUnmounted(() => {
	passwordLoginCaptcha.destroy();
	smsLoginCaptcha.destroy();
	registerCaptcha.destroy();
	
	// 倒计时清理功能已迁移到新的存储服务
	// if (countdownCleanup) {
	// 	countdownCleanup();
	// 	countdownCleanup = null;
	// }
});





</script>

<style lang="scss">
// 引入图标字体
@font-face {
  font-family: 'iconfont';
  src: url('data:font/woff2;charset=utf-8;base64,d09GMgABAAAAAAMMAAsAAAAABtwAAAK+AAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHFQGYACCcApwfwE2AiQDCAsGAAQgBYRnBzAbnwXIHoUZI1GJqJDe7w/E8Pz+29oUTb9nARVzG8kIBKnoDSuBCTOazUy9k+nOZnV3yrCKiAWkNvd8dCSkTqPw5+z+vbNVQc9PQ0ihBFa4SPB6HmDjwuWfZ3l6E9gDbtz5ARcpDehAG0CAxrhxPQvqBYZJlYYxeAEO+BwCCGSQA5WxPeOgEQY9TgBKVlZEaPQoBpsYFEKDGTOiAIdWbAjlBBzM/z49kI8WEI4BBpuHwzsJyr1c78dF4S78i+MHAgG4AdhADjAAQEBZU9EOS9EzQyAQ/JmbQgA0NIjAiyv9n6xBiM7BfzwEADMKNAHADsAi4gDzGv0AAF5AzjIwBYQKU1MZc9uE5DgkJzG3a+HsjnR2JrsAKgvNsqI7a93Nnpw87Dv7mUd+ZFVWr02u6vPJI+9p1DWwu/eHXYDSVuQzD/vevZ6k3z9Iz97jnr7j6xvtYfqGnL3X0+2D7r6p3+w//a7H66/unV/dNz9xLxAYKjZ+/jZjD/+UyV3JH3vavNx5OX1ePpx3nH1t2C65/BbxrNj8tG/5tmXr1sP/d9Yzp+WxfyubXLyotHg/aExJWZLaXrb9ePb1M/6JtR1rXzOAwfYACh6vj42HAXwvvVoH/rGTGT7B922YgTpGXpEzgF/aw+CfZGU7BRoMcJcEAjGQJGKRYvTOQg9IBrqwBXA+vBw0RqFiAJAtjAIQQdkPQgj3gBLUM0gx+j0IIb1CE+IbCGQeVBwzChSFHvSZsAVKjBMszB1OOUdzpDM+iJNszknC3RmN9RhnkwmYcJOLe3OJl2Nl/ZgwRZ/RVVjCTIbDhqpagaoaXdx7r2XYKdAnaAugCMOECmYdOKNw1Bx9zsd9QLjEznEka09mDPPhrEMLUFb+ygXvLYn4sDAYbINUKfvhTYEVZFFDg9YJ1+hUQrNgT1h9R2Dw2BkdGBozKgz7SuNyXYP3N6CAzg9yiGGM446a2Iiv+GiEYYo9AAAA') format('woff2');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

.auth-container {
	min-height: 100vh;
	  background: linear-gradient(135deg, #00D4FF 0%, #5B73FF 30%, #FF6B35 60%, #F7931E 100%);
	padding: 40rpx;
	box-sizing: border-box;
	position: relative;
	overflow: hidden;
	
	.bg-decoration {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 0;
		pointer-events: none;
		
		// 几何装饰形状
		.bg-shape {
			position: absolute;
			animation: float 8s ease-in-out infinite;
			
			&.bg-shape-1 {
				width: 120rpx;
				height: 120rpx;
				top: 8%;
				left: -30rpx;
				background: linear-gradient(45deg, #00D4FF, #5B73FF);
				border-radius: 30rpx;
				transform: rotate(45deg);
				animation-delay: 0s;
				opacity: 0.7;
			}
			
			&.bg-shape-2 {
				width: 80rpx;
				height: 80rpx;
				top: 25%;
				right: 10%;
				background: linear-gradient(135deg, #FF6B35, #F7931E);
				border-radius: 50%;
				animation-delay: 2s;
				opacity: 0.6;
			}
			
			&.bg-shape-3 {
				width: 0;
				height: 0;
				top: 45%;
				left: 5%;
				border-left: 40rpx solid transparent;
				border-right: 40rpx solid transparent;
				border-bottom: 70rpx solid rgba(168, 230, 207, 0.6);
				animation-delay: 4s;
			}
			
			&.bg-shape-4 {
				width: 100rpx;
				height: 100rpx;
				bottom: 20%;
				right: -20rpx;
				background: linear-gradient(225deg, #5B73FF, #00D4FF);
				clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
				animation-delay: 6s;
				opacity: 0.5;
			}
		}
		
		// 运动生活主题装饰
		.life-decoration {
			position: absolute;
			font-size: 48rpx;
			animation: bounce 3s ease-in-out infinite;
			opacity: 0.8;
			filter: drop-shadow(0 4rpx 8rpx rgba(0,0,0,0.1));
			
			&.life-1 {
				top: 12%;
				right: 20%;
				animation-delay: 0.5s;
			}
			
			&.life-2 {
				top: 35%;
				left: 8%;
				animation-delay: 1.5s;
			}
			
			&.life-3 {
				top: 55%;
				right: 8%;
				animation-delay: 2.5s;
			}
			
			&.life-4 {
				bottom: 35%;
				left: 15%;
				animation-delay: 3.5s;
			}
			
			&.life-5 {
				bottom: 15%;
				right: 30%;
				animation-delay: 4.5s;
			}
			
			&.life-6 {
				top: 70%;
				left: 25%;
				animation-delay: 5.5s;
			}
			
			&.life-7 {
				top: 18%;
				left: 35%;
				animation-delay: 6.5s;
			}
			
			&.life-8 {
				bottom: 25%;
				right: 15%;
				animation-delay: 7.5s;
			}
		}
		
		// 动态光效
		.light-effect {
			position: absolute;
			border-radius: 50%;
			background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
			animation: pulse 4s ease-in-out infinite;
			
			&.light-1 {
				width: 300rpx;
				height: 300rpx;
				top: -50rpx;
				right: -100rpx;
				animation-delay: 1s;
			}
			
			&.light-2 {
				width: 200rpx;
				height: 200rpx;
				bottom: -50rpx;
				left: -50rpx;
				animation-delay: 3s;
			}
		}
	}
	
	.main-content {
		position: relative;
		z-index: 1;
		
		.logo-section {
		display: flex;
		flex-direction: column;
		align-items: center;
			margin: 80rpx 0 60rpx;
			opacity: 0;
			transform: translateY(30rpx);
			transition: all 0.8s ease;
			
			&.animate-in {
				opacity: 1;
				transform: translateY(0);
			}
		
			.logo-wrapper {
			width: 160rpx;
			height: 160rpx;
				margin-bottom: 30rpx;
				background: rgba(255, 255, 255, 0.2);
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				backdrop-filter: blur(10rpx);
				
				.logo {
					width: 120rpx;
					height: 120rpx;
				}
		}
		
			.brand-title {
				font-size: 48rpx;
				font-weight: 700;
				color: #fff;
				margin-bottom: 10rpx;
				text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
			}
			
			.brand-subtitle {
				font-size: 28rpx;
				color: rgba(255, 255, 255, 0.8);
				text-align: center;
		}
	}
	
		.auth-card {
			background: rgba(255, 255, 255, 0.98);
			backdrop-filter: blur(25rpx);
			border-radius: 32rpx;
			padding: 60rpx 40rpx;
			box-shadow: 
				0 20rpx 60rpx rgba(255, 107, 157, 0.25),
				0 8rpx 32rpx rgba(0, 0, 0, 0.15),
				inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
			border: 2rpx solid rgba(255, 255, 255, 0.6);
			opacity: 0;
			transform: translateY(50rpx);
			transition: all 0.8s ease 0.3s;
			
			&.animate-in {
				opacity: 1;
				transform: translateY(0);
			}
			
			.tab-switcher {
		display: flex;
				background: #f8f9fa;
				border-radius: 16rpx;
				padding: 8rpx;
				margin-bottom: 50rpx;
				position: relative;
		
				.tab-slider {
					position: absolute;
					top: 8rpx;
					width: calc(50% - 8rpx);
					height: calc(100% - 16rpx);
					background: linear-gradient(135deg, #00D4FF 0%, #5B73FF 100%);
					border-radius: 12rpx;
					transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					box-shadow: 0 4rpx 16rpx rgba(0, 212, 255, 0.4);
				}
				
				.tab-option {
			flex: 1;
			text-align: center;
					font-size: 30rpx;
			color: #666;
			padding: 20rpx 0;
			position: relative;
					z-index: 1;
					transition: color 0.3s ease;
					
					.tab-text {
						font-weight: 500;
					}
			
			&.active {
						color: #fff;
						
						.tab-text {
							font-weight: 600;
				}
			}
		}
	}
	
			.form-section {
				.login-method-selector {
		display: flex;
					gap: 20rpx;
		margin-bottom: 40rpx;
		
					.method-item {
			flex: 1;
						display: flex;
						flex-direction: column;
						align-items: center;
						padding: 30rpx 20rpx;
						background: #f8f9fa;
						border-radius: 16rpx;
						border: 2rpx solid transparent;
						transition: all 0.3s ease;
						cursor: pointer;
						
						.method-icon {
							font-size: 40rpx;
							margin-bottom: 10rpx;
			}
			
						.method-text {
							font-size: 26rpx;
							color: #666;
							font-weight: 500;
			}
			
			&.active {
							background: linear-gradient(135deg, #00D4FF 0%, #5B73FF 100%);
				color: #fff;
							transform: translateY(-2rpx);
							box-shadow: 0 8rpx 24rpx rgba(0, 212, 255, 0.4);
							border-color: rgba(255, 255, 255, 0.3);
							
							.method-text {
								color: #fff;
			}
		}
	}
				}
				
		.form-content {
					.input-group {
				position: relative;
						margin-bottom: 32rpx;
				
						.input-wrapper {
							display: flex;
							align-items: center;
							width: 100%;
							height: 96rpx;
							background: #f8f9fa;
							border-radius: 16rpx;
							padding: 0 24rpx;
							box-sizing: border-box;
							font-size: 30rpx;
							border: 2rpx solid transparent;
							transition: all 0.3s ease;
							
							&:focus-within {
								background: #fff;
								border-color: #00D4FF;
								box-shadow: 0 0 0 6rpx rgba(0, 212, 255, 0.15);
								transform: translateY(-2rpx);
							}
							
							.input-icon {
								font-size: 32rpx;
								margin-right: 24rpx;
								opacity: 0.7;
							}
							
							.modern-input {
								flex: 1;
								height: 100%;
								background: none;
								border: none;
								outline: none;
					color: #333;
								
								&::placeholder {
									color: #999;
								}
							}
							
							.eye-toggle {
								padding: 12rpx;
					border-radius: 8rpx;
								transition: background 0.2s ease;
								cursor: pointer;
								
								&:active {
									background: rgba(0, 0, 0, 0.05);
								}
								
								.eye-icon {
									font-size: 32rpx;
								}
							}
							
							.verification-input {
								padding-right: 200rpx;
					}
				}
				
						.code-btn {
					position: absolute;
							right: 8rpx;
							top: 50%;
							transform: translateY(-50%);
							height: 80rpx;
							background: linear-gradient(135deg, #00D4FF 0%, #5B73FF 100%);
							color: #fff;
							font-size: 26rpx;
							border-radius: 12rpx;
							padding: 0 24rpx;
							min-width: 160rpx;
					display: flex;
					align-items: center;
					justify-content: center;
							margin: 0;
							border: none;
							transition: all 0.3s ease;
							box-shadow: 0 4rpx 16rpx rgba(0, 212, 255, 0.4);
					
							.code-btn-text {
								font-weight: 500;
							}
							
							&:active {
								transform: translateY(-50%) scale(0.98);
							}
							
							&::after {
								border: none;
							}
							
							&[disabled] {
								background: #ddd;
								color: #999;
								box-shadow: none;
								transform: translateY(-50%);
							}
						}
						
						.password-match {
							padding: 12rpx;
							border-radius: 8rpx;
							
							.match-icon {
								font-size: 28rpx;
								
								&.match {
									color: #4cae4c;
								}
							}
						}
					}
					
					.captcha-group {
						.verify-card {
							display: flex;
							align-items: center;
							width: 100%;
							height: 96rpx;
							background: #f8f9fa;
							border-radius: 16rpx;
							padding: 0 24rpx;
							box-sizing: border-box;
							border: 2rpx solid transparent;
							transition: all 0.3s ease;
							cursor: pointer;
							
							&:hover {
								background: #fff;
								border-color: #ff97b6;
								box-shadow: 0 0 0 6rpx rgba(255, 149, 182, 0.15);
					}
					
							.verify-icon {
								margin-right: 24rpx;
								
								.shield-icon {
									font-size: 32rpx;
									transition: all 0.3s ease;
									
									&.verified {
										color: #4cae4c;
									}
								}
						}
						
							.verify-text {
								flex: 1;
								font-size: 30rpx;
								color: #666;
								
								&.verified {
									color: #4cae4c;
									font-weight: 500;
					}
				}
				
							.verify-arrow {
								font-size: 24rpx;
								color: #999;
								transition: transform 0.2s ease;
							}
							
							&:active .verify-arrow {
								transform: translateX(4rpx);
							}
						}
					}
					
					.primary-btn {
						width: 100%;
						height: 100rpx;
						background: linear-gradient(135deg, #00D4FF 0%, #5B73FF 100%);
					color: #fff;
						font-size: 32rpx;
						border-radius: 20rpx;
						margin-top: 40rpx;
					display: flex;
					align-items: center;
					justify-content: center;
						border: none;
						box-shadow: 0 8rpx 24rpx rgba(0, 212, 255, 0.5);
						transition: all 0.3s ease;
						position: relative;
						overflow: hidden;
						
						.btn-text {
							font-weight: 600;
							margin-right: 12rpx;
						}
						
						.btn-icon {
							font-size: 28rpx;
						}
					
					&:active {
							transform: translateY(2rpx);
							box-shadow: 0 4rpx 16rpx rgba(0, 212, 255, 0.5);
					}
					
					&::after {
						border: none;
					}
					
					&[disabled] {
							background: #ddd;
							color: #999;
							box-shadow: none;
							transform: none;
				}
			}
			
			.submit-button {
				width: 100%;
				height: 88rpx;
				background-color: #007AFF;
				color: #fff;
				font-size: 32rpx;
				border-radius: 8rpx;
				margin-top: 60rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				
				&:active {
					opacity: 0.8;
				}
				
				&::after {
					border: none;
				}
				
				&[disabled] {
					background-color: #bbb;
					color: #fff;
				}
			}
				}
			}
		}
		
		.footer-links {
			margin-top: 60rpx;
			text-align: center;
			font-size: 28rpx;
			
			.link-text {
				color: rgba(255, 255, 255, 0.7);
				margin-right: 10rpx;
			}
			
			.link-action {
				color: #fff;
				font-weight: 600;
				text-decoration: underline;
				transition: opacity 0.2s ease;
				
				&:active {
					opacity: 0.8;
				}
			}
		}
	}
}

// 动画定义
@keyframes float {
	0%, 100% {
		transform: translateY(0px);
	}
	50% {
		transform: translateY(-20px);
	}
}

@keyframes bounce {
	0%, 100% {
		transform: translateY(0px) scale(1);
	}
	50% {
		transform: translateY(-10px) scale(1.05);
	}
}

@keyframes pulse {
	0%, 100% {
		opacity: 0.3;
		transform: scale(1);
	}
	50% {
		opacity: 0.6;
		transform: scale(1.1);
	}
}

// 验证码组件样式修复
:deep(.el-dialog) {
	border-radius: 16rpx !important;
	overflow: hidden !important;
}

:deep(.verification-code-wrapper) {
	display: flex !important;
	justify-content: center !important;
	align-items: center !important;
	min-height: 200px !important;
}

:deep(.verification-img) {
	max-width: 100% !important;
	height: auto !important;
	border-radius: 8rpx !important;
}

// 移除旧的验证码样式
.captcha-item {
	.verify-btn {
		width: 100%;
		height: 88rpx;
		background-color: #f5f5f5;
		border-radius: 8rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		
		.verify-btn-text {
			font-size: 28rpx;
			color: #666;
			
			&.verified {
				color: #4cae4c;
			}
		}
		
		&:active {
			opacity: 0.8;
		}
	}
}

.captcha-item {
	.verify-btn {
		width: 100%;
		height: 88rpx;
		background-color: #f5f5f5;
		border-radius: 8rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		
		.verify-btn-text {
			font-size: 28rpx;
			color: #666;
			
			&.verified {
				color: #4cae4c;
			}
		}
		
		&:active {
			opacity: 0.8;
		}
	}
}

/* ==================== 🔧 Token刷新调试工具样式 ==================== */
.debug-tools {
	margin-top: 60rpx;
	padding: 32rpx;
	background: rgba(240, 245, 255, 0.6);
	border-radius: 16rpx;
	border: 2rpx dashed #007aff;
	
	.debug-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #007aff;
		text-align: center;
		margin-bottom: 24rpx;
	}
	
	.debug-actions {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
		
		.debug-btn {
			width: 100%;
			height: 72rpx;
			border-radius: 12rpx;
			font-size: 28rpx;
			font-weight: 500;
			border: none;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.3s ease;
			
			&.primary {
				background: linear-gradient(135deg, #007aff 0%, #0051d5 100%);
				color: #fff;
				box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);
			}
			
			&.info {
				background: linear-gradient(135deg, #34c759 0%, #30d158 100%);
				color: #fff;
				box-shadow: 0 4rpx 16rpx rgba(52, 199, 89, 0.3);
			}
			
			&.warning {
				background: linear-gradient(135deg, #ff9500 0%, #ff8c00 100%);
				color: #fff;
				box-shadow: 0 4rpx 16rpx rgba(255, 149, 0, 0.3);
			}
			
			&:active {
				transform: scale(0.98);
			}
			
			&::after {
				border: none;
			}
		}
	}
	
	.debug-hint {
		margin-top: 24rpx;
		text-align: center;
		
		.hint-text {
			font-size: 24rpx;
			color: #666;
			background: rgba(255, 255, 255, 0.8);
			padding: 8rpx 16rpx;
			border-radius: 8rpx;
		}
	}
}

/* ==================== 🛡️ 设备安全状态样式 ==================== */
.device-security-status {
	margin: 32rpx 0;
	
	.security-item {
		display: flex;
		align-items: center;
		padding: 24rpx;
		margin-bottom: 16rpx;
		border-radius: 16rpx;
		font-size: 28rpx;
		transition: all 0.3s ease;
		
		&.loading {
			background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
			border: 2rpx solid #2196f3;
			
			.security-icon {
				animation: spin 2s linear infinite;
			}
		}
		
		&.verifying {
			background: linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%);
			border: 2rpx solid #ff9800;
			
			.security-icon {
				animation: pulse 1.5s ease-in-out infinite;
			}
		}
		
		&.result {
			&.passed {
				background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
				border: 2rpx solid #4caf50;
				color: #2e7d32;
			}
			
			&.failed {
				background: linear-gradient(135deg, #ffebee 0%, #fce4ec 100%);
				border: 2rpx solid #f44336;
				color: #c62828;
			}
			
			&.high-risk {
				background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
				border: 2rpx solid #ff5722;
				color: #d84315;
				
				.security-icon {
					animation: warning-pulse 2s ease-in-out infinite;
				}
			}
			
			&.requires-auth {
				background: linear-gradient(135deg, #e1f5fe 0%, #e0f2f1 100%);
				border: 2rpx solid #00bcd4;
				color: #00838f;
			}
		}
		
		&.error {
			background: linear-gradient(135deg, #ffebee 0%, #fce4ec 100%);
			border: 2rpx solid #f44336;
			color: #c62828;
		}
		
		.security-icon {
			font-size: 32rpx;
			margin-right: 16rpx;
			min-width: 32rpx;
			text-align: center;
		}
		
		.security-content {
			flex: 1;
			display: flex;
			flex-direction: column;
			
			.security-text {
				font-size: 28rpx;
				font-weight: 500;
				line-height: 1.4;
			}
			
			.risk-level {
				font-size: 24rpx;
				margin-top: 4rpx;
				opacity: 0.8;
			}
		}
		
		.retry-btn {
			margin-left: 16rpx;
			padding: 8rpx 16rpx;
			background: rgba(255, 255, 255, 0.9);
			border-radius: 8rpx;
			font-size: 24rpx;
			color: #666;
			border: 1rpx solid #ddd;
			transition: all 0.2s ease;
			
			&:active {
				background: rgba(255, 255, 255, 1);
				transform: scale(0.95);
			}
			
			.retry-text {
				font-weight: 500;
			}
		}
	}
}

/* 设备安全动画效果 */
@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

@keyframes pulse {
	0%, 100% { 
		transform: scale(1);
		opacity: 1;
	}
	50% { 
		transform: scale(1.1);
		opacity: 0.8;
	}
}

@keyframes warning-pulse {
	0%, 100% { 
		transform: scale(1);
		box-shadow: 0 0 0 0 rgba(255, 87, 34, 0.7);
	}
	50% { 
		transform: scale(1.05);
		box-shadow: 0 0 0 10rpx rgba(255, 87, 34, 0);
	}
}
</style>