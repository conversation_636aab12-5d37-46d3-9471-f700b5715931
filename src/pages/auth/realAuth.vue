<template>
  <view class="real-auth-container">
    <!-- 页面标题 -->
    <view class="page-header">
      <view class="page-title">实名认证</view>
      <view class="page-subtitle">为了保障您的账号安全，请完成实名认证</view>
      <view class="skip-button" @click="handleSkip">跳过</view>
    </view>

    <!-- 认证表单 -->
    <view class="auth-form">
      <view class="form-item">
        <text class="label">姓名</text>
        <input
          class="input"
          v-model="formData.name"
          placeholder="请输入真实姓名"
          placeholder-style="color: #999;"
          @blur="validateName"
        />
      </view>

      <view class="form-item">
        <view class="label-wrapper">
          <text class="label">身份证号</text>
          <text v-if="idCardError" class="error-text">身份证号码格式不完整</text>
        </view>
        <input
          class="input"
          v-model="formData.idCard"
          placeholder="请输入18位身份证号码"
          placeholder-style="color: #999;"
          maxlength="18"
          @input="validateIdCard"
        />
      </view>

      <view class="form-item">
        <text class="label">手机号</text>
        <view class="phone-display">
          <text class="phone-text">{{ maskedPhone || '加载中...' }}</text>
        </view>
      </view>
    </view>

    <!-- 提示信息 -->
    <view class="tips-box">
      <view class="tips-title">
        <text class="icon">!</text>
        <text>温馨提示</text>
      </view>
      <view class="tips-content">
        <text>1. 请确保填写的信息真实有效</text>
        <text>2. 认证信息将用于账号安全验证</text>
        <text>3. 您的信息将被严格保密</text>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button
        class="submit-btn"
        :disabled="!isFormValid"
        @click="handleSubmit"
      >
        提交认证
      </button>
    </view>

    <!-- 跳过确认弹窗 -->
    <uni-popup ref="skipPopup" type="dialog">
      <uni-popup-dialog
        type="warning"
        title="提示"
        content="跳过实名认证，部分功能使用将受到限制，您确定需要跳过吗?"
        :before-close="true"
        @confirm="confirmSkip"
        @close="cancelSkip"
      />
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRealAuth } from '@/pages/realAuth/composables/useRealAuth';
import type { RealAuthFormData } from '@/api/auth/types';
import { getMaskedPhone } from '@/api/member';
import { useFormValidation } from '@/hooks/useFormValidation';
import { COMMON_RULES } from '@/hooks/useFormValidation';
import { useLoading } from '@/hooks/useLoading';
import { useErrorHandler } from '@/hooks/useErrorHandler';
import { ErrorHandler } from '../../utils/errorHandler';

// 表单数据
const formData = ref<RealAuthFormData>({
  name: '',
  idCard: '',
  phone: ''
});

// 遮罩手机号
const maskedPhone = ref('');

// 弹窗引用
const skipPopup = ref<any>(null);

// ==================== 使用统一验证框架 ====================
const {
  addField,
  validateField,
  validateAndShowError,
  validationState,
  getFieldError
} = useFormValidation();

// 初始化表单验证
const initFormValidation = () => {
  // 添加字段验证规则
  addField('name', {
    label: '真实姓名',
    rules: [
      COMMON_RULES.required('请输入真实姓名'),
      COMMON_RULES.chinese('请输入正确的中文姓名')
    ]
  });
  addField('idCard', {
    label: '身份证号码',
    rules: [
      COMMON_RULES.required('请输入身份证号码'),
      COMMON_RULES.idCard('请输入正确的身份证号码')
    ]
  });
};

// 获取身份证验证错误信息
const idCardError = computed(() => {
  return getFieldError('idCard');
});

// 表单验证状态
const isFormValid = computed(() => {
  return Object.keys(validationState.errors).length === 0 && 
         formData.value.name.trim() !== '' && 
         formData.value.idCard.trim() !== '';
});

// 身份证号验证
const validateIdCard = () => {
  validateField('idCard', formData.value.idCard);
};

// 姓名验证
const validateName = () => {
  validateField('name', formData.value.name);
};

// 获取遮罩手机号
const fetchMaskedPhone = async () => {
  try {
    const res = await getMaskedPhone();
    if (res.code === 200) {
      maskedPhone.value = String(res.data);
    } else {
      handleApiError(res, '获取手机号失败');
    }
  } catch (error: any) {
    handleError(error, '获取手机号失败');
  }
};

// 使用认证逻辑
const { submitAuth } = useRealAuth();
const { withLoading, loading } = useLoading();
const { handleError, handleApiError } = useErrorHandler();

// 提交认证
const handleSubmit = async () => {
  // 验证所有字段
  const isValid = validateAndShowError(formData.value);
  if (!isValid) {
    // 显示验证错误信息
    const errors = validationState.errors;
    const firstError = Object.values(errors)[0];
    if (firstError) {
      ErrorHandler.showError(firstError);
    }
    return;
  }

  await withLoading(async () => {
    const result = await submitAuth(formData.value);
    if (result.success) {
      ErrorHandler.showSuccess('认证成功');
      // 认证成功后跳转到首页
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/home/<USER>/home'
        });
      }, 1500);
    } else {
      ErrorHandler.showError(result.message || '认证失败');
    }
  }, '认证中...');
};

// 处理跳过
const handleSkip = () => {
  if (skipPopup.value) {
    skipPopup.value.open();
  }
};

// 确认跳过
const confirmSkip = () => {
  if (skipPopup.value) {
    skipPopup.value.close();
  }
  // 跳转到首页
  uni.switchTab({
            url: '/pages/home/<USER>/home'
  });
};

// 取消跳过
const cancelSkip = () => {
  if (skipPopup.value) {
    skipPopup.value.close();
  }
};

// 页面加载时获取手机号
onMounted(() => {
  // 初始化表单验证
  initFormValidation();
  
  fetchMaskedPhone();
});
</script>

<style lang="scss">
.real-auth-container {
  min-height: 100vh;
  background-color: #faf5f5;
  padding: 40rpx 30rpx;

  .page-header {
    margin-bottom: 40rpx;
    position: relative;

    .page-title {
      font-size: 44rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 16rpx;
    }

    .page-subtitle {
      font-size: 28rpx;
      color: #666;
    }

    .skip-button {
      position: absolute;
      top: 0;
      right: 0;
      font-size: 28rpx;
      color: #666;
      padding: 10rpx 20rpx;
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 30rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    }
  }

  .auth-form {
    background-color: #fff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);

    .form-item {
      margin-bottom: 30rpx;
      position: relative;

      &:last-child {
        margin-bottom: 0;
      }

      .label-wrapper {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;

        .label {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
        }

        .error-text {
          font-size: 24rpx;
          color: #ff4d4f;
          margin-left: 16rpx;
        }
      }

      .input {
        width: 100%;
        height: 88rpx;
        background-color: #f8f8f8;
        border-radius: 12rpx;
        padding: 0 24rpx;
        font-size: 28rpx;
        color: #333;
        box-sizing: border-box;
      }

      .phone-display {
        width: 100%;
        height: 88rpx;
        background-color: #f8f8f8;
        border-radius: 12rpx;
        padding: 0 24rpx;
        display: flex;
        align-items: center;
        box-sizing: border-box;

        .phone-text {
          font-size: 28rpx;
          color: #333;
        }
      }
    }
  }

  .tips-box {
    background-color: #fff5f8;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 40rpx;

    .tips-title {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      .icon {
        width: 32rpx;
        height: 32rpx;
        background-color: #ff79a5;
        border-radius: 50%;
        color: #fff;
        font-size: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12rpx;
      }

      text {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }
    }

    .tips-content {
      text {
        display: block;
        font-size: 26rpx;
        color: #666;
        line-height: 1.6;
      }
    }
  }

  .submit-section {
    padding: 40rpx 0;

    .submit-btn {
      width: 100%;
      height: 90rpx;
      background: linear-gradient(135deg, #ff97b6, #ff79a5);
      color: #fff;
      font-size: 32rpx;
      font-weight: bold;
      border-radius: 45rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 6rpx 12rpx rgba(255,149,182,0.25);

      &:active {
        transform: scale(0.98);
        background: linear-gradient(135deg, #ff86a8, #ff6897);
      }

      &[disabled] {
        background: linear-gradient(135deg, #ddd, #ccc);
        color: rgba(255,255,255,0.8);
        box-shadow: none;
      }
    }
  }
}
</style>
