<template>
	<view class="auth-container">
		<!-- 背景装饰 -->
		<view class="bg-decoration">
			<!-- 几何装饰元素 -->
			<view class="bg-shape bg-shape-1"></view>
			<view class="bg-shape bg-shape-2"></view>
			<view class="bg-shape bg-shape-3"></view>
			<view class="bg-shape bg-shape-4"></view>
			
			<!-- 年轻清新生活主题装饰 -->
			<view class="life-decoration life-1">🌟</view>
			<view class="life-decoration life-2">🎯</view>
			<view class="life-decoration life-3">🚀</view>
			<view class="life-decoration life-4">🎨</view>
			<view class="life-decoration life-5">🌈</view>
			<view class="life-decoration life-6">⚡</view>
			<view class="life-decoration life-7">🎪</view>
			<view class="life-decoration life-8">🎭</view>
			
			<!-- 动态光效 -->
			<view class="light-effect light-1"></view>
			<view class="light-effect light-2"></view>
		</view>
		
		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- Logo区域 -->
			<view class="logo-section" :class="{ 'animate-in': isAnimated }">
				<view class="logo-wrapper">
					<image class="logo" src="@/assets/images/logo.png" mode="aspectFit"></image>
				</view>
				<text class="brand-title">乐享生活</text>
				<text class="brand-subtitle">运动玩乐，精彩每一天</text>
			</view>

			<!-- 认证卡片 -->
			<view class="auth-card" :class="{ 'animate-in': isAnimated }">
				<!-- 选项卡切换 -->
				<view class="tab-switcher">
					<view class="tab-slider" :style="{ transform: `translateX(${activeTab === 'login' ? '0%' : '100%'})` }"></view>
					<view 
						:class="['tab-option', { active: activeTab === 'login' }]" 
						@click="switchTab('login')"
					>
						<text class="tab-text">登录</text>
					</view>
					<view 
						:class="['tab-option', { active: activeTab === 'register' }]" 
						@click="switchTab('register')"
					>
						<text class="tab-text">注册</text>
					</view>
				</view>

				<!-- 注册表单 -->
				<view v-if="activeTab === 'register'" class="form-section">
					<view class="form-content">
						<!-- 手机号输入 -->
						<view class="input-group">
							<view class="input-wrapper">
								<view class="input-icon">🏃‍♀️</view>
								<input 
									class="modern-input" 
									type="number" 
									v-model="registerForm.phonenumber" 
									placeholder="请输入手机号" 
									maxlength="11"
								/>
							</view>
						</view>

						<!-- 密码输入 -->
						<view class="input-group">
							<view class="input-wrapper">
								<view class="input-icon">🎯</view>
								<input 
								class="modern-input" 
								type="password" 
								v-model="registerForm.password" 
								placeholder="请设置登录密码"
								@blur="handlePasswordBlur"
							/>
							</view>
						</view>

						<!-- 确认密码输入 -->
						<view class="input-group">
							<view class="input-wrapper" :class="{ 
								'password-error': isPasswordMismatch, 
								'password-success': isPasswordMatch 
							}">
								<view v-if="isPasswordMismatch" class="password-error-tip">密码不一致</view>
								<view class="input-icon">🔒</view>
								<input 
								class="modern-input" 
								type="password"
								v-model="confirmPassword" 
								placeholder="请再次输入密码"
								@blur="handleConfirmPasswordBlur"
							/>
							</view>
						</view>

						<!-- 验证码输入 -->
						<view class="input-group">
							<view class="input-wrapper">
								<view class="input-icon">⚡</view>
								<input 
									class="modern-input verification-input" 
									type="number" 
									v-model="registerForm.smsCode" 
									placeholder="请输入验证码" 
									maxlength="6"
								/>
								<button 
									class="code-btn" 
									:disabled="countdown > 0 || !registerForm.phonenumber || registerForm.phonenumber.length !== 11 || !registerForm.password || !confirmPassword || isPasswordMismatch" 
									@click="handleShowVerifyAndGetSmsCode"
								>
									<text class="code-btn-text">{{ countdown > 0 ? `${countdown}s` : '获取验证码' }}</text>
								</button>
							</view>
						</view>

						<!-- 注册验证码 -->
						<Verify
							ref="registerVerify"
							@success="handleRegisterSuccess"
							@error="handleRegisterError"
							:mode="'pop'"
							:captchaType="'blockPuzzle'"
							:imgSize="{ width: '330px', height: '155px' }"
						></Verify>

						<!-- 注册按钮 -->
						<button 
							class="primary-btn register-btn" 
							:disabled="registerBtnDisabled" 
							@click="handleRegister"
						>
							<text class="btn-text">{{ isRegistering ? '注册中...' : '立即注册' }}</text>
							<view v-if="!isRegistering" class="btn-icon">🚀</view>
						</button>
					</view>
				</view>
			</view>

			<!-- 底部链接 -->
			<view class="footer-links">
				<text class="link-text">遇到问题？</text>
				<text class="link-action">联系客服</text>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
/**
 * 注册页面 - 用户注册功能
 * <AUTHOR>
 * @since 2024-01-01
 */

// ==================== 导入 ====================
import { ref, computed, watch, onMounted, nextTick, onUnmounted } from 'vue'
import { register, getSmsCode } from '@/api/auth/auth'
import { tokenUtil } from '@/utils/token/TokenUtil'
import { StoreUtil } from '@/utils/storage/StoreUtil'
import Verify from '@/components/business/verification/Verify.vue'
import { useCaptchaService, type CaptchaVerifyData } from '@/api/modules/captcha'
import { useEnhancedFormPage } from '@/hooks/useEnhancedFormPage'
import { useErrorHandler } from '@/hooks/useErrorHandler'
import { COMMON_RULES } from '@/hooks/useFormValidation'
import { createLogger } from '@/utils/logger'

// 创建日志服务实例
const logger = createLogger('RegisterPage')

// ==================== 响应式状态 ====================
const activeTab = ref<'login' | 'register'>('register')
const countdown = ref(0)
// 新增：记录是否已经验证过
const hasVerified = ref(false)
// 动画状态
const isAnimated = ref(false)

// 错误处理 Hook
const { handleError, showSuccess: showSuccessMessage } = useErrorHandler({ context: 'Register' })

// 兼容原有的错误处理函数
const showError = (message: string) => handleError(new Error(message))
const showSuccess = (message: string) => showSuccessMessage(message)
const logError = (message: string, error: any) => {
	logger.error(message, { error })
	handleError(error)
}

// 增强表单页面 Hook
const {
	formData: registerForm,
	formState,
	loading,
	validateForm,
	validateField,
	setSubmitFunction,
	submitForm,
	getFieldError,
	hasFieldError,
	setFieldError,
	clearFieldError,
	addField
} = useEnhancedFormPage(
	{
		phonenumber: '',
		password: '',
		smsCode: '',
		confirmPassword: ''
	},
	{
		preset: 'register',
		autoValidate: true,
		validateOnBlur: true
	}
)

// 确认密码（使用表单数据中的字段）
const confirmPassword = computed({
	get: () => registerForm.confirmPassword,
	set: (value) => {
		registerForm.confirmPassword = value
	}
})

// 添加确认密码验证规则
addField('confirmPassword', {
	label: '确认密码',
	rules: [
		COMMON_RULES.required(),
		COMMON_RULES.custom((value) => {
			return value === registerForm.password || '两次输入的密码不一致'
		})
	]
})

// 计算属性：是否正在注册
const isRegistering = computed(() => formState.isSubmitting || loading.value)

// 验证码组件引用
const registerVerify = ref<any>(null)

// 创建注册验证码服务实例
const registerCaptcha = useCaptchaService({
	captchaType: 'blockPuzzle',
	onSuccess: (data) => {
		// logger.info('注册验证成功', { data })
		hasVerified.value = true
		// 验证成功后自动获取短信验证码
		handleGetSmsCodeAfterVerify(data)
	}
})

// ==================== 计算属性 ====================
// 计算属性：密码匹配状态
const isPasswordMismatch = computed(() => {
	return confirmPassword.value && registerForm.password && registerForm.password !== confirmPassword.value
})

const isPasswordMatch = computed(() => {
	return confirmPassword.value && registerForm.password && registerForm.password === confirmPassword.value
})

// 计算属性：注册按钮是否禁用
const registerBtnDisabled = computed(() => {
	if (!registerForm.phonenumber || registerForm.phonenumber.length !== 11) {
		return true
	}
	
	if (!registerForm.smsCode) {
		return true
	}
	
	if (!registerForm.password || !confirmPassword) {
		return true
	}
	
	// 密码和确认密码不一致
	if (isPasswordMismatch.value) {
		return true
	}
	
	return false
})

// ==================== 方法 ====================
/**
 * 切换到登录页面
 */
const switchTab = (tab: 'login' | 'register') => {
	activeTab.value = tab
	
	// 如果切换到登录，跳转到登录页面
	if (tab === 'login') {
		uni.navigateTo({ url: '/pages/auth/login' })
	}
}

/**
 * 处理密码失焦验证
 */
const handlePasswordBlur = () => {
	// 验证密码字段本身
	validateField('password')
	
	// 如果确认密码已填写，检查一致性
	if (confirmPassword.value && registerForm.password) {
		handlePasswordConsistencyCheck()
	}
}

/**
 * 处理确认密码失焦验证
 */
const handleConfirmPasswordBlur = () => {
	// 验证确认密码字段本身
	validateField('confirmPassword')
	
	// 检查密码一致性
	if (confirmPassword.value && registerForm.password) {
		handlePasswordConsistencyCheck()
	}
}

/**
 * 密码一致性检查
 */
const handlePasswordConsistencyCheck = () => {
	// 只有当确认密码框有值时才执行校验
	if (confirmPassword.value && confirmPassword.value.trim() !== '') {
		if (registerForm.password && registerForm.password !== confirmPassword.value) {
			// 显示错误提示并设置字段错误
			showError('两次输入的密码不一致')
			setFieldError('confirmPassword', '两次输入的密码不一致')
		} else if (registerForm.password && registerForm.password === confirmPassword.value) {
			// 密码一致时清除确认密码字段的错误
			clearFieldError('confirmPassword')
		}
	}
}

/**
 * 处理显示验证码并获取短信验证码的方法
 */
const handleShowVerifyAndGetSmsCode = () => {
	if (countdown.value > 0) {
		return
	}
	
	const phonenumber = registerForm.phonenumber
	
	if (!phonenumber || phonenumber.length !== 11) {
		showError('请输入有效的手机号')
		return
	}
	
	// 验证密码
	if (!registerForm.password) {
		showError('请输入密码')
		return
	}
	
	if (!confirmPassword) {
		showError('请确认密码')
		return
	}
	
	if (registerForm.password !== confirmPassword.value) {
		showError('两次输入的密码不一致')
		return
	}
	
	// 重置验证状态
	hasVerified.value = false
	
	// 显示验证码
	showRegisterCaptcha()
}

// 添加请求状态管理
const isGettingSmsCode = ref(false);

/**
 * 验证码验证成功后获取短信验证码
 * 注意：移除了try-catch，让HTTP错误交给统一拦截器处理
 */
const handleGetSmsCodeAfterVerify = async (captchaData: CaptchaVerifyData) => {
	// 防止重复请求
	if (isGettingSmsCode.value) {
		// console.log('正在获取验证码，请勿重复点击');
		return;
	}
	
	const phonenumber = registerForm.phonenumber
	
	// 确保验证码数据完整
	if (!captchaData.captchaVerification) {
		// logger.error('验证码数据不完整', { captchaData })
		showError('验证失败，请重试')
		
		// 重置验证码状态
		registerCaptcha.resetCaptcha()
		return
	}
	
	// logger.debug('获取短信验证码', { captchaData })
	
	try {
		isGettingSmsCode.value = true;
		
		// 调用获取验证码API，传入验证码相关数据
		// 移除try-catch，让500等HTTP错误交给统一拦截器处理
		const res = await getSmsCode({
			phonenumber,
			type: 'register',
			captchaType: captchaData?.captchaType,
			captchaVerification: captchaData?.captchaVerification || undefined
		})
		
		// 记录响应信息
		// logger.debug('getSmsCode响应', {
		// 	code: res.code,
		// 	msg: res.msg,
		// 	message: res.message
		// })
		
		if (res.code === 200) {
			// 成功发送，开始倒计时
			startCountdown()
			showSuccess('验证码已发送')
		} else {
			// 处理各种错误情况，包括频率限制
			let errorMessage = '获取验证码失败';
			if (res.code === 500) {
				// 服务器错误，显示具体错误信息
				errorMessage = res.msg || res.message || '服务器繁忙，请稍后重试';
			} else {
				errorMessage = res.msg || res.message || '获取验证码失败';
			}
			
			showError(errorMessage)
			
			// 🔧 清除倒计时状态，确保按钮可以再次点击
			if (countdownCleanup) {
				countdownCleanup();
				countdownCleanup = null;
			}
			countdown.value = 0;
			
			// 只有在验证码相关错误时才重置验证码状态
			// 如果是其他错误（如手机号已注册等），保留验证码状态
			const errorMsg = res.msg || res.message || '';
			if (errorMsg && (errorMsg.includes('验证码') || errorMsg.includes('验证失败'))) {
				registerCaptcha.resetCaptcha()
				hasVerified.value = false
			}
		}
	} catch (error) {
		// logger.error('获取验证码异常', { error })
		showError('网络异常，请重试')
		
		// 🔧 清除倒计时状态，确保按钮可以再次点击
		if (countdownCleanup) {
			countdownCleanup();
			countdownCleanup = null;
		}
		countdown.value = 0;
		
		// 重置验证码状态
		registerCaptcha.resetCaptcha()
		hasVerified.value = false
	} finally {
		isGettingSmsCode.value = false;
	}
}

/**
 * 注册 - 使用新的表单提交方法
 */
const handleRegister = async () => {
	// 提交表单
	await submitForm()
}

/**
 * 倒计时清理函数 - 已迁移到新的存储服务
 */
// 临时创建空的清理函数以避免类型错误
let countdownCleanup: (() => void) | null = null

/**
 * 启动倒计时（带持久化）
 */
const startCountdown = async () => {
	// 清理之前的倒计时 - 已迁移到新的存储服务
	// if (countdownCleanup) {
	// 	countdownCleanup()
	// }
	
	const countdownKey = 'register_sms_countdown'
	
	// 倒计时功能已迁移到新的存储服务
	// countdownCleanup = await startCountdownWithPersistence(
	// 	countdownKey,
	// 	60, // 60秒倒计时
	// 	(remaining) => {
	// 		countdown.value = remaining
	// 	},
	// 	() => {
	// 		countdown.value = 0
	// 		countdownCleanup = null
	// 	}
	// )
	
	// 简化的倒计时实现
	countdown.value = 60
	const timer = setInterval(() => {
		countdown.value--
		if (countdown.value <= 0) {
			clearInterval(timer)
		}
	}, 1000)
}

/**
 * 处理验证码组件事件
 */
const handleRegisterSuccess = (params: any) => {
	// logger.info('注册验证成功', { params })
	registerCaptcha.verifyCaptchaSuccess(params)
}

const handleRegisterError = () => {
	registerCaptcha.verifyCaptchaError()
	hasVerified.value = false
}

/**
 * 显示验证码组件
 */
const showRegisterCaptcha = () => {
	if (registerVerify.value) {
		registerVerify.value.show()
	}
}

// ==================== 监听器 ====================
// 观察手机号变化
watch(
	() => registerForm.phonenumber,
	(newVal, oldVal) => {
		// 如果手机号改变，需要重新验证
		if (newVal !== oldVal) {
			hasVerified.value = false
		}
		
		registerCaptcha.updateOptions({
			phoneNumber: newVal
		})
	}
)

// ==================== 生命周期 ====================
onMounted(async () => {
	// 启动入场动画
	setTimeout(() => {
		isAnimated.value = true
	}, 100)
	
	// 设置表单提交函数
	setSubmitFunction(async (formData) => {
		// 验证确认密码
		if (formData.password !== confirmPassword.value) {
			throw new Error('两次输入的密码不一致')
		}
		
		// 获取验证码数据
		const captchaData = registerCaptcha.getCaptchaFormData()
		
		// 组装注册参数
		const data: any = { ...formData }
		
		// 添加验证码参数
		if (hasVerified.value && captchaData) {
			data.captchaType = captchaData.captchaType
			data.captchaVerification = captchaData.captchaVerification || ''
		}
		
		// 调用注册API
		const res = await register(data)
		
		// 🔧 修复：处理注册成功但响应体为空的情况
		if (res.code === 200) {
			// 注册成功，清除验证码状态
			registerCaptcha.resetCaptcha()
			hasVerified.value = false
			
			// 如果有返回数据，保存token和用户信息
			if (res.data) {
				if (res.data.access_token) {
					await tokenUtil.saveToken({ accessToken: res.data.access_token })
				}
				if (res.data.userInfo) {
					await StoreUtil.set('user_info', res.data.userInfo)
				}
			}
			
			showSuccess('注册成功')
			
			// 跳转到用户名性别设置页面
			setTimeout(() => {
				uni.reLaunch({
					url: '/pages/user/setupProfile/setupProfile'
				})
			}, 1500)
			
			return res.data || { success: true }
		} else {
			// 处理注册失败
			const errorMsg = res.msg || res.message || '注册失败'
			
			// 只有验证码相关错误才重置验证码状态
			if (errorMsg.includes('验证码') || errorMsg.includes('验证失败')) {
				registerCaptcha.resetCaptcha()
				hasVerified.value = false
			}
			
			throw new Error(errorMsg)
		}
	})
	
	// 🔧 表单数据恢复和自动保存功能已迁移到新的存储服务
	// const savedRegisterData = registerFormPersistence.restoreForm()
	// 
	// if (savedRegisterData) {
	// 	registerForm.value = { ...registerForm.value, ...savedRegisterData }
	// 	if (savedRegisterData.confirmPassword) {
	// 		confirmPassword.value = savedRegisterData.confirmPassword
	// 	}
	// 	console.log('📱 已恢复注册表单数据')
	// }
	// 
	// // 🔧 恢复倒计时状态
	// const countdownKey = 'register_sms_countdown'
	// countdownCleanup = await restoreCountdownState(
	// 	countdownKey,
	// 	(remaining) => {
	// 		countdown.value = remaining
	// 		console.log(`[注册倒计时恢复] 当前剩余: ${remaining}秒`)
	// 	},
	// 	() => {
	// 		countdown.value = 0
	// 		countdownCleanup = null
	// 		console.log('[注册倒计时恢复] 倒计时已结束')
	// 	}
	// )
	// 
	// // 🔧 设置自动保存
	// const registerCleanup = registerFormPersistence.setupAutoSave(() => ({
	// 	phonenumber: registerForm.value.phonenumber,
	// 	password: registerForm.value.password,
	// 	confirmPassword: confirmPassword.value
	// }))
	
	// 使用 nextTick 确保组件完全渲染后再设置引用
	nextTick(() => {
		// 初始化验证码引用
		if (registerVerify.value) {
			registerCaptcha.verifyCaptcha = registerVerify
		}
	})
})

// 组件卸载时清理验证码服务
onUnmounted(() => {
	registerCaptcha.destroy()
	
	// 清理倒计时 - 已迁移到新的存储服务
	// if (countdownCleanup) {
	// 	countdownCleanup()
	// 	countdownCleanup = null
	// }
})
</script>

<style lang="scss" scoped>
// 引入图标字体
@font-face {
  font-family: 'iconfont';
  src: url('data:font/woff2;charset=utf-8;base64,d09GMgABAAAAAAMMAAsAAAAABtwAAAK+AAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHFQGYACCcApwfwE2AiQDCAsGAAQgBYRnBzAbnwXIHoUZI1GJqJDe7w/E8Pz+29oUTb9nARVzG8kIBKnoDSuBCTOazUy9k+nOZnV3yrCKiAWkNvd8dCSkTqPw5+z+vbNVQc9PQ0ihBFa4SPB6HmDjwuWfZ3l6E9gDbtz5ARcpDehAG0CAxrhxPQvqBYZJlYYxeAEO+BwCCGSQA5WxPeOgEQY9TgBKVlZEaPQoBpsYFEKDGTOiAIdWbAjlBBzM/z49kI8WEI4BBpuHwzsJyr1c78dF4S78i+MHAgG4AdhADjAAQEBZU9EOS9EzQyAQ/JmbQgA0NIjAiyv9n6xBiM7BfzwEADMKNAHADsAi4gDzGv0AAF5AzjIwBYQKU1MZc9uE5DgkJzG3a+HsjnR2JrsAKgvNsqI7a93Nnpw87Dv7mUd+ZFVWr02u6vPJI+9p1DWwu/eHXYDSVuQzD/vevZ6k3z9Iz97jnr7j6xvtYfqGnL3X0+2D7r6p3+w//a7H66/unV/dNz9xLxAYKjZ+/jZjD/+UyV3JH3vavNx5OX1ePpx3nH1t2C65/BbxrNj8tG/5tmXr1sP/d9Yzp+WxfyubXLyotHg/aExJWZLaXrb9ePb1M/6JtR1rXzOAwfYACh6vj42HAXwvvVoH/rGTGT7B922YgTpGXpEzgF/aw+CfZGU7BRoMcJcEAjGQJGKRYvTOQg9IBrqwBXA+vBw0RqFiAJAtjAIQQdkPQgj3gBLUM0gx+j0IIb1CE+IbCGQeVBwzChSFHvSZsAVKjBMszB1OOUdzpDM+iJNszknC3RmN9RhnkwmYcJOLe3OJl2Nl/ZgwRZ/RVVjCTIbDhqpagaoaXdx7r2XYKdAnaAugCMOECmYdOKNw1Bx9zsd9QLjEznEka09mDPPhrEMLUFb+ygXvLYn4sDAYbINUKfvhTYEVZFFDg9YJ1+hUQrNgT1h9R2Dw2BkdGBozKgz7SuNyXYP3N6CAzg9yiGGM446a2Iiv+GiEYYo9AAAA') format('woff2');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

.auth-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #00D4FF 0%, #5B73FF 30%, #FF6B35 60%, #F7931E 100%);
	padding: 40rpx;
	box-sizing: border-box;
	position: relative;
	overflow: hidden;
	
	.bg-decoration {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 0;
		pointer-events: none;
		
		// 几何装饰形状
		.bg-shape {
			position: absolute;
			animation: float 8s ease-in-out infinite;
			
			&.bg-shape-1 {
				width: 120rpx;
				height: 120rpx;
				top: 8%;
				left: -30rpx;
				background: linear-gradient(45deg, #00D4FF, #5B73FF);
				border-radius: 30rpx;
				transform: rotate(45deg);
				animation-delay: 0s;
				opacity: 0.7;
			}
			
			&.bg-shape-2 {
				width: 80rpx;
				height: 80rpx;
				top: 25%;
				right: 10%;
				background: linear-gradient(135deg, #FF6B35, #F7931E);
				border-radius: 50%;
				animation-delay: 2s;
				opacity: 0.6;
			}
			
			&.bg-shape-3 {
				width: 0;
				height: 0;
				top: 45%;
				left: 5%;
				border-left: 40rpx solid transparent;
				border-right: 40rpx solid transparent;
				border-bottom: 70rpx solid rgba(168, 230, 207, 0.6);
				animation-delay: 4s;
			}
			
			&.bg-shape-4 {
				width: 100rpx;
				height: 100rpx;
				bottom: 20%;
				right: -20rpx;
				background: linear-gradient(225deg, #5B73FF, #00D4FF);
				clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
				animation-delay: 6s;
				opacity: 0.5;
			}
		}
		
		// 运动生活主题装饰
		.life-decoration {
			position: absolute;
			font-size: 48rpx;
			animation: bounce 3s ease-in-out infinite;
			opacity: 0.8;
			filter: drop-shadow(0 4rpx 8rpx rgba(0,0,0,0.1));
			
			&.life-1 {
				top: 12%;
				right: 20%;
				animation-delay: 0.5s;
			}
			
			&.life-2 {
				top: 35%;
				left: 8%;
				animation-delay: 1.5s;
			}
			
			&.life-3 {
				top: 55%;
				right: 8%;
				animation-delay: 2.5s;
			}
			
			&.life-4 {
				bottom: 35%;
				left: 15%;
				animation-delay: 3.5s;
			}
			
			&.life-5 {
				bottom: 15%;
				right: 30%;
				animation-delay: 4.5s;
			}
			
			&.life-6 {
				top: 70%;
				left: 25%;
				animation-delay: 5.5s;
			}
			
			&.life-7 {
				top: 18%;
				left: 35%;
				animation-delay: 6.5s;
			}
			
			&.life-8 {
				bottom: 25%;
				right: 15%;
				animation-delay: 7.5s;
			}
		}
		
		// 动态光效
		.light-effect {
			position: absolute;
			border-radius: 50%;
			background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
			animation: pulse 4s ease-in-out infinite;
			
			&.light-1 {
				width: 300rpx;
				height: 300rpx;
				top: -50rpx;
				right: -100rpx;
				animation-delay: 1s;
			}
			
			&.light-2 {
				width: 200rpx;
				height: 200rpx;
				bottom: -50rpx;
				left: -50rpx;
				animation-delay: 3s;
			}
		}
	}
	
	.main-content {
		position: relative;
		z-index: 1;
		
		.logo-section {
			display: flex;
			flex-direction: column;
			align-items: center;
			margin: 80rpx 0 60rpx;
			opacity: 0;
			transform: translateY(30rpx);
			transition: all 0.8s ease;
			
			&.animate-in {
				opacity: 1;
				transform: translateY(0);
			}
			
			.logo-wrapper {
				width: 160rpx;
				height: 160rpx;
				margin-bottom: 30rpx;
				background: rgba(255, 255, 255, 0.2);
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				backdrop-filter: blur(10rpx);
				
				.logo {
					width: 120rpx;
					height: 120rpx;
				}
			}
			
			.brand-title {
				font-size: 48rpx;
				font-weight: 700;
				color: #fff;
				margin-bottom: 10rpx;
				text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
			}
			
			.brand-subtitle {
				font-size: 28rpx;
				color: rgba(255, 255, 255, 0.8);
				text-align: center;
			}
		}
		
		.auth-card {
			background: rgba(255, 255, 255, 0.98);
			backdrop-filter: blur(25rpx);
			border-radius: 32rpx;
			padding: 60rpx 40rpx;
			box-shadow: 
				0 20rpx 60rpx rgba(255, 107, 157, 0.25),
				0 8rpx 32rpx rgba(0, 0, 0, 0.15),
				inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
			border: 2rpx solid rgba(255, 255, 255, 0.6);
			opacity: 0;
			transform: translateY(50rpx);
			transition: all 0.8s ease 0.3s;
			
			&.animate-in {
				opacity: 1;
				transform: translateY(0);
			}
			
			.tab-switcher {
				display: flex;
				background: #f8f9fa;
				border-radius: 16rpx;
				padding: 8rpx;
				margin-bottom: 50rpx;
				position: relative;
				
				.tab-slider {
					position: absolute;
					top: 8rpx;
					width: calc(50% - 8rpx);
					height: calc(100% - 16rpx);
					background: linear-gradient(135deg, #00D4FF 0%, #5B73FF 100%);
					border-radius: 12rpx;
					transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
					box-shadow: 0 4rpx 16rpx rgba(255, 149, 182, 0.4);
				}
				
				.tab-option {
					flex: 1;
					text-align: center;
					font-size: 30rpx;
					color: #666;
					padding: 20rpx 0;
					position: relative;
					z-index: 1;
					transition: color 0.3s ease;
					
					.tab-text {
						font-weight: 500;
					}
					
					&.active {
						color: #fff;
						
						.tab-text {
							font-weight: 600;
						}
					}
				}
			}
			
			.form-section {
				.form-content {
					.input-group {
						position: relative;
						margin-bottom: 32rpx;
						
						.input-wrapper {
							height: 96rpx;
							display: flex;
							align-items: center;
							background: #f8f9fa;
							border-radius: 16rpx;
							padding: 0 24rpx;
							box-sizing: border-box;
							font-size: 30rpx;
							border: 2rpx solid transparent;
							transition: all 0.3s ease;
							position: relative;
							
							&:focus-within {
							background: #fff;
							border-color: #00D4FF;
							box-shadow: 0 0 0 6rpx rgba(0, 212, 255, 0.15);
							transform: translateY(-2rpx);
						}
							
							// 密码错误状态
							&.password-error {
								border-color: #ff4757;
								background: #fff5f5;
								
								&:focus-within {
									border-color: #ff4757;
									box-shadow: 0 0 0 6rpx rgba(255, 71, 87, 0.15);
								}
							}
							
							// 密码成功状态
							&.password-success {
								border-color: #2ed573;
								background: #f0fff4;
								
								&:focus-within {
									border-color: #2ed573;
									box-shadow: 0 0 0 6rpx rgba(46, 213, 115, 0.15);
								}
							}
							
							// 密码错误提示
							.password-error-tip {
								position: absolute;
								top: -24rpx;
								left: 0;
								font-size: 22rpx;
								color: #ff4757;
								background: #fff;
								padding: 0 8rpx;
								border-radius: 4rpx;
								animation: fadeInDown 0.3s ease;
							}
							
							.input-icon {
								font-size: 32rpx;
								margin-right: 24rpx;
								opacity: 0.7;
							}
							
							.modern-input {
								flex: 1;
								height: 100%;
								background: none;
								border: none;
								outline: none;
								font-size: inherit;
								color: #333;
								
								&::placeholder {
									color: #999;
								}
							}
							
							.verification-input {
								padding-right: 200rpx;
							}
						}
						
						.password-match {
							padding: 12rpx;
							border-radius: 8rpx;
							
							.match-icon {
								font-size: 28rpx;
								
								&.match {
									color: #4cae4c;
								}
							}
						}
						
						.code-btn {
							position: absolute;
							right: 8rpx;
							top: 50%;
							transform: translateY(-50%);
							height: 80rpx;
							background: linear-gradient(135deg, #00D4FF 0%, #5B73FF 100%);
							color: #fff;
							font-size: 26rpx;
							border-radius: 12rpx;
							padding: 0 24rpx;
							min-width: 160rpx;
							display: flex;
							align-items: center;
							justify-content: center;
							margin: 0;
							border: none;
							transition: all 0.3s ease;
							box-shadow: 0 4rpx 16rpx rgba(0, 212, 255, 0.4);
							
							.code-btn-text {
								font-weight: 500;
							}
							
							&:active {
								transform: translateY(-50%) scale(0.98);
							}
							
							&::after {
								border: none;
							}
							
							&[disabled] {
								background: #ddd;
								color: #999;
								box-shadow: none;
								transform: translateY(-50%);
							}
						}
					}
					
					.primary-btn {
						width: 100%;
						height: 100rpx;
						background: linear-gradient(135deg, #ff97b6 0%, #ff79a5 100%);
						color: #fff;
						font-size: 32rpx;
						border-radius: 20rpx;
						margin-top: 40rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						border: none;
						box-shadow: 0 8rpx 24rpx rgba(0, 212, 255, 0.5);
						transition: all 0.3s ease;
						position: relative;
						overflow: hidden;
						
						.btn-text {
							font-weight: 600;
							margin-right: 12rpx;
						}
						
						.btn-icon {
							font-size: 28rpx;
						}
						
						&:active {
							transform: translateY(2rpx);
							box-shadow: 0 4rpx 16rpx rgba(0, 212, 255, 0.5);
						}
						
						&::after {
							border: none;
						}
						
						&[disabled] {
							background: #ddd;
							color: #999;
							box-shadow: none;
							transform: none;
						}
					}
				}
			}
		}
		
		.footer-links {
			margin-top: 60rpx;
			text-align: center;
			font-size: 28rpx;
			
			.link-text {
				color: rgba(255, 255, 255, 0.7);
				margin-right: 10rpx;
			}
			
			.link-action {
				color: #fff;
				font-weight: 600;
				text-decoration: underline;
				transition: opacity 0.2s ease;
				
				&:active {
					opacity: 0.8;
				}
			}
		}
	}
}

// 动画定义
@keyframes float {
	0%, 100% {
		transform: translateY(0px);
	}
	50% {
		transform: translateY(-20px);
	}
}

@keyframes bounce {
	0%, 100% {
		transform: translateY(0px) scale(1);
	}
	50% {
		transform: translateY(-10px) scale(1.05);
	}
}

@keyframes pulse {
	0%, 100% {
		opacity: 0.3;
		transform: scale(1);
	}
	50% {
		opacity: 0.6;
		transform: scale(1.1);
	}
}

@keyframes fadeInDown {
	0% {
		opacity: 0;
		transform: translateY(-10rpx);
	}
	100% {
		opacity: 1;
		transform: translateY(0);
	}
}

// 验证码组件样式修复
:deep(.el-dialog) {
	border-radius: 16rpx !important;
	overflow: hidden !important;
}

:deep(.verification-code-wrapper) {
	display: flex !important;
	justify-content: center !important;
	align-items: center !important;
	min-height: 200px !important;
}

:deep(.verification-img) {
	max-width: 100% !important;
	height: auto !important;
	border-radius: 8rpx !important;
}
</style>