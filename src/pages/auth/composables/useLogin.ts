/**
 * 登录页面逻辑
 */
import { ref } from 'vue'
import { useAuth } from '@/hooks/useAuth'
import { useLoading } from '@/hooks/useLoading'
import type { LoginParams } from '@/api/auth/auth'
import { <PERSON>rrorHandler } from '../../../utils/errorHandler'

export const useLogin = () => {
  const loginType = ref<'password' | 'sms'>('password')
  const { loginWithPassword, loginWithSms } = useAuth()
  const { withLoading, loading } = useLoading()
  
  /**
   * 处理登录
   */
  const handleLogin = async (params: LoginParams) => {
    if (loading.value) return

    await withLoading(async () => {
      // 执行登录
      if (params.loginGrantType === 'password') {
        await loginWithPassword(params)
      } else {
        await loginWithSms(params)
      }

      // loginWithPassword 和 loginWithSms 在失败时会直接抛出异常
      // 成功时返回 { success: true, data: ... }

      // 显示成功提示
      ErrorHandler.showSuccess('登录成功')

      // 跳转到首页
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/home/<USER>/home'
        })
      }, 1500)
    }, '登录中...')
  }

  /**
   * 验证码验证成功
   */
  const handleCaptchaSuccess = (_captchaVerification: string) => {
    // 这里可以处理验证码验证成功后的逻辑
  }

  /**
   * 验证码验证失败
   */
  const handleCaptchaError = (_message: string) => {
    // 这里可以处理验证码验证失败后的逻辑
  }



  return {
    // 基础状态
    loginType,
    loading,
    
    // 方法
    handleLogin,
    handleCaptchaSuccess,
    handleCaptchaError
  }
}