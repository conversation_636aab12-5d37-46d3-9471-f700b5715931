<template>
  <view>
    <!-- 简单的用户信息 -->
    <view style="background-color: #ffffff; margin: 20rpx; padding: 20rpx; border-radius: 10rpx;">
      <view style="display: flex; align-items: center;">
        <image style="width: 100rpx; height: 100rpx; border-radius: 50%;" src="/static/default-avatar.png" mode="aspectFill"></image>
        <view style="margin-left: 20rpx;">
          <view style="font-size: 32rpx; font-weight: bold; color: #333;">{{ userName || '未登录' }}</view>
          <view style="font-size: 24rpx; color: #999; margin-top: 8rpx;">账号ID: {{ userId || '' }}</view>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view style="background-color: #ffffff; margin: 20rpx; padding: 0; border-radius: 10rpx;">
      <view style="padding: 30rpx; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;">
        <text>我的资料</text>
        <text>></text>
      </view>
      <view style="padding: 30rpx; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;">
        <text>我的收藏</text>
        <text>></text>
      </view>
      <view style="padding: 30rpx; display: flex; justify-content: space-between;">
        <text>浏览记录</text>
        <text>></text>
      </view>
    </view>

    <!-- 退出按钮 -->
    <view style="margin: 20rpx; margin-bottom: 150rpx;">
      <button @click="handleLogout" style="background-color: #ff4d4f; color: white; height: 90rpx; line-height: 90rpx; border: none; border-radius: 8rpx;">退出登录</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { http } from '@/http/http';
import { tokenUtil } from '@/utils/token/TokenUtil';
import { StoreUtil } from '@/utils/storage/StoreUtil'
import { ErrorHandler } from '../../../utils/errorHandler';
import { logout } from '@/api/auth/auth';
import { ROUTES, RouterService } from '@/config';
import { createContextLogger } from '@/utils/common/logger';

// 创建日志服务实例
const logger = createContextLogger('MinePage');

const userInfo = ref<any>(null);
const userName = ref<string>('');
const userId = ref<string>('');

// 直接解析JWT token
const parseJWT = async () => {
  const token = await tokenUtil.getAccessToken();
  if (!token) {
    return;
  }

  try {
    // 分割JWT token
    const parts = token.split('.');
    if (parts.length !== 3) {
      logger.warn('无效的JWT格式');
      return;
    }

    // 解码payload部分
    const payload = parts[1];

    // 将Base64URL转换为标准Base64
    const base64 = payload.replace(/-/g, '+').replace(/_/g, '/');

    // 处理填充
    const pad = base64.length % 4;
    const paddedBase64 = pad ? base64 + '='.repeat(4 - pad) : base64;

    // 解码并解析JSON
    let decodedPayload;
    try {
      decodedPayload = decodeURIComponent(
        atob(paddedBase64)
          .split('')
          .map(c => `%${('00' + c.charCodeAt(0).toString(16)).slice(-2)}`)
          .join('')
      );
    } catch (e) {
      // 如果解码失败，尝试直接使用atob
      decodedPayload = atob(paddedBase64);
    }

    // 解析为JSON对象
    const payloadObj = JSON.parse(decodedPayload);

    // 查找calmJson
    if (payloadObj.calmJson) {
      let calmData;

      // 检查calmJson是字符串还是对象
      if (typeof payloadObj.calmJson === 'string') {
        try {
          calmData = JSON.parse(payloadObj.calmJson);
        } catch (e) {
          logger.warn('calmJson解析失败，作为字符串使用:', payloadObj.calmJson);
          calmData = payloadObj.calmJson;
        }
      } else {
        calmData = payloadObj.calmJson;
      }

      // 尝试所有可能的字段名
      const possibleUserNameFields = ['userName', 'userNam', 'username', 'name', 'nickName'];
      for (const field of possibleUserNameFields) {
        if (calmData[field]) {
          userName.value = calmData[field];
          break;
        }
      }

      const possibleUserIdFields = ['userId', 'id', 'user_id', 'uid'];
      for (const field of possibleUserIdFields) {
        if (calmData[field]) {
          userId.value = calmData[field];
          break;
        }
      }
    } else {
      // 如果没有calmJson，直接在payload中查找
      const possibleUserNameFields = ['userName', 'userNam', 'username', 'name', 'sub', 'nickname'];
      for (const field of possibleUserNameFields) {
        if (payloadObj[field]) {
          userName.value = payloadObj[field];
          break;
        }
      }

      const possibleUserIdFields = ['userId', 'id', 'user_id', 'uid', 'jti'];
      for (const field of possibleUserIdFields) {
        if (payloadObj[field]) {
          userId.value = payloadObj[field];
          break;
        }
      }
    }
  } catch (error) {
    logger.error('解析JWT Token失败:', error);
  }
};

const getUserInfo = async () => {
  // 解析JWT token
  await parseJWT();

  // 保持原有的用户信息获取逻辑
  const storedUserInfo = await StoreUtil.get('USER_INFO');
  if (storedUserInfo) {
    userInfo.value = storedUserInfo;

    // 如果还没有从JWT中获取到用户名，从存储的用户信息中获取
    if (!userName.value && (storedUserInfo as any).username) {
      userName.value = (storedUserInfo as any).username;
    }

    // 如果还没有从JWT中获取到用户ID，从存储的用户信息中获取
    if (!userId.value && (storedUserInfo as any).id) {
      userId.value = (storedUserInfo as any).id;
    }
  }
};

const handleLogout = async () => {
  try {
    // 获取token，确保能正常退出
    const token = await tokenUtil.getAccessToken();
    if (!token) {
      // 无token时直接跳转到登录页
      ErrorHandler.showError('未登录或登录已过期');

      setTimeout(() => {
        RouterService.reLaunch({ url: ROUTES.AUTH.LOGIN });
      }, 1500);
      return;
    }

    // 显示确认对话框
    uni.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: async function (res) {
        if (res.confirm) {
          try {
            // 调用退出登录接口，使用POST方式，不传参，但请求头会自动携带token
            const res = await logout();

            // 清除本地存储的token和用户信息
            await tokenUtil.clearToken();
            await StoreUtil.remove('USER_INFO');

            // 清除解析的用户信息
            userName.value = '';
            userId.value = '';

            ErrorHandler.showSuccess('退出成功');

            // 延迟跳转到登录页
            setTimeout(() => {
              RouterService.reLaunch({ url: ROUTES.AUTH.LOGIN });
            }, 1500);
          } catch (error) {
            logger.error('调用退出登录接口失败:', error);

            // 即使接口调用失败，也要清除本地数据并退出
            await tokenUtil.clearToken();
            await StoreUtil.remove('USER_INFO');
            userName.value = '';
            userId.value = '';

            ErrorHandler.showSuccess('退出成功');

            setTimeout(() => {
              RouterService.reLaunch({ url: ROUTES.AUTH.LOGIN });
            }, 1500);
          }
        }
      }
    });
  } catch (error) {
    logger.error('退出失败:', error);
    ErrorHandler.showError('退出失败');
  }
};

onMounted(async () => {
  await getUserInfo();
});
</script>

<style>
page {
  background-color: #f5f5f5;
  height: 100%;
  width: 100%;
}
</style>
