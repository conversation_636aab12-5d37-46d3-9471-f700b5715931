<template>
  <view class="auth-container">
    <!-- 背景装饰，与infomation.vue保持一致 -->
    <view class="bg-decoration">
      <view class="bg-circle bg-circle-1"></view>
      <view class="bg-circle bg-circle-2"></view>
      <view class="bg-circle bg-circle-3"></view>
      <view class="love-decoration love-1">💕</view>
      <view class="love-decoration love-2">💖</view>
      <view class="love-decoration love-3">💝</view>
    </view>

    <!-- 右上角跳过按钮 -->
    <view class="skip-header">
      <view class="skip-btn-header" @click="handleSkip">跳过</view>
    </view>

    <view class="main-content">
      <!-- Logo区域 -->
      <view class="logo-section" :class="{ 'animate-in': isAnimated }">
        <view class="logo-wrapper">
          <image class="logo" src="@/assets/images/logo.png" mode="aspectFit"></image>
        </view>
        <text class="brand-title">扩展信息</text>
        <text class="brand-subtitle">完善更多资料，提升匹配成功率</text>
      </view>

      <!-- 资料填写卡片 -->
      <view class="auth-card" :class="{ 'animate-in': isAnimated }">
        <view class="form-section">
          <view class="form-content">
            <!-- 是否有车 -->
            <view class="input-group">
              <view class="field-title">是否有车<text class="optional-text">（可选）</text></view>
              <view class="input-wrapper" @click="showCarPicker = true">
                <view class="input-icon">🚗</view>
                <view class="picker-content">
                  <text class="picker-text" :class="{ 'placeholder': !carText }">
                    {{ carText || '请选择是否有车' }}
                  </text>
                </view>
              </view>
            </view>
            
            <!-- 是否有房 -->
            <view class="input-group">
              <view class="field-title">是否有房<text class="optional-text">（可选）</text></view>
              <view class="input-wrapper" @click="showHousePicker = true">
                <view class="input-icon">🏠</view>
                <view class="picker-content">
                  <text class="picker-text" :class="{ 'placeholder': !houseText }">
                    {{ houseText || '请选择是否有房' }}
                  </text>
                </view>
              </view>
            </view>
            
            <!-- 工作单位 -->
            <view class="input-group">
              <view class="field-title">工作单位<text class="optional-text">（可选）</text></view>
              <view class="input-wrapper">
                <view class="input-icon">🏢</view>
                <input 
                  class="uni-input" 
                  v-model="form.company" 
                  placeholder="请输入工作单位"
                  :focus="inputFocus"
                  @focus="handleInputFocus"
                  @blur="handleInputBlur"
                />
              </view>
            </view>
            
            <!-- 年收入范围 -->
            <view class="input-group">
              <view class="field-title">年收入范围（万）<text class="optional-text">（可选）</text></view>
              <view class="input-wrapper" @click="showIncomePicker = true">
                <view class="input-icon">💰</view>
                <view class="picker-content">
                  <text class="picker-text" :class="{ 'placeholder': !form.yearAnnualIncome }">
                    {{ form.yearAnnualIncome || '请选择年收入范围' }}
                  </text>
                </view>
              </view>
            </view>
            
            <!-- 子女情况 -->
            <view class="input-group">
              <view class="field-title">子女情况<text class="optional-text">（可选）</text></view>
              <view class="input-wrapper" @click="showChildrenPicker = true">
                <view class="input-icon">👶</view>
                <view class="picker-content">
                  <text class="picker-text" :class="{ 'placeholder': form.childrenStatus === null }">
                    {{ form.childrenStatus !== null ? form.childrenStatus + '个子女' : '请选择子女情况' }}
                  </text>
                </view>
              </view>
            </view>
            
            <!-- 兴趣爱好 -->
            <view class="input-group">
              <view class="field-title">兴趣爱好<text class="optional-text">（可选）</text></view>
              <view class="input-wrapper" @click="showHobbyPicker = true">
                <view class="input-icon">❤️</view>
                <view class="picker-content">
                  <text class="picker-text" :class="{ 'placeholder': selectedHobbies.length === 0 }">
                    {{ selectedHobbies.length > 0 ? selectedHobbies.join('、') : '请选择兴趣爱好（可选5个）' }}
                  </text>
                </view>
              </view>
            </view>
            
            <!-- 提交按钮 -->
            <button class="primary-btn submit-btn" :disabled="loading" @click="handleSubmit">
              <text class="btn-text">{{ loading ? '保存中...' : '保存并继续' }}</text>
              <view v-if="!loading" class="btn-icon">🚀</view>
            </button>
          </view>
        </view>
      </view>
      
      <!-- 底部链接 -->
      <view class="footer-links">
        <text class="link-text">遇到问题？</text>
        <text class="link-action">联系客服</text>
      </view>
    </view>
    
    <!-- 是否有车选择器弹窗 -->
    <uni-popup ref="carPopup" type="bottom" @change="handlePopupChange">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-cancel" @click="cancelCar">取消</text>
          <view class="popup-title">是否有车</view>
          <text class="popup-confirm" @click="confirmCar">确定</text>
        </view>
        <picker-view class="picker-view" :value="[tempCarIndex]" @change="onCarChange" indicator-style="height: 50px;">
          <picker-view-column>
            <view class="picker-item" v-for="(item, idx) in carOptions" :key="item.value">
              {{ item.label }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </uni-popup>
    
    <!-- 是否有房选择器弹窗 -->
    <uni-popup ref="housePopup" type="bottom" @change="handlePopupChange">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-cancel" @click="cancelHouse">取消</text>
          <view class="popup-title">是否有房</view>
          <text class="popup-confirm" @click="confirmHouse">确定</text>
        </view>
        <picker-view class="picker-view" :value="[tempHouseIndex]" @change="onHouseChange" indicator-style="height: 50px;">
          <picker-view-column>
            <view class="picker-item" v-for="(item, idx) in houseOptions" :key="item.value">
              {{ item.label }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </uni-popup>
    
    <!-- 年收入选择器弹窗 -->
    <uni-popup ref="incomePopup" type="bottom" @change="handlePopupChange">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-cancel" @click="cancelIncome">取消</text>
          <view class="popup-title">选择年收入范围</view>
          <text class="popup-confirm" @click="confirmIncome">确定</text>
        </view>
        <picker-view class="picker-view" :value="[incomeIndex]" @change="onIncomeChange" indicator-style="height: 50px;">
          <picker-view-column>
            <view class="picker-item" v-for="(item, idx) in incomeOptions" :key="item">
              {{ item }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </uni-popup>
    
    <!-- 子女情况选择器弹窗 -->
    <uni-popup ref="childrenPopup" type="bottom" @change="handlePopupChange">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-cancel" @click="cancelChildren">取消</text>
          <view class="popup-title">子女情况</view>
          <text class="popup-confirm" @click="confirmChildren">确定</text>
        </view>
        <picker-view class="picker-view" :value="[childrenIndex]" @change="onChildrenChange" indicator-style="height: 50px;">
          <picker-view-column>
            <view class="picker-item" v-for="(item, idx) in childrenOptions" :key="item">
              {{ item }}个子女
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </uni-popup>
    
    <!-- 兴趣爱好选择器弹窗 -->
    <uni-popup ref="hobbyPopup" type="bottom" @change="handlePopupChange">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-cancel" @click="cancelHobby">取消</text>
          <view class="popup-title">已选择 {{ tempSelectedHobbies.length }}/5 个爱好</view>
          <text class="popup-confirm" @click="confirmHobby">确定</text>
        </view>
        <view class="hobby-picker-content">
          <view class="hobby-tags">
            <view
              v-for="(tag, idx) in hobbyTags"
              :key="tag"
              :class="['hobby-tag', { selected: tempSelectedHobbies.includes(tag) }]"
              @click="toggleTempHobby(tag)"
            >
              {{ tag }}
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue';
import { editExtendInfo, type ExtendInfoParams as ApiExtendInfoParams } from '@/api/modules/member';
import { useLoading } from '@/hooks/useLoading';
import { useErrorHandler } from '@/hooks/useErrorHandler';
import { ErrorHandler } from '../../../utils/errorHandler';

// 表单数据类型
interface ExtendInfoParams {
  hasCar: number | null;
  hasHouse: number | null;
  company: string;
  yearAnnualIncome: string;
  childrenStatus: number | null;
  hobbies: string;
}

const form = reactive<ExtendInfoParams>({
  hasCar: null,
  hasHouse: null,
  company: '',
  yearAnnualIncome: '',
  childrenStatus: null,
  hobbies: ''
});

// 选项数据
const carOptions = [
  { label: '无', value: 0 },
  { label: '有', value: 1 },
  { label: '计划购车', value: 2 }
];
const houseOptions = [
  { label: '无', value: 0 },
  { label: '有', value: 1 },
  { label: '计划购房', value: 2 }
];
const incomeOptions = [
  '0~10', '11~20', '21~30', '31~40', '41~50', '51~60', '61~70', '71~80', '81~90', '91~100', '100万以上'
];

// 选择器状态
const showCarPicker = ref(false);
const showHousePicker = ref(false);
const showIncomePicker = ref(false);
const showChildrenPicker = ref(false);
const showHobbyPicker = ref(false);

// 选择器弹窗引用
const carPopup = ref<any>(null);
const housePopup = ref<any>(null);
const incomePopup = ref<any>(null);
const childrenPopup = ref<any>(null);
const hobbyPopup = ref<any>(null);

// 选择器索引
const carIndex = ref(0);
const houseIndex = ref(0);
const incomeIndex = ref(0);
const childrenIndex = ref(0);

// 临时索引（用于取消操作时恢复）
const tempCarIndex = ref(1); // 弹窗默认停留在"有"
const tempHouseIndex = ref(1); // 弹窗默认停留在"有"
const tempIncomeIndex = ref(0);
const tempChildrenIndex = ref(0);

// 计算属性：显示文本
const carText = computed(() => {
  if (form.hasCar === null) return '';
  return carOptions.find(item => item.value === form.hasCar)?.label || '';
});

const houseText = computed(() => {
  if (form.hasHouse === null) return '';
  return houseOptions.find(item => item.value === form.hasHouse)?.label || '';
});

const childrenOptions = [0, 1, 2, 3, 4];

// 兴趣爱好标签
const hobbyTags = [
  '旅游', '阅读', '运动', '美食', '音乐', '电影', '摄影', '绘画', '舞蹈', '编程', '游戏', '健身', '手工', '宠物', '旅行', '书法', '唱歌', '钓鱼', '爬山', '跑步', '瑜伽'
];
const selectedHobbies = ref<string[]>([]);
const tempSelectedHobbies = ref<string[]>([]);

// 兴趣爱好临时选择逻辑
const toggleTempHobby = (tag: string) => {
  const idx = tempSelectedHobbies.value.indexOf(tag);
  if (idx > -1) {
    tempSelectedHobbies.value.splice(idx, 1);
  } else {
    if (tempSelectedHobbies.value.length >= 5) {
      ErrorHandler.showError('最多只能选择5个兴趣爱好');
      return;
    }
    tempSelectedHobbies.value.push(tag);
  }
};

// 是否有车选择器事件
const onCarChange = (e: any) => {
  tempCarIndex.value = e.detail.value[0];
};
const confirmCar = () => {
  carIndex.value = tempCarIndex.value;
  form.hasCar = carOptions[carIndex.value].value;
  showCarPicker.value = false;
  if (carPopup.value) carPopup.value.close();
};
const cancelCar = () => {
  showCarPicker.value = false;
  if (carPopup.value) carPopup.value.close();
};

// 是否有房选择器事件
const onHouseChange = (e: any) => {
  tempHouseIndex.value = e.detail.value[0];
};
const confirmHouse = () => {
  houseIndex.value = tempHouseIndex.value;
  form.hasHouse = houseOptions[houseIndex.value].value;
  showHousePicker.value = false;
  if (housePopup.value) housePopup.value.close();
};
const cancelHouse = () => {
  showHousePicker.value = false;
  if (housePopup.value) housePopup.value.close();
};

// 年收入选择器事件
const onIncomeChange = (e: any) => {
  tempIncomeIndex.value = e.detail.value[0];
};
const confirmIncome = () => {
  incomeIndex.value = tempIncomeIndex.value;
  form.yearAnnualIncome = incomeOptions[incomeIndex.value];
  showIncomePicker.value = false;
  if (incomePopup.value) incomePopup.value.close();
};
const cancelIncome = () => {
  showIncomePicker.value = false;
  if (incomePopup.value) incomePopup.value.close();
};

// 子女情况选择器事件
const onChildrenChange = (e: any) => {
  tempChildrenIndex.value = e.detail.value[0];
};
const confirmChildren = () => {
  childrenIndex.value = tempChildrenIndex.value;
  form.childrenStatus = childrenOptions[childrenIndex.value];
  showChildrenPicker.value = false;
  if (childrenPopup.value) childrenPopup.value.close();
};
const cancelChildren = () => {
  showChildrenPicker.value = false;
  if (childrenPopup.value) childrenPopup.value.close();
};

// 兴趣爱好选择器事件
const confirmHobby = () => {
  selectedHobbies.value = [...tempSelectedHobbies.value];
  form.hobbies = selectedHobbies.value.join(',');
  showHobbyPicker.value = false;
  if (hobbyPopup.value) hobbyPopup.value.close();
};
const cancelHobby = () => {
  tempSelectedHobbies.value = [...selectedHobbies.value]; // 恢复原始选择
  showHobbyPicker.value = false;
  if (hobbyPopup.value) hobbyPopup.value.close();
};

const handlePopupChange = (e: any) => {
  if (!e.show) {
    showCarPicker.value = false;
    showHousePicker.value = false;
    showIncomePicker.value = false;
    showChildrenPicker.value = false;
    showHobbyPicker.value = false;
  }
};

// 监听弹窗显示状态
watch([showCarPicker, showHousePicker, showIncomePicker, showChildrenPicker, showHobbyPicker], (newVal) => {
  const [car, house, income, children, hobby] = newVal;
  nextTick(() => {
    if (car && carPopup.value) {
      tempCarIndex.value = 1; // 默认停留在"有"选项
      carPopup.value.open('bottom');
    }
    if (house && housePopup.value) {
      tempHouseIndex.value = 1; // 默认停留在"有"选项  
      housePopup.value.open('bottom');
    }
    if (income && incomePopup.value) incomePopup.value.open('bottom');
    if (children && childrenPopup.value) childrenPopup.value.open('bottom');
    if (hobby && hobbyPopup.value) {
      tempSelectedHobbies.value = [...selectedHobbies.value]; // 初始化临时选择
      hobbyPopup.value.open('bottom');
    }
  });
});

// 动画与加载
const isAnimated = ref(false);
const { loading, withLoading } = useLoading();
const { handleError, handleApiError } = useErrorHandler();

// 提交
const handleSubmit = async () => {
  await withLoading(async () => {
    // 检查是否有任何字段被填写
    const hasAnyData = form.hasCar !== null || 
                      form.hasHouse !== null || 
                      form.company.trim() !== '' || 
                      form.yearAnnualIncome !== '' || 
                      form.childrenStatus !== null || 
                      form.hobbies !== '';
    
    // 如果什么都没填写，直接跳转
    if (!hasAnyData) {
      ErrorHandler.showSuccess('跳过扩展信息');
      setTimeout(() => {
        uni.navigateTo({ url: '/pages/auth/realAuth' });
      }, 1500);
      return;
    }
    
    // 构建提交数据，处理null值转换
    const submitData: Partial<ApiExtendInfoParams> = {};
    if (form.hasCar !== null) submitData.hasCar = form.hasCar;
    if (form.hasHouse !== null) submitData.hasHouse = form.hasHouse;
    if (form.company) submitData.company = form.company;
    if (form.yearAnnualIncome) submitData.yearAnnualIncome = form.yearAnnualIncome;
    if (form.childrenStatus !== null) submitData.childrenStatus = form.childrenStatus;
    if (form.hobbies) submitData.hobbies = form.hobbies;
    
    try {
      const res = await editExtendInfo(submitData as ApiExtendInfoParams);
      if (res.code === 200) {
        ErrorHandler.showSuccess('保存成功');
        setTimeout(() => {
          uni.navigateTo({ url: '/pages/auth/realAuth' });
        }, 1500);
      } else {
        handleApiError(res, '保存失败');
      }
    } catch (error) {
      handleError(error, '网络错误，请重试');
    }
  });
};
// 跳过
const handleSkip = () => {
  uni.navigateTo({ url: '/pages/auth/realAuth' });
};

onMounted(() => {
  setTimeout(() => {
    isAnimated.value = true;
    // 延迟聚焦到工作单位输入框
    setTimeout(() => {
      inputFocus.value = true;
    }, 500);
  }, 100);
  
  uni.setNavigationBarColor({
    frontColor: '#000000',
    backgroundColor: '#faf5f5',
    animation: { duration: 400, timingFunc: 'easeIn' }
  });
});

// 输入框自动聚焦逻辑
const inputFocus = ref(false);
const handleInputFocus = () => {
  inputFocus.value = true;
};
const handleInputBlur = () => {
  inputFocus.value = false;
};
</script>

<style lang="scss">
page {
  background-color: #faf5f5;
  min-height: 100%;
}

.auth-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff97b6 0%, #ff79a5 50%, #f8a5c2 100%);
  padding: 0 30rpx;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  
  .bg-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
    
    .bg-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      animation: float 6s ease-in-out infinite;
    
      &.bg-circle-1 {
        width: 300rpx;
        height: 300rpx;
        top: -100rpx;
        right: -50rpx;
        animation: float 6s ease-in-out infinite;
      }
      
      &.bg-circle-2 {
        width: 200rpx;
        height: 200rpx;
        bottom: 100rpx;
        left: -50rpx;
        animation: float 8s ease-in-out infinite reverse;
      }
      
      &.bg-circle-3 {
        width: 150rpx;
        height: 150rpx;
        top: 50%;
        right: 100rpx;
        animation: float 7s ease-in-out infinite;
      }
    }
    
    .love-decoration {
      position: absolute;
      font-size: 40rpx;
      opacity: 0.3;
      animation: float 4s ease-in-out infinite;
      
      &.love-1 {
        top: 20%;
        left: 15%;
        animation-delay: 0s;
      }
      
      &.love-2 {
        top: 60%;
        right: 20%;
        animation-delay: 2s;
      }
      
      &.love-3 {
        bottom: 30%;
        left: 25%;
        animation-delay: 4s;
      }
    }
  }
  
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding-bottom: 100rpx;
    
    .logo-section {
      text-align: center;
      padding: 80rpx 30rpx 60rpx;
      color: #fff;
      position: relative;
      overflow: hidden;
      border-radius: 0 0 30rpx 30rpx;
      opacity: 0;
      transform: translateY(50rpx);
      transition: all 0.8s ease;
      
      &.animate-in {
        opacity: 1;
        transform: translateY(0);
      }
      
      &::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 30%, transparent 70%);
        animation: rotate 20s linear infinite;
      }
      
      .logo-wrapper {
        margin-bottom: 20rpx;
        
        .logo {
          width: 120rpx;
          height: 120rpx;
          border-radius: 24rpx;
          background: rgba(255, 255, 255, 0.2);
          padding: 20rpx;
        }
      }
      
      .brand-title {
        font-size: 48rpx;
        font-weight: bold;
        margin-bottom: 16rpx;
        position: relative;
        z-index: 1;
      }
      
      .brand-subtitle {
        font-size: 28rpx;
        opacity: 0.9;
        position: relative;
        z-index: 1;
        margin-bottom: 40rpx;
      }
    }
    
    .auth-card {
      background-color: #fff;
      border-radius: 20rpx;
      padding: 30rpx 20rpx;
      box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
      opacity: 0;
      transform: translateY(50rpx);
      transition: all 0.8s ease 0.2s;
      
      &.animate-in {
        opacity: 1;
        transform: translateY(0);
      }
      
      .form-section {
        .form-content {
          .input-group {
            margin-bottom: 40rpx;
            
            .field-title {
              font-size: 32rpx;
              font-weight: 600;
              color: #333;
              margin-bottom: 12rpx;
              padding-left: 8rpx;
              
              .optional-text {
                font-size: 24rpx;
                color: #999;
                font-weight: normal;
                margin-left: 8rpx;
              }
            }
            
            .input-wrapper {
              position: relative;
              display: flex;
              align-items: center;
              background: #f8f9fa;
              border-radius: 16rpx;
              padding: 20rpx;
              border: 2rpx solid transparent;
              transition: all 0.3s ease;
              cursor: pointer;
              min-height: 48rpx;
              
              &:active {
                border-color: #ff97b6;
                background: #fff;
                box-shadow: 0 0 0 6rpx rgba(255, 149, 182, 0.1);
              }
              
              .input-icon {
                font-size: 32rpx;
                margin-right: 20rpx;
                color: #666;
              }
              
              .picker-content {
                flex: 1;
                display: flex;
                align-items: center;
                
                .picker-text {
                  font-size: 30rpx;
                  color: #333;
                  flex: 1;
                  
                  &.placeholder {
                    color: #999;
                  }
                }
              }
              
              .uni-input {
                flex: 1;
                font-size: 30rpx;
                color: #333;
                border: none;
                outline: none;
                background: transparent;
              }
              
              .radio-label {
                display: flex;
                align-items: center;
                margin-right: 40rpx;
                font-size: 28rpx;
                color: #333;
                
                radio {
                  margin-right: 12rpx;
                }
              }
            }
          }
          
          .primary-btn {
            width: 100%;
            height: 100rpx;
            background: linear-gradient(135deg, #ff97b6 0%, #ff79a5 100%);
            color: #fff;
            font-size: 32rpx;
            border-radius: 20rpx;
            margin-top: 20rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            box-shadow: 0 8rpx 24rpx rgba(255, 149, 182, 0.5);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            
            .btn-text {
              font-weight: 600;
              margin-right: 12rpx;
            }
            
            .btn-icon {
              font-size: 28rpx;
            }
            
            &:active {
              transform: translateY(2rpx);
              box-shadow: 0 4rpx 16rpx rgba(255, 149, 182, 0.5);
            }
            
            &::after {
              border: none;
            }
            
            &[disabled] {
              background: #ddd;
              color: #999;
              box-shadow: none;
              transform: none;
            }
          }
          
          .skip-btn {
            background: #fff;
            color: #ff79a5;
            border: 2rpx solid #ff79a5;
            box-shadow: 0 4rpx 16rpx rgba(255, 149, 182, 0.2);
            
            &:active {
              background: rgba(255, 149, 182, 0.1);
            }
          }
        }
      }
    }
    
    .footer-links {
      text-align: center;
      padding: 24rpx 30rpx calc(24rpx + env(safe-area-inset-bottom));
      background-color: rgba(250, 245, 245, 0.98);
      backdrop-filter: blur(10px);
      z-index: 99;
      
      .link-text {
        font-size: 28rpx;
        color: #999;
      }
      
      .link-action {
        font-size: 28rpx;
        color: #ff79a5;
        font-weight: 500;
      }
    }
  }
}

// 右上角跳过按钮样式
.skip-header {
  position: absolute;
  top: 40rpx;
  right: 30rpx;
  z-index: 10;

  .skip-btn-header {
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
    font-size: 28rpx;
    padding: 12rpx 24rpx;
    border-radius: 40rpx;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    
    &:active {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

// 兴趣爱好弹窗内容样式
.hobby-picker-content {
  padding: 20rpx;
  max-height: 400rpx;
  overflow-y: auto;
  
  .hobby-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
    
    .hobby-tag {
      background: #f8f9fa;
      color: #ff79a5;
      border-radius: 40rpx;
      padding: 12rpx 28rpx;
      font-size: 28rpx;
      border: 1px solid #ff79a5;
      margin-bottom: 8rpx;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &.selected {
        background: linear-gradient(135deg, #ff97b6 0%, #ff79a5 100%);
        color: #fff;
        border: none;
        transform: scale(1.05);
      }
      
      &:active {
        transform: scale(0.95);
      }
    }
  }
}

/* 动画效果 */
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 弹窗样式 */
.popup-content {
  background-color: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  overflow: hidden;
  
  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 90rpx;
    padding: 0 30rpx;
    border-bottom: 1px solid #eee;
    
    .popup-title {
      font-size: 32rpx;
      color: #333;
      font-weight: 500;
    }
    
    .popup-cancel {
      font-size: 30rpx;
      color: #999;
      padding: 20rpx 10rpx;
    }
    
    .popup-confirm {
      font-size: 30rpx;
      color: #ff79a5;
      font-weight: 500;
      padding: 20rpx 10rpx;
    }
  }
  
  .picker-view {
    width: 100%;
    height: 360rpx;
    
    .picker-item {
      line-height: 50px;
      text-align: center;
      font-size: 28rpx;
    }
  }
}
</style>