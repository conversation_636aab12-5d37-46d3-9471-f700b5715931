<template>
  <view class="auth-container">
    <!-- 背景装饰 - 与setupProfile保持一致 -->
    <view class="bg-decoration">
      <view class="bg-circle bg-circle-1"></view>
      <view class="bg-circle bg-circle-2"></view>
      <view class="bg-circle bg-circle-3"></view>
      <!-- 浪漫装饰元素 -->
      <view class="love-decoration love-1">💕</view>
      <view class="love-decoration love-2">💖</view>
      <view class="love-decoration love-3">💝</view>
    </view>
    
    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- Logo区域 -->
      <view class="logo-section" :class="{ 'animate-in': isAnimated }">
        <view class="logo-wrapper">
          <image class="logo" src="@/assets/images/logo.png" mode="aspectFit"></image>
        </view>
        <text class="brand-title">基础资料</text>
        <text class="brand-subtitle">让更多人了解真实的你</text>
        <!-- 进度指示器 -->
        <view class="progress-wrapper">
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: progressPercentage + '%' }"></view>
          </view>
          <text class="progress-text">{{ filledFieldsCount }}/{{ totalFieldsCount }} 已完成</text>
        </view>
      </view>

      <!-- 资料填写卡片 -->
      <view class="auth-card" :class="{ 'animate-in': isAnimated }">
        <view class="form-section">
          <view class="form-content">
            
            <!-- 出生年月 -->
            <view class="input-group">
              <view class="field-title">
                <text class="required-mark">*</text>生日
              </view>
              <view class="input-wrapper" @click="showBirthdayPicker = true">
                <view class="input-icon">🎂</view>
                <view class="picker-content">
                  <text class="picker-text" :class="{ 'placeholder': !form.birthDay }">
                    {{ form.birthDay || '请选择出生年月' }}
                  </text>
                </view>
              </view>
            </view>
            
            <!-- 婚姻状况 -->
            <view class="input-group">
              <view class="field-title">
                <text class="required-mark">*</text>婚姻状况
              </view>
              <view class="input-wrapper" @click="showMarriagePicker = true">
                <view class="input-icon">💑</view>
                <view class="picker-content">
                  <text class="picker-text" :class="{ 'placeholder': !marriageText }">
                    {{ marriageText || '请选择婚姻状况' }}
                  </text>
                </view>
              </view>
            </view>
            
            <!-- 身高 -->
            <view class="input-group">
              <view class="field-title">
                <text class="required-mark">*</text>净身高
              </view>
              <view class="input-wrapper" @click="showHeightPicker = true">
                <view class="input-icon">📏</view>
                <view class="picker-content">
                  <text class="picker-text" :class="{ 'placeholder': !form.height }">
                    {{ form.height ? form.height + ' cm' : '请选择净身高' }}
                  </text>
                </view>
              </view>
            </view>
            
            <!-- 体重 -->
            <view class="input-group">
              <view class="field-title">
                <text class="required-mark">*</text>体重
              </view>
              <view class="input-wrapper" @click="showWeightPicker = true">
                <view class="input-icon">⚖️</view>
                <view class="picker-content">
                  <text class="picker-text" :class="{ 'placeholder': !form.weight }">
                    {{ form.weight ? form.weight + ' kg' : '请选择体重' }}
                  </text>
                </view>
              </view>
            </view>
            
            <!-- 学历 -->
            <view class="input-group">
              <view class="field-title">
                <text class="required-mark">*</text>学历
              </view>
              <view class="input-wrapper" @click="showEducationPicker = true">
                <view class="input-icon">🎓</view>
                <view class="picker-content">
                  <text class="picker-text" :class="{ 'placeholder': !form.education }">
                    {{ form.education || '请选择学历' }}
                  </text>
                </view>
              </view>
            </view>

            <!-- 职业行业 -->
            <view class="input-group">
              <view class="field-title">
                <text class="required-mark">*</text>职业行业
              </view>
              <view class="input-wrapper" @click="showCareerPicker = true">
                <view class="input-icon">💼</view>
                <view class="picker-content">
                  <text class="picker-text" :class="{ 'placeholder': !form.career }">
                    {{ form.career || '请选择职业行业' }}
                  </text>
                </view>
              </view>
            </view>

            <!-- 籍贯 -->
            <view class="input-group">
              <view class="field-title">
                <text class="required-mark">*</text>籍贯
              </view>
              <view class="input-wrapper" @click="handleNativePlacePickerOpen">
                <view class="input-icon">🏠</view>
                <view class="picker-content">
                  <text class="picker-text" :class="{ 'placeholder': !form.nativePlace }">
                    {{ form.nativePlace || '请选择籍贯' }}
                  </text>
                </view>
              </view>
            </view>

            <!-- 现居住地 -->
            <view class="input-group">
              <view class="field-title">
                <text class="required-mark">*</text>现居住地
              </view>
              <view class="input-wrapper residence-wrapper" @click="handleCurrentResidencePickerOpen">
                <view class="input-icon">📍</view>
                <view class="picker-content">
                  <text class="picker-text" :class="{ 'placeholder': !form.nowPlace }">
                    {{ form.nowPlace || '请选择现居住地' }}
                  </text>
                </view>
                <view 
                  class="same-as-native-btn" 
                  @click.stop="fillCurrentResidenceFromNativePlace"
                  v-if="!form.nowPlace && form.nativePlace"
                >
                  同籍贯
                </view>
              </view>
            </view>

            <!-- 提交按钮 -->
            <button 
              class="primary-btn submit-btn" 
              :disabled="!isFormValid || loading" 
              @click="handleSubmit"
            >
              <text class="btn-text">{{ loading ? '保存中...' : '保存并继续' }}</text>
              <view v-if="!loading" class="btn-icon">🚀</view>
            </button>
            
          </view>
        </view>
      </view>

      <!-- 底部链接 -->
      <view class="footer-links">
        <text class="link-text">遇到问题？</text>
        <text class="link-action">联系客服</text>
      </view>
    </view>

    <!-- 选择器弹窗保持原有的实现 -->
    <uni-popup ref="birthdayPopup" type="bottom" @change="handlePopupChange">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-cancel" @click="cancelBirthday">取消</text>
          <view class="popup-title">选择出生日期</view>
          <text class="popup-confirm" @click="confirmBirthday">确定</text>
        </view>
        <picker-view 
          class="picker-view" 
          :value="birthdayIndex" 
          @change="onBirthdayChange"
          indicator-style="height: 50px;"
        >
          <!-- 年份选择 -->
          <picker-view-column>
            <view class="picker-item" v-for="(year, index) in yearArray" :key="'year-'+index">
              {{ year }}
            </view>
          </picker-view-column>
          <!-- 月份选择 -->
          <picker-view-column>
            <view class="picker-item" v-for="(month, index) in monthArray" :key="'month-'+index">
              {{ month }}
            </view>
          </picker-view-column>
          <!-- 日期选择 -->
          <picker-view-column>
            <view class="picker-item" v-for="(day, index) in dayArray" :key="'day-'+index">
              {{ day }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </uni-popup>
    
    <!-- 身高选择器弹窗 -->
    <uni-popup ref="heightPopup" type="bottom" @change="handlePopupChange">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-cancel" @click="cancelHeight">取消</text>
          <view class="popup-title">选择身高</view>
          <text class="popup-confirm" @click="confirmHeight">确定</text>
        </view>
        <picker-view 
          class="picker-view" 
          :value="[heightIndex]" 
          @change="onHeightChange"
          indicator-style="height: 50px;"
        >
          <picker-view-column>
            <view class="picker-item" v-for="(height, index) in heightArray" :key="'height-'+index">
              {{ height }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </uni-popup>

    <!-- 体重选择器弹窗 -->
    <uni-popup ref="weightPopup" type="bottom" @change="handlePopupChange">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-cancel" @click="cancelWeight">取消</text>
          <view class="popup-title">选择体重</view>
          <text class="popup-confirm" @click="confirmWeight">确定</text>
        </view>
        <picker-view 
          class="picker-view" 
          :value="[weightIndex]" 
          @change="onWeightChange"
          indicator-style="height: 50px;"
        >
          <picker-view-column>
            <view class="picker-item" v-for="(weight, index) in weightArray" :key="'weight-'+index">
              {{ weight }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </uni-popup>
    
    <!-- 学历选择器弹窗 -->
    <uni-popup ref="educationPopup" type="bottom" @change="handlePopupChange">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-cancel" @click="cancelEducation">取消</text>
          <view class="popup-title">选择学历</view>
          <text class="popup-confirm" @click="confirmEducation">确定</text>
        </view>
        <picker-view 
          class="picker-view" 
          :value="[educationIndex]" 
          @change="onEducationChange"
          indicator-style="height: 50px;"
        >
          <picker-view-column>
            <view class="picker-item" v-for="(edu, index) in educationArray" :key="'edu-'+index">
              {{ edu }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </uni-popup>

    <!-- 婚姻状况选择器弹窗 -->
    <uni-popup ref="marriagePopup" type="bottom" @change="handlePopupChange">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-cancel" @click="cancelMarriage">取消</text>
          <view class="popup-title">选择婚姻状况</view>
          <text class="popup-confirm" @click="confirmMarriage">确定</text>
        </view>
        <picker-view 
          class="picker-view" 
          :value="[marriageIndex]" 
          @change="onMarriageChange"
          indicator-style="height: 50px;"
        >
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in marriageArray" :key="'marriage-'+index">
              {{ item }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </uni-popup>

    <!-- 职业行业选择器弹窗 -->
    <uni-popup ref="careerPopup" type="bottom" @change="handlePopupChange">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-cancel" @click="cancelCareer">取消</text>
          <view class="popup-title">选择职业行业</view>
          <text class="popup-confirm" @click="confirmCareer">确定</text>
        </view>
        <picker-view 
          class="picker-view" 
          :value="[careerIndex]" 
          @change="onCareerChange"
          indicator-style="height: 50px;"
        >
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in careerArray" :key="'career-'+index">
              {{ item }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </uni-popup>

    <!-- 籍贯选择器弹窗 -->
    <uni-popup ref="nativePlacePopup" type="bottom" @change="handlePopupChange">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-cancel" @click="cancelNativePlace">取消</text>
          <view class="popup-title">选择籍贯</view>
          <text class="popup-confirm" @click="confirmNativePlace">确定</text>
        </view>
        <picker-view 
          class="picker-view" 
          :value="nativePlaceIndex" 
          @change="onNativePlaceChange"
          indicator-style="height: 50px;"
        >
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in nativePlaceArray" :key="'province-'+index">
              {{ item }}
            </view>
          </picker-view-column>
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in nativePlaceCityArray" :key="'city-'+index">
              {{ item }}
            </view>
          </picker-view-column>
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in nativePlaceDistrictArray" :key="'district-'+index">
              {{ item }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </uni-popup>

    <!-- 现居住地选择器弹窗 -->
    <uni-popup ref="currentResidencePopup" type="bottom" @change="handlePopupChange">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-cancel" @click="cancelCurrentResidence">取消</text>
          <view class="popup-title">选择现居住地</view>
          <text class="popup-confirm" @click="confirmCurrentResidence">确定</text>
        </view>
        <picker-view 
          class="picker-view" 
          :value="currentResidenceIndex" 
          @change="onCurrentResidenceChange"
          indicator-style="height: 50px;"
        >
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in currentResidenceArray" :key="'province-'+index">
              {{ item }}
            </view>
          </picker-view-column>
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in currentResidenceCityArray" :key="'city-'+index">
              {{ item }}
            </view>
          </picker-view-column>
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in currentResidenceDistrictArray" :key="'district-'+index">
              {{ item }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </uni-popup>
    
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive, watch, nextTick } from 'vue';
import { tokenUtil } from '@/utils/token/TokenUtil';
import { getUserName } from '@/utils/token/tokenService';
import { editBaseInfo, getCareerList, getAddressList } from '@/api/modules/member';
import type { AddressListParams } from '@/api/modules/member';
import { getProvinces, getCities, getDistricts } from '@/utils/common/addressUtils';
import { useFormValidation, COMMON_RULES } from '@/hooks/useFormValidation';
import { useLoading } from '@/hooks/useLoading';
import { useErrorHandler } from '@/hooks/useErrorHandler';
import { ErrorHandler } from '../../../utils/errorHandler';
import { createContextLogger } from '@/utils/common/logger';

// 创建日志服务实例
const logger = createContextLogger('InformationPage');

// 基础资料参数类型，去除用户名和性别
interface InformationParams {
  birthDay: string;
  height: number;
  weight: number;
  education: string;
  marriage: number;
  career: string;
  nativePlace: string;
  nowPlace: string;
}

// ===== 表单数据 =====
const form = reactive<InformationParams>({
  birthDay: '',
  height: null as unknown as number,
  weight: null as unknown as number,
  education: '',
  marriage: null as unknown as number, // 婚姻状况
  career: '', // 职业行业
  nativePlace: '', // 籍贯
  nowPlace: '' // 现居住地
});



// 婚姻状况相关
const marriageArray = ['未婚', '离异', '丧偶'];
const marriageIndex = ref(0);
const tempMarriageIndex = ref(0);
const showMarriagePicker = ref(false);
const marriagePopup = ref<any>(null);
const marriageText = computed(() => {
  if (form.marriage === null) return '';
  return marriageArray[form.marriage - 1];
});

// 职业行业相关
const careerArray = ref<string[]>([]);
const careerIndex = ref(0);
const tempCareerIndex = ref(0);
const showCareerPicker = ref(false);
const careerPopup = ref<any>(null);

// 籍贯相关
const nativePlace = ref({
  province: '',
  city: '',
  district: ''
});
const nativePlaceArray = ref<string[]>([]);
const nativePlaceCityArray = ref<string[]>([]);
const nativePlaceDistrictArray = ref<string[]>([]);
const nativePlaceIndex = ref([0, 0, 0]);
const tempNativePlaceIndex = ref([0, 0, 0]);
const showNativePlacePicker = ref(false);
const nativePlacePopup = ref<any>(null);

// 现居住地相关
const currentResidence = ref({
  province: '',
  city: '',
  district: ''
});
const currentResidenceArray = ref<string[]>([]);
const currentResidenceCityArray = ref<string[]>([]);
const currentResidenceDistrictArray = ref<string[]>([]);
const currentResidenceIndex = ref([0, 0, 0]);
const tempCurrentResidenceIndex = ref([0, 0, 0]);
const showCurrentResidencePicker = ref(false);
const currentResidencePopup = ref<any>(null);

// 使用统一验证框架
const {
  addField,
  validateField,
  validateAndShowError,
  validationState,
  getFieldError
} = useFormValidation();

// 初始化表单验证
const initFormValidation = () => {
  // 添加字段验证规则
  addField('birthDay', { label: '生日', rules: [COMMON_RULES.required('请选择生日')] });
  addField('marriage', { label: '婚姻状况', rules: [COMMON_RULES.required('请选择婚姻状况')] });
  addField('height', { label: '身高', rules: [COMMON_RULES.required('请选择身高')] });
  addField('weight', { label: '体重', rules: [COMMON_RULES.required('请选择体重')] });
  addField('education', { label: '学历', rules: [COMMON_RULES.required('请选择学历')] });
  addField('career', { label: '职业行业', rules: [COMMON_RULES.required('请选择职业行业')] });
  addField('nativePlace', { label: '籍贯', rules: [COMMON_RULES.required('请选择籍贯')] });
  addField('nowPlace', { label: '现居住地', rules: [COMMON_RULES.required('请选择现居住地')] });
};

// 表单验证和进度计算
const totalFieldsCount = 8; // 总字段数量
const filledFieldsCount = computed(() => {
  return Object.keys(validationState.touched).length - Object.keys(validationState.errors).length;
});

const progressPercentage = computed(() => {
  return Math.round((filledFieldsCount.value / totalFieldsCount) * 100);
});

const isFormValid = computed(() => {
  return Object.keys(validationState.errors).length === 0 && Object.keys(validationState.touched).length === totalFieldsCount;
});

// ===== 页面状态 =====
const isAnimated = ref(false);
const { withLoading, loading } = useLoading();
const { handleError, handleApiError } = useErrorHandler();

// ===== 选择器状态 =====
// 显示状态
const showBirthdayPicker = ref(false);
const showHeightPicker = ref(false);
const showWeightPicker = ref(false);
const showEducationPicker = ref(false);

// 选择器弹窗引用
const birthdayPopup = ref<any>(null);
const heightPopup = ref<any>(null);
const weightPopup = ref<any>(null);
const educationPopup = ref<any>(null);

// ===== 选择器数据 =====

// 出生日期选择器
const birthdayIndex = ref([0, 0, 0]);
const tempBirthdayIndex = ref([0, 0, 0]);
const yearArray = ref<number[]>([]);
const monthArray = ref<number[]>([]);
const dayArray = ref<number[]>([]);

// 身高选择器
const heightIndex = ref(30); // 默认选择170cm (140 + 30 = 170)
const heightArray = ref<number[]>([]);
const tempHeightIndex = ref(heightIndex.value);

// 体重选择器
const weightIndex = ref(20); // 默认选择60kg
const weightArray = ref<number[]>([]);
const tempWeightIndex = ref(weightIndex.value);

// 学历选择器
const educationIndex = ref(2); // 默认选择本科
const educationArray = ref([
  '高中及以下', 
  '大专', 
  '本科', 
  '硕士', 
  '博士及以上'
]);
const tempEducationIndex = ref(educationIndex.value);

// ===== 选择器初始化 =====

// 初始化日期数组
const initDateArrays = () => {
  const currentYear = new Date().getFullYear();
  
  // 年份范围：当前年份 - 60 岁 到 当前年份 - 18 岁
  yearArray.value = [];
  for (let i = currentYear - 18; i >= currentYear - 60; i--) {
    yearArray.value.push(i);
  }
  
  // 月份：1-12
  monthArray.value = [];
  for (let i = 1; i <= 12; i++) {
    monthArray.value.push(i);
  }
  
  // 初始化日期
  updateDayArray();
};

// 根据年月更新日期数组
const updateDayArray = () => {
  const year = yearArray.value[birthdayIndex.value[0]];
  const month = monthArray.value[birthdayIndex.value[1]];
  
  // 获取当月天数
  let daysInMonth;
  if (month === 2) {
    // 二月判断闰年
    daysInMonth = (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0 ? 29 : 28;
  } else if ([4, 6, 9, 11].includes(month)) {
    daysInMonth = 30;
  } else {
    daysInMonth = 31;
  }
  
  dayArray.value = [];
  for (let i = 1; i <= daysInMonth; i++) {
    dayArray.value.push(i);
  }
  
  // 调整日期选择，避免超出范围
  if (birthdayIndex.value[2] >= dayArray.value.length) {
    birthdayIndex.value[2] = dayArray.value.length - 1;
  }
};

// 初始化身高数组 (140cm-220cm)
const initHeightArray = () => {
  heightArray.value = [];
  for (let i = 140; i <= 220; i++) {
    heightArray.value.push(i);
  }
};

// 初始化体重数组 (40kg-120kg)
const initWeightArray = () => {
  weightArray.value = [];
  for (let i = 40; i <= 120; i++) {
    weightArray.value.push(i);
  }
};

// ===== 事件处理函数 =====

// ===== 选择器事件处理 =====

// 出生日期选择器
const onBirthdayChange = (e: any) => {
  tempBirthdayIndex.value = e.detail.value;
  
  // 如果年或月变了，需要更新日期数组
  if (tempBirthdayIndex.value[0] !== birthdayIndex.value[0] || 
      tempBirthdayIndex.value[1] !== birthdayIndex.value[1]) {
    birthdayIndex.value = [...tempBirthdayIndex.value];
    updateDayArray();
  } else {
    birthdayIndex.value = [...tempBirthdayIndex.value];
  }
};

const confirmBirthday = () => {
  const year = yearArray.value[birthdayIndex.value[0]];
  const month = monthArray.value[birthdayIndex.value[1]];
  const day = dayArray.value[birthdayIndex.value[2]];
  
  // 格式化为YYYY-MM-DD
  form.birthDay = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
  
  // 更新验证字段
  validateField('birthDay', form.birthDay);
  
  showBirthdayPicker.value = false;
  // 手动关闭弹窗
  if (birthdayPopup.value) {
    birthdayPopup.value.close();
  }
};

const cancelBirthday = () => {
  showBirthdayPicker.value = false;
  if (birthdayPopup.value) {
    birthdayPopup.value.close();
  }
};

// 身高选择器
const onHeightChange = (e: any) => {
  tempHeightIndex.value = e.detail.value[0];
};

const confirmHeight = () => {
  heightIndex.value = tempHeightIndex.value;
  form.height = heightArray.value[heightIndex.value];
  
  // 更新验证字段
  validateField('height', form.height);
  
  showHeightPicker.value = false;
  // 手动关闭弹窗
  if (heightPopup.value) {
    heightPopup.value.close();
  }
};

const cancelHeight = () => {
  showHeightPicker.value = false;
  if (heightPopup.value) {
    heightPopup.value.close();
  }
};

// 体重选择器
const onWeightChange = (e: any) => {
  tempWeightIndex.value = e.detail.value[0];
};

const confirmWeight = () => {
  weightIndex.value = tempWeightIndex.value;
  form.weight = weightArray.value[weightIndex.value];
  
  // 更新验证字段
  validateField('weight', form.weight);
  
  showWeightPicker.value = false;
  // 手动关闭弹窗
  if (weightPopup.value) {
    weightPopup.value.close();
  }
};

const cancelWeight = () => {
  showWeightPicker.value = false;
  if (weightPopup.value) {
    weightPopup.value.close();
  }
};

// 学历选择器
const onEducationChange = (e: any) => {
  tempEducationIndex.value = e.detail.value[0];
};

const confirmEducation = () => {
  educationIndex.value = tempEducationIndex.value;
  form.education = educationArray.value[educationIndex.value];
  
  // 更新验证字段
  validateField('education', form.education);
  
  showEducationPicker.value = false;
  // 手动关闭弹窗
  if (educationPopup.value) {
    educationPopup.value.close();
  }
};

const cancelEducation = () => {
  showEducationPicker.value = false;
  if (educationPopup.value) {
    educationPopup.value.close();
  }
};

// 婚姻状况选择器事件处理
const onMarriageChange = (e: any) => {
  tempMarriageIndex.value = e.detail.value[0];
};

const confirmMarriage = () => {
  marriageIndex.value = tempMarriageIndex.value;
  form.marriage = marriageIndex.value + 1; // 转换为后端对应的值
  
  // 更新验证字段
  validateField('marriage', form.marriage);
  
  showMarriagePicker.value = false;
  if (marriagePopup.value) {
    marriagePopup.value.close();
  }
};

const cancelMarriage = () => {
  showMarriagePicker.value = false;
  if (marriagePopup.value) {
    marriagePopup.value.close();
  }
};

// 职业行业选择器事件处理
const onCareerChange = (e: any) => {
  tempCareerIndex.value = e.detail.value[0];
};

const confirmCareer = () => {
  careerIndex.value = tempCareerIndex.value;
  form.career = careerArray.value[careerIndex.value];
  
  // 更新验证字段
  validateField('career', form.career);
  
  showCareerPicker.value = false;
  if (careerPopup.value) {
    careerPopup.value.close();
  }
};

const cancelCareer = () => {
  showCareerPicker.value = false;
  if (careerPopup.value) {
    careerPopup.value.close();
  }
};

// 处理弹窗显示状态变化
const handlePopupChange = (e: any) => {
  // 当弹窗关闭时，手动更新状态变量
  if (!e.show) {
    showBirthdayPicker.value = false;
    showHeightPicker.value = false;
    showWeightPicker.value = false;
    showEducationPicker.value = false;
    showMarriagePicker.value = false;
    showCareerPicker.value = false;
  }
};

// 监听弹窗显示状态
watch([showBirthdayPicker, showHeightPicker, showWeightPicker, showEducationPicker, showMarriagePicker, showCareerPicker, showNativePlacePicker, showCurrentResidencePicker], (newVal, oldVal) => {
  const [birthday, height, weight, education, marriage, career, nativePlace, currentResidence] = newVal;
  
  nextTick(() => {
    if (birthday && birthdayPopup.value) {
      birthdayPopup.value.open('bottom');
    }
    if (height && heightPopup.value) {
      heightPopup.value.open('bottom');
    }
    if (weight && weightPopup.value) {
      weightPopup.value.open('bottom');
    }
    if (education && educationPopup.value) {
      educationPopup.value.open('bottom');
    }
    if (marriage && marriagePopup.value) {
      marriagePopup.value.open('bottom');
    }
    if (career && careerPopup.value) {
      careerPopup.value.open('bottom');
    }
    if (nativePlace && nativePlacePopup.value) {
      nativePlacePopup.value.open('bottom');
    }
    if (currentResidence && currentResidencePopup.value) {
      currentResidencePopup.value.open('bottom');
    }
  });
});



// 获取职业列表
const fetchCareerList = async () => {
  try {
    const res = await getCareerList();
    
    if (res && res.code === 200 && res.data && Array.isArray(res.data.carrers)) {
      careerArray.value = res.data.carrers;
    } else {
      handleApiError(res, '获取职业列表失败');
    }
  } catch (error: any) {
    handleError(error, '获取职业列表失败');
  }
};

// 获取错误字段信息
const getErrorFieldsInfo = () => {
  const fieldNames: Record<string, string> = {
    birthDay: '生日',
    marriage: '婚姻状况', 
    height: '身高',
    weight: '体重',
    education: '学历',
    career: '职业行业',
    nativePlace: '籍贯',
    nowPlace: '现居住地'
  };
  
  return Object.keys(validationState.errors).map((field: string) => fieldNames[field] || field);
};

// 提交表单
const handleSubmit = async () => {
  // 验证所有字段
  const isValid = validateAndShowError(form);
  if (!isValid) {
    const errorFields = getErrorFieldsInfo();
    ErrorHandler.showError(`请填写${errorFields.join('、')}`);
    return;
  }
  
  await withLoading(async () => {
    // 构造提交参数，替换空格为横杠
    const submitData = {
      birthDay: form.birthDay,
      height: form.height,
      weight: form.weight,
      education: form.education,
      marriage: form.marriage,
      career: form.career,
      nativePlace: form.nativePlace.replace(/\s+/g, '-'),
      nowPlace: form.nowPlace.replace(/\s+/g, '-')
    };
    
    // 调用编辑基础资料API
    const res = await editBaseInfo(submitData);
    
    if (res.code === 200) {
      ErrorHandler.showSuccess('保存成功');
      
      // 保存成功后跳转到扩展信息页面
      setTimeout(() => {
        uni.navigateTo({
          url: '/pages/user/extendInfo/ExtendInfo'
        });
      }, 1500);
    } else {
      handleApiError(res, '保存失败');
    }
  }, '保存中...');
};

// 获取地址列表
const fetchAddressList = async (queryLevel: number, province?: string, city?: string) => {
  try {
    switch (queryLevel) {
      case 1:
        return getProvinces();
      case 2:
        return province ? getCities(province) : [];
      case 3:
        return province && city ? getDistricts(province, city) : [];
      default:
        return [];
    }
  } catch (error) {
    logger.error('获取地址列表失败:', error);
    return [];
  }
};

// 初始化地址数据
const initAddressData = async () => {
  try {
    // 获取省份列表
    const provinces = await fetchAddressList(1);
    nativePlaceArray.value = provinces;
    currentResidenceArray.value = provinces;
    
    // 如果有省份数据，默认选择第一个省份并加载其城市列表
    if (provinces.length > 0) {
      const firstProvince = provinces[0];
      const cities = await fetchAddressList(2, firstProvince);
      nativePlaceCityArray.value = cities;
      currentResidenceCityArray.value = cities;
      
      // 如果有城市数据，加载第一个城市的区县列表
      if (cities.length > 0) {
        const firstCity = cities[0];
        const districts = await fetchAddressList(3, firstProvince, firstCity);
        nativePlaceDistrictArray.value = districts;
        currentResidenceDistrictArray.value = districts;
      }
    }
  } catch (error) {
    logger.error('初始化地址数据失败:', error);
  }
};

// 加载指定省份的城市和区县数据
const loadCityAndDistrictData = async (province: string, city?: string) => {
  try {
    const cities = await fetchAddressList(2, province);
    if (cities.length > 0) {
      const targetCity = city || cities[0];
      const districts = await fetchAddressList(3, province, targetCity);
      return { cities, districts };
    }
    return { cities, districts: [] };
  } catch (error) {
    logger.error('加载城市和区县数据失败:', error);
    return { cities: [], districts: [] };
  }
};

// 处理籍贯选择框打开
const handleNativePlacePickerOpen = async () => {
  showNativePlacePicker.value = true;
  
  // 如果已经选择了省份，确保加载对应的市区数据
  if (nativePlace.value.province) {
    const { cities, districts } = await loadCityAndDistrictData(
      nativePlace.value.province,
      nativePlace.value.city
    );
    nativePlaceCityArray.value = cities;
    nativePlaceDistrictArray.value = districts;
  }
  // 否则加载第一个省份的数据
  else if (nativePlaceArray.value.length > 0) {
    const firstProvince = nativePlaceArray.value[0];
    const { cities, districts } = await loadCityAndDistrictData(firstProvince);
    nativePlaceCityArray.value = cities;
    nativePlaceDistrictArray.value = districts;
  }
};

// 处理现居住地选择框打开
const handleCurrentResidencePickerOpen = async () => {
  showCurrentResidencePicker.value = true;
  
  // 如果已经选择了省份，确保加载对应的市区数据
  if (currentResidence.value.province) {
    const { cities, districts } = await loadCityAndDistrictData(
      currentResidence.value.province,
      currentResidence.value.city
    );
    currentResidenceCityArray.value = cities;
    currentResidenceDistrictArray.value = districts;
  }
  // 否则加载第一个省份的数据
  else if (currentResidenceArray.value.length > 0) {
    const firstProvince = currentResidenceArray.value[0];
    const { cities, districts } = await loadCityAndDistrictData(firstProvince);
    currentResidenceCityArray.value = cities;
    currentResidenceDistrictArray.value = districts;
  }
};

// 籍贯选择器事件处理
const onNativePlaceChange = async (e: any) => {
  const [provinceIndex, cityIndex, districtIndex] = e.detail.value;
  
  // 如果省份改变
  if (provinceIndex !== nativePlaceIndex.value[0]) {
    // 更新省份索引
    tempNativePlaceIndex.value[0] = provinceIndex;
    
    // 获取选中的省份
    const province = nativePlaceArray.value[provinceIndex];
    
    try {
      // 获取该省份下的城市列表
      const cities = await fetchAddressList(2, province);
      nativePlaceCityArray.value = cities;
      
      // 重置城市和区县索引
      tempNativePlaceIndex.value[1] = 0;
      tempNativePlaceIndex.value[2] = 0;
      
      // 清空区县数组
      nativePlaceDistrictArray.value = [];
      
      // 如果有城市数据，自动获取第一个城市的区县列表
      if (cities.length > 0) {
        const districts = await fetchAddressList(3, province, cities[0]);
        nativePlaceDistrictArray.value = districts;
      }
    } catch (error) {
      logger.error('获取城市列表失败:', error);
    }
  }
  // 如果城市改变
  else if (cityIndex !== nativePlaceIndex.value[1]) {
    // 更新城市索引
    tempNativePlaceIndex.value[1] = cityIndex;
    
    // 获取选中的省份和城市
    const province = nativePlaceArray.value[provinceIndex];
    const city = nativePlaceCityArray.value[cityIndex];
    
    try {
      // 获取该城市下的区县列表
      const districts = await fetchAddressList(3, province, city);
      nativePlaceDistrictArray.value = districts;
      
      // 重置区县索引
      tempNativePlaceIndex.value[2] = 0;
    } catch (error) {
      logger.error('获取区县列表失败:', error);
    }
  }
  // 如果只是区县改变
  else if (districtIndex !== nativePlaceIndex.value[2]) {
    tempNativePlaceIndex.value[2] = districtIndex;
  }
  
  // 更新临时索引
  nativePlaceIndex.value = [...tempNativePlaceIndex.value];
};

const confirmNativePlace = () => {
  nativePlaceIndex.value = [...tempNativePlaceIndex.value];
  const [provinceIndex, cityIndex, districtIndex] = nativePlaceIndex.value;
  
  nativePlace.value = {
    province: nativePlaceArray.value[provinceIndex] || '',
    city: nativePlaceCityArray.value[cityIndex] || '',
    district: nativePlaceDistrictArray.value[districtIndex] || ''
  };
  
  // 更新表单数据
  form.nativePlace = `${nativePlace.value.province} ${nativePlace.value.city} ${nativePlace.value.district}`.trim();
  
  // 更新验证字段
  validateField('nativePlace', form.nativePlace);
  
  showNativePlacePicker.value = false;
  if (nativePlacePopup.value) {
    nativePlacePopup.value.close();
  }
};

const cancelNativePlace = () => {
  showNativePlacePicker.value = false;
  if (nativePlacePopup.value) {
    nativePlacePopup.value.close();
  }
};

// 现居住地选择器事件处理
const onCurrentResidenceChange = async (e: any) => {
  const [provinceIndex, cityIndex, districtIndex] = e.detail.value;
  
  // 如果省份改变
  if (provinceIndex !== currentResidenceIndex.value[0]) {
    // 更新省份索引
    tempCurrentResidenceIndex.value[0] = provinceIndex;
    
    // 获取选中的省份
    const province = currentResidenceArray.value[provinceIndex];
    
    try {
      // 获取该省份下的城市列表
      const cities = await fetchAddressList(2, province);
      currentResidenceCityArray.value = cities;
      
      // 重置城市和区县索引
      tempCurrentResidenceIndex.value[1] = 0;
      tempCurrentResidenceIndex.value[2] = 0;
      
      // 清空区县数组
      currentResidenceDistrictArray.value = [];
      
      // 如果有城市数据，自动获取第一个城市的区县列表
      if (cities.length > 0) {
        const districts = await fetchAddressList(3, province, cities[0]);
        currentResidenceDistrictArray.value = districts;
      }
    } catch (error) {
      logger.error('获取城市列表失败:', error);
    }
  }
  // 如果城市改变
  else if (cityIndex !== currentResidenceIndex.value[1]) {
    // 更新城市索引
    tempCurrentResidenceIndex.value[1] = cityIndex;
    
    // 获取选中的省份和城市
    const province = currentResidenceArray.value[provinceIndex];
    const city = currentResidenceCityArray.value[cityIndex];
    
    try {
      // 获取该城市下的区县列表
      const districts = await fetchAddressList(3, province, city);
      currentResidenceDistrictArray.value = districts;
      
      // 重置区县索引
      tempCurrentResidenceIndex.value[2] = 0;
    } catch (error) {
      logger.error('获取区县列表失败:', error);
    }
  }
  // 如果只是区县改变
  else if (districtIndex !== currentResidenceIndex.value[2]) {
    tempCurrentResidenceIndex.value[2] = districtIndex;
  }
  
  // 更新临时索引
  currentResidenceIndex.value = [...tempCurrentResidenceIndex.value];
};

const confirmCurrentResidence = () => {
  currentResidenceIndex.value = [...tempCurrentResidenceIndex.value];
  const [provinceIndex, cityIndex, districtIndex] = currentResidenceIndex.value;
  
  currentResidence.value = {
    province: currentResidenceArray.value[provinceIndex] || '',
    city: currentResidenceCityArray.value[cityIndex] || '',
    district: currentResidenceDistrictArray.value[districtIndex] || ''
  };
  
  // 更新表单数据
  form.nowPlace = `${currentResidence.value.province} ${currentResidence.value.city} ${currentResidence.value.district}`.trim();
  
  // 更新验证字段
  validateField('nowPlace', form.nowPlace);
  
  showCurrentResidencePicker.value = false;
  if (currentResidencePopup.value) {
    currentResidencePopup.value.close();
  }
};

const cancelCurrentResidence = () => {
  showCurrentResidencePicker.value = false;
  if (currentResidencePopup.value) {
    currentResidencePopup.value.close();
  }
};

// 添加自动填充现居住地的方法
const fillCurrentResidenceFromNativePlace = () => {
  // 检查籍贯是否已选择
  if (!nativePlace.value.province) {
    ErrorHandler.showError('请先选择籍贯');
    return;
  }

  // 复制籍贯数据到现居住地
  currentResidence.value = {
    province: nativePlace.value.province,
    city: nativePlace.value.city,
    district: nativePlace.value.district
  };
  
  // 复制数据数组
  currentResidenceArray.value = [...nativePlaceArray.value];
  currentResidenceCityArray.value = [...nativePlaceCityArray.value];
  currentResidenceDistrictArray.value = [...nativePlaceDistrictArray.value];
  
  // 复制索引
  currentResidenceIndex.value = [...nativePlaceIndex.value];
  tempCurrentResidenceIndex.value = [...nativePlaceIndex.value];
  
  // 更新表单数据
  form.nowPlace = form.nativePlace;
};

// 页面加载初始化
onMounted(() => {
  logger.debug('页面加载，初始化...');
  
  // 开始动画
  setTimeout(() => {
    isAnimated.value = true;
  }, 100);
  
  // 初始化各种选择器数据
  initDateArrays();
  initHeightArray();
  initWeightArray();
  
  // 获取职业列表和地址列表
  fetchCareerList();
  initAddressData();
  
  // 初始化表单验证
  initFormValidation();
  
  // 修复TabBar错误：改用当前页面样式设置，避免使用hideTabBar
  uni.setNavigationBarColor({
    frontColor: '#000000',
    backgroundColor: '#faf5f5',
    animation: {
      duration: 400,
      timingFunc: 'easeIn'
    }
  });
});
</script>

<style lang="scss">
page {
  background-color: #faf5f5;
  min-height: 100%;
}

.auth-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff97b6 0%, #ff79a5 50%, #f8a5c2 100%);
  padding: 0 30rpx;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  
  .bg-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
    
          .bg-circle {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        animation: float 6s ease-in-out infinite;
      
              &.bg-circle-1 {
          width: 300rpx;
          height: 300rpx;
          top: -100rpx;
          right: -50rpx;
          animation: float 6s ease-in-out infinite;
        }
        
        &.bg-circle-2 {
          width: 200rpx;
          height: 200rpx;
          bottom: 100rpx;
          left: -50rpx;
          animation: float 8s ease-in-out infinite reverse;
        }
        
        &.bg-circle-3 {
          width: 150rpx;
          height: 150rpx;
          top: 50%;
          right: 100rpx;
          animation: float 7s ease-in-out infinite;
        }
    }
    
          .love-decoration {
        position: absolute;
        font-size: 40rpx;
        opacity: 0.3;
        animation: float 4s ease-in-out infinite;
        
        &.love-1 {
          top: 20%;
          left: 15%;
          animation-delay: 0s;
        }
        
        &.love-2 {
          top: 60%;
          right: 20%;
          animation-delay: 2s;
        }
        
        &.love-3 {
          bottom: 30%;
          left: 25%;
          animation-delay: 4s;
        }
      }
  }
  
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding-bottom: 100rpx;
    
    .logo-section {
      text-align: center;
      padding: 80rpx 30rpx 60rpx;
      color: #fff;
      position: relative;
      overflow: hidden;
      border-radius: 0 0 30rpx 30rpx;
      opacity: 0;
      transform: translateY(50rpx);
      transition: all 0.8s ease;
      
      &.animate-in {
        opacity: 1;
        transform: translateY(0);
      }
      
      &::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 30%, transparent 70%);
        animation: rotate 20s linear infinite;
      }
      
      .logo-wrapper {
        margin-bottom: 20rpx;
        
        .logo {
          width: 120rpx;
          height: 120rpx;
          border-radius: 24rpx;
          background: rgba(255, 255, 255, 0.2);
          padding: 20rpx;
        }
      }
      
      .brand-title {
        font-size: 48rpx;
        font-weight: bold;
        margin-bottom: 16rpx;
        position: relative;
        z-index: 1;
      }
      
      .brand-subtitle {
        font-size: 28rpx;
        opacity: 0.9;
        position: relative;
        z-index: 1;
        margin-bottom: 40rpx;
      }
      
      .progress-wrapper {
        position: relative;
        z-index: 1;
        margin-top: 30rpx;
        
        .progress-bar {
          width: 80%;
          height: 8rpx;
          background-color: rgba(255, 255, 255, 0.3);
          border-radius: 4rpx;
          margin: 0 auto 16rpx;
          overflow: hidden;
          
          .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #fff, rgba(255, 255, 255, 0.8));
            border-radius: 4rpx;
            transition: width 0.3s ease;
            box-shadow: 0 0 10rpx rgba(255, 255, 255, 0.5);
          }
        }
        
        .progress-text {
          font-size: 24rpx;
          opacity: 0.9;
          font-weight: 500;
        }
      }
    }
    
    .auth-card {
      background-color: #fff;
      border-radius: 20rpx;
      padding: 30rpx 20rpx;
      box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
      opacity: 0;
      transform: translateY(50rpx);
      transition: all 0.8s ease 0.2s;
      
      &.animate-in {
        opacity: 1;
        transform: translateY(0);
      }
      
      .form-section {
        .form-content {
          .input-group {
            margin-bottom: 40rpx;
            
            .field-title {
              font-size: 32rpx;
              font-weight: 600;
              color: #333;
              margin-bottom: 12rpx;
              padding-left: 8rpx;
              
              .required-mark {
                color: #ff4d4f;
                margin-right: 4rpx;
                font-size: 28rpx;
              }
            }
            
            .input-wrapper {
              position: relative;
              display: flex;
              align-items: center;
              background: #f8f9fa;
              border-radius: 16rpx;
              padding: 0 20rpx;
              height: 88rpx;
              border: 2rpx solid transparent;
              transition: all 0.3s ease;
              cursor: pointer;
              
              &:active {
                border-color: #ff97b6;
                background: #fff;
                                  box-shadow: 0 0 0 6rpx rgba(255, 149, 182, 0.1);
              }
              
              .input-icon {
                font-size: 32rpx;
                margin-right: 20rpx;
                color: #666;
              }
              
              .picker-content {
                flex: 1;
                display: flex;
                align-items: center;
                
                .picker-text {
                  font-size: 30rpx;
                  color: #333;
                  flex: 1;
                  
                  &.placeholder {
                    color: #999;
                  }
                }
              }
              
              .same-as-native-btn {
                background: rgba(255, 149, 182, 0.1);
                color: #ff97b6;
                font-size: 24rpx;
                padding: 16rpx 20rpx;
                border-radius: 40rpx;
                margin-left: 16rpx;
                white-space: nowrap;
                border: 1px solid rgba(255, 149, 182, 0.3);
                transition: all 0.3s ease;
                
                &:active {
                  background: rgba(255, 149, 182, 0.2);
                  transform: scale(0.95);
                }
              }
            }
          }
          
          .primary-btn {
            width: 100%;
            height: 100rpx;
            background: linear-gradient(135deg, #ff97b6 0%, #ff79a5 100%);
            color: #fff;
            font-size: 32rpx;
            border-radius: 20rpx;
            margin-top: 20rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            box-shadow: 0 8rpx 24rpx rgba(255, 149, 182, 0.5);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            
            .btn-text {
              font-weight: 600;
              margin-right: 12rpx;
            }
            
            .btn-icon {
              font-size: 28rpx;
            }
            
            &:active {
              transform: translateY(2rpx);
              box-shadow: 0 4rpx 16rpx rgba(255, 149, 182, 0.5);
            }
            
            &::after {
              border: none;
            }
            
            &[disabled] {
              background: #ddd;
              color: #999;
              box-shadow: none;
              transform: none;
            }
          }
        }
      }
    }
    
    .footer-links {
      text-align: center;
      padding: 24rpx 30rpx calc(24rpx + env(safe-area-inset-bottom));
      background-color: rgba(250, 245, 245, 0.98);
      backdrop-filter: blur(10px);
      z-index: 99;
      
      .link-text {
        font-size: 28rpx;
        color: #999;
      }
      
      .link-action {
        font-size: 28rpx;
        color: #ff79a5;
        font-weight: 500;
      }
    }
  }
}

/* 弹窗样式 */
.popup-content {
  background-color: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  overflow: hidden;
  
  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 90rpx;
    padding: 0 30rpx;
    border-bottom: 1px solid #eee;
    
    .popup-title {
      font-size: 32rpx;
      color: #333;
      font-weight: 500;
    }
    
    .popup-cancel {
      font-size: 30rpx;
      color: #999;
      padding: 20rpx 10rpx;
    }
    
    .popup-confirm {
      font-size: 30rpx;
      color: #ff79a5;
      font-weight: 500;
      padding: 20rpx 10rpx;
    }
  }
  
  .picker-view {
    width: 100%;
    height: 360rpx;
    
    .picker-item {
      line-height: 50px;
      text-align: center;
      font-size: 28rpx;
    }
  }
}

/* 专门针对现居住地选择器的样式 */
.residence-picker {
  width: calc(100% - 100rpx) !important; /* 为同籍贯按钮留出空间 */
}

/* 动画效果 */
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}
</style>
