<template>
  <view class="auth-container">
    <!-- 背景装饰 - 与登录页保持一致 -->
    <view class="bg-decoration">
      <view class="bg-circle bg-circle-1"></view>
      <view class="bg-circle bg-circle-2"></view>
      <view class="bg-circle bg-circle-3"></view>
      <!-- 浪漫装饰元素 -->
      <view class="love-decoration love-1">💕</view>
      <view class="love-decoration love-2">💖</view>
      <view class="love-decoration love-3">💝</view>
    </view>
    
    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- Logo区域 -->
      <view class="logo-section" :class="{ 'animate-in': isAnimated }">
        <view class="logo-wrapper">
          <image class="logo" src="@/assets/images/logo.png" mode="aspectFit"></image>
        </view>
        <text class="brand-title">完善个人资料</text>
        <text class="brand-subtitle">设置用户名与性别，开启你的缘分之旅</text>
      </view>

      <!-- 设置卡片 -->
      <view class="auth-card" :class="{ 'animate-in': isAnimated }">
        <view class="form-section">
          <view class="form-content">
            <!-- 用户名设置 -->
            <view class="input-group">
              <view class="input-wrapper">
                <view class="input-icon">👤</view>
                <input 
                  class="modern-input" 
                  v-model="form.userName" 
                  placeholder="输入你的个性昵称"
                  maxlength="16"
                  :focus="shouldFocusInput"
                  @focus="handleInputFocus"
                  @blur="handleInputBlur"
                />
              </view>
              <view class="input-hint">
                {{ userFromStorage ? '已从账户信息获取，可暂不更改，也可自由更改' : '系统生成的默认名称，可暂不更改，也可自由更改' }}
              </view>
            </view>
            
            <!-- 性别选择 -->
            <view class="gender-section">
              <view class="section-title">选择性别</view>
              <view class="gender-options">
                <view 
                  class="gender-card"
                  :class="{ active: form.sex === 'M' }"
                  @click="selectGender('M')"
                >
                  <view class="gender-content">
                    <view class="gender-icon male">♂</view>
                    <view class="gender-label">男生</view>
                  </view>
                  <view v-if="form.sex === 'M'" class="selected-indicator">✓</view>
                </view>
                <view 
                  class="gender-card"
                  :class="{ active: form.sex === 'U' }"
                  @click="selectGender('U')"
                >
                  <view class="gender-content">
                    <view class="gender-icon female">♀</view>
                    <view class="gender-label">女生</view>
                  </view>
                  <view v-if="form.sex === 'U'" class="selected-indicator">✓</view>
                </view>
              </view>
            </view>
            
            <!-- 温馨提示 -->
            <view class="notice-wrapper">
              <view class="notice-icon">🔒</view>
              <text class="notice-text">请谨慎选择性别，此设置将永久锁定且不可更改</text>
            </view>

            <!-- 提交按钮 -->
            <button 
              class="primary-btn setup-btn" 
              :disabled="!isFormValid || loading" 
              @click="handleSubmit"
            >
              <text class="btn-text">{{ loading ? '保存中...' : '下一步' }}</text>
              <view v-if="!loading" class="btn-icon">🚀</view>
            </button>
          </view>
        </view>
      </view>

      <!-- 底部链接 -->
      <view class="footer-links">
        <text class="link-text">遇到问题？</text>
        <text class="link-action">联系客服</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
/**
 * 用户名和性别设置页面
 * @description 用户注册后的第一个设置页面，用于设置用户名和性别
 * <AUTHOR>
 * @since 2024-12-30
 */

// ==================== 导入 ====================
import { ref, computed, onMounted, reactive, nextTick } from 'vue'
import { getUserName } from '@/utils/token/tokenService'
import { StoreUtil } from '@/utils/storage/StoreUtil'
import { editSexAndUserName } from '@/api/modules/member'
import type { SetupProfileParams } from '@/api/modules/member'
import { useFormValidation, COMMON_RULES } from '@/hooks/useFormValidation'
import { useLoading } from '@/hooks/useLoading'
import { useErrorHandler } from '@/hooks/useErrorHandler'
import { ErrorHandler } from '../../../utils/errorHandler'
import { createContextLogger } from '@/utils/logger/loggerService'

// ==================== 接口类型定义 ====================
interface FormData {
  /** 性别: M-男, U-女 */
  sex: 'M' | 'U' | ''
  /** 用户名 */
  userName: string
}

interface UserInfo {
  userName?: string
  phone?: string
  [key: string]: any
}

// ==================== 响应式状态 ====================
const form = reactive<FormData>({
  sex: '',
  userName: ''
})

const { loading, withLoading } = useLoading()
const { handleError, handleApiError, showErrorToast } = useErrorHandler()
const userFromStorage = ref<UserInfo | null>(null)
const isAnimated = ref(false)
const shouldFocusInput = ref(false)

// 创建日志服务实例
const logger = createContextLogger('SetupProfilePage')

// ==================== 使用统一验证框架 ====================
const {
  addField,
  validateField,
  validateAndShowError,
  validationState,
  getFieldError
} = useFormValidation();

// 初始化表单验证
const initFormValidation = () => {
  // 添加字段验证规则
  addField('sex', {
    label: '性别',
    rules: [COMMON_RULES.required('请选择性别')]
  });
  addField('userName', {
    label: '用户名',
    rules: [
      COMMON_RULES.required('请输入用户名'),
      COMMON_RULES.minLength(2, '用户名至少2个字符'),
      COMMON_RULES.maxLength(16, '用户名最多16个字符')
    ]
  });
};

// 表单验证状态
const isFormValid = computed(() => {
  return Object.keys(validationState.errors).length === 0 && 
         form.sex !== '' && 
         form.userName.trim() !== '';
});

// ==================== 方法 ====================

/**
 * 选择性别
 * @param gender 性别类型
 */
const selectGender = (gender: 'M' | 'U') => {
  form.sex = gender
  
  // 更新验证字段
  validateField('sex', form.sex);
  
  // 添加触觉反馈
  uni.vibrateShort({
    type: 'light'
  })
}

/**
 * 输入框交互处理
 */
const handleInputFocus = () => {
  // 输入框获得焦点时的处理
  logger.debug('📝 用户名输入框获得焦点')
}

const handleInputBlur = () => {
  // 输入框失去焦点时的处理
  logger.debug('📝 用户名输入框失去焦点')
  
  // 更新验证字段
  validateField('userName', form.userName);
  
  // 失去焦点后重置focus状态，避免重复聚焦
  shouldFocusInput.value = false
}

/**
 * 从多种途径获取用户名
 */
const getUserNameFromMultipleSources = async () => {
  try {
    // 方法1: 从存储的用户信息获取
    const userInfo = await StoreUtil.get<UserInfo>('USER_INFO')
    logger.debug('从存储获取的用户信息:', userInfo)
    
    if (userInfo && userInfo.userName) {
      userFromStorage.value = userInfo
      return userInfo.userName.trim()
    }
    
    // 方法2: 从Token中获取
    const tokenUserName = await getUserName()
    logger.debug('从Token获取的用户名:', tokenUserName)
    
    if (tokenUserName && tokenUserName.trim()) {
      return tokenUserName.trim()
    }
    
    // 方法3: 从手机号生成（如果有的话）
    if (userInfo && userInfo.phone) {
      const phone = userInfo.phone.toString()
      if (phone.length >= 7) {
        return `用户${phone.slice(-4)}`
      }
    }
    
    logger.debug('所有方法都未获取到用户名')
    return ''
  } catch (error) {
    logger.error('获取用户名时发生错误:', error)
    return ''
  }
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  // 验证所有字段
  const isValid = validateAndShowError(form);
  if (!isValid || loading.value) {
    // 显示验证错误信息
    const errors = validationState.errors;
    const firstError = Object.values(errors)[0];
    if (firstError) {
      showErrorToast(firstError);
    }
    return
  }

  await withLoading(async () => {
    // 准备请求参数
    const params: SetupProfileParams = {
      sex: form.sex as 'M' | 'U',
      userName: form.userName.trim()
    }

    // logger.info('🚀 提交用户资料设置:', params)

    try {
      // 调用API
      const response = await editSexAndUserName(params)
      
      // logger.debug('✅ API响应:', response)

      // 检查响应状态
      if (response.code === 200) {
        // logger.info('✅ 用户资料设置成功:', response)
        
        ErrorHandler.showSuccess('设置成功！')

        // 延迟跳转，让用户看到成功提示
        setTimeout(() => {
          // 跳转到个人信息页面
          uni.redirectTo({
            url: '/pages/user/infomation/infomation'
          })
        }, 1500)
      } else {
        // 处理业务错误（如：性别已设置不允许修改）
        // logger.warn('❌ 业务错误:', response)
        
        // 如果是性别已设置的错误，给出专门的提示
        if (response.msg && response.msg.includes('性别已设置')) {
          uni.showModal({
            title: '温馨提示',
            content: '您的性别信息已经设置过了，无法更改。请重新选择另一性别选项后再次提交。',
            confirmText: '重新选择',
            showCancel: false,
            confirmColor: '#ff6b9d',
            success: (res) => {
              if (res.confirm) {
                // 用户选择重新选择性别，重置性别选项
                form.sex = ''
                ErrorHandler.showError('请重新选择性别')
              }
            }
          })
        } else {
          handleApiError(response)
        }
      }
    } catch (error) {
      handleError(error, '设置失败，请重试')
    }
  }, '保存中...')
}

// ==================== 生命周期 ====================
onMounted(async () => {
  // 开始动画
  setTimeout(() => {
    isAnimated.value = true
  }, 100)

  // 初始化表单验证
  initFormValidation();

  // 获取并设置用户名
  const userName = await getUserNameFromMultipleSources()
  if (userName) {
    form.userName = userName
    // 更新验证字段
    validateField('userName', form.userName);
    logger.info('📝 已设置初始用户名:', userName)
  } else {
    // 如果没有获取到用户名，生成一个默认的
    const randomSuffix = Math.floor(Math.random() * 9999).toString().padStart(4, '0')
    form.userName = `用户${randomSuffix}`
    // 更新验证字段
    validateField('userName', form.userName);
    logger.info('📝 已生成默认用户名:', form.userName)
  }

  // 延迟设置输入框聚焦，确保动画完成后聚焦
  setTimeout(() => {
    shouldFocusInput.value = true
    logger.debug('📝 用户名输入框已自动聚焦')
  }, 1200) // 等待页面动画完成
})
</script>

<style lang="scss">
// 引入图标字体
@font-face {
  font-family: 'iconfont';
  src: url('data:font/woff2;charset=utf-8;base64,d09GMgABAAAAAAMMAAsAAAAABtwAAAK+AAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHFQGYACCcApwfwE2AiQDCAsGAAQgBYRnBzAbnwXIHoUZI1GJqJDe7w/E8Pz+29oUTb9nARVzG8kIBKnoDSuBCTOazUy9k+nOZnV3yrCKiAWkNvd8dCSkTqPw5+z+vbNVQc9PQ0ihBFa4SPB6HmDjwuWfZ3l6E9gDbtz5ARcpDehAG0CAxrhxPQvqBYZJlYYxeAEO+BwCCGSQA5WxPeOgEQY9TgBKVlZEaPQoBpsYFEKDGTOiAIdWbAjlBBzM/z49kI8WEI4BBpuHwzsJyr1c78dF4S78i+MHAgG4AdhADjAAQEBZU9EOS9EzQyAQ/JmbQgA0NIjAiyv9n6xBiM7BfzwEADMKNAHADsAi4gDzGv0AAF5AzjIwBYQKU1MZc9uE5DgkJzG3a+HsjnR2JrsAKgvNsqI7a93Nnpw87Dv7mUd+ZFVWr02u6vPJI+9p1DWwu/eHXYDSVuQzD/vevZ6k3z9Iz97jnr7j6xvtYfqGnL3X0+2D7r6p3+w//a7H66/unV/dNz9xLxAYKjZ+/jZjD/+UyV3JH3vavNx5OX1ePpx3nH1t2C65/BbxrNj8tG/5tmXr1sP/d9Yzp+WxfyubXLyotHg/aExJWZLaXrb9ePb1M/6JtR1rXzOAwfYACh6vj42HAXwvvVoH/rGTGT7B922YgTpGXpEzgF/aw+CfZGU7BRoMcJcEAjGQJGKRYvTOQg9IBrqwBXA+vBw0RqFiAJAtjAIQQdkPQgj3gBLUM0gx+j0IIb1CE+IbCGQeVBwzChSFHvSZsAVKjBMszB1OOUdzpDM+iJNszknC3RmN9RhnkwmYcJOLe3OJl2Nl/ZgwRZ/RVVjCTIbDhqpagaoaXdx7r2XYKdAnaAugCMOECmYdOKNw1Bx9zsd9QLjEznEka09mDPPhrEMLUFb+ygXvLYn4sDAYbINUKfvhTYEVZFFDg9YJ1+hUQrNgT1h9R2Dw2BkdGBozKgz7SuNyXYP3N6CAzg9yiGGM446a2Iiv+GiEYYo9AAAA') format('woff2');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

.auth-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff97b6 0%, #ff79a5 50%, #f8a5c2 100%);
  padding: 40rpx;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  
  .bg-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    
    .bg-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      
      &.bg-circle-1 {
        width: 300rpx;
        height: 300rpx;
        top: -100rpx;
        right: -50rpx;
        animation: float 6s ease-in-out infinite;
      }
      
      &.bg-circle-2 {
        width: 200rpx;
        height: 200rpx;
        bottom: 100rpx;
        left: -50rpx;
        animation: float 8s ease-in-out infinite reverse;
      }
      
      &.bg-circle-3 {
        width: 150rpx;
        height: 150rpx;
        top: 50%;
        right: 100rpx;
        animation: float 7s ease-in-out infinite;
      }
    }
    
    .love-decoration {
      position: absolute;
      font-size: 40rpx;
      opacity: 0.3;
      animation: float 4s ease-in-out infinite;
      
      &.love-1 {
        top: 20%;
        left: 15%;
        animation-delay: 0s;
      }
      
      &.love-2 {
        top: 60%;
        right: 20%;
        animation-delay: 2s;
      }
      
      &.love-3 {
        bottom: 30%;
        left: 25%;
        animation-delay: 4s;
      }
    }
  }
  
  .main-content {
    position: relative;
    z-index: 1;
    
    .logo-section {
      text-align: center;
      margin-bottom: 60rpx;
      opacity: 0;
      transform: translateY(50rpx);
      transition: all 0.8s ease;
      
      &.animate-in {
        opacity: 1;
        transform: translateY(0);
      }
      
      .logo-wrapper {
        margin-bottom: 20rpx;
        
        .logo {
          width: 120rpx;
          height: 120rpx;
          border-radius: 24rpx;
          background: rgba(255, 255, 255, 0.2);
          padding: 20rpx;
        }
      }
      
      .brand-title {
        display: block;
        font-size: 48rpx;
        font-weight: 700;
        color: #fff;
        margin-bottom: 12rpx;
        text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
      }
      
      .brand-subtitle {
        display: block;
        font-size: 28rpx;
        color: rgba(255, 255, 255, 0.8);
        font-weight: 400;
      }
    }
    
    .auth-card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20rpx);
      border-radius: 32rpx;
      padding: 60rpx 40rpx;
      box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
      opacity: 0;
      transform: translateY(50rpx);
      transition: all 0.8s ease 0.2s;
      
      &.animate-in {
        opacity: 1;
        transform: translateY(0);
      }
      
      .form-section {
        .form-content {
          .input-group {
            margin-bottom: 40rpx;
            
            .input-wrapper {
              position: relative;
              display: flex;
              align-items: center;
              background: #f8f9fa;
              border-radius: 16rpx;
              padding: 0 20rpx;
              height: 88rpx;
              border: 2rpx solid transparent;
              transition: all 0.3s ease;
              
              &:focus-within {
                border-color: #ff97b6;
                background: #fff;
                box-shadow: 0 0 0 6rpx rgba(255, 149, 182, 0.1);
              }
              
              .input-icon {
                font-size: 32rpx;
                margin-right: 20rpx;
                color: #666;
              }
              
              .modern-input {
                flex: 1;
                height: 100%;
                font-size: 30rpx;
                color: #333;
                border: none;
                outline: none;
                background: transparent;
                
                &::placeholder {
                  color: #999;
                }
              }
            }
            
            .input-hint {
              margin-top: 12rpx;
              font-size: 24rpx;
              color: #999;
              padding-left: 20rpx;
              line-height: 1.4;
            }
          }
          
          .gender-section {
            margin-bottom: 40rpx;
            
            .section-title {
              font-size: 32rpx;
              font-weight: 600;
              color: #333;
              margin-bottom: 24rpx;
              padding-left: 8rpx;
            }
            
            .gender-options {
              display: flex;
              gap: 20rpx;
              
              .gender-card {
                flex: 1;
                background: #f8f9fa;
                border: 2rpx solid #e9ecef;
                border-radius: 16rpx;
                padding: 32rpx 20rpx;
                text-align: center;
                transition: all 0.3s ease;
                position: relative;
                cursor: pointer;
                
                &:active {
                  transform: scale(0.98);
                }
                
                &.active {
                                      background: linear-gradient(135deg, #ff97b6, #ffd6e6);
                  border-color: #ff97b6;
                  transform: translateY(-4rpx);
                                      box-shadow: 0 8rpx 24rpx rgba(255, 149, 182, 0.3);
                }
                
                .gender-content {
                  .gender-icon {
                    font-size: 80rpx;
                    margin-bottom: 16rpx;
                    display: block;
                    
                    &.male {
                      color: #3b82f6;
                    }
                    
                    &.female {
                      color: #ff6b9d;
                    }
                  }
                  
                  .gender-label {
                    font-size: 28rpx;
                    font-weight: 500;
                    color: #374151;
                  }
                }
                
                .selected-indicator {
                  position: absolute;
                  top: 12rpx;
                  right: 12rpx;
                  width: 32rpx;
                  height: 32rpx;
                  background: #ff6b9d;
                  color: white;
                  border-radius: 50%;
                  font-size: 18rpx;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-weight: bold;
                  animation: bounceIn 0.5s ease;
                }
              }
            }
          }
          
          .notice-wrapper {
            background: rgba(255, 193, 7, 0.1);
            border: 1rpx solid rgba(255, 193, 7, 0.3);
            border-radius: 12rpx;
            padding: 20rpx;
            display: flex;
            align-items: flex-start;
            margin-bottom: 40rpx;
            
            .notice-icon {
              font-size: 28rpx;
              margin-right: 12rpx;
              margin-top: 2rpx;
            }
            
            .notice-text {
              flex: 1;
              font-size: 26rpx;
              color: #856404;
              line-height: 1.4;
            }
          }
          
          .primary-btn {
            width: 100%;
            height: 100rpx;
                          background: linear-gradient(135deg, #ff97b6 0%, #ff79a5 100%);
            color: #fff;
            font-size: 32rpx;
            border-radius: 20rpx;
            margin-top: 20rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            box-shadow: 0 8rpx 24rpx rgba(255, 107, 157, 0.5);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            
            .btn-text {
              font-weight: 600;
              margin-right: 12rpx;
            }
            
            .btn-icon {
              font-size: 28rpx;
            }
            
            &:active {
              transform: translateY(2rpx);
              box-shadow: 0 4rpx 16rpx rgba(255, 107, 157, 0.5);
            }
            
            &::after {
              border: none;
            }
            
            &[disabled] {
              background: #ddd;
              color: #999;
              box-shadow: none;
              transform: none;
            }
          }
        }
      }
    }
    
    .footer-links {
      margin-top: 60rpx;
      text-align: center;
      font-size: 28rpx;
      
      .link-text {
        color: rgba(255, 255, 255, 0.7);
        margin-right: 10rpx;
      }
      
      .link-action {
        color: #fff;
        font-weight: 600;
        text-decoration: underline;
        transition: opacity 0.2s ease;
        
        &:active {
          opacity: 0.8;
        }
      }
    }
  }
}

// 动画定义
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
</style>