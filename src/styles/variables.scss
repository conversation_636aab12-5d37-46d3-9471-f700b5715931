/**
 * 企业级开发规范 - 样式变量定义 (现代化Sass模块)
 * 乐享生活App设计系统变量
 * 
 * @description 统一的设计变量，支持主题切换和响应式设计
 * @note 使用现代@use语法，避免废弃的@import警告
 * <AUTHOR>
 * @version 2.0.0
 */

// ==================== 颜色系统 ====================

// 主品牌色彩 - 乐享生活主题
$primary-color: #ff6b9d;           // 主粉色
$primary-light: #ff97b6;           // 浅粉色
$primary-dark: #c44569;            // 深粉色
$primary-gradient: linear-gradient(135deg, #ff6b9d 0%, #c44569 50%, #f8b500 100%);

// 辅助色彩
$secondary-color: #5ac461;         // 成功绿色
$warning-color: #f8b500;          // 警告橙色
$error-color: #ff3b30;            // 错误红色
$info-color: #007aff;             // 信息蓝色

// 中性色彩
$text-color: #333333;             // 主文字色
$text-color-secondary: #666666;   // 次要文字色
$text-color-placeholder: #999999; // 占位符色
$text-color-disabled: #c0c0c0;    // 禁用文字色
$text-color-inverse: #ffffff;     // 反色文字

// 背景色彩
$bg-color: #ffffff;               // 主背景色
$bg-color-secondary: #f8f9fa;     // 次要背景色
$bg-color-tertiary: #f5f5f5;      // 三级背景色
$bg-color-hover: #f1f1f1;         // 悬停背景色
$bg-color-active: #e9ecef;        // 激活背景色
$bg-color-mask: rgba(0, 0, 0, 0.4); // 遮罩背景色

// 边框色彩
$border-color: #e1e4e8;           // 主边框色
$border-color-light: #f1f3f4;     // 浅边框色
$border-color-dark: #d1d5da;      // 深边框色

// ==================== 字体系统 ====================

// 字体大小 (使用rpx单位适配小程序)
$font-size-xs: 20rpx;             // 极小字体
$font-size-sm: 24rpx;             // 小字体
$font-size-base: 28rpx;           // 基础字体
$font-size-md: 32rpx;             // 中等字体
$font-size-lg: 36rpx;             // 大字体
$font-size-xl: 40rpx;             // 超大字体
$font-size-xxl: 48rpx;            // 特大字体

// 字体权重
$font-weight-light: 300;          // 细体
$font-weight-normal: 400;         // 常规
$font-weight-medium: 500;         // 中等
$font-weight-semibold: 600;       // 半粗体
$font-weight-bold: 700;           // 粗体

// 行高
$line-height-xs: 1.2;             // 紧密行高
$line-height-sm: 1.4;             // 小行高
$line-height-base: 1.5;           // 基础行高
$line-height-md: 1.6;             // 中等行高
$line-height-lg: 1.8;             // 大行高

// ==================== 间距系统 ====================

// 内间距
$spacing-xs: 8rpx;                // 极小间距
$spacing-sm: 16rpx;               // 小间距
$spacing-base: 24rpx;             // 基础间距
$spacing-md: 32rpx;               // 中等间距
$spacing-lg: 40rpx;               // 大间距
$spacing-xl: 48rpx;               // 超大间距
$spacing-xxl: 64rpx;              // 特大间距

// 外间距
$margin-xs: 8rpx;
$margin-sm: 16rpx;
$margin-base: 24rpx;
$margin-md: 32rpx;
$margin-lg: 40rpx;
$margin-xl: 48rpx;
$margin-xxl: 64rpx;

// ==================== 圆角系统 ====================

$border-radius-xs: 4rpx;          // 极小圆角
$border-radius-sm: 8rpx;          // 小圆角
$border-radius-base: 12rpx;       // 基础圆角
$border-radius-md: 16rpx;         // 中等圆角
$border-radius-lg: 20rpx;         // 大圆角
$border-radius-xl: 24rpx;         // 超大圆角
$border-radius-round: 50%;        // 圆形
$border-radius-circle: 9999rpx;   // 胶囊形

// ==================== 阴影系统 ====================

// 卡片阴影
$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
$shadow-base: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
$shadow-md: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
$shadow-lg: 0 12rpx 32rpx rgba(0, 0, 0, 0.18);

// 品牌色阴影
$shadow-primary: 0 4rpx 16rpx rgba(255, 107, 157, 0.3);
$shadow-primary-lg: 0 8rpx 24rpx rgba(255, 107, 157, 0.4);

// ==================== Z-Index 层级系统 ====================

$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;
$z-index-toast: 1080;

// ==================== 动画系统 ====================

// 过渡时间
$transition-fast: 0.15s;
$transition-base: 0.3s;
$transition-slow: 0.5s;

// 过渡函数
$ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
$ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
$ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
$ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

// ==================== 响应式断点 ====================

$breakpoint-mobile: 750rpx;       // 手机端
$breakpoint-tablet: 1024rpx;      // 平板端
$breakpoint-desktop: 1440rpx;     // 桌面端

// 断点映射
$breakpoints: (
  'mobile': $breakpoint-mobile,
  'tablet': $breakpoint-tablet,
  'desktop': $breakpoint-desktop
);

// ==================== 组件特定变量 ====================

// 按钮
$button-height-sm: 64rpx;
$button-height-base: 80rpx;
$button-height-lg: 96rpx;

// 输入框
$input-height: 88rpx;
$input-padding: 24rpx;

// 卡片
$card-padding: 32rpx;
$card-border-radius: $border-radius-md;

// 头像
$avatar-size-sm: 60rpx;
$avatar-size-base: 80rpx;
$avatar-size-lg: 120rpx;
$avatar-size-xl: 160rpx;