/**
 * 企业级开发规范 - 通用样式 (现代化Sass语法)
 * 乐享生活App通用样式定义
 * 
 * @description 使用现代@use语法引入变量和混入，完全避免废弃的@import API
 * @note 所有字体和图标都使用系统默认字体，避免加载外部字体文件
 * <AUTHOR>
 * @version 2.0.0
 */

// 使用现代@use语法引入模块，通过命名空间访问
@use './variables' as var;
@use './mixins' as mix;

/* 修复字体相关问题 - 使用系统字体栈，避免WOFF2加载失败 */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-family: inherit;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  background: var.$bg-color;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: var.$font-size-base;
  line-height: 1.5;
  color: var.$text-color;
}

// ==================== 通用工具类 ====================

// 文字相关
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var.$primary-color; }
.text-secondary { color: var.$text-color-secondary; }
.text-success { color: var.$secondary-color; }
.text-warning { color: var.$warning-color; }
.text-error { color: var.$error-color; }

.text-xs { font-size: var.$font-size-xs; }
.text-sm { font-size: var.$font-size-sm; }
.text-base { font-size: var.$font-size-base; }
.text-lg { font-size: var.$font-size-lg; }
.text-xl { font-size: var.$font-size-xl; }

// 布局相关
.flex { display: flex; }
.flex-center { 
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between { 
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-column {
  flex-direction: column;
}
.flex-1 { flex: 1; }

// 间距相关
.m-0 { margin: 0; }
.m-xs { margin: var.$spacing-xs; }
.m-sm { margin: var.$spacing-sm; }
.m-md { margin: var.$spacing-md; }
.m-lg { margin: var.$spacing-lg; }

.p-0 { padding: 0; }
.p-xs { padding: var.$spacing-xs; }
.p-sm { padding: var.$spacing-sm; }
.p-md { padding: var.$spacing-md; }
.p-lg { padding: var.$spacing-lg; }

// 背景相关
.bg-primary { background: var.$primary-color; }
.bg-white { background: var.$bg-color; }
.bg-gray { background-color: var.$bg-color-secondary; }

// 边框相关
.rounded { border-radius: var.$border-radius-sm; }
.rounded-md { border-radius: var.$border-radius-md; }
.rounded-lg { border-radius: var.$border-radius-lg; }
.rounded-full { border-radius: var.$border-radius-round; }

// 显示相关
.hidden { display: none; }
.block { display: block; }
.invisible { visibility: hidden; }

// ==================== 页面容器 ====================

.page-container {
  min-height: 100vh;
  background: var.$bg-color;
  
  &.gradient {
    background: var.$primary-gradient;
  }
}

// ==================== 加载状态 ====================

.loading {
  @include mix.flex-center;
  color: var.$text-color-placeholder;
  
  .loading-icon {
    width: 32rpx;
    height: 32rpx;
    border: 2rpx solid var.$border-color;
    border-top-color: var.$primary-color;
    border-radius: var.$border-radius-round;
    animation: spin 1s linear infinite;
    margin-right: var.$spacing-sm;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// ==================== 动画类 ====================

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

// ==================== 响应式设计 ====================

@media (max-width: 768rpx) {
  .mobile-hidden { display: none; }
}

@media (min-width: 768rpx) {
  .desktop-hidden { display: none; }
}

// ==================== 修复验证码图片响应式问题 ====================

.responsive-image {
  width: 100%;
  height: auto;
  max-width: 100%;
  object-fit: contain;
}

.captcha-container {
  width: 100%;
  max-width: 350rpx;
  margin: 0 auto;
}

.captcha-image {
  width: 100%;
  height: auto;
  max-width: 100%;
  object-fit: contain;
  border-radius: var.$border-radius-sm;
}

// ==================== 通用阴影类 ====================

.shadow-sm {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.shadow-md {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.shadow-lg {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}

// ==================== 通用宽度类 ====================

.w-full { width: 100%; }
.h-full { height: 100%; }