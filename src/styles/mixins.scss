/**
 * 企业级开发规范 - 样式混入函数 (现代化Sass模块)
 * 乐享生活App通用样式模式
 * 
 * @description 可复用的样式混入函数，提高开发效率和代码一致性
 * @note 使用现代@use语法，避免废弃的@import警告
 * <AUTHOR>
 * @version 2.0.0
 */

// 使用现代@use语法导入变量模块
@use './variables' as var;

// ==================== 响应式设计混入 ====================

/**
 * 响应式断点混入
 * @param {string} $breakpoint - 断点名称 (mobile, tablet, desktop)
 */
@mixin respond-to($breakpoint) {
  @if $breakpoint == 'mobile' {
    @media (min-width: 750rpx) {
      @content;
    }
  } @else if $breakpoint == 'tablet' {
    @media (min-width: 1024rpx) {
      @content;
    }
  } @else if $breakpoint == 'desktop' {
    @media (min-width: 1440rpx) {
      @content;
    }
  }
}

/**
 * 移动端优先响应式混入
 */
@mixin mobile-first {
  @content;
  
  @include respond-to('tablet') {
    @content;
  }
  
  @include respond-to('desktop') {
    @content;
  }
}

// ==================== 布局混入 ====================

/**
 * Flexbox居中布局
 * @param {string} $direction - flex方向 (row, column)
 */
@mixin flex-center($direction: row) {
  display: flex;
  flex-direction: $direction;
  align-items: center;
  justify-content: center;
}

/**
 * Flexbox间距布局
 * @param {string} $justify - 主轴对齐方式
 * @param {string} $align - 交叉轴对齐方式
 */
@mixin flex-layout($justify: flex-start, $align: stretch) {
  display: flex;
  justify-content: $justify;
  align-items: $align;
}

/**
 * 绝对定位居中
 */
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/**
 * 清除浮动
 */
@mixin clearfix {
  &::after {
    content: "";
    display: table;
    clear: both;
  }
}

// ==================== 文字混入 ====================

/**
 * 文字省略
 * @param {number} $lines - 显示行数，默认1行
 */
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $lines;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

/**
 * 无选择文字
 */
@mixin no-select {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/**
 * 文字渐变效果
 * @param {string} $gradient - 渐变色
 */
@mixin text-gradient($gradient: var.$primary-gradient) {
  background: $gradient;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

// ==================== 视觉效果混入 ====================

/**
 * 卡片样式
 * @param {string} $shadow - 阴影级别 (sm, base, md, lg)
 * @param {string} $radius - 圆角大小
 */
@mixin card($shadow: base, $radius: var.$border-radius-md) {
  background: var.$bg-color;
  border-radius: $radius;
  border: 1rpx solid var.$border-color-light;
  
  @if $shadow == sm {
    box-shadow: var.$shadow-sm;
  } @else if $shadow == base {
    box-shadow: var.$shadow-base;
  } @else if $shadow == md {
    box-shadow: var.$shadow-md;
  } @else if $shadow == lg {
    box-shadow: var.$shadow-lg;
  }
}

/**
 * 毛玻璃效果
 * @param {number} $blur - 模糊程度
 * @param {color} $bg - 背景色
 */
@mixin glass-morphism($blur: 20rpx, $bg: rgba(255, 255, 255, 0.25)) {
  background: $bg;
  backdrop-filter: blur($blur);
  -webkit-backdrop-filter: blur($blur);
  border: 1rpx solid rgba(255, 255, 255, 0.18);
}

/**
 * 渐变边框
 * @param {string} $gradient - 渐变色
 * @param {string} $width - 边框宽度
 */
@mixin gradient-border($gradient: var.$primary-gradient, $width: 2rpx) {
  position: relative;
  background: var.$bg-color;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: $gradient;
    border-radius: inherit;
    padding: $width;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: subtract;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: subtract;
  }
}

// ==================== 动画混入 ====================

/**
 * 基础过渡动画
 * @param {string} $property - 动画属性
 * @param {string} $duration - 动画时长
 * @param {string} $ease - 缓动函数
 */
@mixin transition($property: all, $duration: $transition-base, $ease: $ease-out) {
  transition: $property $duration $ease;
}

/**
 * 悬停上浮效果
 * @param {string} $distance - 上浮距离
 */
@mixin hover-lift($distance: -4rpx) {
  @include transition(transform);
  
  &:hover {
    transform: translateY($distance);
  }
}

/**
 * 点击缩放效果
 * @param {number} $scale - 缩放比例
 */
@mixin active-scale($scale: 0.98) {
  @include transition(transform);
  
  &:active {
    transform: scale($scale);
  }
}

/**
 * 脉冲动画
 * @param {color} $color - 脉冲颜色
 */
@mixin pulse-animation($color: $primary-color) {
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    background: $color;
    opacity: 0.6;
    animation: pulse 2s infinite;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.3;
  }
  100% {
    transform: scale(1);
    opacity: 0.6;
  }
}

/**
 * 浮动动画
 * @param {string} $distance - 浮动距离
 * @param {string} $duration - 动画时长
 */
@mixin float-animation($distance: 20rpx, $duration: 3s) {
  animation: float #{$duration} ease-in-out infinite;
  
  @keyframes float {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-#{$distance});
    }
  }
}

// ==================== 表单混入 ====================

/**
 * 输入框样式
 * @param {color} $focus-color - 聚焦颜色
 */
@mixin input-style($focus-color: $primary-color) {
  width: 100%;
  height: $input-height;
  padding: 0 $input-padding;
  border: 2rpx solid $border-color;
  border-radius: $border-radius-base;
  background: $bg-color-secondary;
  font-size: $font-size-base;
  color: $text-color;
  @include transition(all);
  
  &::placeholder {
    color: $text-color-placeholder;
  }
  
  &:focus {
    outline: none;
    border-color: $focus-color;
    background: $bg-color;
    box-shadow: 0 0 0 4rpx rgba($focus-color, 0.1);
  }
}

/**
 * 按钮样式
 * @param {color} $bg-color - 背景色
 * @param {color} $text-color - 文字色
 * @param {string} $size - 尺寸 (sm, base, lg)
 */
@mixin button-style($bg-color: $primary-color, $text-color: $text-color-inverse, $size: base) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: $border-radius-base;
  background: $bg-color;
  color: $text-color;
  font-weight: $font-weight-medium;
  cursor: pointer;
  @include transition(all);
  @include no-select;
  
  @if $size == sm {
    height: $button-height-sm;
    padding: 0 $spacing-base;
    font-size: $font-size-sm;
  } @else if $size == base {
    height: $button-height-base;
    padding: 0 $spacing-md;
    font-size: $font-size-base;
  } @else if $size == lg {
    height: $button-height-lg;
    padding: 0 $spacing-lg;
    font-size: $font-size-md;
  }
  
  &:hover {
    opacity: 0.9;
    transform: translateY(-2rpx);
  }
  
  &:active {
    transform: translateY(0) scale(0.98);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
}

// ==================== 通用工具混入 ====================

/**
 * 隐藏滚动条
 */
@mixin hide-scrollbar {
  scrollbar-width: none; // Firefox
  -ms-overflow-style: none; // IE 10+
  
  &::-webkit-scrollbar {
    display: none; // Safari and Chrome
  }
}

/**
 * 自定义滚动条
 * @param {color} $track-color - 轨道颜色
 * @param {color} $thumb-color - 滑块颜色
 */
@mixin custom-scrollbar($track-color: $bg-color-tertiary, $thumb-color: $border-color-dark) {
  &::-webkit-scrollbar {
    width: 8rpx;
    height: 8rpx;
  }
  
  &::-webkit-scrollbar-track {
    background: $track-color;
    border-radius: 4rpx;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: 4rpx;
    
    &:hover {
      background: darken($thumb-color, 10%);
    }
  }
}

/**
 * 设置宽高比
 * @param {number} $ratio - 宽高比 (16/9, 4/3, 1/1等)
 */
@mixin aspect-ratio($ratio) {
  position: relative;
  
  &::before {
    content: '';
    display: block;
    padding-top: math.percentage(1 / $ratio);
  }
  
  > * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}