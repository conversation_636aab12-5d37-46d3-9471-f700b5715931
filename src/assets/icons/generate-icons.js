const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');

// 图标配置
const icons = [
  { name: 'home', color: '#999999', activeColor: '#007AFF' },
  { name: 'service', color: '#999999', activeColor: '#007AFF' },
  { name: 'match', color: '#999999', activeColor: '#007AFF' },
  { name: 'message', color: '#999999', activeColor: '#007AFF' },
  { name: 'mine', color: '#999999', activeColor: '#007AFF' }
];

// 创建图标
function createIcon(name, color) {
  const canvas = createCanvas(60, 60);
  const ctx = canvas.getContext('2d');
  
  // 绘制图标
  ctx.fillStyle = color;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.font = '40px Material Icons';
  
  // 根据名称选择对应的图标字符
  let iconChar = '';
  switch (name) {
    case 'home':
      iconChar = '⌂';
      break;
    case 'service':
      iconChar = '⚙';
      break;
    case 'match':
      iconChar = '♥';
      break;
    case 'message':
      iconChar = '✉';
      break;
    case 'mine':
      iconChar = '☺';
      break;
  }
  
  ctx.fillText(iconChar, 30, 30);
  
  return canvas.toBuffer();
}

// 生成所有图标
icons.forEach(icon => {
  // 生成普通图标
  const normalIcon = createIcon(icon.name, icon.color);
  fs.writeFileSync(path.join(__dirname, `${icon.name}.png`), normalIcon);
  
  // 生成激活状态图标
  const activeIcon = createIcon(icon.name, icon.activeColor);
  fs.writeFileSync(path.join(__dirname, `${icon.name}-active.png`), activeIcon);
}); 