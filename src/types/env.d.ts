/**
 * 环境变量类型声明
 * @description 为环境变量提供TypeScript类型支持
 * <AUTHOR>
 * @since 2024-01-01
 */

/// <reference types="vite/client" />

interface ImportMetaEnv {
  // 应用基础配置
  readonly VITE_API_BASE_URL: string
  readonly VITE_APP_ENV: 'development' | 'production' | 'staging'
  readonly VITE_APP_NAME: string
  readonly VITE_APP_VERSION: string
  readonly VITE_APP_DESCRIPTION: string
  
  // 功能开关
  readonly VITE_ENABLE_CONSOLE_LOG: string
  readonly VITE_ENABLE_MOCK: string
  readonly VITE_ENABLE_STRICT_MODE: string
  readonly VITE_ENABLE_HTTPS_ONLY: string
  
  // 请求配置
  readonly VITE_REQUEST_TIMEOUT: string
  readonly VITE_REQUEST_RETRY_COUNT: string
  
  // 安全配置

  readonly VITE_ENCRYPTION_KEY: string
  
  // 验证码配置
  readonly VITE_CAPTCHA_API_URL: string
  readonly VITE_CAPTCHA_SITE_KEY: string
  
  // CDN配置
  readonly VITE_CDN_BASE_URL: string
  readonly VITE_STATIC_RESOURCE_URL: string
  
  // 第三方服务
  readonly VITE_IP_SERVICE_URL: string
  readonly VITE_ANALYTICS_ID: string
  readonly VITE_SENTRY_DSN: string
  
  // 开发工具
  readonly VITE_ENABLE_VCONSOLE: string
  readonly VITE_ENABLE_ERUDA: string
  
  // 性能配置
  readonly VITE_ENABLE_PWA: string
  readonly VITE_ENABLE_GZIP: string
  
  // 测试配置
  readonly VITE_TEST_API_DELAY: string
  readonly VITE_MOCK_USER_ID: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// 扩展 process.env 类型（兼容性）
declare namespace NodeJS {
  interface ProcessEnv extends ImportMetaEnv {
    readonly NODE_ENV: 'development' | 'production' | 'test'
    readonly UNI_PLATFORM: string
    readonly UNI_ROUTER_MODE: string
  }
}

// 全局类型声明
declare global {
  const __APP_VERSION__: string
  const __BUILD_TIME__: string
  const __GIT_HASH__: string
}

export {}