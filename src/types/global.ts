/**
 * 全局类型定义
 * 定义应用中通用的类型接口
 */

// 基础响应结构
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp?: number
}

// 分页请求参数
export interface PageParams {
  page: number
  pageSize: number
  [key: string]: any
}

// 分页响应数据
export interface PageData<T = any> {
  list: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 用户信息
export interface UserInfo {
  id: string | number
  username: string
  nickname?: string
  avatar?: string
  email?: string
  phone?: string
  status: number
  createTime?: string
  updateTime?: string
}

// 登录表单
export interface LoginForm {
  username: string
  password: string
  captcha?: string
  remember?: boolean
}

// 注册表单
export interface RegisterForm {
  username: string
  password: string
  confirmPassword: string
  phone: string
  captcha: string
  agreement: boolean
}

// 文件上传响应
export interface UploadResponse {
  url: string
  name: string
  size: number
  type: string
}

// 选项接口
export interface Option {
  label: string
  value: string | number
  disabled?: boolean
  [key: string]: any
}

// 菜单项接口
export interface MenuItem {
  id: string | number
  title: string
  icon?: string
  path?: string
  children?: MenuItem[]
  meta?: {
    requireAuth?: boolean
    roles?: string[]
    [key: string]: any
  }
}

// 表单验证规则
export interface FormRule {
  required?: boolean
  message?: string
  pattern?: RegExp
  validator?: (value: any) => boolean | string
  trigger?: 'blur' | 'change'
}

// 表格列配置
export interface TableColumn {
  key: string
  title: string
  width?: number | string
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  fixed?: 'left' | 'right'
  render?: (value: any, record: any, index: number) => any
}

// 主题配置
export interface ThemeConfig {
  primaryColor: string
  mode: 'light' | 'dark'
  fontSize: 'small' | 'medium' | 'large'
}

// 设备信息
export interface DeviceInfo {
  platform: string
  system: string
  version: string
  model: string
  brand: string
  screenWidth: number
  screenHeight: number
}

// 错误信息
export interface ErrorInfo {
  code: number
  message: string
  stack?: string
  timestamp: number
  url?: string
  userAgent?: string
}