/**
 * API 相关类型定义
 * 定义 API 请求和响应的类型接口
 */

import type { PageParams, PageData } from './global'

// HTTP 请求方法
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'

// 请求配置
export interface RequestConfig {
  url: string
  method?: HttpMethod
  params?: Record<string, string | number | boolean | null | undefined>
  data?: Record<string, unknown>
  headers?: Record<string, string>
  timeout?: number
  showLoading?: boolean
  loadingText?: string
  loadingDelay?: number
  loadingMask?: boolean
  showError?: boolean
  errorHandler?: (_error: Error | unknown) => void
}

// 响应拦截器配置
export interface ResponseInterceptorConfig {
  showError?: boolean
  errorHandler?: (_error: Error | unknown) => void
  successHandler?: (_response: unknown) => void
}

// 验证码相关接口
export namespace CaptchaAPI {
  // 获取图片验证码响应
  export interface GetImageCaptchaResponse {
    captchaId: string
    captchaImage: string
  }
  
  // 发送短信验证码请求
  export interface SendSmsCodeRequest {
    phone: string
    captchaId?: string
    captcha?: string
  }
  
  // 验证验证码请求
  export interface VerifyCaptchaRequest {
    captchaId: string
    captcha: string
  }
}

// 用户相关接口
export namespace UserAPI {
  // 登录请求
  export interface LoginRequest {
    username: string
    password: string
    captchaId?: string
    captcha?: string
  }
  
  // 登录响应
  export interface LoginResponse {
    token: string
    refreshToken?: string
    userInfo: {
      id: string | number
      username: string
      nickname?: string
      avatar?: string
      email?: string
      phone?: string
      roles?: string[]
    }
  }
  
  // 注册请求
  export interface RegisterRequest {
    username: string
    password: string
    phone: string
    smsCode: string
    captchaId?: string
    captcha?: string
  }
  
  // 修改密码请求
  export interface ChangePasswordRequest {
    oldPassword: string
    newPassword: string
  }
  
  // 重置密码请求
  export interface ResetPasswordRequest {
    phone: string
    smsCode: string
    newPassword: string
  }
  
  // 更新用户信息请求
  export interface UpdateUserInfoRequest {
    nickname?: string
    avatar?: string
    email?: string
    phone?: string
  }
}

// 会员相关接口
export namespace MemberAPI {
  // 会员信息
  export interface MemberInfo {
    id: string | number
    userId: string | number
    level: number
    levelName: string
    points: number
    balance: number
    expireTime?: string
    createTime: string
    updateTime: string
  }
  
  // 会员等级
  export interface MemberLevel {
    level: number
    name: string
    minPoints: number
    maxPoints: number
    benefits: string[]
    discount: number
  }
  
  // 积分记录
  export interface PointsRecord {
    id: string | number
    userId: string | number
    type: 'earn' | 'spend'
    points: number
    reason: string
    createTime: string
  }
  
  // 积分记录查询参数
  export interface PointsRecordQuery extends PageParams {
    type?: 'earn' | 'spend'
    startTime?: string
    endTime?: string
  }
  
  // 积分记录响应
  export interface PointsRecordResponse extends PageData<PointsRecord> {}
}

// 文件上传相关接口
export namespace UploadAPI {
  // 上传文件请求
  export interface UploadFileRequest {
    file: File
    type?: 'image' | 'video' | 'document'
    category?: string
  }
  
  // 上传文件响应
  export interface UploadFileResponse {
    url: string
    name: string
    size: number
    type: string
    hash?: string
  }
}