/**
 * 乐享生活平台 - 业务类型定义
 * 定义平台核心业务实体的类型结构
 */

// ==================== 服务相关类型 ====================

/**
 * 服务类型枚举
 */
export enum ServiceType {
  // 运动健身类
  FITNESS = 'fitness',
  YOGA = 'yoga',
  RUNNING = 'running',
  SWIMMING = 'swimming',
  
  // 游戏陪玩类
  GAME_COMPANION = 'game_companion',
  MOBILE_GAME = 'mobile_game',
  PC_GAME = 'pc_game',
  
  // 生活服务类
  MASSAGE = 'massage',
  FOOT_THERAPY = 'foot_therapy',
  HOUSEKEEPING = 'housekeeping',
  CLEANING = 'cleaning',
  
  // 陪伴服务类
  DINING_COMPANION = 'dining_companion',
  SHOPPING_COMPANION = 'shopping_companion',
  TRAVEL_COMPANION = 'travel_companion',
  STUDY_COMPANION = 'study_companion'
}

/**
 * 服务状态枚举
 */
export enum ServiceStatus {
  DRAFT = 'draft',           // 草稿
  PUBLISHED = 'published',   // 已发布
  PAUSED = 'paused',        // 已暂停
  COMPLETED = 'completed',   // 已完成
  CANCELLED = 'cancelled'    // 已取消
}

/**
 * 服务基础信息接口
 */
export interface ServiceBase {
  id: string;
  title: string;
  description: string;
  type: ServiceType;
  price: number;
  unit: string; // 计价单位：小时、次、天等
  location: string;
  providerId: string;
  status: ServiceStatus;
  tags: string[];
  images: string[];
  createdAt: string;
  updatedAt: string;
}

/**
 * 服务详细信息接口
 */
export interface ServiceDetail extends ServiceBase {
  duration: number; // 服务时长（分钟）
  availableTime: TimeSlot[];
  requirements: string; // 服务要求
  equipment: string; // 所需设备
  experience: string; // 经验要求
  certification: string[]; // 相关认证
  rating: number;
  reviewCount: number;
  orderCount: number;
}

/**
 * 时间段接口
 */
export interface TimeSlot {
  startTime: string;
  endTime: string;
  available: boolean;
}

// ==================== 需求相关类型 ====================

/**
 * 需求状态枚举
 */
export enum DemandStatus {
  OPEN = 'open',           // 开放中
  MATCHED = 'matched',     // 已匹配
  IN_PROGRESS = 'in_progress', // 进行中
  COMPLETED = 'completed', // 已完成
  CANCELLED = 'cancelled'  // 已取消
}

/**
 * 需求信息接口
 */
export interface Demand {
  id: string;
  title: string;
  description: string;
  type: ServiceType;
  budget: number;
  location: string;
  expectedTime: string;
  userId: string;
  status: DemandStatus;
  tags: string[];
  requirements: string;
  createdAt: string;
  updatedAt: string;
}

// ==================== 用户相关类型 ====================

/**
 * 用户角色枚举
 */
export enum UserRole {
  CONSUMER = 'consumer',   // 消费者
  PROVIDER = 'provider',   // 服务提供者
  BOTH = 'both'           // 双重角色
}

/**
 * 认证状态枚举
 */
export enum CertificationStatus {
  NONE = 'none',           // 未认证
  PENDING = 'pending',     // 认证中
  APPROVED = 'approved',   // 已认证
  REJECTED = 'rejected'    // 认证失败
}

/**
 * 用户基础信息接口
 */
export interface UserProfile {
  id: string;
  nickname: string;
  avatar: string;
  phone: string;
  email?: string;
  gender: 'male' | 'female' | 'other';
  age: number;
  location: string;
  role: UserRole;
  certificationStatus: CertificationStatus;
  rating: number;
  reviewCount: number;
  joinDate: string;
}

/**
 * 认证信息接口
 */
export interface CertificationInfo {
  id: string;
  userId: string;
  type: ServiceType;
  realName: string;
  idCard: string;
  phone: string;
  experience: number; // 从业年限
  certificates: CertificateFile[];
  introduction: string;
  portfolio: string[]; // 作品展示
  status: CertificationStatus;
  reviewNote?: string; // 审核备注
  submittedAt: string;
  reviewedAt?: string;
}

/**
 * 证书文件接口
 */
export interface CertificateFile {
  id: string;
  name: string;
  url: string;
  type: string;
  uploadedAt: string;
}

// ==================== 订单相关类型 ====================

/**
 * 订单状态枚举
 */
export enum OrderStatus {
  PENDING = 'pending',         // 待确认
  CONFIRMED = 'confirmed',     // 已确认
  IN_PROGRESS = 'in_progress', // 进行中
  COMPLETED = 'completed',     // 已完成
  CANCELLED = 'cancelled',     // 已取消
  REFUNDED = 'refunded'       // 已退款
}

/**
 * 订单信息接口
 */
export interface Order {
  id: string;
  serviceId: string;
  providerId: string;
  consumerId: string;
  title: string;
  description: string;
  price: number;
  quantity: number;
  totalAmount: number;
  scheduledTime: string;
  location: string;
  status: OrderStatus;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// ==================== 评价相关类型 ====================

/**
 * 评价信息接口
 */
export interface Review {
  id: string;
  orderId: string;
  serviceId: string;
  providerId: string;
  consumerId: string;
  rating: number; // 1-5星
  content: string;
  images?: string[];
  isAnonymous: boolean;
  createdAt: string;
}

// ==================== 消息相关类型 ====================

/**
 * 消息类型枚举
 */
export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  VOICE = 'voice',
  LOCATION = 'location',
  SERVICE_CARD = 'service_card',
  ORDER_UPDATE = 'order_update'
}

/**
 * 消息接口
 */
export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  receiverId: string;
  type: MessageType;
  content: string;
  metadata?: Record<string, any>;
  isRead: boolean;
  createdAt: string;
}

/**
 * 会话接口
 */
export interface Conversation {
  id: string;
  participants: string[];
  lastMessage?: Message;
  unreadCount: number;
  updatedAt: string;
}

// ==================== API 响应类型 ====================

/**
 * 分页参数接口
 */
export interface PaginationParams {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * 分页响应接口
 */
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

/**
 * 搜索参数接口
 */
export interface SearchParams extends PaginationParams {
  keyword?: string;
  type?: ServiceType;
  location?: string;
  priceRange?: [number, number];
  rating?: number;
  tags?: string[];
}

// ==================== 首页金刚位类型 ====================

/**
 * 金刚位配置接口
 */
export interface GridItem {
  id: string;
  title: string;
  icon: string;
  color?: string;
  gradient?: string;
  route: string;
  type: ServiceType;
  isHot?: boolean;
  isNew?: boolean;
  order: number;
}

/**
 * 首页配置接口
 */
export interface HomeConfig {
  banners: Banner[];
  gridItems: GridItem[];
  recommendedServices: ServiceDetail[];
  hotCategories: ServiceType[];
}

/**
 * 轮播图接口
 */
export interface Banner {
  id: string;
  title: string;
  image: string;
  link?: string;
  order: number;
  isActive: boolean;
}