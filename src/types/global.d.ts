/**
 * 全局类型声明文件
 */

declare module 'crypto-js' {
  export = CryptoJS
  export as namespace CryptoJS

  namespace CryptoJS {
    interface WordArray {
      toString(encoder?: any): string
    }

    namespace enc {
      const Utf8: {
        parse(str: string): WordArray
        stringify(wordArray: WordArray): string
      }
      const Base64: {
        parse(str: string): WordArray
        stringify(wordArray: WordArray): string
      }
    }

    namespace mode {
      const ECB: any
    }

    namespace pad {
      const Pkcs7: any
    }

    const AES: {
      encrypt(message: WordArray, key: WordArray, cfg?: any): {
        toString(): string
      }
      decrypt(encryptedData: string, key: WordArray, cfg?: any): WordArray
    }

    function MD5(message: string): WordArray
    function SHA256(message: string): WordArray
  }
}

// 全局类型扩展
declare global {
  interface Window {
    uni: any
  }
}

export {} 