/**
 * 请求相关类型声明
 */

// 通用响应数据结构
export interface ResponseData<T = unknown> {
  code: number
  message: string
  data: T
  timestamp?: number
  traceId?: string
  msg?: string // 兼容字段
  success?: boolean // 兼容字段
}

// 请求配置类型
export interface RequestConfig {
  baseURL: string
  timeout?: number
  url?: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  data?: Record<string, unknown> | FormData | string | null
  params?: Record<string, string | number | boolean | null | undefined>
  headers?: Record<string, string>
  retryCount?: number
}

// 请求拦截器类型
export type RequestInterceptor = (config: RequestConfig) => Promise<RequestConfig> | RequestConfig

// 分页请求参数
export interface PaginationParams {
  pageNum: number
  pageSize: number
  total?: number
}

// 分页响应数据
export interface PaginationResponse<T = unknown> {
  list: T[]
  pagination: {
    pageNum: number
    pageSize: number
    total: number
    totalPages: number
  }
}

// 文件上传响应
export interface UploadResponse {
  url: string
  fileName: string
  fileSize: number
  fileType: string
}