/**
 * API错误处理器
 * 提供统一的错误处理、错误码映射和用户友好的错误提示
 */

import { AxiosError, AxiosResponse } from 'axios'
import { useAuthStore } from '@/store/modules/auth'
import { createLogger } from '@/utils/logger'
// import router from '@/router'

// 创建错误处理器专用日志器
const logger = createLogger('ErrorHandler')

/**
 * 错误类型枚举
 */
export enum ErrorType {
  /** 网络错误 */
  NETWORK = 'NETWORK',
  /** 服务器错误 */
  SERVER = 'SERVER',
  /** 客户端错误 */
  CLIENT = 'CLIENT',
  /** 认证错误 */
  AUTH = 'AUTH',
  /** 权限错误 */
  PERMISSION = 'PERMISSION',
  /** 业务错误 */
  BUSINESS = 'BUSINESS',
  /** 未知错误 */
  UNKNOWN = 'UNKNOWN'
}

/**
 * 错误信息接口
 */
export interface ErrorInfo {
  /** 错误类型 */
  type: ErrorType
  /** 错误代码 */
  code: string | number
  /** 错误消息 */
  message: string
  /** 用户友好的错误消息 */
  userMessage: string
  /** 错误详情 */
  details?: any
  /** 是否需要重试 */
  retryable: boolean
  /** 是否需要用户处理 */
  requiresUserAction: boolean
}

/**
 * 错误处理配置
 */
export interface ErrorHandlerConfig {
  /** 是否显示错误消息 */
  showMessage: boolean
  /** 消息显示方式 */
  messageType: 'message' | 'notification'
  /** 是否自动重试 */
  autoRetry: boolean
  /** 重试次数 */
  retryCount: number
  /** 是否记录错误日志 */
  logError: boolean
  /** 自定义错误处理 */
  customHandler?: (error: ErrorInfo) => void
}

/**
 * HTTP状态码错误映射
 */
const HTTP_ERROR_MAP: Record<number, Partial<ErrorInfo>> = {
  400: {
    type: ErrorType.CLIENT,
    userMessage: '请求参数错误，请检查输入信息',
    retryable: false,
    requiresUserAction: true
  },
  401: {
    type: ErrorType.AUTH,
    userMessage: '登录已过期，请重新登录',
    retryable: false,
    requiresUserAction: true
  },
  403: {
    type: ErrorType.PERMISSION,
    userMessage: '没有权限访问该资源',
    retryable: false,
    requiresUserAction: true
  },
  404: {
    type: ErrorType.CLIENT,
    userMessage: '请求的资源不存在',
    retryable: false,
    requiresUserAction: false
  },
  408: {
    type: ErrorType.NETWORK,
    userMessage: '请求超时，请稍后重试',
    retryable: true,
    requiresUserAction: false
  },
  409: {
    type: ErrorType.BUSINESS,
    userMessage: '数据冲突，请刷新后重试',
    retryable: true,
    requiresUserAction: true
  },
  422: {
    type: ErrorType.BUSINESS,
    userMessage: '数据验证失败，请检查输入信息',
    retryable: false,
    requiresUserAction: true
  },
  429: {
    type: ErrorType.CLIENT,
    userMessage: '请求过于频繁，请稍后重试',
    retryable: true,
    requiresUserAction: false
  },
  500: {
    type: ErrorType.SERVER,
    userMessage: '服务器内部错误，请稍后重试',
    retryable: true,
    requiresUserAction: false
  },
  502: {
    type: ErrorType.SERVER,
    userMessage: '服务器网关错误，请稍后重试',
    retryable: true,
    requiresUserAction: false
  },
  503: {
    type: ErrorType.SERVER,
    userMessage: '服务暂时不可用，请稍后重试',
    retryable: true,
    requiresUserAction: false
  },
  504: {
    type: ErrorType.NETWORK,
    userMessage: '网络超时，请检查网络连接',
    retryable: true,
    requiresUserAction: false
  }
}

/**
 * 业务错误码映射
 */
const BUSINESS_ERROR_MAP: Record<string, Partial<ErrorInfo>> = {
  'USER_NOT_FOUND': {
    type: ErrorType.BUSINESS,
    userMessage: '用户不存在',
    retryable: false,
    requiresUserAction: true
  },
  'INVALID_CREDENTIALS': {
    type: ErrorType.AUTH,
    userMessage: '用户名或密码错误',
    retryable: false,
    requiresUserAction: true
  },
  'ACCOUNT_LOCKED': {
    type: ErrorType.AUTH,
    userMessage: '账户已被锁定，请联系管理员',
    retryable: false,
    requiresUserAction: true
  },
  'TOKEN_EXPIRED': {
    type: ErrorType.AUTH,
    userMessage: '登录已过期，请重新登录',
    retryable: false,
    requiresUserAction: true
  },
  'INSUFFICIENT_PERMISSIONS': {
    type: ErrorType.PERMISSION,
    userMessage: '权限不足，无法执行此操作',
    retryable: false,
    requiresUserAction: true
  },
  'RESOURCE_NOT_FOUND': {
    type: ErrorType.BUSINESS,
    userMessage: '请求的资源不存在',
    retryable: false,
    requiresUserAction: false
  },
  'DUPLICATE_RESOURCE': {
    type: ErrorType.BUSINESS,
    userMessage: '资源已存在，请勿重复创建',
    retryable: false,
    requiresUserAction: true
  },
  'VALIDATION_FAILED': {
    type: ErrorType.BUSINESS,
    userMessage: '数据验证失败，请检查输入信息',
    retryable: false,
    requiresUserAction: true
  },
  'RATE_LIMIT_EXCEEDED': {
    type: ErrorType.CLIENT,
    userMessage: '操作过于频繁，请稍后重试',
    retryable: true,
    requiresUserAction: false
  },
  'SERVICE_UNAVAILABLE': {
    type: ErrorType.SERVER,
    userMessage: '服务暂时不可用，请稍后重试',
    retryable: true,
    requiresUserAction: false
  }
}

/**
 * API错误处理器类
 */
export class ApiErrorHandler {
  private static instance: ApiErrorHandler
  private defaultConfig: ErrorHandlerConfig = {
    showMessage: true,
    messageType: 'message',
    autoRetry: false,
    retryCount: 3,
    logError: true
  }

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): ApiErrorHandler {
    if (!ApiErrorHandler.instance) {
      ApiErrorHandler.instance = new ApiErrorHandler()
    }
    return ApiErrorHandler.instance
  }

  /**
   * 设置默认配置
   */
  public setDefaultConfig(config: Partial<ErrorHandlerConfig>): void {
    this.defaultConfig = { ...this.defaultConfig, ...config }
  }

  /**
   * 处理错误
   */
  public handleError(
    error: any,
    config?: Partial<ErrorHandlerConfig>
  ): ErrorInfo {
    const finalConfig = { ...this.defaultConfig, ...config }
    const errorInfo = this.parseError(error)

    // 记录错误日志
    if (finalConfig.logError) {
      this.logError(errorInfo, error)
    }

    // 处理特殊错误类型
    this.handleSpecialErrors(errorInfo)

    // 显示错误消息
    if (finalConfig.showMessage) {
      this.showErrorMessage(errorInfo, finalConfig.messageType)
    }

    // 执行自定义错误处理
    if (finalConfig.customHandler) {
      finalConfig.customHandler(errorInfo)
    }

    return errorInfo
  }

  /**
   * 解析错误
   */
  private parseError(error: any): ErrorInfo {
    // Axios错误
    if (error.isAxiosError) {
      return this.parseAxiosError(error as AxiosError)
    }

    // 网络错误
    if (error.code === 'NETWORK_ERROR' || !navigator.onLine) {
      return {
        type: ErrorType.NETWORK,
        code: 'NETWORK_ERROR',
        message: '网络连接失败',
        userMessage: '网络连接失败，请检查网络设置',
        retryable: true,
        requiresUserAction: false
      }
    }

    // 超时错误
    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
      return {
        type: ErrorType.NETWORK,
        code: 'TIMEOUT',
        message: '请求超时',
        userMessage: '请求超时，请稍后重试',
        retryable: true,
        requiresUserAction: false
      }
    }

    // 其他错误
    return {
      type: ErrorType.UNKNOWN,
      code: 'UNKNOWN',
      message: error.message || '未知错误',
      userMessage: '发生未知错误，请稍后重试',
      details: error,
      retryable: false,
      requiresUserAction: false
    }
  }

  /**
   * 解析Axios错误
   */
  private parseAxiosError(error: AxiosError): ErrorInfo {
    const response = error.response as AxiosResponse
    const status = response?.status
    const data = response?.data as any

    // 获取错误代码和消息
    const errorCode = data?.code || data?.errorCode || status || 'UNKNOWN'
    const errorMessage = data?.message || data?.msg || error.message || '请求失败'

    // 从HTTP状态码映射获取错误信息
    const httpErrorInfo = status ? HTTP_ERROR_MAP[status] : undefined

    // 从业务错误码映射获取错误信息
    const businessErrorInfo = typeof errorCode === 'string' 
      ? BUSINESS_ERROR_MAP[errorCode] 
      : undefined

    // 合并错误信息
    const errorInfo: ErrorInfo = {
      type: ErrorType.UNKNOWN,
      code: errorCode,
      message: errorMessage,
      userMessage: '请求失败，请稍后重试',
      details: { response: data, status },
      retryable: false,
      requiresUserAction: false,
      ...httpErrorInfo,
      ...businessErrorInfo
    }

    // 如果有自定义用户消息，使用自定义消息
    if (data?.userMessage) {
      errorInfo.userMessage = data.userMessage
    }

    return errorInfo
  }

  /**
   * 处理特殊错误类型
   */
  private handleSpecialErrors(errorInfo: ErrorInfo): void {
    switch (errorInfo.type) {
      case ErrorType.AUTH:
        this.handleAuthError(errorInfo)
        break
      case ErrorType.PERMISSION:
        this.handlePermissionError(errorInfo)
        break
      case ErrorType.NETWORK:
        this.handleNetworkError(errorInfo)
        break
    }
  }

  /**
   * 处理认证错误
   */
  private handleAuthError(errorInfo: ErrorInfo): void {
    const authStore = useAuthStore()
    
    // 清除认证信息
    authStore.clearAuth()
    
    // 跳转到登录页
    // if (router.currentRoute.value.path !== '/login') {
    //   router.push({
    //     path: '/login',
    //     query: {
    //       redirect: router.currentRoute.value.fullPath
    //     }
    //   })
    // }
    
    // 重新加载页面到登录页
    window.location.href = '/login'
  }

  /**
   * 处理权限错误
   */
  private handlePermissionError(errorInfo: ErrorInfo): void {
    // 可以跳转到无权限页面或显示权限申请页面
  }

  /**
   * 处理网络错误
   */
  private handleNetworkError(errorInfo: ErrorInfo): void {
    // 可以显示网络状态指示器或重试按钮
  }

  /**
   * 显示错误消息
   */
  private showErrorMessage(
    errorInfo: ErrorInfo, 
    messageType: 'message' | 'notification'
  ): void {
    const message = errorInfo.userMessage
    
    // 使用uni-app原生API显示错误消息
    if (messageType === 'notification') {
      // 使用模态框显示重要错误信息
      uni.showModal({
        title: '错误提示',
        content: message,
        showCancel: false,
        confirmText: '确定'
      })
    } else {
      // 使用Toast显示一般错误信息
      uni.showToast({
        title: message,
        icon: 'error',
        duration: 3000,
        mask: true
      })
    }
  }

  /**
   * 记录错误日志
   */
  private logError(errorInfo: ErrorInfo, originalError: any): void {
    const context = {
      type: errorInfo.type,
      code: errorInfo.code,
      userMessage: errorInfo.userMessage,
      url: typeof window !== 'undefined' ? window.location?.href : '',
      userAgent: typeof window !== 'undefined' ? window.navigator?.userAgent : '',
      details: errorInfo.details,
      stack: originalError?.stack
    }

    // 使用统一日志服务记录错误
    logger.error(
      `API错误 [${errorInfo.type}] ${errorInfo.code}: ${errorInfo.message}`,
      originalError,
      context
    )

    // 在生产环境下发送错误日志到服务器
    if ((import.meta as any).env?.PROD) {
      this.sendErrorLog({
        timestamp: new Date().toISOString(),
        ...context,
        message: errorInfo.message
      })
    }
  }

  /**
   * 发送错误日志到服务器（生产环境）
   */
  private async sendErrorLog(logData: any): Promise<void> {
    try {
      // 使用日志服务的远程日志功能
      logger.setRemoteEndpoint('/api/logs/error')
      
      // 或者直接发送到错误日志收集服务
      if (typeof window !== 'undefined' && window.fetch) {
        await window.fetch('/api/logs/error', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(logData)
        })
      }
    } catch (sendError) {
      // 使用日志服务记录发送失败的错误
      logger.warn('发送错误日志到服务器失败', sendError)
    }
  }

  /**
   * 检查错误是否可重试
   */
  public isRetryable(error: any): boolean {
    const errorInfo = this.parseError(error)
    return errorInfo.retryable
  }

  /**
   * 获取错误的用户友好消息
   */
  public getUserMessage(error: any): string {
    const errorInfo = this.parseError(error)
    return errorInfo.userMessage
  }

  /**
   * 创建错误处理中间件
   */
  public createMiddleware(config?: Partial<ErrorHandlerConfig>) {
    return (error: any) => {
      return this.handleError(error, config)
    }
  }
}

// 导出单例实例
export const apiErrorHandler = ApiErrorHandler.getInstance()

// 导出便捷方法
export const {
  handleError,
  isRetryable,
  getUserMessage,
  createMiddleware
} = apiErrorHandler