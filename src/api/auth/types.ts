// 表单数据类型
export interface RealAuthFormData {
  name: string;
  idCard: string;
  phone: string;
}

// 认证结果类型
export interface AuthResult {
  success: boolean;
  message?: string;
  data?: any;
}

// 认证服务提供商类型
export enum AuthProvider {
  ALIYUN = 'aliyun',
  TENCENT = 'tencent',
  BAIDU = 'baidu'
}

// 认证请求参数
export interface AuthRequestParams {
  name: string;
  idCard: string;
  timestamp: number;
} 