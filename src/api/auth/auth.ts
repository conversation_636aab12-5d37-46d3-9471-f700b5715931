/**
 * 认证相关接口
 */
import { http } from "@/http";
import type { ResponseData } from '@/types/request'
import type { AuthRequestParams } from './types';

// 登录参数类型
export interface LoginParams {
  phonenumber: string
  password?: string
  smsCode?: string
  loginGrantType: 'password' | 'sms'
  // 验证码相关参数
  captchaType?: string
  captchaVerification?: string
}

// 注册参数类型
export interface RegisterParams {
  phonenumber: string
  password: string
  confirmPassword: string
  smsCode: string
  // 验证码相关参数
  captchaType?: string
  captchaVerification?: string
}

// 用户信息类型
export interface UserInfo {
  id: string
  phonenumber: string
  nickname?: string
  avatar?: string
  // ... 其他用户字段
}

// 登录响应类型（根据后端接口文档更新）
export interface LoginResponse {
  scope?: string | null
  openid?: string | null
  access_token: string
  refresh_token?: string | null
  expire_in: number
  refresh_expire_in: number
  client_id?: string | null
  userInfo?: UserInfo
}

/**
 * 密码登录
 */
export const passwordLogin = (params: LoginParams) => {
  return http.post<LoginResponse>('/oto/auth/login', params)
}

/**
 * 短信登录
 */
export const smsLogin = (params: LoginParams) => {
  return http.post<LoginResponse>('/oto/auth/login', params)
}

/**
 * 用户注册
 */
export const register = (params: RegisterParams) => {
  return http.post<LoginResponse>('/oto/auth/register', params)
}

/**
 * 获取短信验证码
 */
export const getSmsCode = (params: {
  phonenumber: string
  type: 'login' | 'register'
  // 验证码相关参数
  captchaType?: string
  captchaVerification?: string
}) => {
  return http.post<{ message: string }>('/oto/captcha-api/sms/getCode', params, {
    // 禁用自动错误处理，让业务代码处理频率限制等错误
    errorHandle: {
      autoHandleError: false
    }
  })
}

/**
 * 退出登录
 */
export const logout = () => {
  return http.post<void>('/oto/auth/logout')
}

/**
 * 刷新Token（支持跨平台双Token模式）
 * App/小程序：通过请求体传递refreshToken
 * H5：通过HttpOnly Cookie自动携带refresh_token
 */
export const refreshToken = (params?: { refreshToken?: string }) => {
  return http.post<{
    access_token: string
    refresh_token: string
    expire_in: number
    refresh_expire_in: number
  }>('/member/auth/refresh', params)
}

/**
 * 提交实名认证
 * @param params 认证请求参数
 * @returns Promise<AuthResult>
 */
export const submitRealAuth = (params: AuthRequestParams) => {
  return http.post('/auth/real', params);
};

/**
 * 获取认证状态
 * @returns Promise<{status: number, message: string}>
 */
export const getAuthStatus = () => {
  return http.get('/auth/status');
};
