/**
 * API 请求初始化文件
 * 统一导出 http 工具类和各模块 API
 */

// 导出 http 工具类
export { http } from '@/http/index'

// 导出核心API管理器
export { apiManager } from './manager'
export { apiErrorHandler } from './errorHandler'
export { apiCacheManager } from './cache'
export { apiConfigManager } from './config'

// 导出各模块 API
export * from './modules/captcha'
export * from './modules/member'
// export * from './modules/user' // 已删除重复文件

// 导出 API 类型定义
export * from './types'

// 注意：拦截器已统一到 /src/http/interceptors.ts 中管理
// 不再需要单独的API拦截器初始化
