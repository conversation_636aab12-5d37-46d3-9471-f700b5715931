/**
 * 会员相关API
 */
import { http } from '@/http/http';
// 移除不需要的导入

// 用户名和性别设置参数接口
export interface SetupProfileParams {
  sex: 'M' | 'U';  // M男 U女
  userName: string;
}

// 基础资料参数接口
export interface BaseInfoParams {
  birthDay: string;
  height: number;
  weight: number;
  education: string;
  marriage: number;  // 婚姻状况 1未婚 2离异 3丧偶
  career: string;    // 职业行业
  nativePlace: string;  // 籍贯
  nowPlace: string;     // 现居住地
}

// 职业列表响应接口
export interface CareerListResponse {
  carrers: string[];
}

// 地址列表请求参数接口
export interface AddressListParams {
  queryLevel: number;  // 查询级别：1省份 2城市 3区县
  province?: string;   // 省份名称（查询城市和区县时必传）
  city?: string;       // 城市名称（查询区县时必传）
}

// 地址列表响应接口
export interface AddressListResponse {
  code: number;
  msg: string;
  data: string[];
}

// 扩展信息参数接口
export interface ExtendInfoParams {
  hasCar?: number;
  hasHouse?: number;
  company?: string;
  yearAnnualIncome?: string;
  childrenStatus?: number;
  hobbies?: string;
}

/**
 * 设置用户名和性别
 * @param data 用户名和性别参数
 * @returns Promise
 */
export function editSexAndUserName(data: SetupProfileParams) {
  return http.post('/api/v1/editSexAndUserName', data);
}

/**
 * 获取职业列表
 * @returns Promise<CareerListResponse>
 */
export function getCareerList() {
  return http.get<CareerListResponse>('/api/v1/config/getCarrers');
}

/**
 * 编辑会员基础资料
 * @param data 基础资料参数
 * @returns Promise
 */
export function editBaseInfo(data: BaseInfoParams) {
  return http.post('/api/v1/edit', data);
}

/**
 * 获取会员基础资料
 * @returns Promise
 */
export function getBaseInfo() {
  return http.get('/api/v1/baseInfo');
}

/**
 * 获取地址列表
 * @param params 查询参数
 * @returns Promise<AddressListResponse>
 */
export function getAddressList(params: AddressListParams) {
  return http.post<AddressListResponse>('/api/v1/config/getAddrs', params);
}

/**
 * 编辑会员扩展信息
 * @param data 扩展信息参数
 * @returns Promise
 */
export function editExtendInfo(data: ExtendInfoParams) {
  return http.post('/member/ExtendInfo/edit', data);
}
