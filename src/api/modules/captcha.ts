/**
 * 验证码服务封装
 * 统一的验证码接口和状态管理
 */

import { ref } from 'vue'
import { http } from '@/http/http'
import type { ResponseData } from '@/types/request'

// 验证码类型
export type CaptchaType = 'blockPuzzle' | 'clickWord'

// 验证码获取响应
export interface CaptchaResponse {
  token: string
  originalImageBase64: string
  jigsawImageBase64?: string // 滑块验证专用
  wordTips?: string // 点选验证专用
  secretKey: string
}

// 验证码校验请求参数
export interface CaptchaCheckParams {
  captchaType: CaptchaType
  pointJson: string // 加密后的坐标信息
  token: string
}

// 验证码校验响应
export interface CaptchaCheckResponse {
  result: boolean
  captchaVerification?: string // 二次验证参数
}

/**
 * 获取滑块拼图验证码
 */
export const getPuzzleCaptcha = () => {
  return http.post<CaptchaResponse>('/oto/captcha-api/captcha/get', {
    captchaType: 'blockPuzzle'
  })
}

/**
 * 获取文字点选验证码
 */
export const getWordCaptcha = () => {
  return http.post<CaptchaResponse>('/oto/captcha-api/captcha/get', {
    captchaType: 'clickWord'
  })
}

/**
 * 校验滑块拼图验证码
 */
export const checkPuzzleCaptcha = (params: CaptchaCheckParams) => {
  return http.post<CaptchaCheckResponse>('/oto/captcha-api/captcha/check', params)
}

/**
 * 校验文字点选验证码
 */
export const checkWordCaptcha = (params: CaptchaCheckParams) => {
  return http.post<CaptchaCheckResponse>('/oto/captcha-api/captcha/check', params)
}

/**
 * 通用验证码获取方法
 * @param type 验证码类型
 */
export const getCaptcha = (type: CaptchaType) => {
  return http.post<CaptchaResponse>('/oto/captcha-api/captcha/get', {
    captchaType: type
  })
}

/**
 * 通用验证码校验方法
 * @param params 校验参数
 */
export const checkCaptcha = (params: CaptchaCheckParams) => {
  return http.post<CaptchaCheckResponse>('/oto/captcha-api/captcha/check', params)
}

// ==================== 状态管理相关 ====================

// 兼容性接口定义
export interface CaptchaVerifyData {
  captchaType: string;
  captchaVerification: string;
  token?: string;
  pointJson?: string;
}

export interface CaptchaOptions {
  captchaType?: 'blockPuzzle' | 'clickWord';
  onSuccess?: (data: CaptchaVerifyData) => void;
  onError?: () => void;
  phoneNumber?: string;
  enableAdvancedVerification?: boolean;
}

/**
 * 验证码状态管理服务
 * 提供验证码的状态管理和交互功能
 */
export function useCaptchaService(options: CaptchaOptions = {}) {
  // 临时兼容性实现
  const captchaVerified = ref(false);
  const captchaVerifyData = ref<CaptchaVerifyData | null>(null);
  let verifyCaptcha: any = null;

  return {
    captchaVerified,
    captchaVerifyData,
    set verifyCaptcha(ref: any) {
      verifyCaptcha = ref;
    },
    get verifyCaptcha() {
      return verifyCaptcha;
    },
    showVerifyCaptcha: () => {
      if (verifyCaptcha?.value?.show) {
        verifyCaptcha.value.show();
      }
    },
    resetCaptcha: () => {
      captchaVerified.value = false;
      captchaVerifyData.value = null;
    },
    clearCaptchaData: () => {
      captchaVerified.value = false;
      captchaVerifyData.value = null;
    },
    verifyCaptchaSuccess: (params: any) => {
      captchaVerified.value = true;
      captchaVerifyData.value = {
        captchaType: options.captchaType || 'blockPuzzle',
        captchaVerification: params.captchaVerification || `${params.token}_${Date.now()}`
      };
      if (options.onSuccess) {
        options.onSuccess(captchaVerifyData.value);
      }
    },
    verifyCaptchaError: () => {
      captchaVerified.value = false;
      captchaVerifyData.value = null;
      if (options.onError) {
        options.onError();
      }
    },
    getCaptchaFormData: () => {
      return captchaVerifyData.value;
    },
    updateOptions: (newOptions: Partial<CaptchaOptions>) => {
      Object.assign(options, newOptions);
    },
    destroy: () => {
      captchaVerified.value = false;
      captchaVerifyData.value = null;
      verifyCaptcha = null;
    }
  };
}
