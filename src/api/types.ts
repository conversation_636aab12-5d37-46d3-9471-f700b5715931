/**
 * API类型定义文件
 */

// 导入统一的响应数据类型
import type { ResponseData } from '@/types/request';

// API响应基础接口 (使用统一的ResponseData类型)
export type ApiResponse<T = any> = ResponseData<T>;

// 登录响应数据
export interface LoginData {
  scope: string | null;
  openid: string | null;
  access_token: string;
  refresh_token: string | null;
  expire_in: number;
  refresh_expire_in: number | null;
  client_id: string | null;
}

// 登录请求参数
export interface LoginRequest {
  loginGrantType: 'password' | 'sms';
  phonenumber: string;
  password?: string;
  code?: string;
}

// 注册请求参数
export interface RegisterRequest {
  phonenumber: string;
  smsCode: string;
  password: string;
}