/**
 * API 配置管理器
 * 提供统一的API配置和环境管理功能
 */

import { StoreUtil } from '@/utils/storage/StoreUtil'
import { StorageModule } from '@/utils/storage/storageService'
import { createLogger } from '../utils/logger'

// 创建API配置管理器专用日志器
const logger = createLogger('ApiConfigManager')

/**
 * 环境类型枚举
 */
export enum Environment {
  /** 开发环境 */
  DEVELOPMENT = 'development',
  /** 测试环境 */
  TESTING = 'testing',
  /** 预发布环境 */
  STAGING = 'staging',
  /** 生产环境 */
  PRODUCTION = 'production'
}

/**
 * API端点配置接口
 */
export interface ApiEndpoint {
  /** 基础URL */
  baseURL: string
  /** 超时时间（毫秒） */
  timeout: number
  /** 是否启用HTTPS */
  https: boolean
  /** API版本 */
  version: string
  /** 端点描述 */
  description?: string
}

/**
 * 环境配置接口
 */
export interface EnvironmentConfig {
  /** 环境名称 */
  name: Environment
  /** API端点配置 */
  api: ApiEndpoint
  /** 是否启用调试模式 */
  debug: boolean
  /** 是否启用日志 */
  logging: boolean
  /** 是否启用缓存 */
  cache: boolean
  /** 是否启用重试 */
  retry: boolean
  /** 最大重试次数 */
  maxRetries: number
  /** 重试延迟（毫秒） */
  retryDelay: number
}

/**
 * API配置接口
 */
export interface ApiConfig {
  /** 当前环境 */
  currentEnvironment: Environment
  /** 环境配置映射 */
  environments: Record<Environment, EnvironmentConfig>
  /** 全局配置 */
  global: {
    /** 默认请求头 */
    defaultHeaders: Record<string, string>
    /** 认证配置 */
    auth: {
      /** Token键名 */
      tokenKey: string
      /** Token前缀 */
      tokenPrefix: string
      /** 刷新Token键名 */
      refreshTokenKey: string
    }
    /** 设备配置 */
    device: {
      /** 设备ID键名 */
      deviceIdKey: string
      /** 平台标识 */
      platform: string
    }
  }
}

/**
 * 默认环境配置
 */
const DEFAULT_ENVIRONMENTS: Record<Environment, EnvironmentConfig> = {
  [Environment.DEVELOPMENT]: {
    name: Environment.DEVELOPMENT,
    api: {
      baseURL: 'http://localhost:3000/api',
      timeout: 10000,
      https: false,
      version: 'v1',
      description: '开发环境API'
    },
    debug: true,
    logging: true,
    cache: false,
    retry: true,
    maxRetries: 2,
    retryDelay: 1000
  },
  [Environment.TESTING]: {
    name: Environment.TESTING,
    api: {
      baseURL: 'https://test-api.example.com/api',
      timeout: 15000,
      https: true,
      version: 'v1',
      description: '测试环境API'
    },
    debug: true,
    logging: true,
    cache: true,
    retry: true,
    maxRetries: 3,
    retryDelay: 1500
  },
  [Environment.STAGING]: {
    name: Environment.STAGING,
    api: {
      baseURL: 'https://staging-api.example.com/api',
      timeout: 20000,
      https: true,
      version: 'v1',
      description: '预发布环境API'
    },
    debug: false,
    logging: true,
    cache: true,
    retry: true,
    maxRetries: 3,
    retryDelay: 2000
  },
  [Environment.PRODUCTION]: {
    name: Environment.PRODUCTION,
    api: {
      baseURL: 'https://api.example.com/api',
      timeout: 30000,
      https: true,
      version: 'v1',
      description: '生产环境API'
    },
    debug: false,
    logging: false,
    cache: true,
    retry: true,
    maxRetries: 5,
    retryDelay: 3000
  }
}

/**
 * 默认API配置
 */
const DEFAULT_CONFIG: ApiConfig = {
  currentEnvironment: Environment.DEVELOPMENT,
  environments: DEFAULT_ENVIRONMENTS,
  global: {
    defaultHeaders: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    },
    auth: {
      tokenKey: 'accessToken',
      tokenPrefix: 'Bearer',
      refreshTokenKey: 'refreshToken'
    },
    device: {
      deviceIdKey: 'deviceId',
      platform: 'uniapp'
    }
  }
}

/**
 * API配置管理器类
 */
export class ApiConfigManager {
  private static instance: ApiConfigManager
  private config: ApiConfig = DEFAULT_CONFIG
  private readonly CONFIG_KEY = 'api_config'

  /**
   * 获取单例实例
   */
  static getInstance(): ApiConfigManager {
    if (!ApiConfigManager.instance) {
      ApiConfigManager.instance = new ApiConfigManager()
    }
    return ApiConfigManager.instance
  }

  private constructor() {
    this.loadConfig()
  }

  /**
   * 加载配置
   */
  private async loadConfig(): Promise<void> {
    try {
      const savedConfig = await StoreUtil.get<ApiConfig>(
        this.CONFIG_KEY,
        StorageModule.CONFIG
      )
      
      if (savedConfig) {
        this.config = { ...DEFAULT_CONFIG, ...savedConfig }
      }
    } catch (_error) {
      this.config = DEFAULT_CONFIG
    }
  }

  /**
   * 保存配置
   */
  private async saveConfig(): Promise<boolean> {
    try {
      return await StoreUtil.set(
        this.CONFIG_KEY,
        this.config,
        StorageModule.CONFIG
      )
    } catch (error) {
      // console.error('[ApiConfigManager] 保存配置失败:', error)
      return false
    }
  }

  /**
   * 获取当前配置
   */
  getConfig(): ApiConfig {
    return { ...this.config }
  }

  /**
   * 获取当前环境配置
   */
  getCurrentEnvironmentConfig(): EnvironmentConfig {
    return this.config.environments[this.config.currentEnvironment]
  }

  /**
   * 获取当前API端点配置
   */
  getCurrentApiEndpoint(): ApiEndpoint {
    return this.getCurrentEnvironmentConfig().api
  }

  /**
   * 设置当前环境
   */
  async setEnvironment(environment: Environment): Promise<boolean> {
    try {
      this.config.currentEnvironment = environment
      return await this.saveConfig()
    } catch (error) {
      // console.error('[ApiConfigManager] 设置环境失败:', error)
      return false
    }
  }

  /**
   * 更新环境配置
   */
  async updateEnvironmentConfig(
    environment: Environment,
    config: Partial<EnvironmentConfig>
  ): Promise<boolean> {
    try {
      this.config.environments[environment] = {
        ...this.config.environments[environment],
        ...config
      }
      return await this.saveConfig()
    } catch (error) {
      // console.error('[ApiConfigManager] 更新环境配置失败:', error)
      return false
    }
  }

  /**
   * 更新全局配置
   */
  async updateGlobalConfig(config: Partial<ApiConfig['global']>): Promise<boolean> {
    try {
      this.config.global = {
        ...this.config.global,
        ...config
      }
      return await this.saveConfig()
    } catch (error) {
      logger.error('更新全局配置失败', { error, config })
      return false
    }
  }

  /**
   * 重置配置为默认值
   */
  async resetConfig(): Promise<boolean> {
    try {
      this.config = { ...DEFAULT_CONFIG }
      return await this.saveConfig()
    } catch (_error) {
      // console.error('[ApiConfigManager] 重置配置失败:', _error)
      return false
    }
  }

  /**
   * 获取环境列表
   */
  getEnvironments(): Environment[] {
    return Object.values(Environment)
  }

  /**
   * 检查环境是否存在
   */
  hasEnvironment(environment: Environment): boolean {
    return environment in this.config.environments
  }

  /**
   * 获取基础URL
   */
  getBaseURL(environment?: Environment): string {
    const env = environment || this.config.currentEnvironment
    return this.config.environments[env].api.baseURL
  }

  /**
   * 获取超时时间
   */
  getTimeout(environment?: Environment): number {
    const env = environment || this.config.currentEnvironment
    return this.config.environments[env].api.timeout
  }

  /**
   * 是否启用调试模式
   */
  isDebugEnabled(environment?: Environment): boolean {
    const env = environment || this.config.currentEnvironment
    return this.config.environments[env].debug
  }

  /**
   * 是否启用日志
   */
  isLoggingEnabled(environment?: Environment): boolean {
    const env = environment || this.config.currentEnvironment
    return this.config.environments[env].logging
  }

  /**
   * 是否启用缓存
   */
  isCacheEnabled(environment?: Environment): boolean {
    const env = environment || this.config.currentEnvironment
    return this.config.environments[env].cache
  }

  /**
   * 是否启用重试
   */
  isRetryEnabled(environment?: Environment): boolean {
    const env = environment || this.config.currentEnvironment
    return this.config.environments[env].retry
  }

  /**
   * 获取最大重试次数
   */
  getMaxRetries(environment?: Environment): number {
    const env = environment || this.config.currentEnvironment
    return this.config.environments[env].maxRetries
  }

  /**
   * 获取重试延迟
   */
  getRetryDelay(environment?: Environment): number {
    const env = environment || this.config.currentEnvironment
    return this.config.environments[env].retryDelay
  }

  /**
   * 获取默认请求头
   */
  getDefaultHeaders(): Record<string, string> {
    return { ...this.config.global.defaultHeaders }
  }

  /**
   * 获取认证配置
   */
  getAuthConfig(): ApiConfig['global']['auth'] {
    return { ...this.config.global.auth }
  }

  /**
   * 获取设备配置
   */
  getDeviceConfig(): ApiConfig['global']['device'] {
    return { ...this.config.global.device }
  }

  /**
   * 导出配置
   */
  exportConfig(): string {
    return JSON.stringify(this.config, null, 2)
  }

  /**
   * 导入配置
   */
  async importConfig(configJson: string): Promise<boolean> {
    try {
      const importedConfig = JSON.parse(configJson) as ApiConfig
      
      // 验证配置格式
      if (!this.validateConfig(importedConfig)) {
        throw new Error('配置格式无效')
      }
      
      this.config = importedConfig
      return await this.saveConfig()
    } catch (error) {
      logger.error('导入配置失败', { error, configJson })
      return false
    }
  }

  /**
   * 验证配置格式
   */
  private validateConfig(config: any): config is ApiConfig {
    try {
      return (
        config &&
        typeof config === 'object' &&
        'currentEnvironment' in config &&
        'environments' in config &&
        'global' in config &&
        Object.values(Environment).includes(config.currentEnvironment)
      )
    } catch {
      return false
    }
  }
}

// 导出单例实例
export const apiConfigManager = ApiConfigManager.getInstance()

// 导出便捷方法
export const {
  getConfig,
  getCurrentEnvironmentConfig,
  getCurrentApiEndpoint,
  setEnvironment,
  updateEnvironmentConfig,
  updateGlobalConfig,
  resetConfig,
  getEnvironments,
  hasEnvironment,
  getBaseURL,
  getTimeout,
  isDebugEnabled,
  isLoggingEnabled,
  isCacheEnabled,
  isRetryEnabled,
  getMaxRetries,
  getRetryDelay,
  getDefaultHeaders,
  getAuthConfig,
  getDeviceConfig,
  exportConfig,
  importConfig
} = apiConfigManager

// 导出默认实例
export default apiConfigManager