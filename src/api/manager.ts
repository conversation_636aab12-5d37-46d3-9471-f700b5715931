/**
 * API管理器
 * 提供统一的API调用、错误处理、缓存和重试机制
 */

import { http } from '@/http'
import type { ResponseData } from '@/types/request'
import type { 
  PaginationParams, 
  PaginationResponse, 
  SortParams, 
  SearchParams 
} from './types/common'
import { createLogger } from '../utils/logger'

// 创建API管理器专用日志器
const logger = createLogger('ApiManager')

/**
 * API请求配置
 */
export interface ApiRequestConfig {
  /** 是否启用缓存 */
  cache?: boolean
  /** 缓存时间（毫秒） */
  cacheTTL?: number
  /** 是否启用重试 */
  retry?: boolean
  /** 重试次数 */
  retryCount?: number
  /** 重试延迟（毫秒） */
  retryDelay?: number
  /** 是否显示加载状态 */
  showLoading?: boolean
  /** 是否自动处理错误 */
  autoHandleError?: boolean
  /** 请求超时时间（毫秒） */
  timeout?: number
  /** 自定义错误处理 */
  onError?: (error: any) => void
  /** 请求前钩子 */
  beforeRequest?: () => void
  /** 响应后钩子 */
  afterResponse?: (response: any) => void
}

/**
 * API响应结果
 */
export interface ApiResult<T = any> {
  /** 是否成功 */
  success: boolean
  /** 响应数据 */
  data?: T
  /** 错误信息 */
  error?: string
  /** 错误代码 */
  errorCode?: string | number
  /** 原始响应 */
  raw?: ResponseData<T>
}

/**
 * 缓存项
 */
interface CacheItem<T = any> {
  data: T
  timestamp: number
  ttl: number
}

/**
 * API管理器类
 */
export class ApiManager {
  private static instance: ApiManager
  private cache = new Map<string, CacheItem>()
  private pendingRequests = new Map<string, Promise<any>>()
  
  // 默认配置
  private defaultConfig: ApiRequestConfig = {
    cache: false,
    cacheTTL: 5 * 60 * 1000, // 5分钟
    retry: true,
    retryCount: 3,
    retryDelay: 1000,
    showLoading: true,
    autoHandleError: true,
    timeout: 10000
  }

  private constructor() {
    this.setupCacheCleanup()
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): ApiManager {
    if (!ApiManager.instance) {
      ApiManager.instance = new ApiManager()
    }
    return ApiManager.instance
  }

  /**
   * 设置默认配置
   */
  public setDefaultConfig(config: Partial<ApiRequestConfig>): void {
    this.defaultConfig = { ...this.defaultConfig, ...config }
  }

  /**
   * GET请求
   */
  public async get<T = unknown>(
    url: string, 
    params?: Record<string, string | number | boolean | null | undefined>, 
    config?: ApiRequestConfig
  ): Promise<ApiResult<T>> {
    return this.request<T>('GET', url, undefined, params, config)
  }

  /**
   * POST请求
   */
  public async post<T = unknown>(
    url: string, 
    data?: Record<string, unknown> | FormData | string | null, 
    config?: ApiRequestConfig
  ): Promise<ApiResult<T>> {
    return this.request<T>('POST', url, data, undefined, config)
  }

  /**
   * PUT请求
   */
  public async put<T = unknown>(
    url: string, 
    data?: Record<string, unknown> | FormData | string | null, 
    config?: ApiRequestConfig
  ): Promise<ApiResult<T>> {
    return this.request<T>('PUT', url, data, undefined, config)
  }

  /**
   * DELETE请求
   */
  public async delete<T = unknown>(
    url: string, 
    params?: Record<string, string | number | boolean | null | undefined>, 
    config?: ApiRequestConfig
  ): Promise<ApiResult<T>> {
    return this.request<T>('DELETE', url, undefined, params, config)
  }

  /**
   * 分页请求
   */
  public async paginate<T = unknown>(
    url: string,
    pagination: PaginationParams,
    params?: Record<string, string | number | boolean | null | undefined>,
    config?: ApiRequestConfig
  ): Promise<ApiResult<PaginationResponse<T>>> {
    const requestParams = {
      ...params,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    }
    
    return this.get<PaginationResponse<T>>(url, requestParams, config)
  }

  /**
   * 搜索请求
   */
  public async search<T = any>(
    url: string,
    searchParams: SearchParams,
    pagination?: PaginationParams,
    sort?: SortParams,
    config?: ApiRequestConfig
  ): Promise<ApiResult<PaginationResponse<T>>> {
    const requestParams = {
      ...searchParams,
      ...pagination,
      ...sort
    }
    
    return this.get<PaginationResponse<T>>(url, requestParams, config)
  }

  /**
   * 批量请求
   */
  public async batch<T = any>(
    requests: Array<{
      method: 'GET' | 'POST' | 'PUT' | 'DELETE'
      url: string
      data?: any
      params?: Record<string, any>
      config?: ApiRequestConfig
    }>
  ): Promise<ApiResult<T>[]> {
    const promises = requests.map(req => 
      this.request<T>(req.method, req.url, req.data, req.params, req.config)
    )
    
    try {
      const results = await Promise.allSettled(promises)
      return results.map(result => 
        result.status === 'fulfilled' 
          ? result.value 
          : { success: false, error: '请求失败' }
      )
    } catch (error) {
      logger.error('批量请求失败', { error, requestCount: requests.length })
      return requests.map(() => ({ success: false, error: '批量请求失败' }))
    }
  }

  /**
   * 上传文件
   */
  public async upload<T = any>(
    url: string,
    file: File | Blob,
    params?: Record<string, any>,
    config?: ApiRequestConfig & {
      onProgress?: (progress: number) => void
      fieldName?: string
    }
  ): Promise<ApiResult<T>> {
    const formData = new FormData()
    formData.append(config?.fieldName || 'file', file)
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        formData.append(key, String(value))
      })
    }

    return this.request<T>('POST', url, formData, undefined, {
      ...config,
      cache: false // 上传请求不缓存
    })
  }

  /**
   * 下载文件
   */
  public async download(
    url: string,
    params?: Record<string, any>,
    config?: ApiRequestConfig & {
      filename?: string
      onProgress?: (progress: number) => void
    }
  ): Promise<ApiResult<Blob>> {
    try {
      const response = await http.get(url, {
        params,
        responseType: 'blob',
        timeout: config?.timeout || 30000,
        onDownloadProgress: (progressEvent: any) => {
          if (config?.onProgress && progressEvent.total) {
            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
            config.onProgress(progress)
          }
        }
      })

      // 自动下载文件
      if (config?.filename) {
        const blob = response.data
        const downloadUrl = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = config.filename
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(downloadUrl)
      }

      return {
        success: true,
        data: response.data,
        raw: response
      }
    } catch (error) {
      logger.error('文件下载失败', { error, url })
      return {
        success: false,
        error: '文件下载失败'
      }
    }
  }

  /**
   * 清除缓存
   */
  public clearCache(pattern?: string): void {
    if (pattern) {
      const regex = new RegExp(pattern)
      for (const [key] of this.cache) {
        if (regex.test(key)) {
          this.cache.delete(key)
        }
      }
    } else {
      this.cache.clear()
    }
  }

  /**
   * 取消请求
   */
  public cancelRequest(url: string): void {
    const key = this.generateCacheKey('GET', url)
    this.pendingRequests.delete(key)
  }

  /**
   * 核心请求方法
   */
  private async request<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    url: string,
    data?: any,
    params?: Record<string, any>,
    config?: ApiRequestConfig
  ): Promise<ApiResult<T>> {
    const finalConfig = { ...this.defaultConfig, ...config }
    const cacheKey = this.generateCacheKey(method, url, data, params)

    try {
      // 执行请求前钩子
      if (finalConfig.beforeRequest) {
        finalConfig.beforeRequest()
      }

      // 检查缓存
      if (finalConfig.cache && method === 'GET') {
        const cached = this.getFromCache<T>(cacheKey)
        if (cached) {
          return {
            success: true,
            data: cached
          }
        }
      }

      // 检查是否有相同的请求正在进行
      if (this.pendingRequests.has(cacheKey)) {
        const result = await this.pendingRequests.get(cacheKey)
        return result
      }

      // 创建请求Promise
      const requestPromise = this.executeRequest<T>(method, url, data, params, finalConfig)
      this.pendingRequests.set(cacheKey, requestPromise)

      const result = await requestPromise

      // 清除pending请求
      this.pendingRequests.delete(cacheKey)

      // 缓存成功的GET请求结果
      if (result.success && finalConfig.cache && method === 'GET' && result.data) {
        this.setCache(cacheKey, result.data, finalConfig.cacheTTL!)
      }

      // 执行响应后钩子
      if (finalConfig.afterResponse) {
        finalConfig.afterResponse(result)
      }

      return result
    } catch (error) {
      this.pendingRequests.delete(cacheKey)
      
      if (finalConfig.onError) {
        finalConfig.onError(error)
      }

      // console.error(`[ApiManager] ${method} ${url} 请求失败:`, error)
      
      return {
        success: false,
        error: error instanceof Error ? error.message : '请求失败'
      }
    }
  }

  /**
   * 执行HTTP请求
   */
  private async executeRequest<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    url: string,
    data?: any,
    params?: Record<string, any>,
    config?: ApiRequestConfig
  ): Promise<ApiResult<T>> {
    let attempt = 0
    const maxAttempts = config?.retry ? (config.retryCount || 3) + 1 : 1

    while (attempt < maxAttempts) {
      try {
        let response: ResponseData<T>

        switch (method) {
          case 'GET':
            response = await http.get<T>(url, { params, timeout: config?.timeout })
            break
          case 'POST':
            response = await http.post<T>(url, data, { timeout: config?.timeout })
            break
          case 'PUT':
            response = await http.put<T>(url, data, { timeout: config?.timeout })
            break
          case 'DELETE':
            response = await http.delete<T>(url, { params, timeout: config?.timeout })
            break
          default:
            throw new Error(`不支持的请求方法: ${method}`)
        }

        return {
          success: true,
          data: response.data,
          raw: response
        }
      } catch (error) {
        attempt++
        
        if (attempt >= maxAttempts) {
          throw error
        }

        // 等待重试延迟
        if (config?.retryDelay) {
          await new Promise(resolve => setTimeout(resolve, config.retryDelay))
        }

        // console.warn(`[ApiManager] ${method} ${url} 第${attempt}次重试...`)
      }
    }

    throw new Error('请求重试次数已用完')
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(
    method: string,
    url: string,
    data?: any,
    params?: Record<string, any>
  ): string {
    const key = `${method}:${url}`
    const queryString = params ? JSON.stringify(params) : ''
    const bodyString = data ? JSON.stringify(data) : ''
    return `${key}:${queryString}:${bodyString}`
  }

  /**
   * 从缓存获取数据
   */
  private getFromCache<T>(key: string): T | null {
    const item = this.cache.get(key)
    if (!item) {
      return null
    }

    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }

    return item.data
  }

  /**
   * 设置缓存
   */
  private setCache<T>(key: string, data: T, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  /**
   * 设置缓存清理定时器
   */
  private setupCacheCleanup(): void {
    setInterval(() => {
      const now = Date.now()
      for (const [key, item] of this.cache) {
        if (now - item.timestamp > item.ttl) {
          this.cache.delete(key)
        }
      }
    }, 5 * 60 * 1000) // 每5分钟清理一次过期缓存
  }
}

// 导出单例实例
export const apiManager = ApiManager.getInstance()

// 导出便捷方法
export const {
  get: apiGet,
  post: apiPost,
  put: apiPut,
  delete: apiDelete,
  paginate: apiPaginate,
  search: apiSearch,
  batch: apiBatch,
  upload: apiUpload,
  download: apiDownload
} = apiManager