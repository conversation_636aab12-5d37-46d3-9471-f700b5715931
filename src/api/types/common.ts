/**
 * 通用API类型定义
 */

// 分页参数
export interface PaginationParams {
  pageNum: number
  pageSize: number
}

// 分页响应
export interface PaginationResponse<T = any> {
  list: T[]
  total: number
  pageNum: number
  pageSize: number
  totalPages: number
}

// 排序参数
export interface SortParams {
  sortField: string
  sortOrder: 'asc' | 'desc'
}

// 搜索参数
export interface SearchParams {
  keyword?: string
  startTime?: string
  endTime?: string
} 