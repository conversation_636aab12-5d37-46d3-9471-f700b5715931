/**
 * API 缓存管理器
 * 提供统一的API缓存策略和管理功能
 */

import { StoreUtil } from '@/utils/storage/StoreUtil'
import { StorageModule, StorageType } from '@/utils/storage/storageService'
import { createLogger } from '../utils/logger'

// 创建API缓存管理器专用日志器
const logger = createLogger('ApiCacheManager')

/**
 * 缓存策略枚举
 */
export enum CacheStrategy {
  /** 无缓存 */
  NONE = 'none',
  /** 内存缓存 */
  MEMORY = 'memory',
  /** 本地存储缓存 */
  LOCAL = 'local',
  /** 缓存存储缓存 */
  CACHE_STORAGE = 'cache'
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  /** 缓存策略 */
  strategy: CacheStrategy
  /** 缓存时间（秒），0表示永不过期 */
  ttl: number
  /** 缓存键前缀 */
  keyPrefix?: string
  /** 是否启用版本控制 */
  enableVersioning?: boolean
  /** 缓存版本 */
  version?: string
}

/**
 * 缓存项接口
 */
export interface CacheItem<T = any> {
  /** 缓存数据 */
  data: T
  /** 创建时间戳 */
  timestamp: number
  /** 过期时间戳 */
  expireAt: number
  /** 缓存版本 */
  version?: string
  /** 请求参数哈希 */
  paramsHash?: string
}

/**
 * API缓存管理器类
 */
export class ApiCacheManager {
  private static instance: ApiCacheManager
  private readonly defaultConfig: CacheConfig = {
    strategy: CacheStrategy.MEMORY,
    ttl: 300, // 5分钟
    keyPrefix: 'api_cache',
    enableVersioning: true,
    version: '1.0.0'
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ApiCacheManager {
    if (!ApiCacheManager.instance) {
      ApiCacheManager.instance = new ApiCacheManager()
    }
    return ApiCacheManager.instance
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(
    url: string, 
    params?: Record<string, unknown>, 
    config?: Partial<CacheConfig>
  ): string {
    const prefix = config?.keyPrefix || this.defaultConfig.keyPrefix
    const paramsHash = params ? this.hashParams(params) : ''
    const version = config?.version || this.defaultConfig.version
    
    return `${prefix}:${version}:${url}:${paramsHash}`
  }

  /**
   * 参数哈希化
   */
  private hashParams(params: Record<string, unknown>): string {
    try {
      const sortedParams = this.sortObject(params)
      return btoa(JSON.stringify(sortedParams)).replace(/[+/=]/g, '')
    } catch {
      return ''
    }
  }

  /**
   * 对象排序（用于生成一致的哈希）
   */
  private sortObject(obj: unknown): unknown {
    if (obj === null || typeof obj !== 'object') {
      return obj
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.sortObject(item))
    }
    
    const objRecord = obj as Record<string, unknown>
    const sortedKeys = Object.keys(objRecord).sort()
    const sortedObj: Record<string, unknown> = {}
    
    for (const key of sortedKeys) {
      sortedObj[key] = this.sortObject(objRecord[key])
    }
    
    return sortedObj
  }

  /**
   * 获取存储类型
   */
  private getStorageType(strategy: CacheStrategy): StorageType {
    switch (strategy) {
      case CacheStrategy.MEMORY:
        return StorageType.MEMORY
      case CacheStrategy.LOCAL:
        return StorageType.LOCAL
      case CacheStrategy.CACHE_STORAGE:
        return StorageType.CACHE
      default:
        return StorageType.MEMORY
    }
  }

  /**
   * 设置缓存
   */
  async set<T>(
    url: string,
    data: T,
    params?: Record<string, unknown>,
    config?: Partial<CacheConfig>
  ): Promise<boolean> {
    try {
      const finalConfig = { ...this.defaultConfig, ...config }
      
      if (finalConfig.strategy === CacheStrategy.NONE) {
        return false
      }

      const cacheKey = this.generateCacheKey(url, params, finalConfig)
      const now = Date.now()
      const expireAt = finalConfig.ttl > 0 ? now + (finalConfig.ttl * 1000) : 0
      
      const cacheItem: CacheItem<T> = {
        data,
        timestamp: now,
        expireAt,
        version: finalConfig.version,
        paramsHash: params ? this.hashParams(params) : undefined
      }

      const storageType = this.getStorageType(finalConfig.strategy)
      return await StoreUtil.set(
        cacheKey,
        cacheItem,
        StorageModule.CONFIG,
        storageType
      )
    } catch (_error) {
      // console.error('[ApiCacheManager] 设置缓存失败:', _error)
      return false
    }
  }

  /**
   * 获取缓存
   */
  async get<T>(
    url: string,
    params?: Record<string, unknown>,
    config?: Partial<CacheConfig>
  ): Promise<T | null> {
    try {
      const finalConfig = { ...this.defaultConfig, ...config }
      
      if (finalConfig.strategy === CacheStrategy.NONE) {
        return null
      }

      const cacheKey = this.generateCacheKey(url, params, finalConfig)
      const storageType = this.getStorageType(finalConfig.strategy)
      
      const cacheItem = await StoreUtil.get<CacheItem<T>>(
        cacheKey,
        StorageModule.CONFIG,
        storageType
      )

      if (!cacheItem) {
        return null
      }

      // 检查是否过期
      if (cacheItem.expireAt > 0 && Date.now() > cacheItem.expireAt) {
        await this.delete(url, params, config)
        return null
      }

      // 检查版本
      if (finalConfig.enableVersioning && 
          cacheItem.version !== finalConfig.version) {
        await this.delete(url, params, config)
        return null
      }

      return cacheItem.data
    } catch (_error) {
      // console.error('[ApiCacheManager] 获取缓存失败:', _error)
      return null
    }
  }

  /**
   * 删除缓存
   */
  async delete(
    url: string,
    params?: Record<string, unknown>,
    config?: Partial<CacheConfig>
  ): Promise<boolean> {
    try {
      const finalConfig = { ...this.defaultConfig, ...config }
      const cacheKey = this.generateCacheKey(url, params, finalConfig)
      const storageType = this.getStorageType(finalConfig.strategy)
      
      return await StoreUtil.remove(
        cacheKey,
        StorageModule.CONFIG,
        storageType
      )
    } catch (_error) {
      // console.error('[ApiCacheManager] 删除缓存失败:', _error)
      return false
    }
  }

  /**
   * 清除所有缓存
   */
  async clear(strategy?: CacheStrategy): Promise<boolean> {
    try {
      if (strategy) {
        const storageType = this.getStorageType(strategy)
        return await StoreUtil.clearModule(StorageModule.CONFIG)
      } else {
        // 清除所有策略的缓存
        const strategies = [CacheStrategy.MEMORY, CacheStrategy.LOCAL, CacheStrategy.CACHE_STORAGE]
        const results = await Promise.all(
          strategies.map(() => StoreUtil.clearModule(StorageModule.CONFIG))
        )
        return results.every(result => result)
      }
    } catch (_error) {
      // console.error('[ApiCacheManager] 清除缓存失败:', _error)
      return false
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getStats(): Promise<{
    totalItems: number
    totalSize: number
    strategies: Record<CacheStrategy, number>
  }> {
    try {
      // 这里需要根据实际存储实现来获取统计信息
      // 暂时返回模拟数据
      return {
        totalItems: 0,
        totalSize: 0,
        strategies: {
          [CacheStrategy.NONE]: 0,
          [CacheStrategy.MEMORY]: 0,
          [CacheStrategy.LOCAL]: 0,
          [CacheStrategy.CACHE_STORAGE]: 0
        }
      }
    } catch (_error) {
      // console.error('[ApiCacheManager] 获取缓存统计失败:', _error)
      return {
        totalItems: 0,
        totalSize: 0,
        strategies: {
          [CacheStrategy.NONE]: 0,
          [CacheStrategy.MEMORY]: 0,
          [CacheStrategy.LOCAL]: 0,
          [CacheStrategy.CACHE_STORAGE]: 0
        }
      }
    }
  }

  /**
   * 检查缓存是否存在且有效
   */
  async exists(
    url: string,
    params?: any,
    config?: Partial<CacheConfig>
  ): Promise<boolean> {
    const data = await this.get(url, params, config)
    return data !== null
  }

  /**
   * 刷新缓存（重新设置过期时间）
   */
  async refresh(
    url: string,
    params?: any,
    config?: Partial<CacheConfig>
  ): Promise<boolean> {
    try {
      const data = await this.get(url, params, config)
      if (data !== null) {
        return await this.set(url, data, params, config)
      }
      return false
    } catch (error) {
      logger.error('刷新缓存失败', { error })
      return false
    }
  }
}

// 导出单例实例
export const apiCacheManager = ApiCacheManager.getInstance()

// 导出便捷方法
export const {
  set: setCache,
  get: getCache,
  delete: deleteCache,
  clear: clearCache,
  exists: cacheExists,
  refresh: refreshCache,
  getStats: getCacheStats
} = apiCacheManager

// 导出默认实例
export default apiCacheManager