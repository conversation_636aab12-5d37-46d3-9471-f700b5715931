# OTO相亲交友App - Cursor Rules 开发规范

> 🎯 本文档基于项目企业级开发规范，为 Cursor AI 提供代码生成和修改约束

## 📋 项目核心信息

### 项目基础信息
- **项目名称**: OTO相亲交友App  
- **技术栈**: UniApp v3.0.0 + Vue3.4.21 + TypeScript4.9.4 + Pinia2.1.7
- **目标平台**: Android/iOS App(最高优先级) > 微信小程序 > 抖音小程序 > H5调试
- **开发语言**: TypeScript + Vue3 Composition API
- **样式预处理**: Sass v1.89.0 (现代化模块语法)

### 核心开发原则
1. **App端优先**: 所有功能优先确保在Android/iOS上完美运行
2. **类型安全**: 严格使用TypeScript，避免any类型
3. **模块化设计**: 按业务模块组织代码，松耦合高内聚
4. **现代化语法**: 使用Vue3 Composition API、现代Sass模块语法
5. **跨平台兼容**: 使用条件编译适配不同平台

---

## 🏗️ 项目架构约束

### 目录结构规范
```
src/
├─ api/                    # API接口层
│  ├─ modules/            # 按业务模块组织接口
│  ├─ interceptors/       # 请求/响应拦截器
│  ├─ types/              # 接口类型定义
│  └─ auth.ts            # 统一导出
├─ components/            # 组件层
│  ├─ base/              # 基础UI组件(无业务逻辑)
│  ├─ business/          # 业务组件(包含业务逻辑)
│  └─ auth.ts           # 统一导出
├─ composables/          # Composition API逻辑复用层
├─ config/               # 项目配置层
├─ store/                # Pinia状态管理层
│  ├─ modules/           # 状态模块
│  └─ types/             # 状态类型定义
├─ utils/                # 工具库层
├─ types/                # 全局类型声明层
├─ pages/                # 页面组件层
│  └─ modules/           # 按业务模块分组
└─ static/               # 静态资源层
```

### 模块化导出约束
**必须**: 每个模块必须提供统一的导出文件 `auth.ts`
```typescript
// ✅ 正确：统一导出
export * from './auth'
export * from './user'
export { default as authApi } from './auth'

// ❌ 错误：直接从子模块导入
import { login } from '@/api/modules/auth/login'
```

---

## 🔤 命名规范约束

### 文件和目录命名
- **目录**: kebab-case (短横线连接) - `user-profile`、`message-list`
- **Vue组件**: PascalCase - `UserProfile.vue`、`MessageList.vue`
- **TypeScript文件**: camelCase - `useAuth.ts`、`userService.ts`
- **配置文件**: kebab-case - `vite.config.ts`

### 页面文件命名规范 ⭐
**必须**: 页面文件名要见名知意，避免统一使用 `index.vue`
- **业务页面**: 使用模块名称 - `login.vue`、`register.vue`、`information.vue`
- **首页类型**: 使用 `home.vue` 或具体功能名
- **列表页面**: 使用 `xxxList.vue` - `messageList.vue`、`userList.vue`
- **详情页面**: 使用 `xxxDetail.vue` - `userDetail.vue`、`orderDetail.vue`
- **设置类页面**: 使用 `xxxSettings.vue` - `userSettings.vue`

**页面命名对照表**:
```
modules/home/<USER>/        -> home.vue
modules/auth/login/        -> login.vue  
modules/auth/register/     -> register.vue
modules/auth/realAuth/     -> realAuth.vue
modules/user/mine/         -> mine.vue
modules/user/information/  -> information.vue
modules/service/index/     -> service.vue
modules/match/index/       -> match.vue
modules/message/index/     -> message.vue
```

### 代码命名
- **变量/函数**: camelCase - `userInfo`、`handleSubmit`
- **常量**: SCREAMING_SNAKE_CASE - `API_BASE_URL`、`MAX_RETRY_COUNT`
- **类/接口**: PascalCase - `UserInfo`、`LoginParams`
- **枚举**: PascalCase，值用SCREAMING_SNAKE_CASE

### CSS类名
**必须**: 使用BEM方法论
```css
/* Block块 */
.user-card {}

/* Element元素 */
.user-card__avatar {}
.user-card__name {}

/* Modifier修饰符 */
.user-card--featured {}
.user-card__avatar--large {}
```

---

## 📋 TypeScript严格约束

### 类型定义要求
**必须**: 严格类型定义，避免any类型
```typescript
// ✅ 正确：严格类型定义
export interface UserInfo {
  /** 用户ID */
  id: string
  /** 手机号 */
  phone: string
  /** 昵称 */
  nickname?: string
  /** 创建时间 */
  createTime: string
}

// ❌ 错误：使用any类型
const userInfo: any = {}
```

### 接口设计约束
**必须**: 所有接口参数和返回值都要有明确的类型定义
```typescript
// ✅ 正确：明确的接口类型
export const getUserInfo = (userId: string): Promise<ResponseData<UserInfo>> => {
  return request.get<UserInfo>(`/user/${userId}`)
}

// ❌ 错误：缺少类型定义
export const getUserInfo = (userId) => {
  return request.get(`/user/${userId}`)
}
```

### 函数注释要求
**必须**: 所有public函数都要有完整的JSDoc注释
```typescript
/**
 * 获取用户信息
 * @param userId 用户ID
 * @param includeProfile 是否包含详细资料
 * @returns 用户信息
 * @throws {Error} 当用户不存在时抛出错误
 */
export const getUserInfo = async (
  userId: string, 
  includeProfile: boolean = false
): Promise<UserInfo> => {
  // 实现逻辑
}
```

---

## 🧩 Vue组件开发约束

### 组件结构标准
**必须**: 严格按照以下结构组织组件代码
```vue
<template>
  <!-- 组件模板 -->
</template>

<script setup lang="ts">
/**
 * 组件名称 - 组件描述
 * <AUTHOR>
 * @since 2024-01-01
 */

// ==================== 导入 ====================
import { ref, computed } from 'vue'

// ==================== Props ====================
interface Props {
  /** 属性描述 */
  modelValue?: string
  /** 是否禁用 */
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  disabled: false
})

// ==================== Emits ====================
const emit = defineEmits<{
  /** 值变化事件 */
  'update:modelValue': [value: string]
}>()

// ==================== 响应式状态 ====================
const isActive = ref(false)

// ==================== 方法 ====================
const handleClick = () => {
  // 处理逻辑
}
</script>

<style scoped>
.component-name {
  /* 组件样式 */
}
</style>
```

### 组件分类约束
- **基础组件** (`src/components/base/`): 无业务逻辑的纯UI组件
- **业务组件** (`src/components/business/`): 包含特定业务逻辑的组件

### Props和Emits约束
**必须**: 使用TypeScript接口定义Props和Emits
```typescript
// ✅ 正确：接口定义
interface Props {
  userId: string
  showAvatar?: boolean
}

const emit = defineEmits<{
  'update:modelValue': [value: string]
  success: [data: UserInfo]
}>()

// ❌ 错误：缺少类型定义
const props = defineProps(['userId', 'showAvatar'])
```

---

## 🔌 API开发约束

### API文件组织
**必须**: 按业务模块组织API文件
```
api/
├─ modules/
│  ├─ auth.ts          # 认证相关接口
│  ├─ user.ts          # 用户相关接口
│  └─ auth.ts         # 模块统一导出
├─ types/              # 接口类型定义
└─ interceptors/       # 拦截器
```

### 请求封装约束
**必须**: 所有API请求都要有完整的类型定义和错误处理
```typescript
/**
 * 密码登录
 */
export const passwordLogin = (params: LoginParams): Promise<ResponseData<LoginResponse>> => {
  return request.post<LoginResponse>('/auth/login', params)
}

// 类型定义
export interface LoginParams {
  phone: string
  password: string
  loginType: 'password' | 'sms'
}

export interface LoginResponse {
  token: string
  userInfo: UserInfo
}
```

### 错误处理约束
**必须**: 统一的错误处理机制
```typescript
// ✅ 正确：统一错误处理
try {
  const response = await api.login(params)
  if (response.code === 200) {
    return { success: true, data: response.data }
  } else {
    throw new Error(response.message)
  }
} catch (error: any) {
  return { success: false, message: error.message }
}
```

---

## 🗃️ 状态管理约束

### Pinia Store约束
**必须**: 使用Composition API语法，按模块组织Store
```typescript
// src/store/modules/auth.ts
export const useAuthStore = defineStore('auth', () => {
  // ==================== 状态 ====================
  const token = ref<string>('')
  const userInfo = ref<UserInfo | null>(null)
  
  // ==================== 计算属性 ====================
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)
  
  // ==================== 操作方法 ====================
  const setAuth = (authData: { token: string; userInfo: UserInfo }) => {
    token.value = authData.token
    userInfo.value = authData.userInfo
  }
  
  return {
    // 状态
    token,
    userInfo,
    // 计算属性
    isLoggedIn,
    // 操作方法
    setAuth
  }
})
```

### 状态持久化约束
**必须**: 重要状态需要持久化到本地存储
```typescript
const setAuth = (authData: { token: string; userInfo: UserInfo }) => {
  token.value = authData.token
  userInfo.value = authData.userInfo
  
  // 持久化存储
  uni.setStorageSync('token', authData.token)
  uni.setStorageSync('userInfo', authData.userInfo)
}
```

---

## 🔄 Composable开发约束

### 命名和结构约束
**必须**: 文件名和函数名保持一致，使用`use`前缀
```typescript
// src/composables/useAuth.ts
export const useAuth = () => {
  // ==================== 状态 ====================
  const loading = ref(false)
  
  // ==================== 计算属性 ====================
  const isLoggedIn = computed(() => ...)
  
  // ==================== 方法 ====================
  const login = async (params: LoginParams) => {
    // 逻辑实现
  }
  
  // ==================== 返回 ====================
  return {
    // 状态
    loading,
    // 计算属性
    isLoggedIn,
    // 方法
    login
  }
}
```

### 业务逻辑封装约束
**必须**: 将复杂的业务逻辑封装到Composable中，保持组件简洁

---

## 🎨 样式开发约束 (现代化Sass)

### 现代Sass语法约束 ⭐
**必须**: 完全避免即将废弃的@import语法，使用现代@use和@forward

```scss
// ✅ 正确：现代@use语法
@use '@/static/styles/variables' as var;
@use '@/static/styles/mixins' as mix;

.component {
  color: var.$primary-color;
  @include mix.flex-center;
}

// ❌ 错误：废弃@import语法
@import '@/static/styles/variables';
```

### 样式文件组织约束
```
src/static/styles/
├─ variables.scss    # 变量定义模块
├─ mixins.scss      # 混入函数模块  
├─ common.scss      # 通用样式模块
└─ index.scss       # 统一导出入口
```

### 响应式设计约束
**必须**: 使用rpx单位，考虑多端适配
```scss
// ✅ 正确：使用rpx单位
.container {
  padding: 32rpx;
  font-size: 28rpx;
}

// ✅ 正确：响应式混入
@mixin respond-to($breakpoint) {
  @if $breakpoint == 'tablet' {
    @media (min-width: 1024rpx) {
      @content;
    }
  }
}
```

---

## 📱 平台适配约束

### 条件编译约束
**必须**: 使用条件编译适配不同平台，App端优先
```typescript
// ✅ 正确：App端优先的条件编译
// #ifdef APP-PLUS
// Android/iOS原生功能实现
uni.getSystemInfo({
  success: (res) => {
    console.log('设备信息:', res)
  }
})
// #endif

// #ifdef MP-WEIXIN
// 微信小程序特定功能
wx.getUserProfile({
  desc: '用于完善用户资料'
})
// #endif

// #ifdef MP-TOUTIAO
// 抖音小程序特定功能
// #endif

// #ifdef H5
// H5调试功能 (仅开发环境)
console.log('H5调试模式')
// #endif
```

### 原生能力使用约束
**必须**: 优先使用uni-app封装的API，确保跨平台兼容
```typescript
// ✅ 正确：使用uni-app API
uni.chooseImage({
  count: 1,
  success: (res) => {
    // 处理图片
  }
})

// ✅ 正确：使用uni-app定位
uni.getLocation({
  type: 'gcj02',
  success: (res) => {
    // 处理定位
  }
})
```

---

## 📦 依赖管理约束

### 版本锁定约束
**必须**: 使用文档中指定的稳定版本，避免频繁升级
```json
{
  "dependencies": {
    "@dcloudio/uni-app": "3.0.0-4030620241128001",
    "vue": "^3.4.21",
    "typescript": "^4.9.4",
    "pinia": "^2.1.7",
    "sass": "^1.89.0"
  }
}
```

### 新依赖添加约束
**必须**: 添加新依赖前需要评估：
1. 是否与UniApp兼容
2. 是否支持多端运行
3. 是否有活跃的社区维护
4. 包大小对App端的影响

---

## 🔒 代码质量约束

### ESLint规则约束
**必须**: 严格遵循ESLint规则，不允许eslint-disable
```typescript
// ✅ 正确：遵循ESLint规则
const userInfo = ref<UserInfo | null>(null)

// ❌ 错误：禁用ESLint规则
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const userInfo: any = null
```

### 代码提交约束
**必须**: 遵循Git提交规范
```bash
# 提交信息格式
<type>(<scope>): <subject>

# 示例
feat(auth): 添加短信登录功能
fix(user): 修复头像上传失败问题
docs(api): 更新API文档
style(components): 统一组件样式规范
```

---

## 🚀 性能优化约束

### 包大小优化约束
**必须**: 关注App端包大小，优先考虑性能
```typescript
// ✅ 正确：按需导入
import { ref, computed } from 'vue'

// ❌ 错误：全量导入
import * as Vue from 'vue'
```

### 图片资源约束
**必须**: 图片资源优化
- 使用WebP格式（App端支持）
- 图片压缩
- 前期不考虑使用CDN,后期可能考虑 到时候再告诉你

### 网络请求优化约束
**必须**: 
- 实现请求缓存
- 添加Loading状态
- 实现错误重试机制

---

## 🎯 特殊功能约束

### 验证码组件约束
基于项目中的验证码实现，需要遵循：
```typescript
// 验证码组件接口约束
interface CaptchaConfig {
  /** 验证码类型 */
  captchaType: string
  /** 验证方式 */
  captchaVerification: string
  /** 验证成功回调 */
  success: (params: CaptchaResult) => void
  /** 验证失败回调 */
  error: (message: string) => void
}
```

### 用户认证约束
**必须**: 所有需要认证的页面都要检查登录状态
```typescript
// 路由守卫
const checkAuth = () => {
  const authStore = useAuthStore()
  if (!authStore.isLoggedIn) {
    uni.reLaunch({ url: '/pages/modules/auth/login/index' })
    return false
  }
  return true
}
```

---

## 📝 文档和注释约束

### 组件文档约束
**必须**: 每个组件都要有README.md文档
```markdown
# ComponentName 组件名称

## 功能特性
- ✅ 特性1
- ✅ 特性2

## 使用方式
\`\`\`vue
<ComponentName v-model="value" />
\`\`\`

## API
### Props
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| modelValue | 绑定值 | string | '' |
```

### 代码注释约束
**必须**: 关键业务逻辑都要有详细注释
```typescript
/**
 * 处理用户登录
 * 1. 验证表单数据
 * 2. 调用登录接口
 * 3. 保存用户信息
 * 4. 跳转到首页
 */
const handleLogin = async () => {
  // 实现逻辑
}
```

---

## ⚠️ 严格禁止事项

### 代码禁止项
- ❌ 使用`any`类型
- ❌ 使用废弃的`@import`语法
- ❌ 直接操作DOM（除非必要）
- ❌ 使用`var`声明变量
- ❌ 忽略TypeScript类型检查
- ❌ 硬编码配置信息

### 架构禁止项
- ❌ 跨层级直接引用（如页面直接引用API）
- ❌ 循环依赖
- ❌ 全局变量污染
- ❌ 绕过统一导出直接引用

### 平台禁止项
- ❌ 使用浏览器专有API（除H5平台）
- ❌ 使用未经验证的第三方插件
- ❌ 忽略App端兼容性

---

## 🔥 AI代码生成特别约束

当你作为Cursor AI帮助开发时，必须：

1. **严格遵循上述所有规范**，不得违反任何约束条件
2. **App端优先思考**，确保生成的代码在Android/iOS上能正常运行
3. **类型安全第一**，所有代码都要有完整的TypeScript类型定义
4. **模块化设计**，按照项目架构组织代码
5. **现代化语法**，使用Vue3 Composition API和现代Sass语法
6. **性能考虑**，生成的代码要考虑App端性能影响
7. **错误处理**，每个函数都要有完善的错误处理
8. **文档完整**，生成的代码要有详细的注释和文档

**记住：这是一个企业级的相亲交友App项目，代码质量和用户体验是第一优先级！** 🎯

## cursor每次生成的报告或总结
把每次生成的报告或总结放在这里，方便后续查看和参考。 docs/改动报告。在报告中需要描述提问次以及提问的时间 和 生成报告的时间 格式为年月日时分秒

## MCP 插件使用
自动检查已安装的mcp插件 并在需要的时候使用
