---
description:
globs:
alwaysApply: true
---
# OTO相亲交友App - Cursor Rules 开发规范

> 🎯 本文档基于项目企业级开发规范，为 Cursor AI 提供代码生成和修改约束
# AI代码修改范围约束规则

> 🚨 **强制执行规则** - 防止AI修改到不应该修改的地方

## 📋 问题背景

在使用AI修改代码时，经常遇到以下问题：
1. **范围扩散** - 只想修改A页面，结果修改了全局配置
2. **误伤无关代码** - 修改某个功能时，影响了其他不相关的功能
3. **破坏现有逻辑** - 为了实现新功能，破坏了已有的稳定代码
4. **过度重构** - 简单修改变成了大范围重构

## 🚨 强制约束规则

### 1. 修改范围明确性约束

#### 1.1 单一职责修改原则
**必须**: 每次修改只能针对一个明确的功能点或文件
```typescript
// ✅ 正确：只修改登录页面的验证逻辑
// 文件：src/pages/modules/auth/login/login.vue
const validateForm = () => {
  // 只修改这个函数内部的逻辑
  if (!formData.value.phone) {
    return '请输入手机号'
  }
  // 新增验证逻辑
  if (!/^1[3-9]\d{9}$/.test(formData.value.phone)) {
    return '手机号格式不正确'
  }
  return null
}

// ❌ 错误：同时修改多个不相关的文件
// 不能在修改登录页面时，同时修改全局配置、其他页面等
```

#### 1.2 影响范围声明约束
**必须**: 修改前必须明确声明影响范围
```markdown
## 修改影响范围声明
- **目标文件**: src/pages/modules/auth/login/login.vue
- **修改功能**: 手机号验证逻辑
- **影响范围**: 仅限登录页面的表单验证
- **不影响**: 其他页面、全局配置、API接口
- **依赖检查**: 无其他文件依赖此修改
```

#### 1.3 修改边界约束
**必须**: 严格限制修改边界，不得超出指定范围
```typescript
// ✅ 正确：只修改指定函数
const handleLogin = async () => {
  // 只在这个函数内部添加新逻辑
  const validation = validateForm()
  if (validation) {
    uni.showToast({ title: validation, icon: 'none' })
    return
  }
  // ... 其他逻辑保持不变
}

// ❌ 错误：修改了函数外部的代码
// 不能修改其他函数、组件结构、导入语句等
```

### 2. 文件级别约束

#### 2.1 单文件修改原则
**必须**: 除非明确要求，否则一次只修改一个文件
```bash
# ✅ 正确：单文件修改
修改文件: src/pages/modules/auth/login/login.vue
修改内容: 添加手机号验证

# ❌ 错误：多文件修改
同时修改:
- src/pages/modules/auth/login/login.vue
- src/utils/validation.ts
- src/api/modules/auth.ts
- src/store/modules/auth.ts
```

#### 2.2 文件类型隔离约束
**必须**: 不同类型的文件修改需要明确授权
```typescript
// 页面文件修改 - 需要明确指定
// src/pages/modules/auth/login/login.vue

// 工具函数修改 - 需要单独授权
// src/utils/validation.ts

// API接口修改 - 需要单独授权
// src/api/modules/auth.ts

// 全局配置修改 - 需要特别授权
// src/config/auth.ts
```

#### 2.3 依赖文件保护约束
**必须**: 被多个文件依赖的公共文件需要特别保护
```typescript
// 🔒 高保护级别文件（需要特别授权才能修改）
src/utils/request/auth.ts        // 网络请求基础库
src/store/modules/auth.ts          // 认证状态管理
src/config/auth.ts               // 全局配置
src/components/base/               // 基础组件库
src/api/interceptors/              // 请求拦截器

// 🔓 低保护级别文件（可以正常修改）
src/pages/modules/*/               // 具体页面文件
src/components/business/           // 业务组件
```

### 3. 功能模块约束

#### 3.1 模块边界约束
**必须**: 修改必须在指定的功能模块内进行
```typescript
// ✅ 正确：在认证模块内修改
src/pages/modules/auth/            // 认证相关页面
src/api/modules/auth.ts            // 认证相关API
src/store/modules/auth.ts          // 认证相关状态

// ❌ 错误：跨模块修改
修改认证功能时，不能同时修改：
src/pages/modules/user/            // 用户模块
src/pages/modules/message/         // 消息模块
src/pages/modules/match/           // 匹配模块
```

#### 3.2 核心功能保护约束
**必须**: 核心功能模块需要特别保护
```typescript
// 🔒 核心功能（禁止随意修改）
- 用户认证系统
- 支付相关功能
- 数据加密解密
- 网络请求基础设施
- 状态管理核心逻辑

// 🔓 业务功能（可以正常修改）
- 页面UI展示
- 表单验证逻辑
- 页面交互效果
- 业务流程优化
```

### 4. 代码层级约束

#### 4.1 函数级别修改约束
**必须**: 优先在函数内部修改，避免修改函数签名
```typescript
// ✅ 正确：函数内部修改
const validatePhone = (phone: string): boolean => {
  // 在函数内部添加新的验证逻辑
  if (!phone) return false

  // 新增：更严格的手机号验证
  if (!/^1[3-9]\d{9}$/.test(phone)) return false

  return true
}

// ❌ 错误：修改函数签名
const validatePhone = (phone: string, countryCode: string): boolean => {
  // 修改函数签名会影响所有调用此函数的地方
}
```

#### 4.2 组件结构保护约束
**必须**: 保护现有组件结构，避免破坏性修改
```vue
<!-- ✅ 正确：在现有结构内添加 -->
<template>
  <view class="login-page">
    <view class="form-container">
      <input v-model="formData.phone" placeholder="请输入手机号" />
      <!-- 新增：错误提示 -->
      <view v-if="phoneError" class="error-tip">{{ phoneError }}</view>
      <input v-model="formData.password" placeholder="请输入密码" />
      <button @click="handleLogin">登录</button>
    </view>
  </view>
</template>

<!-- ❌ 错误：破坏现有结构 -->
<template>
  <!-- 完全重写组件结构会影响样式和其他逻辑 -->
  <view class="new-login-layout">
    <!-- 全新的结构 -->
  </view>
</template>
```

### 5. 样式修改约束

#### 5.1 样式作用域约束
**必须**: 样式修改必须限制在指定组件内
```scss
// ✅ 正确：组件内样式修改
.login-page {
  .form-container {
    // 只修改登录页面的样式
    .error-tip {
      color: #ff4757;
      font-size: 24rpx;
      margin-top: 8rpx;
    }
  }
}

// ❌ 错误：全局样式修改
.error-tip {
  // 这会影响所有页面的错误提示样式
  color: #ff4757;
}
```

#### 5.2 样式变量保护约束
**必须**: 全局样式变量需要特别保护
```scss
// 🔒 受保护的全局变量（需要特别授权）
$primary-color: #007aff;
$success-color: #4cd964;
$error-color: #ff4757;
$font-size-base: 28rpx;

// 🔓 组件内样式变量（可以正常修改）
.login-page {
  $local-error-color: #ff6b7a; // 组件内局部变量
}
```

### 6. 配置文件约束

#### 6.1 配置修改授权约束
**必须**: 配置文件修改需要明确授权
```typescript
// 🔒 高风险配置（需要特别授权）
src/config/auth.ts               // 全局配置
src/manifest.json                 // 应用配置
src/pages.json                    // 页面路由配置
vite.config.ts                    // 构建配置
package.json                      // 依赖配置

// 🔓 低风险配置（可以正常修改）
src/config/constants.ts           // 业务常量
src/config/enums.ts              // 枚举定义
```

#### 6.2 环境配置保护约束
**必须**: 环境相关配置严格保护
```typescript
// 🔒 严格保护（禁止修改）
.env.production                   // 生产环境配置
.env.development                  // 开发环境配置
src/config/api.ts                // API地址配置

// ❌ 错误：不能随意修改环境配置
const API_BASE_URL = 'https://new-api.example.com' // 可能导致生产问题
```

### 7. 修改确认机制

#### 7.1 修改前确认约束
**必须**: 修改前必须确认影响范围
```markdown
## 修改确认清单
- [ ] 确认修改目标文件
- [ ] 确认修改功能范围
- [ ] 确认不会影响其他模块
- [ ] 确认不会破坏现有功能
- [ ] 确认修改方式最小化
```

#### 7.2 修改后验证约束
**必须**: 修改后必须验证影响范围
```markdown
## 修改验证清单
- [ ] 目标功能正常工作
- [ ] 其他功能未受影响
- [ ] 没有引入新的错误
- [ ] 代码风格保持一致
- [ ] 类型检查通过
```

### 8. 特殊情况处理

#### 8.1 紧急修复约束
**必须**: 紧急修复也要遵循最小化原则
```typescript
// ✅ 正确：最小化紧急修复
const handleLogin = async () => {
  try {
    // 只修复这一行的bug
    const response = await api.auth.login(formData.value) // 修复参数传递
    // 其他代码保持不变
  } catch (error) {
    // 处理错误
  }
}

// ❌ 错误：借机大范围重构
// 紧急修复时不能进行大范围的代码重构
```

#### 8.2 重构需求约束
**必须**: 重构需求需要明确标识
```markdown
## 重构需求标识
当用户明确提出以下关键词时，才可以进行大范围修改：
- "重构整个模块"
- "全面优化"
- "重新设计"
- "完全重写"
- "架构调整"

## 普通修改需求
以下情况只能进行最小化修改：
- "修复bug"
- "添加功能"
- "优化性能"
- "调整样式"
- "更新逻辑"
```

## 🔧 实施检查清单

### 修改前检查
- [ ] 明确修改目标和范围
- [ ] 确认不会影响其他模块
- [ ] 选择最小化修改方案
- [ ] 备份现有代码（如需要）

### 修改中检查
- [ ] 只修改指定的文件/函数
- [ ] 保持现有代码结构
- [ ] 不修改函数签名（除非必要）
- [ ] 不影响其他功能模块

### 修改后检查
- [ ] 验证目标功能正常
- [ ] 确认其他功能未受影响
- [ ] 检查代码风格一致性
- [ ] 运行类型检查和测试

## 🚨 违规处罚

如果AI违反修改范围约束：

1. **立即停止修改**
2. **回滚到修改前状态**
3. **重新明确修改范围**
4. **采用最小化修改方案**
5. **增加额外的保护措施**

## 📝 修改范围模板

```markdown
## AI修改任务模板

### 修改目标
- **文件**: [具体文件路径]
- **功能**: [具体功能描述]
- **范围**: [修改范围限制]

### 修改约束
- **只能修改**: [明确可以修改的部分]
- **不能修改**: [明确不能修改的部分]
- **保护对象**: [需要保护的代码/功能]

### 验证标准
- **成功标准**: [修改成功的判断标准]
- **失败标准**: [修改失败的判断标准]
- **影响检查**: [需要检查的影响范围]
```

## 🎯 最佳实践

### 1. 渐进式修改
```typescript
// 第一步：最小化修改
const validatePhone = (phone: string) => {
  if (!phone) return false
  // 只添加这一行新逻辑
  if (!/^1[3-9]\d{9}$/.test(phone)) return false
  return true
}

// 第二步：如果需要，再进行下一步修改
// 第三步：逐步完善功能
```

### 2. 功能隔离
```typescript
// ✅ 正确：新功能独立实现
const validatePhoneFormat = (phone: string): boolean => {
  return /^1[3-9]\d{9}$/.test(phone)
}

const validatePhone = (phone: string) => {
  if (!phone) return false
  // 调用新函数，不修改原有逻辑
  if (!validatePhoneFormat(phone)) return false
  return true
}

// ❌ 错误：直接修改原有函数
```

### 3. 向后兼容
```typescript
// ✅ 正确：保持向后兼容
const handleLogin = async (params?: LoginParams) => {
  // 兼容原有调用方式
  const loginData = params || formData.value
  // 新增功能
  const validation = validateForm(loginData)
  if (validation) {
    // 处理验证错误
  }
  // 原有逻辑保持不变
}
```

**记住：最小化修改是最安全的修改！** 🎯


## 🚨 强制执行规则 - Interactive Feedback

**⚠️ 此规则为强制执行，无例外！**

### 任务确认机制
1. **每次任务完成后必须使用 `interactive-feedback` MCP 服务询问用户任务解决情况**
2. **只有当用户明确反馈"问题已解决"或"可以继续下一个任务"时，才可以继续执行下一个任务或结束当前任务**
3. **如果用户没有明确告知可以继续下一个任务或结束本次任务，则不可以结束当前任务**
4. **必须继续按照用户给出的指令执行，直到用户明确表示满意为止**

### 执行流程
```
任务执行 → 使用 interactive-feedback 询问 → 等待用户反馈 →
用户反馈满意 ✅ → 继续下一任务/结束
用户反馈不满意 ❌ → 继续修改直到满意
```

### 使用方式
```typescript
// 每次任务完成后必须调用
mcp_interactive-feedback_interactive-feedback({
  work_summary: "详细描述已完成的工作内容和结果"
})
```

**注意**: 此规则适用于所有开发任务，包括但不限于：代码修复、功能开发、样式调整、配置修改等。

---

## MCP 插件使用
目前安装了 interactive-feedback mcp服务，在需要的地方请使用。
也安装了browser-tools mcp 也请你再合适的时机时使用。
自动检查所有已安装的mcp插件 并在需要的时候使用

## 每次ai改动代码范围约束
能最小化改动解决代码问题时请最小化改动，让你改 "A",绝不去碰"B"。
除非提示词中明确指出需要进行 范围修改的要求,比如 "全部、所以"等具有范围属性的情况下，可改动较大范围 或指定范围的代码

## 📋 项目核心信息

### 项目基础信息
- **项目名称**: OTO相亲交友App
- **技术栈**: UniApp v3.0.0 + Vue3.4.21 + TypeScript4.9.4 + Pinia2.1.7
- **目标平台**: Android/iOS App(最高优先级) > 微信小程序 > 抖音小程序 > H5调试
- **开发语言**: TypeScript + Vue3 Composition API
- **样式预处理**: Sass v1.89.0 (现代化模块语法)

### 核心开发原则
1. **App端优先**: 所有功能优先确保在Android/iOS上完美运行
2. **类型安全**: 严格使用TypeScript，避免any类型
3. **模块化设计**: 按业务模块组织代码，松耦合高内聚
4. **现代化语法**: 使用Vue3 Composition API、现代Sass模块语法
5. **跨平台兼容**: 使用条件编译适配不同平台

---

## 🏗️ 项目架构约束

### 目录结构规范
```
src/
├─ api/                    # API接口层
│  ├─ modules/            # 按业务模块组织接口
│  ├─ interceptors/       # 请求/响应拦截器
│  ├─ types/              # 接口类型定义
│  └─ auth.ts            # 统一导出
├─ components/            # 组件层
│  ├─ base/              # 基础UI组件(无业务逻辑)
│  ├─ business/          # 业务组件(包含业务逻辑)
│  └─ auth.ts           # 统一导出
├─ composables/          # Composition API逻辑复用层
├─ config/               # 项目配置层
├─ store/                # Pinia状态管理层
│  ├─ modules/           # 状态模块
│  └─ types/             # 状态类型定义
├─ utils/                # 工具库层
├─ types/                # 全局类型声明层
├─ pages/                # 页面组件层
│  └─ modules/           # 按业务模块分组
└─ static/               # 静态资源层
```

### 模块化导出约束
**必须**: 每个模块必须提供统一的导出文件 `auth.ts`
```typescript
// ✅ 正确：统一导出
export * from './auth'
export * from './user'
export { default as authApi } from './auth'

// ❌ 错误：直接从子模块导入
import { login } from '@/api/modules/auth/login'
```

---

## 🔤 命名规范约束

### 文件和目录命名
- **目录**: kebab-case (短横线连接) - `user-profile`、`message-list`
- **Vue组件**: PascalCase - `UserProfile.vue`、`MessageList.vue`
- **TypeScript文件**: camelCase - `useAuth.ts`、`userService.ts`
- **配置文件**: kebab-case - `vite.config.ts`

### 页面文件命名规范 ⭐
**必须**: 页面文件名要见名知意，避免统一使用 `index.vue`
- **业务页面**: 使用模块名称 - `login.vue`、`register.vue`、`information.vue`
- **首页类型**: 使用 `home.vue` 或具体功能名
- **列表页面**: 使用 `xxxList.vue` - `messageList.vue`、`userList.vue`
- **详情页面**: 使用 `xxxDetail.vue` - `userDetail.vue`、`orderDetail.vue`
- **设置类页面**: 使用 `xxxSettings.vue` - `userSettings.vue`

**页面命名对照表**:
```
modules/home/<USER>/        -> home.vue
modules/auth/login/        -> login.vue
modules/auth/register/     -> register.vue
modules/auth/realAuth/     -> realAuth.vue
modules/user/mine/         -> mine.vue
modules/user/information/  -> information.vue
modules/service/index/     -> service.vue
modules/match/index/       -> match.vue
modules/message/index/     -> message.vue
```

### 代码命名
- **变量/函数**: camelCase - `userInfo`、`handleSubmit`
- **常量**: SCREAMING_SNAKE_CASE - `API_BASE_URL`、`MAX_RETRY_COUNT`
- **类/接口**: PascalCase - `UserInfo`、`LoginParams`
- **枚举**: PascalCase，值用SCREAMING_SNAKE_CASE

### CSS类名
**必须**: 使用BEM方法论
```css
/* Block块 */
.user-card {}

/* Element元素 */
.user-card__avatar {}
.user-card__name {}

/* Modifier修饰符 */
.user-card--featured {}
.user-card__avatar--large {}
```

---

## 📋 TypeScript严格约束

### 类型定义要求
**必须**: 严格类型定义，避免any类型
```typescript
// ✅ 正确：严格类型定义
export interface UserInfo {
  /** 用户ID */
  id: string
  /** 手机号 */
  phone: string
  /** 昵称 */
  nickname?: string
  /** 创建时间 */
  createTime: string
}

// ❌ 错误：使用any类型
const userInfo: any = {}
```

### 接口设计约束
**必须**: 所有接口参数和返回值都要有明确的类型定义
```typescript
// ✅ 正确：明确的接口类型
export const getUserInfo = (userId: string): Promise<ResponseData<UserInfo>> => {
  return request.get<UserInfo>(`/user/${userId}`)
}

// ❌ 错误：缺少类型定义
export const getUserInfo = (userId) => {
  return request.get(`/user/${userId}`)
}
```

### 函数注释要求
**必须**: 所有public函数都要有完整的JSDoc注释
```typescript
/**
 * 获取用户信息
 * @param userId 用户ID
 * @param includeProfile 是否包含详细资料
 * @returns 用户信息
 * @throws {Error} 当用户不存在时抛出错误
 */
export const getUserInfo = async (
  userId: string,
  includeProfile: boolean = false
): Promise<UserInfo> => {
  // 实现逻辑
}
```

---

## 🧩 Vue组件开发约束

### 组件结构标准
**必须**: 严格按照以下结构组织组件代码
```vue
<template>
  <!-- 组件模板 -->
</template>

<script setup lang="ts">
/**
 * 组件名称 - 组件描述
 * <AUTHOR>
 * @since 2024-01-01
 */

// ==================== 导入 ====================
import { ref, computed } from 'vue'

// ==================== Props ====================
interface Props {
  /** 属性描述 */
  modelValue?: string
  /** 是否禁用 */
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  disabled: false
})

// ==================== Emits ====================
const emit = defineEmits<{
  /** 值变化事件 */
  'update:modelValue': [value: string]
}>()

// ==================== 响应式状态 ====================
const isActive = ref(false)

// ==================== 方法 ====================
const handleClick = () => {
  // 处理逻辑
}
</script>

<style scoped>
.component-name {
  /* 组件样式 */
}
</style>
```

### 组件分类约束
- **基础组件** (`src/components/base/`): 无业务逻辑的纯UI组件
- **业务组件** (`src/components/business/`): 包含特定业务逻辑的组件

### Props和Emits约束
**必须**: 使用TypeScript接口定义Props和Emits
```typescript
// ✅ 正确：接口定义
interface Props {
  userId: string
  showAvatar?: boolean
}

const emit = defineEmits<{
  'update:modelValue': [value: string]
  success: [data: UserInfo]
}>()

// ❌ 错误：缺少类型定义
const props = defineProps(['userId', 'showAvatar'])
```

---

## 🔌 API开发约束

### API文件组织
**必须**: 按业务模块组织API文件
```
api/
├─ modules/
│  ├─ auth.ts          # 认证相关接口
│  ├─ user.ts          # 用户相关接口
│  └─ auth.ts         # 模块统一导出
├─ types/              # 接口类型定义
└─ interceptors/       # 拦截器
```

### 请求封装约束
**必须**: 所有API请求都要有完整的类型定义和错误处理
```typescript
/**
 * 密码登录
 */
export const passwordLogin = (params: LoginParams): Promise<ResponseData<LoginResponse>> => {
  return request.post<LoginResponse>('/auth/login', params)
}

// 类型定义
export interface LoginParams {
  phone: string
  password: string
  loginType: 'password' | 'sms'
}

export interface LoginResponse {
  token: string
  userInfo: UserInfo
}
```

### 错误处理约束
**必须**: 统一的错误处理机制
```typescript
// ✅ 正确：统一错误处理
try {
  const response = await api.login(params)
  if (response.code === 200) {
    return { success: true, data: response.data }
  } else {
    throw new Error(response.message)
  }
} catch (error: any) {
  return { success: false, message: error.message }
}
```

---

## 🗃️ 状态管理约束

### Pinia Store约束
**必须**: 使用Composition API语法，按模块组织Store
```typescript
// src/store/modules/auth.ts
export const useAuthStore = defineStore('auth', () => {
  // ==================== 状态 ====================
  const token = ref<string>('')
  const userInfo = ref<UserInfo | null>(null)

  // ==================== 计算属性 ====================
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)

  // ==================== 操作方法 ====================
  const setAuth = (authData: { token: string; userInfo: UserInfo }) => {
    token.value = authData.token
    userInfo.value = authData.userInfo
  }

  return {
    // 状态
    token,
    userInfo,
    // 计算属性
    isLoggedIn,
    // 操作方法
    setAuth
  }
})
```

### 状态持久化约束
**必须**: 重要状态需要持久化到本地存储
```typescript
const setAuth = (authData: { token: string; userInfo: UserInfo }) => {
  token.value = authData.token
  userInfo.value = authData.userInfo

  // 持久化存储
  uni.setStorageSync('token', authData.token)
  uni.setStorageSync('userInfo', authData.userInfo)
}
```

---

## 🔄 Composable开发约束

### 命名和结构约束
**必须**: 文件名和函数名保持一致，使用`use`前缀
```typescript
// src/composables/useAuth.ts
export const useAuth = () => {
  // ==================== 状态 ====================
  const loading = ref(false)

  // ==================== 计算属性 ====================
  const isLoggedIn = computed(() => ...)

  // ==================== 方法 ====================
  const login = async (params: LoginParams) => {
    // 逻辑实现
  }

  // ==================== 返回 ====================
  return {
    // 状态
    loading,
    // 计算属性
    isLoggedIn,
    // 方法
    login
  }
}
```

### 业务逻辑封装约束
**必须**: 将复杂的业务逻辑封装到Composable中，保持组件简洁

---

## 🎨 样式开发约束 (现代化Sass)

### 现代Sass语法约束 ⭐
**必须**: 完全避免即将废弃的@import语法，使用现代@use和@forward

```scss
// ✅ 正确：现代@use语法
@use '@/static/styles/variables' as var;
@use '@/static/styles/mixins' as mix;

.component {
  color: var.$primary-color;
  @include mix.flex-center;
}

// ❌ 错误：废弃@import语法
@import '@/static/styles/variables';
```

### 样式文件组织约束
```
src/static/styles/
├─ variables.scss    # 变量定义模块
├─ mixins.scss      # 混入函数模块
├─ common.scss      # 通用样式模块
└─ index.scss       # 统一导出入口
```

### 响应式设计约束
**必须**: 使用rpx单位，考虑多端适配
```scss
// ✅ 正确：使用rpx单位
.container {
  padding: 32rpx;
  font-size: 28rpx;
}

// ✅ 正确：响应式混入
@mixin respond-to($breakpoint) {
  @if $breakpoint == 'tablet' {
    @media (min-width: 1024rpx) {
      @content;
    }
  }
}
```

---

## 📱 平台适配约束

### 条件编译约束
**必须**: 使用条件编译适配不同平台，App端优先
```typescript
// ✅ 正确：App端优先的条件编译
// #ifdef APP-PLUS
// Android/iOS原生功能实现
uni.getSystemInfo({
  success: (res) => {
    console.log('设备信息:', res)
  }
})
// #endif

// #ifdef MP-WEIXIN
// 微信小程序特定功能
wx.getUserProfile({
  desc: '用于完善用户资料'
})
// #endif

// #ifdef MP-TOUTIAO
// 抖音小程序特定功能
// #endif

// #ifdef H5
// H5调试功能 (仅开发环境)
console.log('H5调试模式')
// #endif
```

### 原生能力使用约束
**必须**: 优先使用uni-app封装的API，确保跨平台兼容
```typescript
// ✅ 正确：使用uni-app API
uni.chooseImage({
  count: 1,
  success: (res) => {
    // 处理图片
  }
})

// ✅ 正确：使用uni-app定位
uni.getLocation({
  type: 'gcj02',
  success: (res) => {
    // 处理定位
  }
})
```

---

## 📦 依赖管理约束

### 版本锁定约束
**必须**: 使用文档中指定的稳定版本，避免频繁升级
```json
{
  "dependencies": {
    "@dcloudio/uni-app": "3.0.0-4030620241128001",
    "vue": "^3.4.21",
    "typescript": "^4.9.4",
    "pinia": "^2.1.7",
    "sass": "^1.89.0"
  }
}
```

### 新依赖添加约束
**必须**: 添加新依赖前需要评估：
1. 是否与UniApp兼容
2. 是否支持多端运行
3. 是否有活跃的社区维护
4. 包大小对App端的影响

---

## 🔒 代码质量约束

### ESLint规则约束
**必须**: 严格遵循ESLint规则，不允许eslint-disable
```typescript
// ✅ 正确：遵循ESLint规则
const userInfo = ref<UserInfo | null>(null)

// ❌ 错误：禁用ESLint规则
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const userInfo: any = null
```

### 代码提交约束
**必须**: 遵循Git提交规范
```bash
# 提交信息格式
<type>(<scope>): <subject>

# 示例
feat(auth): 添加短信登录功能
fix(user): 修复头像上传失败问题
docs(api): 更新API文档
style(components): 统一组件样式规范
```

---

## 🚀 性能优化约束

### 包大小优化约束
**必须**: 关注App端包大小，优先考虑性能
```typescript
// ✅ 正确：按需导入
import { ref, computed } from 'vue'

// ❌ 错误：全量导入
import * as Vue from 'vue'
```

### 图片资源约束
**必须**: 图片资源优化
- 使用WebP格式（App端支持）
- 图片压缩
- 考虑使用CDN

### 网络请求优化约束
**必须**:
- 实现请求缓存
- 添加Loading状态
- 实现错误重试机制

---

## 🎯 特殊功能约束

### 验证码组件约束
基于项目中的验证码实现，需要遵循：
```typescript
// 验证码组件接口约束
interface CaptchaConfig {
  /** 验证码类型 */
  captchaType: string
  /** 验证方式 */
  captchaVerification: string
  /** 验证成功回调 */
  success: (params: CaptchaResult) => void
  /** 验证失败回调 */
  error: (message: string) => void
}
```

### 用户认证约束
**必须**: 所有需要认证的页面都要检查登录状态
```typescript
// 路由守卫
const checkAuth = () => {
  const authStore = useAuthStore()
  if (!authStore.isLoggedIn) {
    uni.reLaunch({ url: '/pages/modules/auth/login/index' })
    return false
  }
  return true
}
```

---

## 📝 文档和注释约束

### 组件文档约束
**必须**: 每个组件都要有README.md文档
```markdown
# ComponentName 组件名称

## 功能特性
- ✅ 特性1
- ✅ 特性2

## 使用方式
\`\`\`vue
<ComponentName v-model="value" />
\`\`\`

## API
### Props
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| modelValue | 绑定值 | string | '' |
```

### 代码注释约束
**必须**: 关键业务逻辑都要有详细注释
```typescript
/**
 * 处理用户登录
 * 1. 验证表单数据
 * 2. 调用登录接口
 * 3. 保存用户信息
 * 4. 跳转到首页
 */
const handleLogin = async () => {
  // 实现逻辑
}
```

---

## ⚠️ 严格禁止事项

### 代码禁止项
- ❌ 使用`any`类型
- ❌ 使用废弃的`@import`语法
- ❌ 直接操作DOM（除非必要）
- ❌ 使用`var`声明变量
- ❌ 忽略TypeScript类型检查
- ❌ 硬编码配置信息

### 架构禁止项
- ❌ 跨层级直接引用（如页面直接引用API）
- ❌ 循环依赖
- ❌ 全局变量污染
- ❌ 绕过统一导出直接引用

### 平台禁止项
- ❌ 使用浏览器专有API（除H5平台）
- ❌ 使用未经验证的第三方插件
- ❌ 忽略App端兼容性

---

## 🔥 AI代码生成特别约束

当你作为Cursor AI帮助开发时，必须：

1. **严格遵循上述所有规范**，不得违反任何约束条件
2. **App端优先思考**，确保生成的代码在Android/iOS上能正常运行
3. **类型安全第一**，所有代码都要有完整的TypeScript类型定义
4. **模块化设计**，按照项目架构组织代码
5. **现代化语法**，使用Vue3 Composition API和现代Sass语法
6. **性能考虑**，生成的代码要考虑App端性能影响
7. **错误处理**，每个函数都要有完善的错误处理
8. **文档完整**，生成的代码要有详细的注释和文档

**记住：这是一个企业级的相亲交友App项目，代码质量和用户体验是第一优先级！** 🎯

