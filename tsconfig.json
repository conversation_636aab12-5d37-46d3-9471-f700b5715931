{
  "extends": "@vue/tsconfig/tsconfig.json",
  "compilerOptions": {
    "sourceMap": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/api/*": ["./src/api/*"],
      "@/components/*": ["./src/components/*"],
      "@/composables/*": ["./src/composables/*"],
      "@/utils/*": ["./src/utils/*"],
      "@/types/*": ["./src/types/*"],
      "@/store/*": ["./src/store/*"],
      "@/config/*": ["./src/config/*"]
    },
    "lib": ["esnext", "dom"],
    "types": ["@dcloudio/types", "node"],

    "module": "esnext",
    "moduleResolution": "node",
    "noEmit": true,
    "strict": true,
    "allowJs": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    
    // 覆盖继承配置中的废弃选项
    "preserveValueImports": false,
    "importsNotUsedAsValues": "remove"
  },
  "include": [
    "src/**/*.ts", 
    "src/**/*.d.ts", 
    "src/**/*.tsx", 
    "src/**/*.vue", 
    "src/**/*.json"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "src/components/verifition/**/*.js"
  ]
}