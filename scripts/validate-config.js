#!/usr/bin/env node

/**
 * 配置验证脚本
 * 用于在Node.js环境下验证应用配置
 */

const fs = require('fs')
const path = require('path')

/**
 * 加载环境变量文件
 */
function loadEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return {}
  }
  
  const content = fs.readFileSync(filePath, 'utf8')
  const env = {}
  
  content.split('\n').forEach(line => {
    line = line.trim()
    if (line && !line.startsWith('#')) {
      const [key, ...valueParts] = line.split('=')
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').replace(/^["']|["']$/g, '')
        env[key] = value
      }
    }
  })
  
  return env
}

/**
 * 设置环境变量到process.env
 */
function setEnvironmentVariables() {
  const rootDir = path.resolve(__dirname, '..')
  
  // 按优先级加载环境变量文件
  const envFiles = [
    path.join(rootDir, '.env'),
    path.join(rootDir, '.env.local'),
    path.join(rootDir, '.env.development'),
    path.join(rootDir, '.env.development.local')
  ]
  
  envFiles.forEach(filePath => {
    const env = loadEnvFile(filePath)
    Object.assign(process.env, env)
  })
}

/**
 * 验证配置
 */
function validateConfig() {
  // 设置环境变量
  setEnvironmentVariables()
  
  console.log('🔧 Validating application configuration...')
  
  // 检查必需的环境变量
  const requiredVars = {
    'VITE_API_BASE_URL': 'API Base URL',
    'VITE_APP_NAME': 'Application Name'
  }
  
  const errors = []
  
  // 验证必需变量
  Object.entries(requiredVars).forEach(([key, description]) => {
    const value = process.env[key]
    if (!value) {
      errors.push(`${description} (${key}) is required`)
    }
  })
  
  // 验证API_BASE_URL格式
  const apiBaseUrl = process.env.VITE_API_BASE_URL
  if (apiBaseUrl) {
    try {
      new URL(apiBaseUrl)
    } catch {
      errors.push('API_BASE_URL must be a valid URL')
    }
  }
  
  // 生产环境额外验证
  const appEnv = process.env.VITE_APP_ENV || 'development'
  if (appEnv === 'production') {
    if (apiBaseUrl && apiBaseUrl.includes('localhost')) {
      errors.push('Production environment should not use localhost')
    }
    
    if (process.env.VITE_ENABLE_DEBUG_TOOLS === 'true') {
      errors.push('Debug tools should be disabled in production')
    }
  }
  
  // 输出结果
  if (errors.length > 0) {
    console.error('❌ Configuration validation failed:')
    errors.forEach(error => console.error(`  - ${error}`))
    console.error('\n💡 Please check your .env.local file and ensure all required variables are set.')
    process.exit(1)
  } else {
    console.log('✅ Configuration validation passed')
    console.log(`   Environment: ${appEnv}`)
    console.log(`   API Base URL: ${apiBaseUrl}`)
    console.log(`   App Name: ${process.env.VITE_APP_NAME || 'OTO健身课程预约系统'}`)
  }
}

// 运行验证
if (require.main === module) {
  validateConfig()
}

module.exports = { validateConfig, loadEnvFile, setEnvironmentVariables }