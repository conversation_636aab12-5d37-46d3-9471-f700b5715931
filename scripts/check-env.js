#!/usr/bin/env node
/**
 * 环境配置检查脚本
 * @description 验证环境变量配置的完整性和正确性
 * <AUTHOR>
 * @since 2024-01-01
 */

const fs = require('fs')
const path = require('path')

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✓${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}✗${colors.reset} ${msg}`),
  title: (msg) => console.log(`\n${colors.cyan}${msg}${colors.reset}`)
}

// 必需的环境变量
const REQUIRED_VARS = [
  'VITE_API_BASE_URL',
  'VITE_APP_ENV',
  'VITE_APP_VERSION'
]

// 敏感环境变量（不应在开发环境中使用生产值）
const SENSITIVE_VARS = [
  'VITE_DEVICE_FINGERPRINT_SECRET',
  'VITE_ENCRYPTION_KEY'
]

// 环境文件列表
const ENV_FILES = [
  '.env',
  '.env.development',
  '.env.production',
  '.env.staging',
  '.env.local.example'
]

/**
 * 检查文件是否存在
 */
function checkFileExists(filePath) {
  return fs.existsSync(path.resolve(process.cwd(), filePath))
}

/**
 * 解析环境文件
 */
function parseEnvFile(filePath) {
  try {
    const content = fs.readFileSync(path.resolve(process.cwd(), filePath), 'utf8')
    const vars = {}
    
    content.split('\n').forEach(line => {
      line = line.trim()
      if (line && !line.startsWith('#')) {
        const [key, ...valueParts] = line.split('=')
        if (key && valueParts.length > 0) {
          vars[key.trim()] = valueParts.join('=').trim().replace(/^["']|["']$/g, '')
        }
      }
    })
    
    return vars
  } catch (error) {
    log.error(`无法解析文件 ${filePath}: ${error.message}`)
    return {}
  }
}

/**
 * 检查环境文件
 */
function checkEnvFiles() {
  log.title('🔍 检查环境文件')
  
  let hasErrors = false
  
  ENV_FILES.forEach(file => {
    if (checkFileExists(file)) {
      log.success(`${file} 存在`)
      
      // 检查文件内容
      const vars = parseEnvFile(file)
      const varCount = Object.keys(vars).length
      
      if (varCount === 0) {
        log.warning(`${file} 为空或格式错误`)
      } else {
        log.info(`${file} 包含 ${varCount} 个变量`)
      }
    } else {
      if (file === '.env.local.example') {
        log.warning(`${file} 不存在（建议创建作为模板）`)
      } else {
        log.error(`${file} 不存在`)
        hasErrors = true
      }
    }
  })
  
  return !hasErrors
}

/**
 * 检查必需变量
 */
function checkRequiredVars() {
  log.title('🔧 检查必需变量')
  
  let hasErrors = false
  
  ENV_FILES.slice(1, -1).forEach(file => { // 排除 .env 和 .env.local.example
    if (checkFileExists(file)) {
      const vars = parseEnvFile(file)
      log.info(`检查 ${file}:`)
      
      REQUIRED_VARS.forEach(varName => {
        if (vars[varName]) {
          log.success(`  ${varName}: ${vars[varName].substring(0, 20)}${vars[varName].length > 20 ? '...' : ''}`)
        } else {
          log.error(`  ${varName}: 缺失`)
          hasErrors = true
        }
      })
    }
  })
  
  return !hasErrors
}

/**
 * 检查安全配置
 */
function checkSecurity() {
  log.title('🔒 检查安全配置')
  
  let hasWarnings = false
  
  // 检查生产环境配置
  if (checkFileExists('.env.production')) {
    const prodVars = parseEnvFile('.env.production')
    
    // 检查HTTPS
    if (prodVars.VITE_API_BASE_URL && !prodVars.VITE_API_BASE_URL.startsWith('https://')) {
      log.error('生产环境必须使用HTTPS')
      hasWarnings = true
    } else {
      log.success('生产环境使用HTTPS')
    }
    
    // 检查敏感变量
    SENSITIVE_VARS.forEach(varName => {
      if (prodVars[varName] && (prodVars[varName] === 'your-secret-here' || prodVars[varName].length < 16)) {
        log.warning(`生产环境 ${varName} 可能使用了默认值或过于简单`)
        hasWarnings = true
      }
    })
  }
  
  // 检查开发环境
  if (checkFileExists('.env.development')) {
    const devVars = parseEnvFile('.env.development')
    
    if (devVars.VITE_API_BASE_URL && devVars.VITE_API_BASE_URL.includes('localhost')) {
      log.success('开发环境使用本地服务器')
    }
  }
  
  return !hasWarnings
}

/**
 * 检查配置一致性
 */
function checkConsistency() {
  log.title('⚖️ 检查配置一致性')
  
  const envConfigs = {}
  let hasErrors = false
  
  // 加载所有环境配置
  ENV_FILES.slice(1, -1).forEach(file => {
    if (checkFileExists(file)) {
      envConfigs[file] = parseEnvFile(file)
    }
  })
  
  // 检查版本一致性
  const versions = new Set()
  Object.entries(envConfigs).forEach(([file, vars]) => {
    if (vars.VITE_APP_VERSION) {
      versions.add(vars.VITE_APP_VERSION)
    }
  })
  
  if (versions.size > 1) {
    log.warning(`发现不同的应用版本: ${Array.from(versions).join(', ')}`)
    hasErrors = true
  } else {
    log.success('应用版本一致')
  }
  
  // 检查是否有硬编码的服务器地址
  const hardcodedUrls = []
  Object.entries(envConfigs).forEach(([file, vars]) => {
    Object.entries(vars).forEach(([key, value]) => {
      if (typeof value === 'string' && value.includes('api.production.com')) {
        hardcodedUrls.push(`${file}: ${key}=${value}`)
      }
    })
  })
  
  if (hardcodedUrls.length > 0) {
    log.warning(`发现硬编码的生产环境地址: ${hardcodedUrls.join(', ')}`)
    hasErrors = true
  }
  
  return !hasErrors
}

/**
 * 生成报告
 */
function generateReport() {
  log.title('📊 配置报告')
  
  const report = {
    timestamp: new Date().toISOString(),
    files: {},
    summary: {
      totalFiles: 0,
      existingFiles: 0,
      totalVars: 0,
      missingRequired: 0
    }
  }
  
  ENV_FILES.forEach(file => {
    report.summary.totalFiles++
    
    if (checkFileExists(file)) {
      report.summary.existingFiles++
      const vars = parseEnvFile(file)
      report.files[file] = {
        exists: true,
        varCount: Object.keys(vars).length,
        vars: Object.keys(vars)
      }
      report.summary.totalVars += Object.keys(vars).length
    } else {
      report.files[file] = {
        exists: false,
        varCount: 0,
        vars: []
      }
    }
  })
  
  log.info(`文件覆盖率: ${report.summary.existingFiles}/${report.summary.totalFiles}`)
  log.info(`总变量数: ${report.summary.totalVars}`)
  
  // 保存报告
  try {
    fs.writeFileSync(
      path.resolve(process.cwd(), 'env-check-report.json'),
      JSON.stringify(report, null, 2)
    )
    log.success('报告已保存到 env-check-report.json')
  } catch (error) {
    log.warning(`无法保存报告: ${error.message}`)
  }
}

/**
 * 主函数
 */
function main() {
  console.log(`${colors.magenta}🔍 环境配置检查工具${colors.reset}\n`)
  
  const results = {
    files: checkEnvFiles(),
    required: checkRequiredVars(),
    security: checkSecurity(),
    consistency: checkConsistency()
  }
  
  generateReport()
  
  // 总结
  log.title('📋 检查总结')
  
  const allPassed = Object.values(results).every(result => result)
  
  if (allPassed) {
    log.success('所有检查通过！')
    process.exit(0)
  } else {
    log.error('发现配置问题，请检查上述输出')
    
    log.info('\n💡 建议操作:')
    if (!results.files) {
      log.info('  1. 运行 npm run env:copy 创建本地配置文件')
    }
    if (!results.required) {
      log.info('  2. 检查并补充缺失的必需变量')
    }
    if (!results.security) {
      log.info('  3. 检查安全配置，确保生产环境使用强密钥')
    }
    if (!results.consistency) {
      log.info('  4. 统一各环境的版本号和关键配置')
    }
    
    process.exit(1)
  }
}

// 运行检查
if (require.main === module) {
  main()
}

module.exports = {
  checkEnvFiles,
  checkRequiredVars,
  checkSecurity,
  checkConsistency
}