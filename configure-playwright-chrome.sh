#!/bin/bash

# Playwright Chrome配置脚本
# 自动配置Playwright使用系统Chrome浏览器

echo "🚀 开始配置Playwright使用系统Chrome..."

# 检查系统Chrome是否存在
CHROME_PATH="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
if [ ! -f "$CHROME_PATH" ]; then
    echo "❌ 未找到系统Chrome，请先安装Google Chrome"
    exit 1
fi

echo "✅ 找到系统Chrome: $CHROME_PATH"

# 获取用户数据目录
USER_DATA_DIR="$HOME/Library/Application Support/Google/Chrome"
if [ ! -d "$USER_DATA_DIR" ]; then
    echo "❌ 未找到Chrome用户数据目录"
    exit 1
fi

echo "✅ 找到Chrome用户数据目录: $USER_DATA_DIR"

# 创建Playwright配置文件
cat > playwright.config.js << EOF
// Playwright配置 - 使用系统Chrome
const { defineConfig, devices } = require('@playwright/test');

module.exports = defineConfig({
  testDir: './tests',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:5173',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { 
        ...devices['Desktop Chrome'],
        // 使用系统Chrome
        launchOptions: {
          executablePath: '$CHROME_PATH',
          args: [
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--no-sandbox'
          ]
        }
      },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:5173',
    reuseExistingServer: !process.env.CI,
  },
});
EOF

echo "✅ 创建了playwright.config.js配置文件"

# 创建环境变量配置
cat > .env.playwright << EOF
# Playwright环境变量配置
PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH=$CHROME_PATH
PLAYWRIGHT_BROWSERS_PATH=/Applications/Google Chrome.app/Contents/MacOS/
EOF

echo "✅ 创建了.env.playwright环境变量文件"

# 创建启动脚本
cat > start-chrome-debug.sh << EOF
#!/bin/bash
# 启动Chrome调试模式
echo "🔧 启动Chrome调试模式..."
"$CHROME_PATH" --remote-debugging-port=9222 --user-data-dir="$USER_DATA_DIR" &
echo "✅ Chrome调试模式已启动，端口: 9222"
echo "📝 现在可以在Playwright中连接到: http://localhost:9222"
EOF

chmod +x start-chrome-debug.sh
echo "✅ 创建了start-chrome-debug.sh启动脚本"

# 创建连接示例代码
cat > playwright-connect-example.js << EOF
// Playwright连接系统Chrome示例
const { chromium } = require('playwright');

async function connectToChrome() {
  try {
    // 方法1: 连接到调试端口
    const browser = await chromium.connectOverCDP('http://localhost:9222');
    console.log('✅ 成功连接到系统Chrome');
    
    const page = await browser.newPage();
    await page.goto('http://localhost:5173');
    
    // 在这里进行你的操作...
    
    await browser.close();
  } catch (error) {
    console.error('❌ 连接失败:', error.message);
    console.log('💡 请先运行: ./start-chrome-debug.sh');
  }
}

// 方法2: 直接启动系统Chrome
async function launchSystemChrome() {
  const browser = await chromium.launch({
    executablePath: '$CHROME_PATH',
    headless: false,
    args: [
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor'
    ]
  });
  
  const page = await browser.newPage();
  await page.goto('http://localhost:5173');
  
  // 在这里进行你的操作...
  
  await browser.close();
}

// 运行示例
if (require.main === module) {
  connectToChrome();
}

module.exports = { connectToChrome, launchSystemChrome };
EOF

echo "✅ 创建了playwright-connect-example.js示例代码"

# 更新shell配置文件
SHELL_CONFIG="$HOME/.zshrc"
if [ ! -f "$SHELL_CONFIG" ]; then
    SHELL_CONFIG="$HOME/.bash_profile"
fi

if [ -f "$SHELL_CONFIG" ]; then
    echo "" >> "$SHELL_CONFIG"
    echo "# Playwright Chrome配置" >> "$SHELL_CONFIG"
    echo "export PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH='$CHROME_PATH'" >> "$SHELL_CONFIG"
    echo "export PLAYWRIGHT_BROWSERS_PATH='/Applications/Google Chrome.app/Contents/MacOS/'" >> "$SHELL_CONFIG"
    echo "✅ 已更新 $SHELL_CONFIG"
fi

echo ""
echo "🎉 配置完成！"
echo ""
echo "📋 使用方法:"
echo "1. 重新加载shell配置: source $SHELL_CONFIG"
echo "2. 启动Chrome调试模式: ./start-chrome-debug.sh"
echo "3. 运行Playwright测试: npx playwright test"
echo "4. 查看示例代码: cat playwright-connect-example.js"
echo ""
echo "💡 提示:"
echo "- 现在Playwright将使用您的系统Chrome"
echo "- 可以保持登录状态和插件"
echo "- 调试时更加方便"
echo ""