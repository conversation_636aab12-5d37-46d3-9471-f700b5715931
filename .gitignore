# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
.DS_Store
dist
*.local

# 环境变量文件（企业级安全规范）
# 注意：以下文件现已加入版本管理，请确保仓库访问安全
# .env.local
# .env.*.local
# .env.development.local
# .env.production.local
.env.staging.local

# 敏感配置文件
.env.secrets
config/secrets.json

# 开发者个人配置
.vscode/settings.json
.idea/workspace.xml

# Editor directories and files
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?