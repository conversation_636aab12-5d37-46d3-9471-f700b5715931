# AI代码修改范围约束规则

> 🚨 **强制执行规则** - 防止AI修改到不应该修改的地方

## 📋 问题背景

在使用AI修改代码时，经常遇到以下问题：
1. **范围扩散** - 只想修改A页面，结果修改了全局配置
2. **误伤无关代码** - 修改某个功能时，影响了其他不相关的功能
3. **破坏现有逻辑** - 为了实现新功能，破坏了已有的稳定代码
4. **过度重构** - 简单修改变成了大范围重构

## 🚨 强制约束规则

### 1. 修改范围明确性约束

#### 1.1 单一职责修改原则
**必须**: 每次修改只能针对一个明确的功能点或文件
```typescript
// ✅ 正确：只修改登录页面的验证逻辑
// 文件：src/pages/auth/login.vue
const validateForm = () => {
  // 只修改这个函数内部的逻辑
  if (!formData.value.phone) {
    return '请输入手机号'
  }
  // 新增验证逻辑
  if (!/^1[3-9]\d{9}$/.test(formData.value.phone)) {
    return '手机号格式不正确'
  }
  return null
}

// ❌ 错误：同时修改多个不相关的文件
// 不能在修改登录页面时，同时修改全局配置、其他页面等
```

#### 1.2 影响范围声明约束
**必须**: 修改前必须明确声明影响范围
```markdown
## 修改影响范围声明
- **目标文件**: src/pages/auth/login.vue
- **修改功能**: 手机号验证逻辑
- **影响范围**: 仅限登录页面的表单验证
- **不影响**: 其他页面、全局配置、API接口
- **依赖检查**: 无其他文件依赖此修改
```

#### 1.3 修改边界约束
**必须**: 严格限制修改边界，不得超出指定范围
```typescript
// ✅ 正确：只修改指定函数
const handleLogin = async () => {
  // 只在这个函数内部添加新逻辑
  const validation = validateForm()
  if (validation) {
    uni.showToast({ title: validation, icon: 'none' })
    return
  }
  // ... 其他逻辑保持不变
}

// ❌ 错误：修改了函数外部的代码
// 不能修改其他函数、组件结构、导入语句等
```

### 2. 文件级别约束

#### 2.1 单文件修改原则
**必须**: 除非明确要求，否则一次只修改一个文件
```bash
# ✅ 正确：单文件修改
修改文件: src/pages/auth/login.vue
修改内容: 添加手机号验证

# ❌ 错误：多文件修改
同时修改:
- src/pages/auth/login.vue
- src/utils/validation.ts
- src/api/modules/auth.ts
- src/store/modules/auth.ts
```

#### 2.2 文件类型隔离约束
**必须**: 不同类型的文件修改需要明确授权
```typescript
// 页面文件修改 - 需要明确指定
// src/pages/auth/login.vue

// 工具函数修改 - 需要单独授权
// src/utils/validation.ts

// API接口修改 - 需要单独授权
// src/api/modules/auth.ts

// 全局配置修改 - 需要特别授权
// src/config/auth.ts
```

#### 2.3 依赖文件保护约束
**必须**: 被多个文件依赖的公共文件需要特别保护
```typescript
// 🔒 高保护级别文件（需要特别授权才能修改）
src/utils/request/auth.ts        // 网络请求基础库
src/store/modules/auth.ts          // 认证状态管理
src/config/auth.ts               // 全局配置
src/components/base/               // 基础组件库
src/api/interceptors/              // 请求拦截器

// 🔓 低保护级别文件（可以正常修改）
src/pages/modules/*/               // 具体页面文件
src/components/business/           // 业务组件
```

### 3. 功能模块约束

#### 3.1 模块边界约束
**必须**: 修改必须在指定的功能模块内进行
```typescript
// ✅ 正确：在认证模块内修改
src/pages/auth/                   // 认证相关页面
src/api/modules/auth.ts            // 认证相关API
src/store/modules/auth.ts          // 认证相关状态

// ❌ 错误：跨模块修改
修改认证功能时，不能同时修改：
src/pages/modules/user/            // 用户模块
src/pages/modules/message/         // 消息模块
src/pages/modules/match/           // 匹配模块
```

#### 3.2 核心功能保护约束
**必须**: 核心功能模块需要特别保护
```typescript
// 🔒 核心功能（禁止随意修改）
- 用户认证系统
- 支付相关功能
- 数据加密解密
- 网络请求基础设施
- 状态管理核心逻辑

// 🔓 业务功能（可以正常修改）
- 页面UI展示
- 表单验证逻辑
- 页面交互效果
- 业务流程优化
```

### 4. 代码层级约束

#### 4.1 函数级别修改约束
**必须**: 优先在函数内部修改，避免修改函数签名
```typescript
// ✅ 正确：函数内部修改
const validatePhone = (phone: string): boolean => {
  // 在函数内部添加新的验证逻辑
  if (!phone) return false
  
  // 新增：更严格的手机号验证
  if (!/^1[3-9]\d{9}$/.test(phone)) return false
  
  return true
}

// ❌ 错误：修改函数签名
const validatePhone = (phone: string, countryCode: string): boolean => {
  // 修改函数签名会影响所有调用此函数的地方
}
```

#### 4.2 组件结构保护约束
**必须**: 保护现有组件结构，避免破坏性修改
```vue
<!-- ✅ 正确：在现有结构内添加 -->
<template>
  <view class="login-page">
    <view class="form-container">
      <input v-model="formData.phone" placeholder="请输入手机号" />
      <!-- 新增：错误提示 -->
      <view v-if="phoneError" class="error-tip">{{ phoneError }}</view>
      <input v-model="formData.password" placeholder="请输入密码" />
      <button @click="handleLogin">登录</button>
    </view>
  </view>
</template>

<!-- ❌ 错误：破坏现有结构 -->
<template>
  <!-- 完全重写组件结构会影响样式和其他逻辑 -->
  <view class="new-login-layout">
    <!-- 全新的结构 -->
  </view>
</template>
```

### 5. 样式修改约束

#### 5.1 样式作用域约束
**必须**: 样式修改必须限制在指定组件内
```scss
// ✅ 正确：组件内样式修改
.login-page {
  .form-container {
    // 只修改登录页面的样式
    .error-tip {
      color: #ff4757;
      font-size: 24rpx;
      margin-top: 8rpx;
    }
  }
}

// ❌ 错误：全局样式修改
.error-tip {
  // 这会影响所有页面的错误提示样式
  color: #ff4757;
}
```

#### 5.2 样式变量保护约束
**必须**: 全局样式变量需要特别保护
```scss
// 🔒 受保护的全局变量（需要特别授权）
$primary-color: #007aff;
$success-color: #4cd964;
$error-color: #ff4757;
$font-size-base: 28rpx;

// 🔓 组件内样式变量（可以正常修改）
.login-page {
  $local-error-color: #ff6b7a; // 组件内局部变量
}
```

### 6. 配置文件约束

#### 6.1 配置修改授权约束
**必须**: 配置文件修改需要明确授权
```typescript
// 🔒 高风险配置（需要特别授权）
src/config/auth.ts               // 全局配置
src/manifest.json                 // 应用配置
src/pages.json                    // 页面路由配置
vite.config.ts                    // 构建配置
package.json                      // 依赖配置

// 🔓 低风险配置（可以正常修改）
src/config/constants.ts           // 业务常量
src/config/enums.ts              // 枚举定义
```

#### 6.2 环境配置保护约束
**必须**: 环境相关配置严格保护
```typescript
// 🔒 严格保护（禁止修改）
.env.production                   // 生产环境配置
.env.development                  // 开发环境配置
src/config/api.ts                // API地址配置

// ❌ 错误：不能随意修改环境配置
const API_BASE_URL = 'https://new-api.example.com' // 可能导致生产问题
```

### 7. 修改确认机制

#### 7.1 修改前确认约束
**必须**: 修改前必须确认影响范围
```markdown
## 修改确认清单
- [ ] 确认修改目标文件
- [ ] 确认修改功能范围
- [ ] 确认不会影响其他模块
- [ ] 确认不会破坏现有功能
- [ ] 确认修改方式最小化
```

#### 7.2 修改后验证约束
**必须**: 修改后必须验证影响范围
```markdown
## 修改验证清单
- [ ] 目标功能正常工作
- [ ] 其他功能未受影响
- [ ] 没有引入新的错误
- [ ] 代码风格保持一致
- [ ] 类型检查通过
```

### 8. 特殊情况处理

#### 8.1 紧急修复约束
**必须**: 紧急修复也要遵循最小化原则
```typescript
// ✅ 正确：最小化紧急修复
const handleLogin = async () => {
  try {
    // 只修复这一行的bug
    const response = await api.auth.login(formData.value) // 修复参数传递
    // 其他代码保持不变
  } catch (error) {
    // 处理错误
  }
}

// ❌ 错误：借机大范围重构
// 紧急修复时不能进行大范围的代码重构
```

#### 8.2 重构需求约束
**必须**: 重构需求需要明确标识
```markdown
## 重构需求标识
当用户明确提出以下关键词时，才可以进行大范围修改：
- "重构整个模块"
- "全面优化"
- "重新设计"
- "完全重写"
- "架构调整"

## 普通修改需求
以下情况只能进行最小化修改：
- "修复bug"
- "添加功能"
- "优化性能"
- "调整样式"
- "更新逻辑"
```

## 🔧 实施检查清单

### 修改前检查
- [ ] 明确修改目标和范围
- [ ] 确认不会影响其他模块
- [ ] 选择最小化修改方案
- [ ] 备份现有代码（如需要）

### 修改中检查
- [ ] 只修改指定的文件/函数
- [ ] 保持现有代码结构
- [ ] 不修改函数签名（除非必要）
- [ ] 不影响其他功能模块

### 修改后检查
- [ ] 验证目标功能正常
- [ ] 确认其他功能未受影响
- [ ] 检查代码风格一致性
- [ ] 运行类型检查和测试

## 🚨 违规处罚

如果AI违反修改范围约束：

1. **立即停止修改**
2. **回滚到修改前状态**
3. **重新明确修改范围**
4. **采用最小化修改方案**
5. **增加额外的保护措施**

## 📝 修改范围模板

```markdown
## AI修改任务模板

### 修改目标
- **文件**: [具体文件路径]
- **功能**: [具体功能描述]
- **范围**: [修改范围限制]

### 修改约束
- **只能修改**: [明确可以修改的部分]
- **不能修改**: [明确不能修改的部分]
- **保护对象**: [需要保护的代码/功能]

### 验证标准
- **成功标准**: [修改成功的判断标准]
- **失败标准**: [修改失败的判断标准]
- **影响检查**: [需要检查的影响范围]
```

## 🎯 最佳实践

### 1. 渐进式修改
```typescript
// 第一步：最小化修改
const validatePhone = (phone: string) => {
  if (!phone) return false
  // 只添加这一行新逻辑
  if (!/^1[3-9]\d{9}$/.test(phone)) return false
  return true
}

// 第二步：如果需要，再进行下一步修改
// 第三步：逐步完善功能
```

### 2. 功能隔离
```typescript
// ✅ 正确：新功能独立实现
const validatePhoneFormat = (phone: string): boolean => {
  return /^1[3-9]\d{9}$/.test(phone)
}

const validatePhone = (phone: string) => {
  if (!phone) return false
  // 调用新函数，不修改原有逻辑
  if (!validatePhoneFormat(phone)) return false
  return true
}

// ❌ 错误：直接修改原有函数
```

### 3. 向后兼容
```typescript
// ✅ 正确：保持向后兼容
const handleLogin = async (params?: LoginParams) => {
  // 兼容原有调用方式
  const loginData = params || formData.value
  // 新增功能
  const validation = validateForm(loginData)
  if (validation) {
    // 处理验证错误
  }
  // 原有逻辑保持不变
}
```

**记住：最小化修改是最安全的修改！** 🎯
