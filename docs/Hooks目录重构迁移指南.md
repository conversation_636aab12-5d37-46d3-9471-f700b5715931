# Hooks目录重构迁移指南

## 📋 概述

本文档详细说明了从旧的 `composables/useValidation` 验证系统迁移到新的 `hooks/useFormValidation` 统一验证框架的完整过程。

## 🔄 主要变化

### 1. 目录结构变化

```diff
- src/composables/
-   ├─ useValidation.ts
-   └─ ...
+ src/hooks/
+   ├─ useFormValidation.ts
+   ├─ useAuth.ts
+   ├─ useCaptcha.ts
+   ├─ useRequest.ts
+   ├─ useStorage.ts
+   └─ index.ts
```

### 2. API 变化对比

#### 旧版 API (已废弃)
```typescript
// 旧版 useValidation
const { addField, updateField, validateAll, formState } = useValidation()

// 添加字段
addField('username', '', [{ required: true, message: '请输入用户名' }])

// 更新字段值
updateField('username', 'newValue')

// 验证所有字段
const isValid = validateAll()

// 获取错误信息
const errors = formState.value.errorFields
```

#### 新版 API (推荐使用)
```typescript
// 新版 useFormValidation
const { 
  addField, 
  validateField, 
  validateAndShowError, 
  validationState, 
  getFieldError 
} = useFormValidation()

// 添加字段配置
addField('username', {
  label: '用户名',
  rules: [COMMON_RULES.required('请输入用户名')]
})

// 验证单个字段
validateField('username', formData.username)

// 验证所有字段并显示错误
const isValid = await validateAndShowError()

// 获取错误信息
const usernameError = getFieldError('username')
const allErrors = validationState.errors
```

## 🛠️ 迁移步骤

### 步骤 1: 更新导入语句

```diff
- import { useValidation } from '@/composables/useValidation'
+ import { useFormValidation } from '@/hooks/useFormValidation'
+ import { COMMON_RULES } from '@/hooks/useFormValidation'
```

### 步骤 2: 更新初始化代码

```diff
- const { addField, updateField, validateAll, formState } = useValidation()
+ const { 
+   addField, 
+   validateField, 
+   validateAndShowError, 
+   validationState, 
+   getFieldError 
+ } = useFormValidation()
```

### 步骤 3: 更新字段添加方式

```diff
- addField('username', '', [{ required: true, message: '请输入用户名' }])
+ addField('username', {
+   label: '用户名',
+   rules: [COMMON_RULES.required('请输入用户名')]
+ })
```

### 步骤 4: 更新验证逻辑

```diff
- const isValid = validateAll()
+ const isValid = await validateAndShowError()

- updateField('username', newValue)
+ validateField('username', newValue)
```

### 步骤 5: 更新错误处理

```diff
- const errors = formState.value.errorFields
- const usernameError = formState.value.errorFields.username
+ const errors = validationState.errors
+ const usernameError = getFieldError('username')
```

### 步骤 6: 更新表单有效性检查

```diff
- const isFormValid = computed(() => {
-   return Object.keys(formState.value.errorFields).length === 0 && 
-          formData.username && formData.password
- })
+ const isFormValid = computed(() => {
+   return Object.keys(validationState.errors).length === 0 && 
+          formData.username && formData.password
+ })
```

## 📚 常用验证规则

新版本提供了统一的验证规则库 `COMMON_RULES`：

```typescript
import { COMMON_RULES } from '@/hooks/useFormValidation'

// 常用规则示例
addField('username', {
  label: '用户名',
  rules: [
    COMMON_RULES.required('请输入用户名'),
    COMMON_RULES.minLength(3, '用户名至少3个字符')
  ]
})

addField('phone', {
  label: '手机号',
  rules: [
    COMMON_RULES.required('请输入手机号'),
    COMMON_RULES.phone('请输入正确的手机号')
  ]
})

addField('idCard', {
  label: '身份证号',
  rules: [
    COMMON_RULES.required('请输入身份证号'),
    COMMON_RULES.idCard('请输入正确的身份证号')
  ]
})
```

## 🔍 完整迁移示例

### 迁移前 (旧版)
```vue
<template>
  <view class="form">
    <input v-model="formData.username" @blur="validateUsername" />
    <text class="error">{{ formState.errorFields.username }}</text>
    <button :disabled="!isFormValid" @click="handleSubmit">提交</button>
  </view>
</template>

<script setup>
import { useValidation } from '@/composables/useValidation'

const { addField, updateField, validateAll, formState } = useValidation()
const formData = reactive({ username: '', password: '' })

// 初始化字段
addField('username', '', [{ required: true, message: '请输入用户名' }])
addField('password', '', [{ required: true, message: '请输入密码' }])

const validateUsername = () => {
  updateField('username', formData.username)
}

const isFormValid = computed(() => {
  return Object.keys(formState.value.errorFields).length === 0 && 
         formData.username && formData.password
})

const handleSubmit = () => {
  if (validateAll()) {
    // 提交逻辑
  }
}
</script>
```

### 迁移后 (新版)
```vue
<template>
  <view class="form">
    <input v-model="formData.username" @blur="validateUsername" />
    <text class="error">{{ getFieldError('username') }}</text>
    <button :disabled="!isFormValid" @click="handleSubmit">提交</button>
  </view>
</template>

<script setup>
import { useFormValidation, COMMON_RULES } from '@/hooks/useFormValidation'

const { 
  addField, 
  validateField, 
  validateAndShowError, 
  validationState, 
  getFieldError 
} = useFormValidation()

const formData = reactive({ username: '', password: '' })

// 初始化字段配置
addField('username', {
  label: '用户名',
  rules: [COMMON_RULES.required('请输入用户名')]
})
addField('password', {
  label: '密码',
  rules: [COMMON_RULES.required('请输入密码')]
})

const validateUsername = () => {
  validateField('username', formData.username)
}

const isFormValid = computed(() => {
  return Object.keys(validationState.errors).length === 0 && 
         formData.username && formData.password
})

const handleSubmit = async () => {
  if (await validateAndShowError()) {
    // 提交逻辑
  }
}
</script>
```

## ✅ 迁移检查清单

- [ ] 更新所有导入语句
- [ ] 替换 `useValidation` 为 `useFormValidation`
- [ ] 更新字段添加方式（使用 `FieldConfig` 格式）
- [ ] 替换 `updateField` 为 `validateField`
- [ ] 替换 `validateAll` 为 `validateAndShowError`
- [ ] 更新错误获取方式（使用 `getFieldError` 或 `validationState.errors`）
- [ ] 更新表单有效性检查逻辑
- [ ] 使用 `COMMON_RULES` 替换自定义验证规则
- [ ] 测试所有表单功能
- [ ] 确保类型检查通过
- [ ] 确保构建成功

## 🎯 新版本优势

1. **统一的API设计**: 所有验证相关功能使用一致的接口
2. **更好的类型安全**: 完整的TypeScript类型定义
3. **丰富的验证规则**: 内置常用验证规则库
4. **更清晰的错误处理**: 统一的错误状态管理
5. **更好的性能**: 优化的验证逻辑和状态更新
6. **易于维护**: 模块化的代码结构

## 🔗 相关文档

- [企业级开发规范](./企业级开发规范.md)
- [useFormValidation API 文档](../src/hooks/useFormValidation.ts)
- [代码重复优化任务清单](./代码重复优化任务清单.md)

---

**注意**: 旧版 `useValidation` API 已完全废弃，请尽快迁移到新版 `useFormValidation` API。