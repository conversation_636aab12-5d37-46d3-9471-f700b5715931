# 智能Loading功能使用指南

## 概述

为了解决快速响应接口导致Loading闪烁的问题，我们在HTTP拦截器中实现了智能Loading控制功能。该功能支持延迟显示、自定义文本、遮罩控制等多种配置选项。

## 功能特性

### 1. 延迟显示Loading
- **默认延迟**: 200ms后显示Loading，避免快速响应时的闪烁
- **自定义延迟**: 可配置任意延迟时间
- **立即显示**: 设置延迟为0可立即显示

### 2. 智能清理机制
- 自动清除延迟显示的定时器
- 响应完成后自动隐藏Loading
- 请求失败时也能正确清理

### 3. 灵活配置选项
- 自定义Loading文本
- 控制是否显示遮罩
- 完全禁用Loading

## 配置参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `showLoading` | boolean | true | 是否显示Loading |
| `loadingDelay` | number | 200 | 延迟显示时间（毫秒） |
| `loadingText` | string | '加载中...' | Loading显示文本 |
| `loadingMask` | boolean | true | 是否显示遮罩 |

## 使用示例

### 1. 默认配置（推荐）
```typescript
// 使用默认配置，200ms后显示Loading
const response = await http.post('/api/data', data)
```

### 2. 立即显示Loading
```typescript
// 对于预期较慢的请求，立即显示Loading
const response = await http.post('/api/upload', formData, {
  loadingDelay: 0,
  loadingText: '正在上传...'
} as any)
```

### 3. 延迟显示Loading
```typescript
// 对于可能很快完成的请求，延迟更长时间
const response = await http.get('/api/cache-data', {
  loadingDelay: 500,
  loadingText: '正在获取数据...'
} as any)
```

### 4. 禁用Loading
```typescript
// 对于后台静默请求，禁用Loading
const response = await http.post('/api/log', logData, {
  showLoading: false
} as any)
```

### 5. 无遮罩Loading
```typescript
// 允许用户继续操作的Loading
const response = await http.get('/api/background-sync', {
  loadingMask: false,
  loadingText: '后台同步中...'
} as any)
```

### 6. 自定义Loading文本
```typescript
// 提供更具体的操作提示
const response = await http.post('/api/submit-form', formData, {
  loadingText: '正在提交表单，请稍候...',
  loadingDelay: 100
} as any)
```

## 最佳实践

### 1. 根据接口特性选择配置

**快速接口（< 500ms）**
```typescript
// 使用默认延迟或更长延迟
const response = await http.get('/api/quick-data', {
  loadingDelay: 300
} as any)
```

**慢速接口（> 1s）**
```typescript
// 立即显示Loading
const response = await http.post('/api/heavy-process', data, {
  loadingDelay: 0,
  loadingText: '正在处理，请耐心等待...'
} as any)
```

**后台接口**
```typescript
// 禁用Loading或使用无遮罩模式
const response = await http.post('/api/background-task', data, {
  showLoading: false
} as any)
```

### 2. 用户体验优化

**表单提交**
```typescript
const submitForm = async () => {
  try {
    const response = await http.post('/api/submit', formData, {
      loadingText: '正在提交，请勿重复操作...',
      loadingDelay: 0 // 立即显示，防止重复提交
    } as any)
    // 处理成功
  } catch (error) {
    // 处理错误
  }
}
```

**数据刷新**
```typescript
const refreshData = async () => {
  const response = await http.get('/api/refresh', {
    loadingDelay: 200, // 短暂延迟，避免频繁刷新时闪烁
    loadingText: '正在刷新数据...'
  } as any)
}
```

**文件上传**
```typescript
const uploadFile = async (file: File) => {
  const response = await http.post('/api/upload', formData, {
    loadingDelay: 0, // 立即显示
    loadingText: '正在上传文件...',
    loadingMask: true // 阻止用户操作
  } as any)
}
```

## 技术实现

### 1. 请求拦截器
- 根据配置设置延迟显示定时器
- 保存定时器引用到请求配置中
- 支持立即显示和延迟显示两种模式

### 2. 响应拦截器
- 自动清除延迟显示的定时器
- 统一隐藏Loading状态
- 错误情况下也能正确清理

### 3. 错误处理
- 请求失败时清理定时器
- 网络错误时隐藏Loading
- 确保不会出现Loading卡住的情况

## 注意事项

1. **TypeScript类型**: 由于扩展了请求配置，需要使用 `as any` 进行类型断言
2. **定时器清理**: 系统会自动清理定时器，无需手动处理
3. **并发请求**: 每个请求的Loading状态独立管理
4. **兼容性**: 与现有的错误处理和Token刷新机制完全兼容

## 测试建议

1. **快速接口测试**: 验证延迟显示是否生效
2. **慢速接口测试**: 确认Loading正常显示和隐藏
3. **错误场景测试**: 验证错误时Loading能正确清理
4. **并发请求测试**: 确认多个请求的Loading不会冲突

## 示例页面

项目中提供了完整的示例页面：`src/examples/SmartLoadingExample.vue`

该页面演示了所有配置选项的使用方法，可以直接运行测试各种Loading效果。