# 无感刷新Token使用指南

> 🎯 完整的无感刷新access_token实现指南，确保用户无需重新登录

## 📋 系统概述

本系统基于后端返回的双Token机制：
- **access_token**: 短期有效（89秒），存储在本地，用于API调用
- **refresh_token**: 长期有效（604800秒=7天），通过HttpOnly Cookie自动管理

## 🏗️ 核心组件

### 1. TokenManager - Token管理器
```typescript
import { TokenManager } from '@/utils/tokenManager'

// 保存Token（登录成功后调用）
TokenManager.setToken({
  access_token: 'eyJ0eXAiOiJKV1Q...',
  expire_in: 89,
  refresh_expire_in: 604800
})

// 获取当前Token
const token = TokenManager.getToken()

// 检查Token是否有效
const isValid = TokenManager.isTokenValid()

// 获取剩余时间（秒）
const remainingTime = TokenManager.getTokenRemainingTime()

// 手动刷新Token
const success = await TokenManager.refreshToken()

// 清除Token
TokenManager.clearToken()
```

### 2. 数据保护工具
```typescript
import { useFormProtection, usePageState, MultiStorage } from '@/utils/dataProtection'

// 表单数据保护
const { formData, isLoading, clearFormCache } = useFormProtection('login')

// 页面状态保护
const { pageState, clearPageState } = usePageState('userList')

// 多重存储
MultiStorage.set('key', value)
const value = MultiStorage.get('key')
```

### 3. 定时刷新管理器
```typescript
import { accessTokenRefreshManager } from '@/utils/tokenRefreshManager'

// 启动定时刷新（每60秒检查一次）
accessTokenRefreshManager.checkAndStart()

// 停止定时刷新
accessTokenRefreshManager.stopRefreshTimer()

// 检查运行状态
const isRunning = accessTokenRefreshManager.isRunning()
```

### 4. 受保护的请求
```typescript
import { requestWithProtection } from '@/utils/requestWithProtection'

// 带重试和缓存的请求
const response = await requestWithProtection(
  () => api.getUserInfo(userId),
  {
    maxRetries: 3,
    cacheKey: `userInfo_${userId}`,
    cacheTTL: 300000 // 5分钟缓存
  }
)
```

### 5. 受保护的导航
```typescript
import { navigateWithProtection } from '@/utils/navigationProtection'

// 安全的页面跳转
navigateWithProtection('/pages/target/page', {
  pageData: currentPageData,
  saveCurrentState: true,
  navigationType: 'navigateTo'
})
```

## 🔄 工作流程

### 1. 登录流程
```typescript
const handleLogin = async () => {
  try {
    // 1. 调用登录接口
    const response = await passwordLogin(loginParams)
    
    // 2. 保存Token数据
    TokenManager.setToken({
      access_token: response.data.access_token,
      expire_in: response.data.expire_in,
      refresh_expire_in: response.data.refresh_expire_in
    })
    
    // 3. 启动定时刷新
    accessTokenRefreshManager.checkAndStart()
    
    // 4. 跳转到首页
    navigateWithProtection('/pages/home/<USER>', {
      navigationType: 'reLaunch'
    })
  } catch (error) {
    console.error('登录失败:', error)
  }
}
```

### 2. 自动刷新流程

#### 定时器主动刷新
```typescript
// 每60秒检查一次
setInterval(() => {
  if (TokenManager.shouldRefreshToken()) {
    // 剩余时间小于阈值时自动刷新
    TokenManager.refreshToken()
  }
}, 60000)
```

#### 请求拦截器主动检查
```typescript
request.useRequestInterceptor(async (config) => {
  // 每次请求前检查是否需要刷新
  if (TokenManager.shouldRefreshToken()) {
    TokenManager.refreshToken().catch(console.error)
  }
  
  // 添加access_token到请求头
  const token = TokenManager.getToken()
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})
```

#### 响应拦截器兜底刷新
```typescript
request.useResponseInterceptor(async (response) => {
  if (response.code === 401) {
    // 收到401错误时的兜底处理
    const refreshSuccess = await TokenManager.refreshToken()
    if (!refreshSuccess) {
      // 刷新失败，跳转登录页
      uni.reLaunch({ url: '/pages/auth/login' })
    }
  }
  return response
})
```

### 3. 生命周期集成

#### App.vue中的集成
```typescript
import { setupTokenRefreshLifecycle } from '@/utils/tokenRefreshManager'

const { handleAppShow, handleAppHide } = setupTokenRefreshLifecycle()

onShow(() => {
  // 应用显示时启动定时器
  handleAppShow()
})

onHide(() => {
  // 应用隐藏时停止定时器
  handleAppHide()
})
```

## 📱 页面使用示例

### 登录页面
```vue
<script setup lang="ts">
import { useFormProtection } from '@/utils/dataProtection'
import { TokenManager } from '@/utils/tokenManager'
import { navigateWithProtection } from '@/utils/navigationProtection'

// 表单数据保护
const { formData, isLoading, clearFormCache } = useFormProtection('login')

const handleLogin = async () => {
  try {
    const response = await passwordLogin(formData.value)
    
    // 保存Token
    TokenManager.setToken(response.data)
    
    // 清除表单缓存
    clearFormCache()
    
    // 安全跳转
    navigateWithProtection('/pages/home/<USER>', {
      navigationType: 'reLaunch'
    })
  } catch (error) {
    console.error('登录失败:', error)
  }
}
</script>
```

### 业务页面
```vue
<script setup lang="ts">
import { usePageState } from '@/utils/dataProtection'
import { requestWithProtection } from '@/utils/requestWithProtection'

// 页面状态保护
const { pageState } = usePageState('userList')

const loadUserList = async () => {
  try {
    const response = await requestWithProtection(
      () => api.getUserList(params),
      {
        maxRetries: 3,
        cacheKey: 'userList',
        cacheTTL: 300000
      }
    )
    
    userList.value = response.data
  } catch (error) {
    console.error('加载用户列表失败:', error)
  }
}
</script>
```

## ⚠️ 注意事项

### 1. 时间计算
- 所有时间相关字段均以**秒(s)**为单位
- obtain_time使用**毫秒**时间戳
- 动态计算刷新阈值：`Math.min(300, expire_in * 0.8)`

### 2. Cookie处理
- refresh_token通过HttpOnly Cookie自动携带
- 无需手动管理refresh_token
- 确保请求支持withCredentials

### 3. 错误处理
- 网络错误：重试机制
- 401错误：触发Token刷新
- refresh_token过期：跳转登录页

### 4. 性能考虑
- 内存缓存优先，避免频繁读取存储
- 应用隐藏时停止定时器节省资源
- API响应缓存减少重复请求

## 🔧 调试和监控

### 1. 控制台日志
```typescript
// Token状态日志
console.log('[TokenManager] Token状态:', {
  expireIn: '89秒',
  remainingTime: '45秒',
  needRefresh: true
})

// 刷新成功日志
console.log('[TokenManager] access_token刷新成功')

// 定时器状态日志
console.log('[RefreshManager] 定时器已启动（每60秒检查一次）')
```

### 2. 调试工具
```typescript
// 获取Token剩余时间
const remainingTime = TokenManager.getTokenRemainingTime()

// 检查定时器状态
const isRunning = accessTokenRefreshManager.isRunning()

// 获取缓存信息
const cacheInfo = ApiCacheManager.getCacheInfo()
```

## 🚨 故障排除

### 问题1：Token频繁过期
**原因**: 定时器未启动或被意外停止
**解决**: 检查App.vue中的生命周期集成

### 问题2：登录状态丢失
**原因**: 数据存储失败或被清除
**解决**: 检查MultiStorage的多重存储策略

### 问题3：请求401错误
**原因**: 兜底刷新机制未生效
**解决**: 检查请求拦截器的集成

### 问题4：页面刷新后数据丢失
**原因**: 未使用数据保护工具
**解决**: 使用useFormProtection和usePageState

## 📊 监控指标

- Token刷新成功率
- 用户重新登录频率
- API请求401错误率
- 应用启动到首次刷新的时间

通过这套完整的无感刷新Token系统，可以显著提升用户体验，减少因Token过期导致的登录中断问题。 