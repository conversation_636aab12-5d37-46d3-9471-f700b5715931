# 企业级UniApp+Vue3+TypeScript项目结构规范

## 一、整体目录架构

```
src/
├─ api/                    # 接口层
│   ├─ modules/           # 模块化接口
│   │   ├─ auth.ts        # 认证相关接口
│   │   ├─ user.ts        # 用户相关接口
│   │   ├─ captcha.ts     # 验证码相关接口
│   │   └─ auth.ts       # 统一导出
│   ├─ interceptors/      # 请求拦截器
│   │   ├─ request.ts     # 请求拦截器
│   │   ├─ response.ts    # 响应拦截器
│   │   └─ error.ts       # 错误处理拦截器
│   └─ types/            # 接口类型声明
│       ├─ auth.ts        # 认证相关类型
│       ├─ user.ts        # 用户相关类型
│       ├─ common.ts      # 通用类型
│       └─ auth.ts       # 统一导出
├─ components/            # 通用组件
│   ├─ business/         # 业务组件
│   │   ├─ CaptchaModal/  # 验证码模态框组件
│   │   │   ├─ index.vue
│   │   │   ├─ types.ts
│   │   │   └─ composables.ts
│   │   └─ UserCard/      # 用户卡片组件
│   │       ├─ index.vue
│   │       └─ types.ts
│   └─ base/             # 基础UI组件
│       ├─ Button/        # 按钮组件
│       ├─ Modal/         # 模态框组件
│       ├─ Form/          # 表单组件
│       └─ auth.ts       # 统一导出
├─ composables/           # Composition API逻辑复用
│   ├─ useAuth.ts         # 认证相关逻辑
│   ├─ useRequest.ts      # 请求相关逻辑
│   ├─ useCaptcha.ts      # 验证码逻辑
│   ├─ useStorage.ts      # 存储相关逻辑
│   └─ auth.ts           # 统一导出
├─ config/                # 环境配置
│   ├─ env.ts            # 环境变量配置
│   ├─ constants.ts       # 常量配置
│   └─ auth.ts          # 统一导出
├─ store/                 # Pinia状态管理
│   ├─ modules/          # 模块化状态
│   │   ├─ auth.ts       # 认证状态
│   │   ├─ user.ts       # 用户状态
│   │   └─ app.ts        # 应用状态
│   ├─ types/            # Store类型声明
│   │   └─ auth.ts
│   └─ auth.ts          # Store入口
├─ utils/                 # 工具库
│   ├─ request/          # 封装请求实例
│   │   ├─ auth.ts      # 主要请求实例
│   │   ├─ config.ts     # 请求配置
│   │   └─ retry.ts      # 重试机制
│   ├─ validate/         # 校验规则
│   │   ├─ rules.ts      # 校验规则定义
│   │   └─ auth.ts      # 统一导出
│   ├─ helpers/          # 通用工具函数
│   │   ├─ format.ts     # 格式化工具
│   │   ├─ storage.ts    # 存储工具
│   │   ├─ crypto.ts     # 加密工具
│   │   └─ auth.ts      # 统一导出
│   └─ auth.ts          # 工具库统一导出
├─ types/                 # 全局类型声明
│   ├─ global.d.ts       # 全局类型
│   ├─ uni.d.ts          # uni-app相关类型扩展
│   └─ env.d.ts          # 环境变量类型
└─ pages/                 # 页面组件
    ├─ modules/          # 按业务模块分组
    │   ├─ auth/         # 认证模块
    │   │   ├─ login/
    │   │   └─ register/
    │   ├─ user/         # 用户模块
    │   │   ├─ profile/
    │   │   └─ settings/
    │   └─ home/         # 首页模块
    │       └─ index/
    └─ auth.ts          # 页面路由配置
```

## 二、分层架构说明

### 1. API层架构
- **modules/**: 按业务模块组织接口，每个模块独立维护
- **interceptors/**: 请求/响应拦截器，统一处理认证、错误等
- **types/**: 接口相关类型定义，确保类型安全

### 2. 组件层架构
- **business/**: 业务相关组件，与具体业务逻辑耦合
- **base/**: 基础UI组件，可复用的通用组件

### 3. 逻辑层架构
- **composables/**: Composition API形式的逻辑复用
- **utils/**: 纯函数工具库，无状态依赖

### 4. 状态管理架构
- **modules/**: 按功能模块组织Store
- **types/**: Store相关类型定义

### 5. 配置层架构
- **config/**: 环境配置、常量等全局配置
- **types/**: 全局类型声明

## 三、命名规范

### 文件命名
- **组件文件**: PascalCase (如: `UserProfile.vue`)
- **工具函数文件**: camelCase (如: `formatTime.ts`)
- **类型文件**: camelCase + .types.ts (如: `user.types.ts`)
- **常量文件**: UPPER_SNAKE_CASE (如: `API_CONSTANTS.ts`)

### 目录命名
- **功能目录**: camelCase (如: `userManagement/`)
- **组件目录**: PascalCase (如: `UserCard/`)

### 变量命名
- **普通变量**: camelCase
- **常量**: UPPER_SNAKE_CASE
- **类型**: PascalCase
- **接口**: PascalCase + I前缀 (如: `IUserInfo`)

## 四、导入导出规范

### 统一导出模式
每个目录都包含 `auth.ts` 文件作为统一导出入口：

```typescript
// components/base/auth.ts
export { default as Button } from './Button/index.vue'
export { default as Modal } from './Modal/index.vue'
export { default as Form } from './Form/index.vue'
```

### 路径别名配置
```typescript
// tsconfig.json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/api/*": ["./src/api/*"],
      "@/components/*": ["./src/components/*"],
      "@/utils/*": ["./src/utils/*"],
      "@/types/*": ["./src/types/*"]
    }
  }
}
```

## 五、代码组织原则

### 1. 单一职责原则
- 每个文件只负责一个功能
- 组件只关注自身职责

### 2. 依赖倒置原则
- 高层模块不依赖低层模块
- 通过接口和类型约束依赖关系

### 3. 开闭原则
- 对扩展开放，对修改关闭
- 通过配置化、插件化支持扩展

### 4. 模块化原则
- 功能相关的代码组织在同一模块
- 模块间通过明确的接口通信 
