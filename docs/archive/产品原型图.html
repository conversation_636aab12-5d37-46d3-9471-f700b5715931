<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>乐享生活 - 双向服务聚合平台原型</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
            padding: 20px;
        }
        
        .phone-container {
            max-width: 375px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 30px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3), 0 0 0 1px rgba(255,255,255,0.1);
            overflow: hidden;
            position: relative;
            backdrop-filter: blur(20px);
        }
        
        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-size: 14px;
            font-weight: 600;
            letter-spacing: 0.5px;
        }
        
        .header {
            height: 56px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            position: relative;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
        }
        
        .header h1 {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
            letter-spacing: -0.3px;
            color: white;
        }
        
        .role-switch {
            background: rgba(255,255,255,0.15);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 18px;
            padding: 2px;
            display: flex;
            font-size: 12px;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }
        
        .role-switch button {
            background: none;
            border: none;
            color: rgba(255,255,255,0.8);
            padding: 6px 12px;
            border-radius: 16px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            letter-spacing: 0.2px;
            font-size: 12px;
        }
        
        .role-switch button.active {
            background: rgba(255,255,255,0.95);
            color: #667eea;
            box-shadow: 0 2px 10px rgba(0,0,0,0.15);
            transform: translateY(-1px);
        }
        
        .content {
            height: 520px;
            overflow-y: auto;
            padding: 16px;
            background: linear-gradient(180deg, #fafbff 0%, #f8f9ff 100%);
        }
        
        .content::-webkit-scrollbar {
            width: 4px;
        }
        
        .content::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .content::-webkit-scrollbar-thumb {
            background: rgba(102, 126, 234, 0.3);
            border-radius: 2px;
        }
        
        .search-bar {
            background: rgba(255,255,255,0.9);
            border: 1px solid rgba(102, 126, 234, 0.1);
            border-radius: 20px;
            padding: 12px 16px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 2px 12px rgba(102, 126, 234, 0.08);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .search-bar:focus-within {
            border-color: #667eea;
            box-shadow: 0 4px 25px rgba(102, 126, 234, 0.15);
            transform: translateY(-1px);
        }
        
        .search-bar input {
            border: none;
            background: none;
            flex: 1;
            outline: none;
            font-size: 15px;
            font-weight: 400;
            color: #333;
        }
        
        .search-bar input::placeholder {
            color: #999;
            font-weight: 400;
        }
        
        .category-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 4px;
            margin-bottom: 16px;
            padding: 0 4px;
        }
        
        .category-item {
            text-align: center;
            padding: 8px 4px;
            background: rgba(255,255,255,0.9);
            border: 1px solid rgba(102, 126, 234, 0.06);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .category-item:hover {
            transform: translateY(-2px);
            background: rgba(102, 126, 234, 0.05);
        }
        
        .category-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 8px;
            margin: 0 auto 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }
        
        .category-name {
            font-size: 10px;
            color: #666;
            font-weight: 500;
            line-height: 1.1;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 20px;
            color: #2d3748;
            letter-spacing: -0.5px;
        }
        
        .config-note {
            background: rgba(102, 126, 234, 0.08);
            border: 1px solid rgba(102, 126, 234, 0.15);
            border-radius: 12px;
            padding: 12px 16px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .config-icon {
            font-size: 16px;
            opacity: 0.8;
        }
        
        .config-text {
            font-size: 12px;
            color: #667eea;
            font-weight: 500;
            line-height: 1.4;
        }
        
        #dynamicGridItems {
            position: relative;
        }
        
        #dynamicGridItems::before {
            content: "";
            position: absolute;
            top: -8px;
            left: -8px;
            right: -8px;
            bottom: -8px;
            border: 2px dashed rgba(102, 126, 234, 0.2);
            border-radius: 12px;
            pointer-events: none;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.6; }
        }
        
        .service-card {
            background: rgba(255,255,255,0.95);
            border: 1px solid rgba(102, 126, 234, 0.08);
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 30px rgba(102, 126, 234, 0.08);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }
        
        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        
        .service-card:hover {
            transform: translateY(-6px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.15);
            border-color: rgba(102, 126, 234, 0.2);
        }
        
        .service-card:hover::before {
            transform: scaleX(1);
        }
        
        .service-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
        }
        
        .avatar {
            width: 48px;
            height: 48px;
            border-radius: 16px;
            background: linear-gradient(135deg, #ffeaa7, #fab1a0);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 16px;
            box-shadow: 0 4px 15px rgba(255, 234, 167, 0.4);
        }
        
        .service-info {
            flex: 1;
        }
        
        .service-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #2d3748;
            letter-spacing: -0.2px;
        }
        
        .service-meta {
            font-size: 13px;
            color: #718096;
            font-weight: 400;
        }
        
        .service-price {
            color: #e53e3e;
            font-weight: 700;
            font-size: 18px;
            letter-spacing: -0.3px;
        }
        
        .service-description {
            font-size: 14px;
            color: #4a5568;
            line-height: 1.6;
            margin-bottom: 16px;
            font-weight: 400;
        }
        
        .service-tags {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .tag {
            background: linear-gradient(135deg, #e6f3ff, #f0f8ff);
            color: #2b6cb0;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            border: 1px solid rgba(43, 108, 176, 0.1);
            transition: all 0.2s ease;
        }
        
        .tag:hover {
            background: linear-gradient(135deg, #bee3f8, #e6f3ff);
            transform: translateY(-1px);
        }
        
        .bottom-nav {
            height: 80px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(102, 126, 234, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 10px 0;
            box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.05);
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 6px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 8px 12px;
            border-radius: 12px;
            position: relative;
        }
        
        .nav-item:hover {
            background: rgba(102, 126, 234, 0.05);
            transform: translateY(-2px);
        }
        
        .nav-item.active {
            color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }
        
        .nav-item.active::before {
            content: '';
            position: absolute;
            top: -1px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 0 0 3px 3px;
        }
        
        .nav-icon {
            width: 26px;
            height: 26px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            border-radius: 8px;
            transition: all 0.3s ease;
            background: rgba(160, 174, 192, 0.1);
        }
        
        .nav-item.active .nav-icon {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .nav-label {
            font-size: 11px;
            font-weight: 500;
            letter-spacing: -0.1px;
        }
        
        .floating-btn {
            position: absolute;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            transition: transform 0.2s;
        }
        
        .floating-btn:hover {
            transform: scale(1.1);
        }
        
        .mode-indicator {
            position: absolute;
            top: 8px;
            left: 16px;
            background: rgba(255,255,255,0.9);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            color: #667eea;
            font-weight: 600;
            backdrop-filter: blur(10px);
        }
        
        .provider-mode {
            background: linear-gradient(135deg, #ff9a56, #ff6b6b);
        }
        
        .seeker-mode {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
        }
        
        .demo-controls {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .demo-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            margin: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }
        
        .demo-btn:hover {
            background: #5a67d8;
        }
        
        .form-group {
            margin-bottom: 24px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 10px;
            font-weight: 600;
            color: #2d3748;
            font-size: 15px;
            letter-spacing: -0.2px;
        }
        
        .form-input, .form-textarea, .form-select {
            width: 100%;
            padding: 16px;
            border: 2px solid rgba(102, 126, 234, 0.1);
            border-radius: 16px;
            font-size: 15px;
            box-sizing: border-box;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            color: #2d3748;
            font-weight: 400;
        }
        
        .form-input:focus, .form-textarea:focus, .form-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(-2px);
        }
        
        .form-input::placeholder, .form-textarea::placeholder {
            color: #a0aec0;
            font-weight: 400;
        }
        
        .form-textarea {
            height: 100px;
            resize: vertical;
            line-height: 1.6;
        }
        
        .publish-btn {
            width: 100%;
            padding: 18px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 16px;
            font-size: 17px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            letter-spacing: -0.3px;
            position: relative;
            overflow: hidden;
        }
        
        .publish-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .publish-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }
        
        .publish-btn:hover::before {
            left: 100%;
        }
        
        .publish-btn:active {
            transform: translateY(-1px);
        }
        
        .form-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 8px 30px rgba(102, 126, 234, 0.08);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(102, 126, 234, 0.08);
        }
        
        .back-btn {
            background: rgba(102, 126, 234, 0.1);
            border: none;
            border-radius: 12px;
            padding: 10px 16px;
            color: #667eea;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .back-btn:hover {
            background: rgba(102, 126, 234, 0.15);
            transform: translateX(-2px);
        }
        
        /* 运营后台样式 */
        .admin-entry-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .admin-entry-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }
        
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .admin-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .admin-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .config-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .config-section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .mini-phone-preview {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            border: 2px dashed #ddd;
        }
        
        .mini-category-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
        }
        
        .mini-category-item {
            background: white;
            padding: 8px 4px;
            border-radius: 8px;
            text-align: center;
            font-size: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }
        
        .mini-category-item:hover {
            transform: scale(1.05);
        }
        
        .config-action-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }
        
        .config-action-card:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }
        
        .config-action-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .config-action-icon {
            font-size: 20px;
            margin-right: 10px;
        }
        
        .config-action-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        
        .config-action-desc {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
            line-height: 1.4;
        }
        
        .config-action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .config-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .publish-config-area {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 15px;
        }
        
        .publish-note {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .publish-note-icon {
            font-size: 18px;
            margin-right: 8px;
        }
        
        .publish-note-text {
            color: #856404;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .publish-actions {
            display: flex;
            gap: 10px;
        }
        
        .publish-test-btn {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .publish-prod-btn {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .publish-test-btn:hover,
        .publish-prod-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        /* 我的页面样式 */
        .user-profile-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            padding: 20px;
            margin: 16px 0;
            color: white;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        }

        .user-header {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            margin-bottom: 20px;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            border: 3px solid rgba(255, 255, 255, 0.3);
            flex-shrink: 0;
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .user-status {
            display: flex;
            gap: 8px;
            margin-bottom: 8px;
        }

        .status-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-badge.verified {
            background: rgba(76, 175, 80, 0.8);
        }

        .role-badge {
            background: rgba(255, 152, 0, 0.8);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .user-stats {
            display: flex;
            gap: 16px;
            font-size: 13px;
            opacity: 0.9;
        }

        .user-actions {
            flex-shrink: 0;
        }

        .edit-profile-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .edit-profile-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .earnings-section {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
        }

        .earnings-title {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 8px;
        }

        .earnings-amount {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .earnings-detail {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            opacity: 0.8;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin: 20px 0;
        }

        .action-item {
            background: white;
            border-radius: 12px;
            padding: 16px 8px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .action-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .action-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .action-text {
            font-size: 12px;
            color: #333;
            font-weight: 500;
        }

        .action-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #ff4757;
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: 600;
            min-width: 16px;
            text-align: center;
        }

        .service-management, .order-status, .certification-center {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin: 16px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .view-all {
            font-size: 12px;
            color: #667eea;
            cursor: pointer;
            font-weight: 500;
        }

        .service-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .service-item:last-child {
            border-bottom: none;
        }

        .service-icon {
            font-size: 20px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .service-content {
            flex: 1;
        }

        .service-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }

        .service-status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 8px;
            margin-bottom: 4px;
            display: inline-block;
        }

        .service-status.active {
            background: #e8f5e8;
            color: #4caf50;
        }

        .service-status.paused {
            background: #fff3e0;
            color: #ff9800;
        }

        .service-stats {
            font-size: 11px;
            color: #666;
        }

        .manage-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .manage-btn:hover {
            background: #5a6fd8;
        }

        .order-tabs {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
        }

        .order-tab {
            text-align: center;
            padding: 12px 8px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .order-tab.active {
            background: #667eea;
            color: white;
        }

        .order-tab:not(.active) {
            background: #f8f9fa;
            color: #666;
        }

        .order-tab:hover:not(.active) {
            background: #e9ecef;
        }

        .tab-icon {
            font-size: 16px;
            margin-bottom: 4px;
        }

        .tab-text {
            font-size: 11px;
            font-weight: 500;
            margin-bottom: 2px;
        }

        .tab-count {
            font-size: 10px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 1px 4px;
            min-width: 14px;
            display: inline-block;
        }

        .order-tab:not(.active) .tab-count {
            background: #667eea;
            color: white;
        }

        .cert-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .cert-item:last-child {
            border-bottom: none;
        }

        .cert-icon {
            font-size: 16px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
        }

        .cert-item.completed .cert-icon {
            background: #e8f5e8;
        }

        .cert-item.pending .cert-icon {
            background: #fff3e0;
        }

        .cert-item.available .cert-icon {
            background: #f0f0f0;
        }

        .cert-content {
            flex: 1;
        }

        .cert-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
        }

        .cert-status {
            font-size: 12px;
            color: #666;
        }

        .settings-menu {
            background: white;
            border-radius: 12px;
            margin: 16px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .menu-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .menu-item:last-child {
            border-bottom: none;
        }

        .menu-item:hover {
            background: #f8f9fa;
        }

        .menu-icon {
            font-size: 18px;
            width: 32px;
            text-align: center;
        }

        .menu-text {
            flex: 1;
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .menu-arrow {
            font-size: 14px;
            color: #ccc;
        }

        .logout-section {
            margin: 20px 0;
            text-align: center;
        }

        .logout-btn {
            background: #ff4757;
            color: white;
            border: none;
            padding: 12px 32px;
            border-radius: 24px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: #ff3742;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="demo-controls">
        <h2>乐享生活 - 双向服务聚合平台原型演示</h2>
        <p style="margin: 10px 0; color: #666;">点击下方按钮切换不同页面和模式</p>
        <button class="demo-btn" onclick="showPage('home')">首页 - 发现</button>
        <button class="demo-btn" onclick="showPage('services')">服务广场</button>
        <button class="demo-btn" onclick="showPage('publish')">我要发布</button>
        <button class="demo-btn" onclick="toggleMode()">切换角色模式</button>
    </div>

    <div class="phone-container">
        <div class="status-bar">
            <span>9:41</span>
            <span>乐享生活</span>
            <span>🔋 100%</span>
        </div>
        
        <div class="header">
            <div class="mode-indicator" id="modeIndicator">我在寻找</div>
            <h1 id="pageTitle">发现</h1>
            <div class="role-switch">
                <button id="seekerBtn" class="active" onclick="setMode('seeker')">寻找</button>
                <button id="providerBtn" onclick="setMode('provider')">提供</button>
            </div>
        </div>
        
        <div class="content" id="content">
            <!-- 首页内容 -->
            <div id="homePage">
                <div class="search-bar">
                    <span>🔍</span>
                    <input type="text" placeholder="搜索服务或需求...">
                </div>
                
                <!-- 动态金刚位设计 - 后端配置驱动 -->
                <div class="section-title">🎯 服务入口</div>
                <div class="config-note">
                    <span class="config-icon">⚙️</span>
                    <span class="config-text">金刚位内容由后端动态配置，运营可通过管理后台实时调整</span>
                </div>
                <div class="category-grid" id="dynamicGridItems">
                    <!-- 动态渲染的金刚位项目 -->
                    <div class="category-item">
                        <div class="category-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">🧘</div>
                        <div class="category-name">瑜伽</div>
                    </div>
                    <div class="category-item">
                        <div class="category-icon" style="background: linear-gradient(135deg, #ff9a56, #ff6b6b);">💪</div>
                        <div class="category-name">健身</div>
                    </div>
                    <div class="category-item">
                        <div class="category-icon" style="background: linear-gradient(135deg, #4ecdc4, #44a08d);">🎮</div>
                        <div class="category-name">游戏陪玩</div>
                    </div>
                    <div class="category-item">
                        <div class="category-icon" style="background: linear-gradient(135deg, #ffd93d, #ff9a56);">🍽️</div>
                        <div class="category-name">美食分享</div>
                    </div>
                    <div class="category-item">
                        <div class="category-icon" style="background: linear-gradient(135deg, #a8e6cf, #88d8a3);">💆</div>
                        <div class="category-name">上门按摩</div>
                    </div>
                    <div class="category-item">
                        <div class="category-icon" style="background: linear-gradient(135deg, #ff9a9e, #fecfef);">🦶</div>
                        <div class="category-name">足疗服务</div>
                    </div>
                    <div class="category-item">
                        <div class="category-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">🧹</div>
                        <div class="category-name">家政服务</div>
                    </div>
                    <div class="category-item">
                        <div class="category-icon" style="background: linear-gradient(135deg, #ff9a56, #ff6b6b);">💇</div>
                        <div class="category-name">美容美发</div>
                    </div>
                    <div class="category-item">
                        <div class="category-icon" style="background: linear-gradient(135deg, #4ecdc4, #44a08d);">📚</div>
                        <div class="category-name">学习辅导</div>
                    </div>
                    <div class="category-item">
                        <div class="category-icon" style="background: linear-gradient(135deg, #ffd93d, #ff9a56);">🎵</div>
                        <div class="category-name">音乐教学</div>
                    </div>
                    <div class="category-item">
                        <div class="category-icon" style="background: linear-gradient(135deg, #a8e6cf, #88d8a3);">🐕</div>
                        <div class="category-name">宠物服务</div>
                    </div>
                    <div class="category-item">
                        <div class="category-icon" style="background: linear-gradient(135deg, #ff9a9e, #fecfef);">➕</div>
                        <div class="category-name">更多服务</div>
                    </div>
                </div>
                
                <div class="section-title">🔥 热门推荐</div>
                
                <div class="service-card">
                    <div class="service-header">
                        <div class="avatar">按</div>
                        <div class="service-info">
                            <div class="service-title">专业上门按摩服务</div>
                            <div class="service-meta">李师傅 · 中医按摩师 · 8年经验 · 距离2km</div>
                        </div>
                        <div class="service-price">¥150/次</div>
                    </div>
                    <div class="service-description">
                        提供专业中医推拿、足疗按摩服务，持有按摩师资格证书。擅长缓解肩颈疲劳、腰椎不适，上门服务更便捷。
                    </div>
                    <div class="service-tags">
                        <span class="tag">中医推拿</span>
                        <span class="tag">足疗按摩</span>
                        <span class="tag">持证上岗</span>
                        <span class="tag">上门服务</span>
                    </div>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <div class="avatar">家</div>
                        <div class="service-info">
                            <div class="service-title">专业家政清洁服务</div>
                            <div class="service-meta">王阿姨 · 家政服务员 · 5年经验 · 距离1.5km</div>
                        </div>
                        <div class="service-price">¥80/小时</div>
                    </div>
                    <div class="service-description">
                        提供专业家庭清洁、整理收纳服务。持有家政服务员证书，经验丰富，服务细致，让您的家焕然一新。
                    </div>
                    <div class="service-tags">
                        <span class="tag">深度清洁</span>
                        <span class="tag">整理收纳</span>
                        <span class="tag">持证服务</span>
                        <span class="tag">经验丰富</span>
                    </div>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <div class="avatar">小</div>
                        <div class="service-info">
                            <div class="service-title">专业瑜伽私教课程</div>
                            <div class="service-meta">小雅老师 · 5年经验 · 距离500m</div>
                        </div>
                        <div class="service-price">¥80/课时</div>
                    </div>
                    <div class="service-description">
                        提供专业的哈他瑜伽、流瑜伽课程，适合初学者和进阶者。拥有国际瑜伽联盟认证，教学经验丰富。
                    </div>
                    <div class="service-tags">
                        <span class="tag">哈他瑜伽</span>
                        <span class="tag">流瑜伽</span>
                        <span class="tag">初学者友好</span>
                        <span class="tag">认证教练</span>
                    </div>
                </div>
            </div>
            
            <!-- 服务广场页面 -->
            <div id="servicesPage" style="display: none;">
                <div class="search-bar">
                    <span>🔍</span>
                    <input type="text" placeholder="搜索具体服务...">
                </div>
                
                <div class="section-title">🏃 运动健身</div>
                
                <div class="service-card">
                    <div class="service-header">
                        <div class="avatar">跑</div>
                        <div class="service-info">
                            <div class="service-title">晨跑陪跑服务</div>
                            <div class="service-meta">跑步爱好者 · 每天6:00-7:00</div>
                        </div>
                        <div class="service-price">¥30/次</div>
                    </div>
                    <div class="service-description">
                        寻找晨跑伙伴，一起享受清晨的美好时光。路线：公园环湖跑道，约5公里。
                    </div>
                    <div class="service-tags">
                        <span class="tag">晨跑</span>
                        <span class="tag">公园</span>
                        <span class="tag">5公里</span>
                    </div>
                </div>
                
                <div class="section-title">💆 上门按摩服务</div>
                
                <div class="service-card">
                    <div class="service-header">
                        <div class="avatar">按</div>
                        <div class="service-info">
                            <div class="service-title">专业中医推拿按摩</div>
                            <div class="service-meta">张师傅 · 中医按摩师 · 10年经验 · 持证上岗</div>
                        </div>
                        <div class="service-price">¥180/次</div>
                    </div>
                    <div class="service-description">
                        提供专业中医推拿、经络疏通、足疗按摩等服务。持有按摩师资格证书，擅长治疗颈椎病、腰椎间盘突出等问题。
                    </div>
                    <div class="service-tags">
                        <span class="tag">中医推拿</span>
                        <span class="tag">经络疏通</span>
                        <span class="tag">足疗按摩</span>
                        <span class="tag">持证上岗</span>
                    </div>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <div class="avatar">足</div>
                        <div class="service-info">
                            <div class="service-title">专业足疗保健服务</div>
                            <div class="service-meta">李师傅 · 足疗技师 · 6年经验 · 上门服务</div>
                        </div>
                        <div class="service-price">¥120/次</div>
                    </div>
                    <div class="service-description">
                        专业足疗保健，通过足部按摩促进血液循环，缓解疲劳。提供上门服务，让您在家享受专业足疗。
                    </div>
                    <div class="service-tags">
                        <span class="tag">足疗保健</span>
                        <span class="tag">血液循环</span>
                        <span class="tag">缓解疲劳</span>
                        <span class="tag">上门服务</span>
                    </div>
                </div>
                
                <div class="section-title">🧹 家政清洁服务</div>
                
                <div class="service-card">
                    <div class="service-header">
                        <div class="avatar">家</div>
                        <div class="service-info">
                            <div class="service-title">专业家庭深度清洁</div>
                            <div class="service-meta">王阿姨 · 家政服务员 · 8年经验 · 持证服务</div>
                        </div>
                        <div class="service-price">¥100/小时</div>
                    </div>
                    <div class="service-description">
                        提供专业家庭清洁服务，包括厨房、卫生间深度清洁，家具除尘，地板打蜡等。持有家政服务员证书。
                    </div>
                    <div class="service-tags">
                        <span class="tag">深度清洁</span>
                        <span class="tag">厨卫清洁</span>
                        <span class="tag">地板打蜡</span>
                        <span class="tag">持证服务</span>
                    </div>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <div class="avatar">收</div>
                        <div class="service-info">
                            <div class="service-title">专业整理收纳服务</div>
                            <div class="service-meta">刘老师 · 整理师 · 3年经验 · 专业认证</div>
                        </div>
                        <div class="service-price">¥150/小时</div>
                    </div>
                    <div class="service-description">
                        专业整理收纳服务，帮助您打造整洁有序的生活空间。擅长衣物整理、书籍分类、储物空间优化。
                    </div>
                    <div class="service-tags">
                        <span class="tag">整理收纳</span>
                        <span class="tag">空间优化</span>
                        <span class="tag">衣物整理</span>
                        <span class="tag">专业认证</span>
                    </div>
                </div>
                
                <div class="section-title">🎮 游戏陪玩</div>
                
                <div class="service-card">
                    <div class="service-header">
                        <div class="avatar">王</div>
                        <div class="service-info">
                            <div class="service-title">王者荣耀上分陪玩</div>
                            <div class="service-meta">王者段位 · 胜率85%</div>
                        </div>
                        <div class="service-price">¥25/局</div>
                    </div>
                    <div class="service-description">
                        专业上分，擅长打野和中单位置。技术过硬，态度友好，带你轻松上王者！
                    </div>
                    <div class="service-tags">
                        <span class="tag">王者荣耀</span>
                        <span class="tag">上分</span>
                        <span class="tag">打野</span>
                        <span class="tag">中单</span>
                    </div>
                </div>
            </div>
            
            <!-- 发布页面 -->
            <div id="publishPage" style="display: none;">
                <div class="section-title">选择发布类型</div>
                
                <div id="publishOptions">
                    <div class="service-card" onclick="showPublishForm('service')">
                        <div class="service-header">
                            <div class="category-icon" style="background: linear-gradient(135deg, #ff9a56, #ff6b6b);">🎯</div>
                            <div class="service-info">
                                <div class="service-title">发布服务单</div>
                                <div class="service-meta">我能提供什么服务</div>
                            </div>
                            <span style="font-size: 20px;">→</span>
                        </div>
                        <div class="service-description">
                            发布你擅长的技能服务，如瑜伽教学、健身指导、游戏陪玩等，让有需要的人找到你。
                        </div>
                    </div>
                    
                    <div class="service-card" onclick="showPublishForm('demand')">
                        <div class="service-header">
                            <div class="category-icon" style="background: linear-gradient(135deg, #4ecdc4, #44a08d);">🔍</div>
                            <div class="service-info">
                                <div class="service-title">发布需求单</div>
                                <div class="service-meta">我需要什么服务</div>
                            </div>
                            <span style="font-size: 20px;">→</span>
                        </div>
                        <div class="service-description">
                            发布你的服务需求，描述你想要的服务类型和要求，让服务提供者主动联系你。
                        </div>
                    </div>
                </div>
                
                <div id="serviceForm" style="display: none;">
                    <button class="back-btn" onclick="showPublishOptions()">
                        ← 返回选择
                    </button>
                    
                    <div class="form-container">
                        <div class="section-title">🎯 发布服务单</div>
                        
                        <div class="form-group">
                            <label class="form-label">服务类型</label>
                            <select class="form-select">
                                <option>选择服务类型</option>
                                <option>瑜伽教学</option>
                                <option>健身指导</option>
                                <option>游戏陪玩</option>
                                <option>美食分享</option>
                                <option>学习辅导</option>
                                <option>音乐教学</option>
                                <option>艺术创作</option>
                                <option>其他</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">服务标题</label>
                            <input type="text" class="form-input" placeholder="简洁描述你的服务，如：专业瑜伽私教课程">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">详细描述</label>
                            <textarea class="form-textarea" placeholder="详细介绍你的服务内容、经验背景、教学方式等...\n\n例如：\n- 服务内容和特色\n- 个人经验和资质\n- 适合的人群\n- 注意事项等"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">服务价格</label>
                            <input type="text" class="form-input" placeholder="如：80元/课时 或 面议">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">服务地点</label>
                            <input type="text" class="form-input" placeholder="如：上门服务、指定场所、线上服务等">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">可服务时间</label>
                            <input type="text" class="form-input" placeholder="如：周一至周五 18:00-21:00">
                        </div>
                        
                        <button class="publish-btn" onclick="publishService()">
                            发布服务
                        </button>
                    </div>
                </div>
                
                <div id="demandForm" style="display: none;">
                    <button class="back-btn" onclick="showPublishOptions()">
                        ← 返回选择
                    </button>
                    
                    <div class="form-container">
                        <div class="section-title">🔍 发布需求单</div>
                        
                        <div class="form-group">
                            <label class="form-label">需求类型</label>
                            <select class="form-select">
                                <option>选择需求类型</option>
                                <option>瑜伽学习</option>
                                <option>健身指导</option>
                                <option>游戏陪玩</option>
                                <option>美食体验</option>
                                <option>学习辅导</option>
                                <option>音乐学习</option>
                                <option>艺术创作</option>
                                <option>其他</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">需求标题</label>
                            <input type="text" class="form-input" placeholder="简洁描述你的需求，如：寻找专业瑜伽老师">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">详细需求</label>
                            <textarea class="form-textarea" placeholder="详细描述你的需求和期望...\n\n例如：\n- 具体需要什么服务\n- 对服务者的要求\n- 期望的服务方式\n- 时间和地点要求等"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">预算范围</label>
                            <input type="text" class="form-input" placeholder="如：50-100元/课时 或 面议">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">期望地点</label>
                            <input type="text" class="form-input" placeholder="如：就近服务、上门服务、线上服务等">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">期望时间</label>
                            <input type="text" class="form-input" placeholder="如：周末 14:00-16:00">
                        </div>
                        
                        <button class="publish-btn" onclick="publishDemand()">
                            发布需求
                        </button>
                    </div>
                </div>
                
                <div class="section-title">快速发布模板</div>
                
                <div class="category-grid" style="grid-template-columns: repeat(2, 1fr); gap: 12px;">
                    <div class="category-item" onclick="quickPublish('yoga')">
                        <div class="category-icon">🧘</div>
                        <div class="category-name">瑜伽课程</div>
                    </div>
                    <div class="category-item" onclick="quickPublish('fitness')">
                        <div class="category-icon">💪</div>
                        <div class="category-name">健身陪练</div>
                    </div>
                    <div class="category-item" onclick="quickPublish('gaming')">
                        <div class="category-icon">🎮</div>
                        <div class="category-name">游戏陪玩</div>
                    </div>
                    <div class="category-item" onclick="quickPublish('food')">
                        <div class="category-icon">🍽️</div>
                        <div class="category-name">聚餐组织</div>
                    </div>
                </div>
            </div>
            
            <!-- 个人中心页面 -->
            <div id="profilePage" style="display: none;">
                <div class="section-title">👤 我的</div>

                <!-- 用户信息卡片 -->
                <div class="user-profile-card">
                    <div class="user-header">
                        <div class="user-avatar">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzAiIGN5PSIzMCIgcj0iMzAiIGZpbGw9IiM2NjdlZWEiLz4KPHN2ZyB3aWR0aD0iMzAiIGhlaWdodD0iMzAiIHZpZXdCb3g9IjAgMCAzMCAzMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE1IDEwQzEyLjc5IDEwIDExIDExLjc5IDExIDE0QzExIDE2LjIxIDEyLjc5IDE4IDE1IDE4QzE3LjIxIDE4IDE5IDE2LjIxIDE5IDE0QzE5IDExLjc5IDE3LjIxIDEwIDE1IDEwWk0xNSAyMEM5LjQ4IDIwIDUgMjEuNzkgNSAyNFYyNkgyNVYyNEMyNSAyMS43OSAyMC41MiAyMCAxNSAyMFoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo8L3N2Zz4K" alt="头像" />
                        </div>
                        <div class="user-info">
                            <div class="user-name">张小明</div>
                            <div class="user-status">
                                <span class="status-badge verified">✓ 已认证</span>
                                <span class="role-badge provider">服务提供者</span>
                            </div>
                            <div class="user-stats">
                                <span class="stat-item">服务次数: 128</span>
                                <span class="stat-item">好评率: 98%</span>
                            </div>
                        </div>
                        <div class="user-actions">
                            <button class="edit-profile-btn">编辑</button>
                        </div>
                    </div>

                    <!-- 收益统计 -->
                    <div class="earnings-section">
                        <div class="earnings-title">💰 本月收益</div>
                        <div class="earnings-amount">¥2,580</div>
                        <div class="earnings-detail">
                            <span>总收益: ¥15,680</span>
                            <span>待结算: ¥320</span>
                        </div>
                    </div>
                </div>

                <!-- 快捷功能区 -->
                <div class="quick-actions">
                    <div class="action-item" onclick="showMyServices()">
                        <div class="action-icon">📋</div>
                        <div class="action-text">我的服务</div>
                        <div class="action-badge">3</div>
                    </div>
                    <div class="action-item" onclick="showMyOrders()">
                        <div class="action-icon">📦</div>
                        <div class="action-text">我的订单</div>
                        <div class="action-badge">5</div>
                    </div>
                    <div class="action-item" onclick="showMyDemands()">
                        <div class="action-icon">🎯</div>
                        <div class="action-text">我的需求</div>
                        <div class="action-badge">2</div>
                    </div>
                    <div class="action-item" onclick="showWallet()">
                        <div class="action-icon">💳</div>
                        <div class="action-text">我的钱包</div>
                    </div>
                </div>

                <!-- 服务管理区 -->
                <div class="service-management">
                    <div class="section-header">
                        <span class="section-title">🛠️ 服务管理</span>
                        <span class="view-all" onclick="showMyServices()">查看全部 →</span>
                    </div>

                    <div class="service-item">
                        <div class="service-icon">🧘</div>
                        <div class="service-content">
                            <div class="service-name">专业瑜伽私教课程</div>
                            <div class="service-status active">进行中</div>
                            <div class="service-stats">预约: 15次 | 评分: 4.9⭐</div>
                        </div>
                        <div class="service-action">
                            <button class="manage-btn">管理</button>
                        </div>
                    </div>

                    <div class="service-item">
                        <div class="service-icon">💪</div>
                        <div class="service-content">
                            <div class="service-name">健身房陪练指导</div>
                            <div class="service-status paused">已暂停</div>
                            <div class="service-stats">预约: 8次 | 评分: 4.7⭐</div>
                        </div>
                        <div class="service-action">
                            <button class="manage-btn">管理</button>
                        </div>
                    </div>
                </div>

                <!-- 订单状态区 -->
                <div class="order-status">
                    <div class="section-header">
                        <span class="section-title">📋 订单状态</span>
                        <span class="view-all" onclick="showMyOrders()">查看全部 →</span>
                    </div>

                    <div class="order-tabs">
                        <div class="order-tab active">
                            <div class="tab-icon">⏳</div>
                            <div class="tab-text">待确认</div>
                            <div class="tab-count">2</div>
                        </div>
                        <div class="order-tab">
                            <div class="tab-icon">🔄</div>
                            <div class="tab-text">进行中</div>
                            <div class="tab-count">1</div>
                        </div>
                        <div class="order-tab">
                            <div class="tab-icon">✅</div>
                            <div class="tab-text">已完成</div>
                            <div class="tab-count">12</div>
                        </div>
                        <div class="order-tab">
                            <div class="tab-icon">⭐</div>
                            <div class="tab-text">待评价</div>
                            <div class="tab-count">1</div>
                        </div>
                    </div>
                </div>

                <!-- 认证中心 -->
                <div class="certification-center">
                    <div class="section-header">
                        <span class="section-title">🎓 认证中心</span>
                        <span class="view-all" onclick="showCertificationPage()">管理认证 →</span>
                    </div>

                    <div class="cert-item completed">
                        <div class="cert-icon">✅</div>
                        <div class="cert-content">
                            <div class="cert-name">实名认证</div>
                            <div class="cert-status">已完成</div>
                        </div>
                    </div>

                    <div class="cert-item completed">
                        <div class="cert-icon">✅</div>
                        <div class="cert-content">
                            <div class="cert-name">瑜伽教练认证</div>
                            <div class="cert-status">已通过</div>
                        </div>
                    </div>

                    <div class="cert-item pending">
                        <div class="cert-icon">⏳</div>
                        <div class="cert-content">
                            <div class="cert-name">健身教练认证</div>
                            <div class="cert-status">审核中</div>
                        </div>
                    </div>

                    <div class="cert-item available">
                        <div class="cert-icon">➕</div>
                        <div class="cert-content">
                            <div class="cert-name">按摩师认证</div>
                            <div class="cert-status">可申请</div>
                        </div>
                    </div>
                </div>

                <!-- 设置菜单 -->
                <div class="settings-menu">
                    <div class="menu-item" onclick="showAccountSettings()">
                        <div class="menu-icon">⚙️</div>
                        <div class="menu-text">账户设置</div>
                        <div class="menu-arrow">→</div>
                    </div>

                    <div class="menu-item" onclick="showPrivacySettings()">
                        <div class="menu-icon">🔒</div>
                        <div class="menu-text">隐私设置</div>
                        <div class="menu-arrow">→</div>
                    </div>

                    <div class="menu-item" onclick="showNotificationSettings()">
                        <div class="menu-icon">🔔</div>
                        <div class="menu-text">通知设置</div>
                        <div class="menu-arrow">→</div>
                    </div>

                    <div class="menu-item" onclick="showHelp()">
                        <div class="menu-icon">❓</div>
                        <div class="menu-text">帮助中心</div>
                        <div class="menu-arrow">→</div>
                    </div>

                    <div class="menu-item" onclick="showAbout()">
                        <div class="menu-icon">ℹ️</div>
                        <div class="menu-text">关于我们</div>
                        <div class="menu-arrow">→</div>
                    </div>
                </div>

                <!-- 退出登录 -->
                <div class="logout-section">
                    <button class="logout-btn" onclick="logout()">退出登录</button>
                </div>
            </div>
                
                <div class="service-card" onclick="showRegisterPage()">
                    <div class="service-header">
                        <div class="category-icon" style="background: linear-gradient(135deg, #4ecdc4, #44a08d);">📝</div>
                        <div class="service-info">
                            <div class="service-title">注册新账户</div>
                            <div class="service-meta">创建新的账户</div>
                        </div>
                        <span style="font-size: 20px;">→</span>
                    </div>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <div class="category-icon" style="background: linear-gradient(135deg, #ff9a56, #ff6b6b);">📋</div>
                        <div class="service-info">
                            <div class="service-title">我的服务</div>
                            <div class="service-meta">管理发布的服务</div>
                        </div>
                        <span style="font-size: 20px;">→</span>
                    </div>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <div class="category-icon" style="background: linear-gradient(135deg, #a8e6cf, #88d8a3);">📝</div>
                        <div class="service-info">
                            <div class="service-title">我的需求</div>
                            <div class="service-meta">管理发布的需求</div>
                        </div>
                        <span style="font-size: 20px;">→</span>
                    </div>
                </div>
                
                <div class="service-card" onclick="showCertificationPage()">
                    <div class="service-header">
                        <div class="category-icon" style="background: linear-gradient(135deg, #ffd93d, #ff9a56);">🏆</div>
                        <div class="service-info">
                            <div class="service-title">身份认证</div>
                            <div class="service-meta">完成专业技能认证</div>
                        </div>
                        <span style="font-size: 20px;">→</span>
                    </div>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <div class="category-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">⭐</div>
                        <div class="service-info">
                            <div class="service-title">认证状态</div>
                            <div class="service-meta">查看认证进度和等级</div>
                        </div>
                        <span style="font-size: 20px;">→</span>
                    </div>
                </div>
            </div>
            
            <!-- 登录页面 -->
            <div id="loginPage" style="display: none;">
                <button class="back-btn" onclick="showPage('profile')">
                    ← 返回个人中心
                </button>
                
                <div class="form-container">
                    <div class="section-title">🔑 登录账户</div>
                    
                    <div class="form-group">
                        <label class="form-label">手机号</label>
                        <input type="tel" class="form-input" placeholder="请输入手机号">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">密码</label>
                        <input type="password" class="form-input" placeholder="请输入密码">
                    </div>
                    
                    <button class="publish-btn" onclick="login()">
                        登录
                    </button>
                    
                    <div style="text-align: center; margin-top: 20px;">
                        <span style="color: #666;">还没有账户？</span>
                        <a href="#" onclick="showRegisterPage()" style="color: #667eea; text-decoration: none; font-weight: 600;">立即注册</a>
                    </div>
                </div>
            </div>
            
            <!-- 注册页面 -->
            <div id="registerPage" style="display: none;">
                <button class="back-btn" onclick="showPage('profile')">
                    ← 返回个人中心
                </button>
                
                <div class="form-container">
                    <div class="section-title">📝 注册新账户</div>
                    
                    <div class="form-group">
                        <label class="form-label">手机号</label>
                        <input type="tel" class="form-input" placeholder="请输入手机号">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">验证码</label>
                        <div style="display: flex; gap: 10px;">
                            <input type="text" class="form-input" placeholder="请输入验证码" style="flex: 1;">
                            <button style="background: #667eea; color: white; border: none; padding: 16px 20px; border-radius: 16px; font-weight: 600; cursor: pointer;">获取验证码</button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">设置密码</label>
                        <input type="password" class="form-input" placeholder="请设置登录密码">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">确认密码</label>
                        <input type="password" class="form-input" placeholder="请再次输入密码">
                    </div>
                    
                    <button class="publish-btn" onclick="register()">
                        注册
                    </button>
                    
                    <div style="text-align: center; margin-top: 20px;">
                        <span style="color: #666;">已有账户？</span>
                        <a href="#" onclick="showLoginPage()" style="color: #667eea; text-decoration: none; font-weight: 600;">立即登录</a>
                    </div>
                </div>
            </div>
            
            <!-- 身份认证页面 -->
            <div id="certificationPage" style="display: none;">
                <button class="back-btn" onclick="showPage('profile')">
                    ← 返回个人中心
                </button>
                
                <div class="form-container">
                    <div class="section-title">🏆 身份认证</div>
                    
                    <div style="background: rgba(255, 243, 205, 0.8); border: 1px solid #ffd93d; border-radius: 12px; padding: 16px; margin-bottom: 20px;">
                        <div style="font-weight: 600; color: #b7791f; margin-bottom: 8px;">📋 认证说明</div>
                        <div style="font-size: 13px; color: #8b5a00; line-height: 1.5;">
                            为确保服务质量和用户安全，提供专业服务需要完成相应的身份认证。认证通过后将获得专业标识，享受平台推荐优先权。
                        </div>
                    </div>
                    
                    <div class="section-title" style="font-size: 16px; margin-bottom: 16px;">选择认证类型</div>
                    
                    <div class="service-card" onclick="showCertificationForm('yoga')">
                        <div class="service-header">
                            <div class="category-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">🧘</div>
                            <div class="service-info">
                                <div class="service-title">瑜伽教练认证</div>
                                <div class="service-meta">需要瑜伽教练资格证书</div>
                            </div>
                            <span style="font-size: 20px;">→</span>
                        </div>
                    </div>
                    
                    <div class="service-card" onclick="showCertificationForm('fitness')">
                        <div class="service-header">
                            <div class="category-icon" style="background: linear-gradient(135deg, #ff9a56, #ff6b6b);">💪</div>
                            <div class="service-info">
                                <div class="service-title">健身教练认证</div>
                                <div class="service-meta">需要健身教练资格证</div>
                            </div>
                            <span style="font-size: 20px;">→</span>
                        </div>
                    </div>
                    
                    <div class="service-card" onclick="showCertificationForm('tutor')">
                        <div class="service-header">
                            <div class="category-icon" style="background: linear-gradient(135deg, #4ecdc4, #44a08d);">📚</div>
                            <div class="service-info">
                                <div class="service-title">学习辅导认证</div>
                                <div class="service-meta">需要学历证明或教师资格证</div>
                            </div>
                            <span style="font-size: 20px;">→</span>
                        </div>
                    </div>
                    
                    <div class="service-card" onclick="showCertificationForm('cooking')">
                        <div class="service-header">
                            <div class="category-icon" style="background: linear-gradient(135deg, #ffd93d, #ff9a56);">👨‍🍳</div>
                            <div class="service-info">
                                <div class="service-title">烹饪技能认证</div>
                                <div class="service-meta">需要厨师证或相关证书</div>
                            </div>
                            <span style="font-size: 20px;">→</span>
                        </div>
                    </div>
                    
                    <div class="service-card" onclick="showCertificationForm('other')">
                        <div class="service-header">
                            <div class="category-icon" style="background: linear-gradient(135deg, #a8e6cf, #88d8a3);">🎯</div>
                            <div class="service-info">
                                <div class="service-title">其他技能认证</div>
                                <div class="service-meta">提供相关技能证书或作品</div>
                            </div>
                            <span style="font-size: 20px;">→</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 认证表单页面 -->
            <div id="certificationFormPage" style="display: none;">
                <button class="back-btn" onclick="showCertificationPage()">
                    ← 返回认证选择
                </button>
                
                <div class="form-container">
                    <div class="section-title" id="certificationFormTitle">🧘 瑜伽教练认证</div>
                    
                    <div class="form-group">
                        <label class="form-label">真实姓名</label>
                        <input type="text" class="form-input" placeholder="请输入身份证上的真实姓名">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">身份证号</label>
                        <input type="text" class="form-input" placeholder="请输入18位身份证号码">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">联系电话</label>
                        <input type="tel" class="form-input" placeholder="请输入手机号码">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">从业年限</label>
                        <select class="form-select">
                            <option>选择从业年限</option>
                            <option>1年以下</option>
                            <option>1-3年</option>
                            <option>3-5年</option>
                            <option>5年以上</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">资格证书</label>
                        <div style="border: 2px dashed #ccc; border-radius: 12px; padding: 20px; text-align: center; color: #666; cursor: pointer;" onclick="alert('点击上传证书照片')">
                            📷 点击上传证书照片<br>
                            <small>支持JPG、PNG格式，大小不超过5MB</small>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">个人简介</label>
                        <textarea class="form-textarea" placeholder="请简要介绍你的专业背景、教学经验、擅长领域等..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">作品展示（可选）</label>
                        <div style="border: 2px dashed #ccc; border-radius: 12px; padding: 20px; text-align: center; color: #666; cursor: pointer;" onclick="alert('点击上传作品照片')">
                            🖼️ 点击上传作品照片<br>
                            <small>可上传教学照片、学员反馈等</small>
                        </div>
                    </div>
                    
                    <button class="publish-btn" onclick="submitCertification()">
                        提交认证申请
                    </button>
                    
                    <div style="background: rgba(230, 244, 255, 0.8); border: 1px solid #667eea; border-radius: 12px; padding: 16px; margin-top: 20px;">
                        <div style="font-weight: 600; color: #2b6cb0; margin-bottom: 8px;">📝 审核说明</div>
                        <div style="font-size: 13px; color: #2c5aa0; line-height: 1.5;">
                            • 审核时间：1-3个工作日<br>
                            • 审核通过后将获得专业认证标识<br>
                            • 如有问题，客服会主动联系您
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 运营后台配置管理页面 -->
            <div id="adminConfigPage" style="display: none;">
                <div class="admin-header">
                    <div class="admin-title">🔧 金刚位配置管理</div>
                    <div class="admin-subtitle">实时配置首页服务入口</div>
                </div>
                
                <div class="config-section">
                    <div class="config-section-title">📱 当前配置预览</div>
                    <div class="mini-phone-preview">
                        <div class="mini-category-grid">
                            <div class="mini-category-item">🧘 瑜伽</div>
                            <div class="mini-category-item">💪 健身</div>
                            <div class="mini-category-item">🎮 游戏</div>
                            <div class="mini-category-item">🍽️ 美食</div>
                            <div class="mini-category-item">💆 按摩</div>
                            <div class="mini-category-item">🦶 足疗</div>
                            <div class="mini-category-item">🧹 家政</div>
                            <div class="mini-category-item">💇 美发</div>
                            <div class="mini-category-item">📚 辅导</div>
                            <div class="mini-category-item">🎵 音乐</div>
                            <div class="mini-category-item">🐕 宠物</div>
                            <div class="mini-category-item">➕ 更多</div>
                        </div>
                    </div>
                </div>
                
                <div class="config-section">
                    <div class="config-section-title">⚙️ 配置操作</div>
                    
                    <div class="config-action-card">
                        <div class="config-action-header">
                            <span class="config-action-icon">📝</span>
                            <span class="config-action-title">编辑金刚位</span>
                        </div>
                        <div class="config-action-desc">修改现有服务入口的图标、名称、排序等</div>
                        <button class="config-action-btn" onclick="alert('打开编辑界面')">立即编辑</button>
                    </div>
                    
                    <div class="config-action-card">
                        <div class="config-action-header">
                            <span class="config-action-icon">➕</span>
                            <span class="config-action-title">添加新入口</span>
                        </div>
                        <div class="config-action-desc">添加新的服务类型入口</div>
                        <button class="config-action-btn" onclick="alert('打开添加界面')">添加入口</button>
                    </div>
                    
                    <div class="config-action-card">
                        <div class="config-action-header">
                            <span class="config-action-icon">🎯</span>
                            <span class="config-action-title">A/B测试</span>
                        </div>
                        <div class="config-action-desc">为不同用户群体配置不同的金刚位布局</div>
                        <button class="config-action-btn" onclick="alert('配置A/B测试')">配置测试</button>
                    </div>
                    
                    <div class="config-action-card">
                        <div class="config-action-header">
                            <span class="config-action-icon">📊</span>
                            <span class="config-action-title">数据统计</span>
                        </div>
                        <div class="config-action-desc">查看各入口的点击率和转化数据</div>
                        <button class="config-action-btn" onclick="alert('查看统计数据')">查看数据</button>
                    </div>
                </div>
                
                <div class="config-section">
                    <div class="config-section-title">🚀 发布配置</div>
                    <div class="publish-config-area">
                        <div class="publish-note">
                            <span class="publish-note-icon">⚠️</span>
                            <span class="publish-note-text">配置修改后需要发布才能生效，建议先在测试环境验证</span>
                        </div>
                        <div class="publish-actions">
                            <button class="publish-test-btn" onclick="alert('发布到测试环境')">测试环境发布</button>
                            <button class="publish-prod-btn" onclick="alert('发布到生产环境')">生产环境发布</button>
                        </div>
                    </div>
                </div>
                
                <button class="back-btn" onclick="showPage('home')" style="margin-top: 20px;">
                    ← 返回首页预览
                </button>
            </div>
        </div>
        
        <div class="floating-btn" onclick="showPage('publish')">
            ➕
        </div>
        
        <!-- 管理员入口按钮 -->
        <div class="admin-entry-btn" onclick="showPage('adminConfig')" title="运营后台入口">
            ⚙️
        </div>
        
        <div class="bottom-nav">
            <div class="nav-item active" onclick="showPage('home')">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">发现</div>
            </div>
            <div class="nav-item" onclick="showPage('services')">
                <div class="nav-icon">🛍️</div>
                <div class="nav-label">服务广场</div>
            </div>
            <div class="nav-item" onclick="showPage('publish')">
                <div class="nav-icon">➕</div>
                <div class="nav-label">发布</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon">💬</div>
                <div class="nav-label">消息</div>
            </div>
            <div class="nav-item" onclick="showPage('profile')">
                <div class="nav-icon">👤</div>
                <div class="nav-label">我的</div>
            </div>
        </div>
    </div>

    <script>
        let currentMode = 'seeker';
        let currentPage = 'home';
        
        function showPage(page) {
            // 隐藏所有页面
            document.getElementById('homePage').style.display = 'none';
            document.getElementById('servicesPage').style.display = 'none';
            document.getElementById('publishPage').style.display = 'none';
            document.getElementById('profilePage').style.display = 'none';
            document.getElementById('loginPage').style.display = 'none';
            document.getElementById('registerPage').style.display = 'none';
            document.getElementById('certificationPage').style.display = 'none';
            document.getElementById('certificationFormPage').style.display = 'none';
            document.getElementById('adminConfigPage').style.display = 'none';
            
            // 显示目标页面
            if (page === 'home') {
                document.getElementById('homePage').style.display = 'block';
                document.getElementById('pageTitle').textContent = '发现';
            } else if (page === 'services') {
                document.getElementById('servicesPage').style.display = 'block';
                document.getElementById('pageTitle').textContent = '服务广场';
            } else if (page === 'publish') {
                document.getElementById('publishPage').style.display = 'block';
                document.getElementById('pageTitle').textContent = '我要发布';
            } else if (page === 'profile') {
                document.getElementById('profilePage').style.display = 'block';
                document.getElementById('pageTitle').textContent = '个人中心';
            } else if (page === 'certification') {
                document.getElementById('certificationPage').style.display = 'block';
                document.getElementById('pageTitle').textContent = '身份认证';
            } else if (page === 'certificationForm') {
                document.getElementById('certificationFormPage').style.display = 'block';
                document.getElementById('pageTitle').textContent = '认证申请';
            } else if (page === 'adminConfig') {
                document.getElementById('adminConfigPage').style.display = 'block';
                document.getElementById('pageTitle').textContent = '运营后台';
            }
            
            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            if (page === 'home') {
                document.querySelectorAll('.nav-item')[0].classList.add('active');
            } else if (page === 'services') {
                document.querySelectorAll('.nav-item')[1].classList.add('active');
            } else if (page === 'publish') {
                document.querySelectorAll('.nav-item')[2].classList.add('active');
                // 重置发布页面状态
                showPublishOptions();
            } else if (page === 'profile') {
                document.querySelectorAll('.nav-item')[4].classList.add('active');
            }
            
            currentPage = page;
        }
        
        function setMode(mode) {
            currentMode = mode;
            const seekerBtn = document.getElementById('seekerBtn');
            const providerBtn = document.getElementById('providerBtn');
            const modeIndicator = document.getElementById('modeIndicator');
            const header = document.querySelector('.header');
            
            if (mode === 'seeker') {
                seekerBtn.classList.add('active');
                providerBtn.classList.remove('active');
                modeIndicator.textContent = '我在寻找';
                modeIndicator.className = 'mode-indicator seeker-mode';
                header.style.background = 'linear-gradient(135deg, #4ecdc4, #44a08d)';
            } else {
                providerBtn.classList.add('active');
                seekerBtn.classList.remove('active');
                modeIndicator.textContent = '我在提供';
                modeIndicator.className = 'mode-indicator provider-mode';
                header.style.background = 'linear-gradient(135deg, #ff9a56, #ff6b6b)';
            }
        }
        
        function toggleMode() {
            setMode(currentMode === 'seeker' ? 'provider' : 'seeker');
        }
        
        function showPublishForm(type) {
            document.getElementById('publishOptions').style.display = 'none';
            document.querySelector('#publishPage .section-title').style.display = 'none';
            document.querySelector('#publishPage .category-grid').style.display = 'none';
            
            if (type === 'service') {
                document.getElementById('serviceForm').style.display = 'block';
                document.getElementById('demandForm').style.display = 'none';
            } else {
                document.getElementById('demandForm').style.display = 'block';
                document.getElementById('serviceForm').style.display = 'none';
            }
        }
        
        function showPublishOptions() {
            document.getElementById('publishOptions').style.display = 'block';
            document.querySelector('#publishPage .section-title').style.display = 'block';
            document.querySelector('#publishPage .category-grid').style.display = 'grid';
            document.getElementById('serviceForm').style.display = 'none';
            document.getElementById('demandForm').style.display = 'none';
        }
        
        function publishService() {
            // 这里可以添加表单验证逻辑
            alert('服务发布成功！\n\n你的服务已经发布到平台上，其他用户可以看到并联系你。\n\n接下来你可以：\n- 在"我的"页面管理你的服务\n- 查看收到的咨询消息\n- 随时编辑或下架服务');
            
            // 重置表单并返回首页
            showPublishOptions();
            showPage('home');
        }
        
        function publishDemand() {
            // 这里可以添加表单验证逻辑
            alert('需求发布成功！\n\n你的需求已经发布到平台上，服务提供者可以看到并联系你。\n\n接下来你可以：\n- 在"我的"页面管理你的需求\n- 查看收到的服务推荐\n- 随时编辑或关闭需求');
            
            // 重置表单并返回首页
            showPublishOptions();
            showPage('home');
        }
        
        function quickPublish(type) {
            const templates = {
                yoga: {
                    title: '专业瑜伽私教课程',
                    description: '提供专业的哈他瑜伽、流瑜伽课程，适合初学者和进阶者。拥有国际瑜伽联盟认证，教学经验丰富。',
                    price: '80元/课时',
                    location: '上门服务或指定瑜伽馆',
                    time: '周一至周日 9:00-21:00'
                },
                fitness: {
                    title: '健身房陪练指导',
                    description: '专业健身指导，制定个性化训练计划。擅长力量训练、减脂塑形，帮助新手快速入门。',
                    price: '60元/小时',
                    location: '指定健身房',
                    time: '周一至周五 18:00-21:00，周末全天'
                },
                gaming: {
                    title: '王者荣耀上分陪玩',
                    description: '专业上分，擅长打野和中单位置。技术过硬，态度友好，带你轻松上王者！',
                    price: '25元/局',
                    location: '线上服务',
                    time: '每天 19:00-23:00'
                },
                food: {
                    title: '美食探店聚餐组织',
                    description: '热爱美食，熟悉本地各种特色餐厅。组织小型聚餐活动，分享美食体验。',
                    price: '面议（AA制）',
                    location: '本地各大商圈',
                    time: '周末及节假日'
                }
            };
            
            const template = templates[type];
            if (template) {
                alert(`使用快速模板：${template.title}\n\n这将自动填充表单内容，你可以根据需要进行修改。`);
                showPublishForm('service');
                
                // 这里可以添加自动填充表单的逻辑
                setTimeout(() => {
                    const inputs = document.querySelectorAll('#serviceForm input, #serviceForm textarea');
                    if (inputs.length >= 4) {
                        inputs[0].value = template.title;
                        inputs[1].value = template.description;
                        inputs[2].value = template.price;
                        inputs[3].value = template.location;
                        if (inputs[4]) inputs[4].value = template.time;
                    }
                }, 100);
            }
        }
        
        function showLoginPage() {
            document.getElementById('profilePage').style.display = 'none';
            document.getElementById('loginPage').style.display = 'block';
            document.getElementById('pageTitle').textContent = '登录';
        }
        
        function showRegisterPage() {
            document.getElementById('profilePage').style.display = 'none';
            document.getElementById('registerPage').style.display = 'block';
            document.getElementById('pageTitle').textContent = '注册';
        }
        
        function login() {
            alert('登录成功！\n\n欢迎回来！你现在可以：\n- 发布和管理服务\n- 查看消息和订单\n- 完善个人资料');
            showPage('profile');
        }
        
        function register() {
            alert('注册成功！\n\n欢迎加入乐享生活！你现在可以：\n- 发布服务或需求\n- 与其他用户互动\n- 享受平台的各种功能');
            showPage('profile');
        }
        
        function showCertificationPage() {
            showPage('certification');
        }
        
        function showCertificationForm(type) {
            const titles = {
                'yoga': '🧘 瑜伽教练认证',
                'fitness': '💪 健身教练认证',
                'tutor': '📚 学习辅导认证',
                'cooking': '👨‍🍳 烹饪技能认证',
                'other': '🎯 其他技能认证'
            };
            
            document.getElementById('certificationFormTitle').textContent = titles[type] || '🎯 技能认证';
            showPage('certificationForm');
        }
        
        function submitCertification() {
            alert('认证申请已提交！\n\n审核时间：1-3个工作日\n审核结果将通过短信和站内消息通知您');
            showPage('profile');
        }

        // 我的页面相关函数
        function showMyServices() {
            alert('我的服务管理\n\n功能包括：\n- 查看所有发布的服务\n- 编辑服务信息\n- 暂停/恢复服务\n- 查看预约记录\n- 服务数据统计');
        }

        function showMyOrders() {
            alert('我的订单管理\n\n功能包括：\n- 待确认订单 (2个)\n- 进行中订单 (1个)\n- 已完成订单 (12个)\n- 待评价订单 (1个)\n- 订单详情查看');
        }

        function showMyDemands() {
            alert('我的需求管理\n\n功能包括：\n- 查看发布的需求\n- 编辑需求信息\n- 查看响应情况\n- 选择服务提供者\n- 需求状态跟踪');
        }

        function showWallet() {
            alert('我的钱包\n\n功能包括：\n- 账户余额: ¥1,280\n- 收益明细查看\n- 提现申请\n- 交易记录\n- 收入统计图表');
        }

        function showAccountSettings() {
            alert('账户设置\n\n功能包括：\n- 修改密码\n- 绑定手机号\n- 绑定邮箱\n- 安全设置\n- 登录记录');
        }

        function showPrivacySettings() {
            alert('隐私设置\n\n功能包括：\n- 个人信息可见性\n- 位置信息设置\n- 消息接收设置\n- 黑名单管理\n- 数据下载');
        }

        function showNotificationSettings() {
            alert('通知设置\n\n功能包括：\n- 推送通知开关\n- 短信通知设置\n- 邮件通知设置\n- 免打扰时间\n- 通知声音设置');
        }

        function showHelp() {
            alert('帮助中心\n\n功能包括：\n- 常见问题解答\n- 使用教程\n- 联系客服\n- 意见反馈\n- 服务协议');
        }

        function showAbout() {
            alert('关于我们\n\n乐享生活 v2.1.0\n\n双向服务聚合平台\n让每个人的技能都成为价值\n\n© 2024 乐享生活团队\n\n联系我们：<EMAIL>');
        }

        function logout() {
            if (confirm('确定要退出登录吗？')) {
                alert('已退出登录\n\n感谢使用乐享生活！\n期待您的再次光临');
                // 这里可以添加实际的退出登录逻辑
                showPage('home');
            }
        }

        // 初始化
        setMode('seeker');
        showPage('home');
    </script>
</body>
</html>