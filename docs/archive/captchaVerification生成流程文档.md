# captchaVerification 生成流程文档

## 概述

本文档详细说明了在验证码系统中 `captchaVerification` 参数的生成流程，包括滑块验证码和点选验证码两种类型的实现。

## 核心原理

`captchaVerification` 是基于 AJ-Captcha 官方标准生成的二次验证参数，用于后端验证用户操作的真实性。其生成遵循以下格式：

```
captchaVerification = AES加密(token + "---" + JSON.stringify(坐标数据), secretKey)
```

## 技术架构

### 1. 核心文件结构

```
src/
├── api/
│   ├── captchaService.ts          # 验证码服务核心逻辑
│   └── modules/captcha.ts          # 验证码API接口
├── components/verifition/
│   ├── utils/
│   │   ├── utils.js               # 加密工具函数
│   │   ├── VerifySlide.vue        # 滑块验证码组件
│   │   └── VerifyPoints.vue       # 点选验证码组件
│   └── Verify.vue                 # 验证码容器组件
├── composables/
│   └── useCaptcha.ts              # 验证码Composable
└── pages/auth/
    └── login.vue                  # 登录页面
```

### 2. 数据流向

```
用户操作 → 验证码组件 → 坐标数据处理 → API验证 → captchaVerification生成 → 回调传递
```

## 详细流程

### 阶段一：验证码初始化

#### 1.1 获取验证码数据

**文件位置**: `src/composables/useCaptcha.ts`

```javascript
const getCaptchaData = async () => {
  try {
    state.loading = true
    const response = await getCaptcha({ captchaType: 'blockPuzzle' })
    
    if (response.repCode === '0000') {
      state.captchaData = {
        originalImageBase64: response.repData.originalImageBase64,
        jigsawImageBase64: response.repData.jigsawImageBase64,
        token: response.repData.token,
        secretKey: response.repData.secretKey
      }
    }
  } catch (error) {
    state.errorMessage = '获取验证码失败'
  }
}
```

**关键数据**:
- `token`: 后端生成的验证令牌
- `secretKey`: AES加密密钥
- `originalImageBase64`: 原始背景图片
- `jigsawImageBase64`: 滑块图片（仅滑块验证码）

### 阶段二：用户交互与坐标收集

#### 2.1 滑块验证码坐标处理

**文件位置**: `src/components/verifition/utils/VerifySlide.vue`

```javascript
// 用户拖拽结束后的验证逻辑
const verify = async () => {
  // 计算原始移动距离（考虑响应式缩放）
  const originalMoveDistance = Math.round(this.sliderLeft / this.currentScale)
  
  // 生成标准坐标数据
  const pointData = { x: originalMoveDistance, y: 5.0 }
  const pointDataJson = JSON.stringify(pointData)
  
  // 构建验证请求参数
  const data = {
    captchaType: this.captchaType,
    pointJson: this.secretKey ? aesEncrypt(pointDataJson, this.secretKey) : pointDataJson,
    token: this.captchaObj.token
  }
}
```

#### 2.2 点选验证码坐标处理

**文件位置**: `src/components/verifition/utils/VerifyPoints.vue`

```javascript
// 用户点击完成后的验证逻辑
const checkWords = async () => {
  // 响应式坐标转换
  const currentScale = this.imgSize.scale || 1
  const scaledPoints = this.originPointsCoordinate.map(p => {
    let x = Math.round(p.x / currentScale)
    let y = Math.round(p.y / currentScale)
    return {x, y}
  })
  
  // 生成加密的坐标数据
  const pointDataJson = JSON.stringify(this.originPointsCoordinate)
  const data = {
    captchaType: this.captchaType,
    pointJson: this.secretKey ? aesEncrypt(pointDataJson, this.secretKey) : pointDataJson,
    token: this.captchaObj.token
  }
}
```

### 阶段三：API验证

#### 3.1 验证请求

**文件位置**: `src/api/modules/captcha.ts`

```javascript
// 滑块验证码验证接口
export const checkPuzzleCaptcha = (params: CaptchaCheckParams) => {
  return request.post<CaptchaCheckResponse>('/oto/captcha-api/captcha/check', params)
}

// 点选验证码验证接口
export const checkWordCaptcha = (params: CaptchaCheckParams) => {
  return request.post<CaptchaCheckResponse>('/oto/captcha-api/captcha/check', params)
}
```

**请求参数**:
```typescript
interface CaptchaCheckParams {
  captchaType: 'blockPuzzle' | 'clickWord'
  pointJson: string  // 加密后的坐标数据
  token: string      // 验证令牌
}
```

**响应数据**:
```typescript
interface CaptchaCheckResponse {
  result: boolean
  captchaVerification?: string  // 二次验证参数（可选）
}
```

### 阶段四：captchaVerification生成

#### 4.1 加密工具函数

**文件位置**: `src/components/verifition/utils/utils.js`

```javascript
/**
 * AES-ECB加密 - 完全复刻官方代码实现
 */
export function aesEncrypt(word, keyWord = "XwKsGlMcdPMEhR1B") {
  if (!keyWord || !word) {
    return word;
  }
  
  try {
    var key = CryptoJS.enc.Utf8.parse(keyWord);
    var srcs = CryptoJS.enc.Utf8.parse(word);
    var encrypted = CryptoJS.AES.encrypt(srcs, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    });
    return encrypted.toString();
  } catch (e) {
    console.error('AES-ECB加密失败', e);
    return word;
  }
}

/**
 * 生成captchaVerification - 完全按照官方逻辑实现
 */
export function generateOfficialCaptchaVerification(token, pointData, secretKey) {
  if (!token) {
    console.warn('token为空，无法生成验证参数');
    return '';
  }
  
  // 按照官方逻辑：token + "---" + JSON.stringify(pointData)
  var rawString = token + '---' + JSON.stringify(pointData);
  
  // 如果有密钥则加密，否则直接返回原始字符串
  var captchaVerification = secretKey ? 
    aesEncrypt(rawString, secretKey) : 
    rawString;
    
  return captchaVerification;
}
```

#### 4.2 滑块验证码生成逻辑

**文件位置**: `src/components/verifition/utils/VerifySlide.vue`

```javascript
// 验证成功后生成captchaVerification
if (res && res.repCode === '0000' && res.repData && res.repData.result) {
  // 生成captchaVerification，遵循AJ-Captcha官方格式
  const captchaVerification = this.secretKey ?
    aesEncrypt(this.captchaObj.token + '---' + JSON.stringify(pointData), this.secretKey) :
    this.captchaObj.token + '---' + JSON.stringify(pointData)
  
  const verifyResult = {
    captchaVerification: captchaVerification,
    captchaType: this.captchaType
  }
  
  this.callback(verifyResult)
}
```

#### 4.3 点选验证码生成逻辑

**文件位置**: `src/components/verifition/utils/VerifyPoints.vue`

```javascript
// 验证成功后生成captchaVerification
if (res && res.repCode === '0000' && res.repData.result) {
  // 使用缩放后的坐标生成验证参数（按照官方逻辑）
  var captchaVerification = this.secretKey ?
    aesEncrypt(data.token + '---' + JSON.stringify(scaledPoints), this.secretKey) :
    data.token + '---' + JSON.stringify(scaledPoints)
  
  const verifyResult = {
    captchaVerification: captchaVerification,
    captchaType: this.captchaType
  };
  
  this.callback(verifyResult);
}
```

### 阶段五：状态管理与缓存

#### 5.1 验证状态保存

**文件位置**: `src/api/captchaService.ts`

```javascript
const verifyCaptchaSuccess = async (params: any) => {
  // 清除旧的计时器
  if (validityTimer !== null) {
    clearTimeout(validityTimer);
    validityTimer = null;
  }
  
  captchaVerified.value = true;
  captchaVerifyData.value = {
    captchaType: params.captchaType,
    captchaVerification: params.captchaVerification
  };
  
  // 设置10分钟有效期
  validityTimer = setTimeout(() => {
    resetCaptcha();
  }, 10 * 60 * 1000);
  
  // 保存状态到本地存储
  saveCurrentState();
}
```

#### 5.2 状态持久化

**文件位置**: `src/utils/formPersistence.ts`

```javascript
export function saveCaptchaState(serviceKey: string, data: any) {
  try {
    const stateData = {
      ...data,
      timestamp: Date.now()
    };
    uni.setStorageSync(`captcha_${serviceKey}`, JSON.stringify(stateData));
  } catch (error) {
    console.error('保存验证码状态失败:', error);
  }
}

export function getCaptchaState(serviceKey: string) {
  try {
    const stateStr = uni.getStorageSync(`captcha_${serviceKey}`);
    if (stateStr) {
      const stateData = JSON.parse(stateStr);
      // 检查是否过期（10分钟）
      if (Date.now() - stateData.timestamp < 10 * 60 * 1000) {
        return stateData;
      }
    }
  } catch (error) {
    console.error('获取验证码状态失败:', error);
  }
  return null;
}
```

## 数据格式说明

### 1. 滑块验证码坐标格式

```javascript
// 原始坐标数据
const pointData = {
  x: 156,  // 滑块移动的水平距离（像素）
  y: 5.0   // 固定垂直偏移
}

// 生成的原始字符串
const rawString = "token123---{\"x\":156,\"y\":5.0}"

// 最终的captchaVerification（加密后）
const captchaVerification = "U2FsdGVkX1+8QGqjZHNkc..."
```

### 2. 点选验证码坐标格式

```javascript
// 原始坐标数据（多个点击点）
const pointData = [
  {x: 120, y: 45},
  {x: 200, y: 80},
  {x: 150, y: 120},
  {x: 250, y: 60}
]

// 生成的原始字符串
const rawString = "token123---[{\"x\":120,\"y\":45},{\"x\":200,\"y\":80},...]"

// 最终的captchaVerification（加密后）
const captchaVerification = "U2FsdGVkX1+9RHqkZINld..."
```

## 缓存策略分析

### 当前缓存机制

1. **验证状态缓存**: 验证成功后，`captchaVerification` 会被缓存10分钟
2. **页面刷新恢复**: 页面刷新时会尝试恢复之前的验证状态
3. **自动过期**: 10分钟后自动清除验证状态

### 缓存的必要性讨论

#### 优点：
- **用户体验**: 避免用户频繁重复验证
- **性能优化**: 减少不必要的验证请求
- **状态保持**: 页面刷新后保持验证状态

#### 缺点：
- **安全风险**: 长时间缓存可能被恶意利用
- **状态混乱**: 页面刷新后显示"验证通过"可能误导用户
- **一次性使用**: `captchaVerification` 理论上应该是一次性的

### 建议的优化策略

#### 方案1：一次性使用模式
```javascript
// 在API调用成功后立即清除验证状态
const submitForm = async () => {
  const captchaData = getCaptchaFormData();
  
  try {
    await loginAPI(captchaData);
    // 登录成功后立即清除验证状态
    resetCaptcha();
  } catch (error) {
    // 登录失败也清除验证状态，要求重新验证
    resetCaptcha();
  }
}
```

#### 方案2：短期缓存模式
```javascript
// 将缓存时间从10分钟缩短到2分钟
validityTimer = setTimeout(() => {
  resetCaptcha();
}, 2 * 60 * 1000); // 2分钟
```

#### 方案3：禁用页面刷新恢复
```javascript
// 在captchaService.ts中禁用状态恢复
const restoreState = () => {
  // 不再恢复状态，每次都重新验证
  clearCaptchaState(serviceKey);
  resetCaptcha();
}
```

## 安全考虑

### 1. 加密安全
- 使用AES-ECB加密算法
- 密钥由后端动态生成
- 坐标数据在传输前加密

### 2. 防重放攻击
- token具有时效性
- 每次验证使用不同的token
- captchaVerification包含时间戳信息

### 3. 前端安全
- 敏感数据不在前端长期存储
- 验证逻辑主要在后端执行
- 前端仅负责坐标收集和加密

## 总结

`captchaVerification` 的生成是一个涉及多个组件协作的复杂流程，其核心是将用户的操作坐标按照AJ-Captcha官方标准进行加密处理。当前的缓存机制虽然提升了用户体验，但考虑到安全性和一次性使用的原则，建议采用更严格的缓存策略或完全禁用缓存恢复机制。

通过本文档的详细说明，开发者可以清楚地了解整个验证码系统的工作原理，并根据实际需求进行相应的优化和调整。