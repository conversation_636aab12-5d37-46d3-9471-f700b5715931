# Token监听功能完整文档说明

## 📋 功能概述
项目实现了一套完整的Token监听和自动刷新机制，主要功能是定期检查access_token状态，在token即将过期或已过期时自动刷新，如果refresh_token也过期则自动跳转到登录页。

## 🏗️ 核心组件架构

### 1. TokenManager (核心Token管理器)
**文件位置：** `/src/utils/tokenManager.ts`

**主要功能：**
- Token的存储、获取、验证和刷新
- 支持双Token模式（access_token + refresh_token）
- 多重存储保护机制
- 防并发刷新机制

**关键方法：**
- `setToken()` - 保存Token数据
- `getToken()` - 获取access_token
- `getTokenData()` - 获取完整Token数据
- `isTokenValid()` - 检查access_token是否有效
- `shouldRefreshToken()` - 判断是否需要刷新token
- `refreshToken()` - 执行token刷新
- `clearToken()` - 清除token数据

**刷新逻辑：**
- 预防性刷新：剩余时间≤5分钟且>0时触发
- 补救性刷新：已过期但refresh_token未过期时触发
- 失败处理：refresh_token过期时清除数据并跳转登录页

### 2. AccessTokenRefreshManager (定时刷新管理器)
**文件位置：** `/src/utils/tokenRefreshManager.ts`

**主要功能：**
- 定时检查token状态（每10秒检查一次）
- 智能页面检测（登录/注册页面不启动定时器）
- 应用生命周期集成
- 跨平台兼容性处理

**关键方法：**
- `shouldStartTimer()` - 检查当前页面是否适合启动定时器
- `smartCheckToken()` - 智能检查token状态
- `startRefreshTimer()` - 启动定时刷新
- `stopRefreshTimer()` - 停止定时刷新
- `checkAndStart()` - 检查并启动定时器

**页面过滤逻辑：**
```typescript
const authPages = [
  '/pages/modules/auth/login/login',
  '/pages/modules/auth/register/register',
  '/pages/modules/auth/realAuth/realAuth',
  'login', 'register', 'auth'
]
```

## 🔄 工作流程

### 1. 应用启动流程
1. **App.vue onLaunch** → 调用 `handleAppLaunch()`
2. **setupPlatformSpecificLifecycle()** → 设置平台特定的生命周期处理
3. **checkAndStart()** → 检查token状态并启动定时器

### 2. 定时检查流程
1. **每10秒触发** → `smartCheckToken()`
2. **页面检查** → `shouldStartTimer()` 判断是否适合执行
3. **Token检查** → 获取token数据并验证有效性
4. **刷新判断** → 根据剩余时间决定是否刷新
5. **执行刷新** → 调用 `TokenManager.refreshToken()`

### 3. 异常处理流程
1. **无token数据** → 停止定时器
2. **无refresh_token** → 清除数据 + 跳转登录页
3. **refresh_token过期** → 清除数据 + 跳转登录页
4. **刷新失败** → 记录失败时间 + 重试机制

## 📁 文件结构

```
src/
├── utils/
│   ├── tokenManager.ts          # 核心Token管理器
│   └── tokenRefreshManager.ts   # 定时刷新管理器
├── api/modules/
│   └── auth.ts                  # 刷新token的API接口
├── App.vue                      # 应用生命周期集成
└── store/modules/
    └── auth.ts                  # Pinia状态管理
```

## 🚀 生命周期集成

### App.vue中的集成
```typescript
// 导入生命周期管理函数
import { setupTokenRefreshLifecycle, setupPlatformSpecificLifecycle } from '@/utils/tokenRefreshManager'

// 设置Token刷新生命周期管理
const { handleAppShow, handleAppHide, handleAppLaunch } = setupTokenRefreshLifecycle()

// 应用启动
onLaunch(async () => {
  setupPlatformSpecificLifecycle()  // 设置平台特定处理
  await handleAppLaunch()           // 启动时检查
})

// 应用显示
onShow(async () => {
  await handleAppShow()             // 显示时启动定时器
})

// 应用隐藏
onHide(() => {
  handleAppHide()                   // 隐藏时停止定时器
})
```

### 跨平台生命周期处理
- **H5环境：** 监听页面可见性变化、DOMContentLoaded事件
- **App环境：** 监听应用恢复/暂停事件
- **小程序环境：** 使用uni-app生命周期

## ⚙️ 配置参数

- **检查间隔：** 10秒（REFRESH_INTERVAL = 10000ms）
- **刷新阈值：** 5分钟（REFRESH_THRESHOLD_MS = 300000ms）
- **重试间隔：** 10秒（RETRY_INTERVAL_MS = 10000ms）
- **频繁检查保护：** 5秒内不重复检查

## 🔧 调试和监控

项目提供了详细的控制台日志，包括：
- Token状态检查日志
- 刷新触发和结果日志
- 定时器启动/停止日志
- 页面路由检查日志
- 错误处理日志

## 🛡️ 安全特性

1. **多重存储保护** - 使用MultiStorage确保数据安全
2. **防并发刷新** - 避免同时发起多个刷新请求
3. **重试机制** - 失败后有间隔的重试逻辑
4. **页面过滤** - 登录页面不执行token检查
5. **自动跳转** - token失效时自动跳转登录页

## 📝 核心代码示例

### TokenManager核心方法
```typescript
// 检查是否需要刷新token
shouldRefreshToken(): boolean {
  const tokenData = this.getTokenData()
  if (!tokenData?.access_token) return false
  
  const remainingTime = this.getRemainingTokenTime()
  // 剩余时间≤5分钟或已过期但refresh_token未过期时需要刷新
  return remainingTime <= REFRESH_THRESHOLD_MS || 
         (remainingTime <= 0 && !this.isRefreshTokenExpired())
}

// 执行token刷新
async refreshToken(): Promise<boolean> {
  // 防并发处理
  if (this.isRefreshing) {
    return this.refreshPromise!
  }
  
  this.isRefreshing = true
  this.refreshPromise = this._refreshTokenInternal()
  
  try {
    return await this.refreshPromise
  } finally {
    this.isRefreshing = false
    this.refreshPromise = null
  }
}
```

### AccessTokenRefreshManager核心方法
```typescript
// 智能检查token状态
async smartCheckToken(): Promise<void> {
  // 检查是否适合启动定时器
  if (!this.shouldStartTimer()) {
    this.stopRefreshTimer()
    return
  }
  
  // 检查token状态并刷新
  if (TokenManager.shouldRefreshToken()) {
    console.log('[TokenRefresh] 触发token刷新')
    await TokenManager.refreshToken()
  }
}

// 检查并启动定时器
async checkAndStart(): Promise<void> {
  const tokenData = TokenManager.getTokenData()
  
  // 无token数据时停止定时器
  if (!tokenData?.access_token) {
    this.stopRefreshTimer()
    return
  }
  
  // 无refresh_token时清除数据并跳转登录页
  if (!tokenData.refresh_token) {
    console.log('[TokenRefresh] 无refresh_token，清除token数据并跳转登录页')
    TokenManager.clearToken()
    RouterService.reLaunch({ url: ROUTES.AUTH.LOGIN })
    return
  }
  
  // refresh_token过期时清除数据并跳转登录页
  if (TokenManager.isRefreshTokenExpired()) {
    console.log('[TokenRefresh] refresh_token已过期，清除token数据并跳转登录页')
    TokenManager.clearToken()
    RouterService.reLaunch({ url: ROUTES.AUTH.LOGIN })
    return
  }
  
  // 启动定时器
  this.startRefreshTimer()
}
```

这套token监听机制确保了用户在使用应用过程中的无感token刷新体验，同时提供了完善的异常处理和安全保护。