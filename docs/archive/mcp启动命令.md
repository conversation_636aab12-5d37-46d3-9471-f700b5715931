npx @agentdeskai/browser-tools-server@1.2.0

// 5001 是启动服务监听的端口  cursor配置中需要和这个保持一致
npx mcp-interactive-feedback 5002   

如果配置文件这样写  貌似cursor会自动帮我我们启动服务
{
  "mcpServers": {
    "browser-tools": {
      "command": "npx",
      "args": ["@agentdeskai/browser-tools-mcp@1.2.0"]
    },
    "interactive-feedback-mcp": {
      "command": "npx",
      "args": ["mcp-interactive-feedback"]
    }
  }
}   


勾选  “Disable HTTP/2”  选项


这样就会强制 Cursor 使用 HTTP/1.1，从而避开网络兼容性问题。

✅ 设置完成后，连接错误大多会消失，使用起来也顺畅多了

不过，禁用 HTTP/2 后，Cursor 的响应速度会稍微慢一点，不过，至少不会再出现一直断开链接的情况了，希望 Cursor 能够早点修复这个问题。
