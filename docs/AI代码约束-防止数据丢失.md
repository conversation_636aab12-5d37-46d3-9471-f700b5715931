# AI代码约束 - 防止数据丢失专项规范

> 🚨 **强制执行规则** - 防止页面切换导致登录状态和页面数据丢失

## 📋 问题背景

在OTO相亲交友App开发过程中，发现页面切换会导致：
1. **登录状态丢失** - token数据被清除
2. **页面数据丢失** - 用户输入、状态数据等丢失
3. **用户体验极差** - 需要重新登录和重新操作

## 🚨 强制约束规则

### 1. Token管理强制约束

#### 1.1 Token存储约束
**必须**: 所有token相关数据必须使用多重存储策略
```typescript
// ✅ 正确：多重存储
const setToken = (tokenData: TokenData) => {
  // 1. Pinia状态管理
  authStore.setToken(tokenData)
  
  // 2. uni.storage持久化
  uni.setStorageSync('access_token', tokenData.access_token)
  uni.setStorageSync('token_expire_time', tokenData.expire_time)
  
  // 3. 内存缓存备份
  window.__TOKEN_CACHE__ = tokenData
}

// ❌ 错误：单一存储
localStorage.setItem('token', token) // 页面刷新会丢失
```

#### 1.2 Token获取约束
**必须**: 获取token时必须按优先级检查多个存储位置
```typescript
// ✅ 正确：多重检查
const getToken = (): string | null => {
  // 1. 优先从Pinia获取
  const authStore = useAuthStore()
  if (authStore.token) return authStore.token
  
  // 2. 从uni.storage获取
  const storedToken = uni.getStorageSync('access_token')
  if (storedToken) {
    // 恢复到Pinia
    authStore.setToken(storedToken)
    return storedToken
  }
  
  // 3. 从内存缓存获取
  if (window.__TOKEN_CACHE__?.access_token) {
    return window.__TOKEN_CACHE__.access_token
  }
  
  return null
}

// ❌ 错误：单一来源
const getToken = () => authStore.token // 页面刷新后为空
```

#### 1.3 Token刷新约束
**必须**: 实现无感刷新机制，防止token过期导致登录状态丢失
```typescript
// ✅ 正确：无感刷新
const checkTokenExpiry = () => {
  const tokenData = getTokenData()
  if (!tokenData) return false
  
  const now = Math.floor(Date.now() / 1000)
  const remainingTime = tokenData.expire_time - now
  
  // 剩余时间小于5分钟时刷新
  if (remainingTime < 300 && remainingTime > 0) {
    refreshToken()
  }
  
  return remainingTime > 0
}

// ❌ 错误：不处理token过期
// 直接使用过期token会导致401错误
```

### 2. 页面数据保护约束

#### 2.1 表单数据保护
**必须**: 所有用户输入的表单数据必须实时保存
```typescript
// ✅ 正确：实时保存表单数据
const useFormProtection = (formKey: string) => {
  const formData = ref({})
  
  // 监听表单变化，实时保存
  watch(formData, (newData) => {
    uni.setStorageSync(`form_${formKey}`, newData)
  }, { deep: true })
  
  // 页面加载时恢复数据
  onMounted(() => {
    const savedData = uni.getStorageSync(`form_${formKey}`)
    if (savedData) {
      formData.value = savedData
    }
  })
  
  // 提交成功后清除缓存
  const clearFormCache = () => {
    uni.removeStorageSync(`form_${formKey}`)
  }
  
  return { formData, clearFormCache }
}

// ❌ 错误：不保护表单数据
// 页面切换后用户输入全部丢失
```

#### 2.2 页面状态保护
**必须**: 关键页面状态必须持久化
```typescript
// ✅ 正确：状态持久化
const usePageState = (pageKey: string) => {
  const pageState = ref({
    scrollTop: 0,
    activeTab: 0,
    selectedItems: [],
    // ... 其他状态
  })
  
  // 状态变化时保存
  watch(pageState, (newState) => {
    uni.setStorageSync(`page_${pageKey}`, newState)
  }, { deep: true })
  
  // 页面恢复时读取
  onMounted(() => {
    const savedState = uni.getStorageSync(`page_${pageKey}`)
    if (savedState) {
      pageState.value = { ...pageState.value, ...savedState }
    }
  })
  
  return { pageState }
}

// ❌ 错误：不保护页面状态
// 页面切换后滚动位置、选择状态等全部丢失
```

### 3. 网络请求保护约束

#### 3.1 请求重试约束
**必须**: 所有API请求必须有重试机制
```typescript
// ✅ 正确：请求重试
const requestWithRetry = async (apiCall: () => Promise<any>, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await apiCall()
    } catch (error: any) {
      if (error.code === 401) {
        // token过期，尝试刷新
        await refreshToken()
        continue
      }
      
      if (i === maxRetries - 1) {
        throw error
      }
      
      // 延迟重试
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
    }
  }
}

// ❌ 错误：不处理请求失败
// 网络波动或token过期导致请求失败，用户需要重新操作
```

#### 3.2 请求缓存约束
**必须**: 重要数据请求必须有缓存机制
```typescript
// ✅ 正确：请求缓存
const useApiCache = () => {
  const cache = new Map()
  
  const cachedRequest = async (key: string, apiCall: () => Promise<any>, ttl = 300000) => {
    const cached = cache.get(key)
    if (cached && Date.now() - cached.timestamp < ttl) {
      return cached.data
    }
    
    const data = await apiCall()
    cache.set(key, { data, timestamp: Date.now() })
    
    // 同时保存到本地存储
    uni.setStorageSync(`api_cache_${key}`, { data, timestamp: Date.now() })
    
    return data
  }
  
  return { cachedRequest }
}

// ❌ 错误：不缓存请求结果
// 页面切换后重新请求，浪费流量和时间
```

### 4. 路由跳转保护约束

#### 4.1 跳转前数据保存
**必须**: 页面跳转前必须保存当前页面数据
```typescript
// ✅ 正确：跳转前保存
const navigateWithProtection = (url: string, pageData?: any) => {
  // 保存当前页面数据
  if (pageData) {
    const currentPage = getCurrentPages().pop()
    const pageKey = currentPage?.route || 'unknown'
    uni.setStorageSync(`page_data_${pageKey}`, pageData)
  }
  
  // 执行跳转
  uni.navigateTo({ url })
}

// ❌ 错误：直接跳转
uni.navigateTo({ url }) // 当前页面数据丢失
```

#### 4.2 返回时数据恢复
**必须**: 页面返回时必须恢复之前的数据
```typescript
// ✅ 正确：返回时恢复
onShow(() => {
  const currentPage = getCurrentPages().pop()
  const pageKey = currentPage?.route || 'unknown'
  const savedData = uni.getStorageSync(`page_data_${pageKey}`)
  
  if (savedData) {
    // 恢复页面数据
    Object.assign(pageState.value, savedData)
  }
})

// ❌ 错误：不恢复数据
// 返回页面后之前的操作状态全部丢失
```

### 5. 错误处理保护约束

#### 5.1 全局错误捕获
**必须**: 实现全局错误捕获，防止错误导致数据丢失
```typescript
// ✅ 正确：全局错误处理
const setupGlobalErrorHandler = () => {
  // Vue错误处理
  app.config.errorHandler = (err, vm, info) => {
    console.error('Vue Error:', err, info)
    
    // 保存错误现场数据
    const errorContext = {
      error: err.message,
      info,
      timestamp: Date.now(),
      pageData: getCurrentPageData()
    }
    uni.setStorageSync('error_context', errorContext)
  }
  
  // Promise错误处理
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled Promise Rejection:', event.reason)
    
    // 保存错误现场
    const errorContext = {
      error: event.reason,
      timestamp: Date.now(),
      pageData: getCurrentPageData()
    }
    uni.setStorageSync('promise_error_context', errorContext)
  })
}

// ❌ 错误：不处理全局错误
// 错误发生时数据丢失，无法恢复
```

#### 5.2 数据恢复机制
**必须**: 提供数据恢复机制
```typescript
// ✅ 正确：数据恢复
const recoverFromError = () => {
  const errorContext = uni.getStorageSync('error_context')
  if (errorContext && errorContext.pageData) {
    // 尝试恢复页面数据
    return errorContext.pageData
  }
  return null
}

// ❌ 错误：不提供恢复机制
// 错误后用户需要重新开始所有操作
```

### 6. 开发调试约束

#### 6.1 数据流追踪
**必须**: 在开发环境下追踪数据流
```typescript
// ✅ 正确：数据流追踪
const trackDataFlow = (action: string, data: any) => {
  // #ifdef APP-PLUS-NVUE || H5
  if (process.env.NODE_ENV === 'development') {
    console.log(`[DataFlow] ${action}:`, data)
    
    // 保存追踪日志
    const logs = uni.getStorageSync('data_flow_logs') || []
    logs.push({
      action,
      data: JSON.stringify(data),
      timestamp: Date.now()
    })
    
    // 只保留最近100条日志
    if (logs.length > 100) {
      logs.splice(0, logs.length - 100)
    }
    
    uni.setStorageSync('data_flow_logs', logs)
  }
  // #endif
}

// ❌ 错误：不追踪数据流
// 问题发生时无法定位数据丢失的原因
```

#### 6.2 数据完整性检查
**必须**: 定期检查数据完整性
```typescript
// ✅ 正确：数据完整性检查
const checkDataIntegrity = () => {
  const requiredData = ['access_token', 'user_info', 'app_config']
  const missingData = []
  
  requiredData.forEach(key => {
    const data = uni.getStorageSync(key)
    if (!data) {
      missingData.push(key)
    }
  })
  
  if (missingData.length > 0) {
    console.warn('Missing critical data:', missingData)
    
    // 尝试从备份恢复
    missingData.forEach(key => {
      const backup = uni.getStorageSync(`backup_${key}`)
      if (backup) {
        uni.setStorageSync(key, backup)
      }
    })
  }
}

// ❌ 错误：不检查数据完整性
// 数据丢失后无法及时发现和恢复
```

## 🔧 实施检查清单

### 开发阶段检查
- [ ] 所有token操作使用多重存储
- [ ] 所有表单实现实时保存
- [ ] 所有页面状态持久化
- [ ] 所有API请求有重试机制
- [ ] 所有路由跳转保护数据
- [ ] 实现全局错误处理
- [ ] 添加数据流追踪

### 测试阶段检查
- [ ] 测试页面刷新后数据恢复
- [ ] 测试应用切换后状态保持
- [ ] 测试网络异常后数据恢复
- [ ] 测试token过期后无感刷新
- [ ] 测试错误发生后数据恢复

### 上线前检查
- [ ] 关闭开发环境日志
- [ ] 清理测试数据
- [ ] 验证生产环境数据保护
- [ ] 确认错误上报机制

## 🚨 违规处罚

如果AI生成的代码违反以上约束：

1. **立即停止代码生成**
2. **回滚到安全状态**
3. **重新按约束生成代码**
4. **增加额外的保护措施**

## 📝 总结

这些约束的核心目标是：
- **零数据丢失** - 任何情况下都不能丢失用户数据
- **无感体验** - 用户感知不到数据保护机制的存在
- **快速恢复** - 异常情况下能快速恢复到正常状态
- **可追踪性** - 问题发生时能快速定位和解决

**记住：用户体验是第一优先级，数据丢失是不可接受的！** 🎯 