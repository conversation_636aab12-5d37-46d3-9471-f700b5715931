# 数据保护实施指南

> 📖 配套《AI代码约束 - 防止数据丢失专项规范》的具体实施指南

## 🎯 快速开始

### 1. 基础工具函数

首先创建基础的数据保护工具函数：

```typescript
// src/utils/dataProtection.ts

/**
 * 多重存储管理器
 */
export class MultiStorage {
  /**
   * 设置数据到多个存储位置
   */
  static set(key: string, value: any): void {
    try {
      // 1. uni.storage (持久化)
      uni.setStorageSync(key, value)
      
      // 2. 内存缓存
      if (typeof window !== 'undefined') {
        window.__DATA_CACHE__ = window.__DATA_CACHE__ || {}
        window.__DATA_CACHE__[key] = value
      }
      
      // 3. 备份存储
      uni.setStorageSync(`backup_${key}`, value)
      
    } catch (error) {
      console.error('MultiStorage.set error:', error)
    }
  }
  
  /**
   * 从多个存储位置获取数据
   */
  static get(key: string): any {
    try {
      // 1. 优先从内存缓存获取
      if (typeof window !== 'undefined' && window.__DATA_CACHE__?.[key]) {
        return window.__DATA_CACHE__[key]
      }
      
      // 2. 从uni.storage获取
      const value = uni.getStorageSync(key)
      if (value) {
        // 恢复到内存缓存
        if (typeof window !== 'undefined') {
          window.__DATA_CACHE__ = window.__DATA_CACHE__ || {}
          window.__DATA_CACHE__[key] = value
        }
        return value
      }
      
      // 3. 从备份存储获取
      const backup = uni.getStorageSync(`backup_${key}`)
      if (backup) {
        // 恢复主存储
        this.set(key, backup)
        return backup
      }
      
      return null
    } catch (error) {
      console.error('MultiStorage.get error:', error)
      return null
    }
  }
  
  /**
   * 清除数据
   */
  static remove(key: string): void {
    try {
      uni.removeStorageSync(key)
      uni.removeStorageSync(`backup_${key}`)
      
      if (typeof window !== 'undefined' && window.__DATA_CACHE__) {
        delete window.__DATA_CACHE__[key]
      }
    } catch (error) {
      console.error('MultiStorage.remove error:', error)
    }
  }
}

/**
 * 表单数据保护 Composable
 */
export const useFormProtection = (formKey: string) => {
  const formData = ref({})
  const isLoading = ref(false)
  
  // 监听表单变化，实时保存
  watch(formData, (newData) => {
    if (Object.keys(newData).length > 0) {
      MultiStorage.set(`form_${formKey}`, newData)
    }
  }, { deep: true })
  
  // 页面加载时恢复数据
  onMounted(() => {
    const savedData = MultiStorage.get(`form_${formKey}`)
    if (savedData) {
      formData.value = savedData
      console.log(`[FormProtection] 恢复表单数据: ${formKey}`, savedData)
    }
  })
  
  // 提交成功后清除缓存
  const clearFormCache = () => {
    MultiStorage.remove(`form_${formKey}`)
    console.log(`[FormProtection] 清除表单缓存: ${formKey}`)
  }
  
  // 手动保存表单
  const saveForm = () => {
    MultiStorage.set(`form_${formKey}`, formData.value)
  }
  
  return {
    formData,
    isLoading,
    clearFormCache,
    saveForm
  }
}

/**
 * 页面状态保护 Composable
 */
export const usePageState = (pageKey: string) => {
  const pageState = ref({
    scrollTop: 0,
    activeTab: 0,
    selectedItems: [],
    searchKeyword: '',
    filterOptions: {},
    // 可根据需要扩展
  })
  
  // 状态变化时保存
  watch(pageState, (newState) => {
    MultiStorage.set(`page_${pageKey}`, newState)
  }, { deep: true })
  
  // 页面恢复时读取
  onMounted(() => {
    const savedState = MultiStorage.get(`page_${pageKey}`)
    if (savedState) {
      pageState.value = { ...pageState.value, ...savedState }
      console.log(`[PageState] 恢复页面状态: ${pageKey}`, savedState)
    }
  })
  
  // 清除页面状态
  const clearPageState = () => {
    MultiStorage.remove(`page_${pageKey}`)
    pageState.value = {
      scrollTop: 0,
      activeTab: 0,
      selectedItems: [],
      searchKeyword: '',
      filterOptions: {},
    }
  }
  
  return {
    pageState,
    clearPageState
  }
}
```

### 2. Token管理增强

```typescript
// src/utils/tokenManager.ts

export interface TokenData {
  access_token: string
  refresh_token?: string
  expire_in: number
  refresh_expire_in?: number
  obtain_time: number // 获取时间戳(秒)
}

export class TokenManager {
  private static readonly TOKEN_KEY = 'access_token'
  private static readonly TOKEN_DATA_KEY = 'token_data'
  
  /**
   * 保存Token数据
   */
  static setToken(tokenData: TokenData): void {
    const dataWithTime = {
      ...tokenData,
      obtain_time: Math.floor(Date.now() / 1000)
    }
    
    // 多重存储
    MultiStorage.set(this.TOKEN_KEY, tokenData.access_token)
    MultiStorage.set(this.TOKEN_DATA_KEY, dataWithTime)
    
    // 更新Pinia状态
    const authStore = useAuthStore()
    authStore.setToken(dataWithTime)
    
    console.log('[TokenManager] Token已保存', dataWithTime)
  }
  
  /**
   * 获取Token
   */
  static getToken(): string | null {
    // 1. 优先从Pinia获取
    const authStore = useAuthStore()
    if (authStore.token) {
      return authStore.token
    }
    
    // 2. 从存储获取
    const token = MultiStorage.get(this.TOKEN_KEY)
    if (token) {
      // 恢复到Pinia
      const tokenData = this.getTokenData()
      if (tokenData) {
        authStore.setToken(tokenData)
      }
      return token
    }
    
    return null
  }
  
  /**
   * 获取完整Token数据
   */
  static getTokenData(): TokenData | null {
    return MultiStorage.get(this.TOKEN_DATA_KEY)
  }
  
  /**
   * 检查Token是否有效
   */
  static isTokenValid(): boolean {
    const tokenData = this.getTokenData()
    if (!tokenData) return false
    
    const now = Math.floor(Date.now() / 1000)
    const remainingTime = tokenData.expire_in - (now - tokenData.obtain_time)
    
    return remainingTime > 0
  }
  
  /**
   * 获取Token剩余时间(秒)
   */
  static getTokenRemainingTime(): number {
    const tokenData = this.getTokenData()
    if (!tokenData) return 0
    
    const now = Math.floor(Date.now() / 1000)
    const remainingTime = tokenData.expire_in - (now - tokenData.obtain_time)
    
    return Math.max(0, remainingTime)
  }
  
  /**
   * 是否需要刷新Token
   */
  static shouldRefreshToken(): boolean {
    const remainingTime = this.getTokenRemainingTime()
    // 剩余时间小于5分钟时需要刷新
    return remainingTime > 0 && remainingTime < 300
  }
  
  /**
   * 清除Token
   */
  static clearToken(): void {
    MultiStorage.remove(this.TOKEN_KEY)
    MultiStorage.remove(this.TOKEN_DATA_KEY)
    
    const authStore = useAuthStore()
    authStore.clearAuth()
    
    console.log('[TokenManager] Token已清除')
  }
}
```

### 3. 网络请求增强

```typescript
// src/utils/requestWithProtection.ts

/**
 * 带保护的网络请求
 */
export const requestWithProtection = async <T = any>(
  apiCall: () => Promise<T>,
  options: {
    maxRetries?: number
    cacheKey?: string
    cacheTTL?: number
  } = {}
): Promise<T> => {
  const { maxRetries = 3, cacheKey, cacheTTL = 300000 } = options
  
  // 检查缓存
  if (cacheKey) {
    const cached = MultiStorage.get(`api_cache_${cacheKey}`)
    if (cached && Date.now() - cached.timestamp < cacheTTL) {
      console.log(`[RequestProtection] 使用缓存数据: ${cacheKey}`)
      return cached.data
    }
  }
  
  // 重试机制
  for (let i = 0; i < maxRetries; i++) {
    try {
      const result = await apiCall()
      
      // 保存到缓存
      if (cacheKey) {
        MultiStorage.set(`api_cache_${cacheKey}`, {
          data: result,
          timestamp: Date.now()
        })
      }
      
      return result
    } catch (error: any) {
      console.error(`[RequestProtection] 请求失败 (${i + 1}/${maxRetries}):`, error)
      
      // Token过期处理
      if (error.code === 401) {
        console.log('[RequestProtection] Token过期，尝试刷新')
        
        try {
          // 这里应该调用token刷新逻辑
          await refreshToken()
          continue // 重试请求
        } catch (refreshError) {
          console.error('[RequestProtection] Token刷新失败:', refreshError)
          // 跳转到登录页
          uni.reLaunch({ url: '/pages/auth/login' })
          throw refreshError
        }
      }
      
      // 最后一次重试失败
      if (i === maxRetries - 1) {
        throw error
      }
      
      // 延迟重试
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
    }
  }
  
  throw new Error('请求失败')
}

/**
 * 刷新Token函数
 */
const refreshToken = async (): Promise<void> => {
  // 实现token刷新逻辑
  // 这里应该调用实际的刷新接口
  console.log('[RequestProtection] 执行Token刷新')
}
```

### 4. 路由跳转保护

```typescript
// src/utils/navigationProtection.ts

/**
 * 受保护的路由跳转
 */
export const navigateWithProtection = (
  url: string,
  options: {
    pageData?: any
    saveCurrentState?: boolean
    navigationType?: 'navigateTo' | 'redirectTo' | 'reLaunch' | 'switchTab'
  } = {}
) => {
  const { pageData, saveCurrentState = true, navigationType = 'navigateTo' } = options
  
  if (saveCurrentState) {
    // 获取当前页面信息
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const pageKey = currentPage?.route || 'unknown'
    
    // 保存当前页面数据
    if (pageData) {
      MultiStorage.set(`page_data_${pageKey}`, pageData)
      console.log(`[NavigationProtection] 保存页面数据: ${pageKey}`, pageData)
    }
    
    // 保存页面栈信息
    const pageStack = pages.map(page => ({
      route: page.route,
      options: page.options
    }))
    MultiStorage.set('page_stack', pageStack)
  }
  
  // 执行跳转
  switch (navigationType) {
    case 'navigateTo':
      uni.navigateTo({ url })
      break
    case 'redirectTo':
      uni.redirectTo({ url })
      break
    case 'reLaunch':
      uni.reLaunch({ url })
      break
    case 'switchTab':
      uni.switchTab({ url })
      break
  }
}

/**
 * 页面数据恢复 Composable
 */
export const usePageDataRecovery = () => {
  onShow(() => {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const pageKey = currentPage?.route || 'unknown'
    
    // 恢复页面数据
    const savedData = MultiStorage.get(`page_data_${pageKey}`)
    if (savedData) {
      console.log(`[PageDataRecovery] 恢复页面数据: ${pageKey}`, savedData)
      return savedData
    }
    
    return null
  })
}
```

## 🔧 实际应用示例

### 示例1：登录页面

```vue
<template>
  <view class="login-page">
    <view class="form-container">
      <input 
        v-model="formData.phone" 
        placeholder="请输入手机号"
        type="number"
      />
      <input 
        v-model="formData.password" 
        placeholder="请输入密码"
        type="password"
      />
      <button @click="handleLogin" :loading="isLoading">登录</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { useFormProtection } from '@/utils/dataProtection'
import { TokenManager } from '@/utils/tokenManager'
import { requestWithProtection } from '@/utils/requestWithProtection'
import { navigateWithProtection } from '@/utils/navigationProtection'

// 表单数据保护
const { formData, isLoading, clearFormCache } = useFormProtection('login')

// 初始化表单结构
if (!formData.value.phone) {
  formData.value = {
    phone: '',
    password: ''
  }
}

/**
 * 处理登录
 */
const handleLogin = async () => {
  if (!formData.value.phone || !formData.value.password) {
    uni.showToast({ title: '请填写完整信息', icon: 'none' })
    return
  }
  
  isLoading.value = true
  
  try {
    // 使用受保护的请求
    const response = await requestWithProtection(
      () => api.auth.passwordLogin({
        phone: formData.value.phone,
        password: formData.value.password,
        loginType: 'password'
      }),
      {
        maxRetries: 3,
        cacheKey: `login_${formData.value.phone}` // 可选的缓存
      }
    )
    
    if (response.code === 200) {
      // 保存Token
      TokenManager.setToken(response.data)
      
      // 清除表单缓存
      clearFormCache()
      
      // 受保护的跳转
      navigateWithProtection('/pages/modules/home/<USER>', {
        navigationType: 'reLaunch'
      })
      
      uni.showToast({ title: '登录成功' })
    } else {
      uni.showToast({ title: response.msg || '登录失败', icon: 'none' })
    }
  } catch (error: any) {
    console.error('登录失败:', error)
    uni.showToast({ title: error.message || '登录失败', icon: 'none' })
  } finally {
    isLoading.value = false
  }
}
</script>
```

### 示例2：用户信息编辑页面

```vue
<template>
  <view class="user-info-page">
    <view class="form-container">
      <input v-model="formData.nickname" placeholder="昵称" />
      <picker @change="onGenderChange" :value="formData.gender">
        <view class="picker">性别: {{ genderText }}</view>
      </picker>
      <textarea v-model="formData.bio" placeholder="个人简介" />
      
      <button @click="handleSave" :loading="isLoading">保存</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useFormProtection, usePageState } from '@/utils/dataProtection'
import { requestWithProtection } from '@/utils/requestWithProtection'

// 表单数据保护
const { formData, isLoading, clearFormCache, saveForm } = useFormProtection('userInfo')

// 页面状态保护
const { pageState } = usePageState('userInfo')

// 初始化表单数据
if (!formData.value.nickname) {
  formData.value = {
    nickname: '',
    gender: 0, // 0: 未知, 1: 男, 2: 女
    bio: ''
  }
}

const genderText = computed(() => {
  const genderMap = { 0: '未选择', 1: '男', 2: '女' }
  return genderMap[formData.value.gender] || '未选择'
})

/**
 * 性别选择
 */
const onGenderChange = (e: any) => {
  formData.value.gender = parseInt(e.detail.value)
  // 手动触发保存
  saveForm()
}

/**
 * 保存用户信息
 */
const handleSave = async () => {
  isLoading.value = true
  
  try {
    const response = await requestWithProtection(
      () => api.user.updateUserInfo(formData.value),
      {
        maxRetries: 3
      }
    )
    
    if (response.code === 200) {
      // 清除表单缓存
      clearFormCache()
      
      uni.showToast({ title: '保存成功' })
      
      // 返回上一页
      uni.navigateBack()
    } else {
      uni.showToast({ title: response.msg || '保存失败', icon: 'none' })
    }
  } catch (error: any) {
    console.error('保存失败:', error)
    uni.showToast({ title: error.message || '保存失败', icon: 'none' })
  } finally {
    isLoading.value = false
  }
}

// 页面离开时自动保存
onUnload(() => {
  saveForm()
})
</script>
```

### 示例3：列表页面状态保护

```vue
<template>
  <view class="user-list-page">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <input 
        v-model="pageState.searchKeyword" 
        placeholder="搜索用户"
        @input="onSearchInput"
      />
    </view>
    
    <!-- 筛选标签 -->
    <view class="filter-tabs">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="['tab', { active: pageState.activeTab === index }]"
        @click="switchTab(index)"
      >
        {{ tab.name }}
      </view>
    </view>
    
    <!-- 用户列表 -->
    <scroll-view 
      :scroll-top="pageState.scrollTop"
      @scroll="onScroll"
      class="user-list"
    >
      <view 
        v-for="user in userList" 
        :key="user.id"
        :class="['user-item', { selected: pageState.selectedItems.includes(user.id) }]"
        @click="toggleSelect(user.id)"
      >
        {{ user.nickname }}
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { usePageState } from '@/utils/dataProtection'
import { requestWithProtection } from '@/utils/requestWithProtection'

// 页面状态保护
const { pageState } = usePageState('userList')

const userList = ref([])
const tabs = [
  { name: '全部', value: 'all' },
  { name: '在线', value: 'online' },
  { name: '附近', value: 'nearby' }
]

/**
 * 搜索输入处理
 */
const onSearchInput = debounce(() => {
  loadUserList()
}, 500)

/**
 * 切换标签
 */
const switchTab = (index: number) => {
  pageState.value.activeTab = index
  pageState.value.selectedItems = [] // 清空选择
  loadUserList()
}

/**
 * 滚动处理
 */
const onScroll = (e: any) => {
  pageState.value.scrollTop = e.detail.scrollTop
}

/**
 * 切换选择
 */
const toggleSelect = (userId: string) => {
  const index = pageState.value.selectedItems.indexOf(userId)
  if (index > -1) {
    pageState.value.selectedItems.splice(index, 1)
  } else {
    pageState.value.selectedItems.push(userId)
  }
}

/**
 * 加载用户列表
 */
const loadUserList = async () => {
  try {
    const response = await requestWithProtection(
      () => api.user.getUserList({
        keyword: pageState.value.searchKeyword,
        type: tabs[pageState.value.activeTab].value
      }),
      {
        cacheKey: `userList_${pageState.value.activeTab}_${pageState.value.searchKeyword}`,
        cacheTTL: 60000 // 1分钟缓存
      }
    )
    
    if (response.code === 200) {
      userList.value = response.data
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
  }
}

// 防抖函数
function debounce(func: Function, wait: number) {
  let timeout: any
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 页面加载时恢复数据
onMounted(() => {
  loadUserList()
})
</script>
```

## 📋 检查清单

在实施数据保护时，请确保：

### Token管理
- [ ] 使用 `TokenManager` 管理所有token操作
- [ ] 实现token过期检查和自动刷新
- [ ] 多重存储策略已实施

### 表单保护
- [ ] 所有表单使用 `useFormProtection`
- [ ] 表单数据实时保存
- [ ] 提交成功后清除缓存

### 页面状态
- [ ] 重要页面使用 `usePageState`
- [ ] 滚动位置、选择状态等已保护
- [ ] 页面切换时状态保持

### 网络请求
- [ ] 使用 `requestWithProtection` 包装API调用
- [ ] 实现请求重试机制
- [ ] 重要数据有缓存策略

### 路由跳转
- [ ] 使用 `navigateWithProtection` 进行跳转
- [ ] 跳转前保存当前页面数据
- [ ] 返回时恢复页面状态

## 🚨 常见问题

### Q: 数据保护会影响性能吗？
A: 影响很小。我们使用了内存缓存优先的策略，只有在必要时才进行存储操作。

### Q: 如何处理敏感数据？
A: 敏感数据（如密码）不应该被缓存。在 `useFormProtection` 中可以配置排除字段。

### Q: 缓存数据过多怎么办？
A: 实现了自动清理机制，定期清除过期缓存。也可以手动调用清理函数。

### Q: 如何调试数据保护功能？
A: 在开发环境下，所有数据保护操作都有详细的控制台日志输出。

记住：**用户体验是第一优先级，数据丢失是不可接受的！** 🎯