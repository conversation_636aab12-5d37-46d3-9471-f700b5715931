# AI约束快速参考卡

> 🚀 **快速参考** - 开发时必须遵循的AI约束规则

## 🚨 核心原则

### 1. 最小化修改原则
- **只改需要改的** - 能改1行不改10行
- **只改指定文件** - 不要同时修改多个文件
- **只改指定功能** - 不要跨模块修改

### 2. 数据保护原则
- **多重存储** - Token和重要数据必须多重存储
- **实时保存** - 表单数据必须实时保存
- **状态保护** - 页面状态必须持久化

### 3. 范围控制原则
- **明确边界** - 修改前明确影响范围
- **保护核心** - 核心功能需要特别保护
- **渐进修改** - 采用渐进式修改策略

## 🔒 文件保护级别

### 🔴 严格保护（禁止修改）
```
src/utils/request/auth.ts        # 网络请求基础库
src/store/modules/auth.ts          # 认证状态管理
src/config/auth.ts               # 全局配置
src/components/base/               # 基础组件库
src/api/interceptors/              # 请求拦截器
.env.*                            # 环境配置
manifest.json                     # 应用配置
pages.json                        # 路由配置
```

### 🟡 谨慎修改（需要授权）
```
src/utils/                        # 工具函数
src/api/modules/                  # API接口
src/store/modules/                # 状态管理
src/components/business/          # 业务组件
```

### 🟢 正常修改（可以修改）
```
src/pages/modules/                # 页面文件
src/config/constants.ts           # 业务常量
src/config/enums.ts              # 枚举定义
```

## 📋 修改前检查清单

```markdown
- [ ] 明确修改目标文件
- [ ] 确认修改功能范围
- [ ] 确认不会影响其他模块
- [ ] 确认不会破坏现有功能
- [ ] 选择最小化修改方案
```

## 🛡️ 数据保护检查清单

```markdown
- [ ] Token使用多重存储
- [ ] 表单数据实时保存
- [ ] 页面状态持久化
- [ ] API请求有重试机制
- [ ] 路由跳转保护数据
```

## 🚫 严禁操作

### 代码修改禁止项
- ❌ 同时修改多个不相关文件
- ❌ 修改全局配置（除非明确授权）
- ❌ 破坏现有组件结构
- ❌ 修改函数签名（除非必要）
- ❌ 跨模块修改代码

### 数据处理禁止项
- ❌ 使用单一存储策略
- ❌ 不保护表单数据
- ❌ 忽略页面状态保护
- ❌ 不处理网络异常
- ❌ 直接跳转不保护数据

## ✅ 推荐操作

### 代码修改推荐
- ✅ 函数内部修改
- ✅ 在现有结构内添加
- ✅ 新功能独立实现
- ✅ 保持向后兼容
- ✅ 渐进式修改

### 数据保护推荐
- ✅ 使用 `MultiStorage` 类
- ✅ 使用 `useFormProtection`
- ✅ 使用 `usePageState`
- ✅ 使用 `requestWithProtection`
- ✅ 使用 `navigateWithProtection`

## 🔧 常用工具函数

### Token管理
```typescript
import { TokenManager } from '@/utils/tokenManager'

// 保存Token
TokenManager.setToken(tokenData)

// 获取Token
const token = TokenManager.getToken()

// 检查Token有效性
const isValid = TokenManager.isTokenValid()
```

### 表单保护
```typescript
import { useFormProtection } from '@/utils/dataProtection'

const { formData, clearFormCache, saveForm } = useFormProtection('formKey')
```

### 页面状态保护
```typescript
import { usePageState } from '@/utils/dataProtection'

const { pageState, clearPageState } = usePageState('pageKey')
```

### 受保护的请求
```typescript
import { requestWithProtection } from '@/utils/requestWithProtection'

const response = await requestWithProtection(
  () => api.someMethod(params),
  { maxRetries: 3, cacheKey: 'cacheKey' }
)
```

### 受保护的跳转
```typescript
import { navigateWithProtection } from '@/utils/navigationProtection'

navigateWithProtection('/pages/target/page', {
  pageData: currentPageData,
  navigationType: 'navigateTo'
})
```

## 🎯 修改任务模板

```markdown
## 修改任务
- **目标文件**: src/pages/auth/login.vue
- **修改功能**: 添加手机号验证
- **影响范围**: 仅限登录页面表单验证
- **不影响**: 其他页面、全局配置、API接口

## 修改约束
- **只能修改**: validateForm 函数内部逻辑
- **不能修改**: 组件结构、其他函数、导入语句
- **保护对象**: 现有登录流程、API调用逻辑

## 验证标准
- **成功**: 手机号验证正常工作
- **失败**: 影响了其他功能或页面
```

## 🚨 紧急情况处理

### 发现违规修改时
1. **立即停止** - 停止当前修改操作
2. **评估影响** - 检查修改影响范围
3. **回滚代码** - 恢复到修改前状态
4. **重新规划** - 采用最小化修改方案

### 数据丢失时
1. **检查存储** - 查看多重存储位置
2. **恢复数据** - 从备份存储恢复
3. **分析原因** - 找出数据丢失原因
4. **加强保护** - 增加额外保护措施

## 📞 关键词识别

### 允许大范围修改的关键词
- "重构整个模块"
- "全面优化"
- "重新设计"
- "完全重写"
- "架构调整"

### 只能最小化修改的关键词
- "修复bug"
- "添加功能"
- "优化性能"
- "调整样式"
- "更新逻辑"

## 💡 记住这些原则

1. **最小化修改是最安全的修改**
2. **数据丢失是不可接受的**
3. **用户体验是第一优先级**
4. **保护现有功能比添加新功能更重要**
5. **渐进式修改比一次性大改更可靠**

---

**🎯 核心目标：零数据丢失 + 最小化修改 + 最佳用户体验**
