

# 企业级开发规范文档

## 📱 项目概述

### 项目基本信息
- **项目名称**: OTO相亲交友App
- **项目类型**: 跨平台移动应用
- **业务领域**: 社交娱乐 - 相亲交友
- **目标用户**: 单身青年群体，主要年龄段25-35岁
- **核心功能**: 用户认证、个人资料、智能匹配、实时聊天、线下服务

### 技术栈架构
```
🏗️ 核心技术栈
├─ 📱 跨平台框架: UniApp v3.0.0-4030620241128001
├─ ⚡ 前端框架: Vue.js v3.4.21 (Composition API)
├─ 📝 开发语言: TypeScript v4.9.4
├─ 🎨 样式处理: Sass v1.89.0
├─ 🗃️ 状态管理: Pinia v2.1.7
├─ 🌐 国际化: Vue-i18n v9.9.1
├─ 🔐 加密处理: Crypto-js v4.2.0
├─ 🛠️ 构建工具: Vite v5.2.8
├─ 📦 包管理: npm
└─ 💻 IDE支持: VS Code + TypeScript

🎯 目标平台 (优先级排序)
├─ 📱 移动端App: Android APK + iOS IPA (最高优先级)
├─ 💬 微信小程序: 原生微信生态 (高优先级)
├─ 📺 抖音小程序: 字节跳动生态 (高优先级)
└─ 🌐 H5网页版: 仅支持调试环境 (调试优先级)
```

### 开发环境要求
```bash
# Node.js环境
Node.js >= 16.0.0
npm >= 8.0.0

# 开发工具
HBuilderX >= 4.36 (可选，主要用于发布)
VS Code + Vetur/Volar插件
Android Studio (Android开发)
Xcode (iOS开发，需macOS)

# 平台要求
✅ Windows 10+ / macOS 10.15+ / Ubuntu 18.04+
✅ 内存: 8GB+ (推荐16GB+)
✅ 存储: 20GB+ 可用空间
```

### 项目特性与优势
- **🔄 一码多端**: 一套代码同时支持8个平台发布
- **⚡ 高性能**: Vue3 + TypeScript + 现代构建工具
- **🎨 现代UI**: 响应式设计 + 原生交互体验  
- **🔒 企业级**: 完整的认证、权限、安全体系
- **📱 原生能力**: 完整使用设备摄像头、GPS、推送等
- **🌐 国际化**: 支持多语言切换
- **🔧 可维护**: 模块化架构 + 详细文档 + 单元测试

> **重要说明**: 本文档定义了完整的企业级开发规范，任何开发人员都必须严格遵循此规范。新加入的AI Agent或开发者仅需阅读此文档即可完全理解项目架构、技术选型、开发流程和质量标准。

## 📋 文档目录

- [📱 项目概述](#-项目概述)
- [1. 项目架构规范](#1-项目架构规范)
- [2. 目录结构规范](#2-目录结构规范)
- [3. 技术栈详细说明](#3-技术栈详细说明)
- [4. 命名规范](#4-命名规范)
- [5. TypeScript规范](#5-typescript规范)
- [6. 组件开发规范](#6-组件开发规范)
- [7. API开发规范](#7-api开发规范)
- [8. 状态管理规范](#8-状态管理规范)
- [9. Composable规范](#9-composable规范)
- [10. 样式规范](#10-样式规范)
- [11. 配置文件规范](#11-配置文件规范)
- [12. 文档规范](#12-文档规范)
- [13. 代码提交规范](#13-代码提交规范)
- [14. 开发流程规范](#14-开发流程规范)
- [15. 部署发布规范](#15-部署发布规范)

---

## 3. 技术栈详细说明

### 3.1 核心技术选型原则

> **企业级选型标准**: 稳定性优先、社区活跃、长期维护、向后兼容

#### 跨平台框架: UniApp v3.0.0
- **选择理由**: DCloud官方维护，Vue3生态完整，一码八端
- **稳定性**: ✅ 企业级应用广泛使用，更新频繁但向后兼容好
- **优势**: 完整的原生能力封装，丰富的插件生态
- **适用场景**: ✅ Android/iOS原生打包 ✅ 微信/抖音小程序 ✅ H5调试

#### 前端框架: Vue.js v3.4.21
- **选择理由**: Composition API成熟，TypeScript支持完善
- **稳定性**: ✅ Vue3已进入稳定期，API不再频繁变化
- **优势**: 响应式系统优化，更好的Tree Shaking，更小的Bundle
- **核心特性**: Composition API、Teleport、Fragments、更好的TypeScript支持

#### 开发语言: TypeScript v4.9.4
- **选择理由**: 静态类型检查，大型项目必备，IDE支持完善
- **稳定性**: ✅ 4.x版本稳定，5.x版本过于激进暂不采用
- **优势**: 编译时错误检查，代码提示，重构安全
- **配置**: 严格模式开启，完整的类型检查

#### 状态管理: Pinia v2.1.7
- **选择理由**: Vue官方推荐，替代Vuex，更好的TypeScript支持
- **稳定性**: ✅ 已成为Vue生态标准，API稳定
- **优势**: 模块化设计，DevTools支持，轻量级
- **特性**: 自动代码分割，服务端渲染支持

#### 构建工具: Vite v5.2.8
- **选择理由**: 现代构建工具，HMR快速，ESM原生支持
- **稳定性**: ✅ 5.x版本稳定，Vue生态深度集成
- **优势**: 开发服务器启动快，支持多种预处理器
- **配置**: 针对UniApp优化，支持条件编译

#### 样式处理: Sass v1.89.0 (现代化语法)
- **选择理由**: 成熟的CSS预处理器，采用现代化模块语法
- **稳定性**: ✅ Dart Sass版本，性能好，功能完整  
- **现代化要求**: 
  - ✅ 使用`@use`和`@forward`代替废弃的`@import`
  - ✅ 模块化命名空间，避免全局污染
  - ✅ 完全避免即将在Dart Sass 3.0中删除的废弃API
- **配置**: 现代编译器API，静默废弃警告

### 3.2 开发环境要求详解

#### Node.js环境
```bash
# 推荐版本 (LTS稳定版)
Node.js v18.18.0+ (LTS) 
npm v9.8.0+

# 版本要求说明
- 最低要求: Node.js v16.0.0
- 推荐使用: Node.js v18.x LTS版本
- 避免使用: Node.js v20.x (过新，可能有兼容性问题)
```

#### 开发工具链
```bash
# 必装工具
VS Code v1.80.0+              # 主要开发IDE
HBuilderX v4.36+               # UniApp发布工具 
Android Studio v2023.1+       # Android开发环境
Xcode v15.0+ (仅macOS)        # iOS开发环境

# VS Code推荐插件
- Volar (Vue3官方插件)
- TypeScript Hero
- ESLint + Prettier
- Auto Rename Tag
- Bracket Pair Colorizer
- GitLens
```

#### 移动端开发环境 (重点)
```bash
# Android开发环境 (最高优先级)
✅ JDK v11+ (推荐使用JDK 11或17)
✅ Android SDK v33+ (target SDK)
✅ Android模拟器 或 真机调试
✅ ADB工具配置正确

# iOS开发环境 (最高优先级，仅macOS)
✅ Xcode v15.0+
✅ iOS Simulator
✅ 开发者证书配置
✅ 真机调试证书

# 模拟器配置建议
Android: API Level 28+ (Android 9.0+)
iOS: iOS 12.0+ 设备模拟
```

### 3.3 平台适配策略

#### App端优先开发策略
```typescript
// 平台判断和适配
// #ifdef APP-PLUS
// Android/iOS原生功能
// #endif

// #ifdef MP-WEIXIN
// 微信小程序特定功能
// #endif

// #ifdef MP-TOUTIAO
// 抖音小程序特定功能
// #endif

// #ifdef H5
// H5调试功能 (生产环境不使用)
// #endif
```

#### 原生能力使用原则
1. **相机功能**: 优先使用uni.chooseImage，兼容性最好
2. **定位功能**: uni.getLocation，注意权限申请
3. **推送通知**: 使用UniPush，支持厂商推送
4. **支付功能**: 集成原生支付SDK
5. **社交分享**: 使用原生分享组件

---

## 1. 项目架构规范

### 1.1 分层架构原则

项目采用分层架构设计，各层职责明确：

```
┌─────────────────┐
│   Pages 页面层   │  ← 页面组件，负责路由和页面逻辑
├─────────────────┤
│ Components 组件层│  ← UI组件，负责视图渲染
├─────────────────┤
│Composables 逻辑层│  ← 业务逻辑，负责状态和行为
├─────────────────┤
│   Store 状态层   │  ← 全局状态，负责数据管理
├─────────────────┤
│    API 接口层    │  ← 数据接口，负责与后端通信
├─────────────────┤
│   Utils 工具层   │  ← 工具函数，负责通用功能
└─────────────────┘
```

### 1.2 模块化原则

- **按业务模块组织代码**：相关功能集中在同一模块
- **松耦合高内聚**：模块间依赖最小，模块内功能集中
- **统一导出管理**：每个模块提供统一的导出文件

---

## 2. 目录结构规范

### 2.1 项目完整目录结构

```
oto-xq-web/                           # 🎯 项目根目录
├─ src/                               # 📂 源代码目录
│   ├─ api/                          # 🔌 API接口层
│   │   ├─ modules/                  # 按业务模块组织的接口
│   │   │   ├─ auth.ts              # 认证相关接口(登录、注册、实名认证)
│   │   │   ├─ user.ts              # 用户相关接口(资料、设置)
│   │   │   ├─ match.ts             # 匹配相关接口(缘分台、推荐)
│   │   │   ├─ message.ts           # 消息相关接口(聊天、通知)
│   │   │   ├─ service.ts           # 服务相关接口(VIP、活动)
│   │   │   └─ auth.ts             # 接口模块统一导出
│   │   ├─ interceptors/            # 请求/响应拦截器
│   │   │   ├─ request.ts           # 请求拦截器(认证、签名)
│   │   │   ├─ response.ts          # 响应拦截器(错误处理)
│   │   │   └─ auth.ts             # 拦截器统一导出
│   │   ├─ types/                   # 接口类型定义
│   │   │   ├─ common.ts            # 通用接口类型
│   │   │   ├─ auth.ts              # 认证接口类型
│   │   │   ├─ user.ts              # 用户接口类型
│   │   │   └─ auth.ts             # 类型统一导出
│   │   └─ auth.ts                 # API层统一导出
│   ├─ components/                   # 🧩 组件层
│   │   ├─ business/                # 业务组件
│   │   │   ├─ CaptchaModal/        # 验证码模态框组件
│   │   │   │   ├─ index.vue        # 组件主文件
│   │   │   │   ├─ types.ts         # 组件类型定义
│   │   │   │   └─ README.md        # 组件使用文档
│   │   │   ├─ UserCard/            # 用户卡片组件
│   │   │   ├─ MatchCard/           # 匹配卡片组件
│   │   │   └─ auth.ts             # 业务组件统一导出
│   │   ├─ base/                    # 基础UI组件
│   │   │   ├─ Button/              # 基础按钮组件
│   │   │   ├─ Input/               # 基础输入框组件
│   │   │   ├─ Modal/               # 基础模态框组件
│   │   │   ├─ Form/                # 基础表单组件
│   │   │   └─ auth.ts             # 基础组件统一导出
│   │   └─ auth.ts                 # 组件层统一导出
│   ├─ hooks/                       # 🔄 Hooks逻辑复用层
│   │   ├─ useAuth.ts               # 认证相关逻辑(登录、注册状态)
│   │   ├─ useFormValidation.ts     # 表单验证逻辑(新版统一验证框架)
│   │   ├─ useCaptcha.ts            # 验证码逻辑
│   │   ├─ useRequest.ts            # 请求相关逻辑
│   │   ├─ useStorage.ts            # 本地存储逻辑
│   │   └─ index.ts                 # Hooks统一导出
│   ├─ config/                      # ⚙️ 项目配置层
│   │   ├─ env.ts                   # 环境变量配置
│   │   ├─ constants.ts             # 常量配置
│   │   ├─ routes.ts                # 路由配置
│   │   └─ auth.ts                 # 配置统一导出
│   ├─ store/                       # 🗃️ Pinia状态管理层
│   │   ├─ modules/                 # 状态模块
│   │   │   ├─ auth.ts              # 认证状态管理
│   │   │   ├─ user.ts              # 用户状态管理
│   │   │   ├─ app.ts               # 应用全局状态
│   │   │   └─ auth.ts             # 状态模块统一导出
│   │   ├─ types/                   # 状态类型定义
│   │   │   └─ auth.ts             # 状态类型统一导出
│   │   └─ auth.ts                 # Store入口文件
│   ├─ utils/                       # 🛠️ 工具库层
│   │   ├─ request.ts               # 基础请求封装
│   │   ├─ requestService.ts        # 高级请求服务
│   │   ├─ storageService.ts        # 存储服务封装
│   │   ├─ cryptoUtils.ts           # 加密工具函数
│   │   ├─ formatUtils.ts           # 格式化工具函数
│   │   ├─ validationUtils.ts       # 校验工具函数
│   │   └─ auth.ts                 # 工具库统一导出
│   ├─ types/                       # 📋 全局类型声明层
│   │   ├─ global.d.ts              # 全局类型扩展
│   │   ├─ uni.d.ts                 # UniApp类型扩展
│   │   ├─ env.d.ts                 # 环境变量类型
│   │   └─ auth.ts                 # 类型统一导出
│   ├─ static/                      # 📁 静态资源层
│   │   ├─ images/                  # 图片资源
│   │   │   ├─ icons/               # 图标资源
│   │   │   ├─ logos/               # Logo资源
│   │   │   └─ backgrounds/         # 背景图资源
│   │   ├─ styles/                  # 样式资源
│   │   │   ├─ variables.scss       # Sass变量定义
│   │   │   ├─ mixins.scss          # Sass混入函数
│   │   │   ├─ common.scss          # 通用样式类
│   │   │   └─ index.scss           # 样式统一导出
│   │   └─ fonts/                   # 字体资源
│   ├─ pages/                       # 📄 页面组件层
│   │   └─ modules/                 # 按业务模块分组
│   │       ├─ auth/                # 认证模块页面
│   │       │   ├─ login/           # 登录页面
│   │       │   │   ├─ components/  # 页面私有组件
│   │       │   │   ├─ composables/ # 页面私有逻辑
│   │       │   │   └─ index.vue    # 页面主文件
│   │       │   ├─ register/        # 注册页面
│   │       │   └─ realAuth/        # 实名认证页面
│   │       ├─ user/                # 用户模块页面
│   │       │   ├─ mine/            # 我的页面
│   │       │   ├─ information/     # 基础资料页面
│   │       │   └─ settings/        # 设置页面
│   │       ├─ home/                # 首页模块
│   │       │   └─ index/           # 首页
│   │       ├─ match/               # 匹配模块
│   │       │   └─ index/           # 缘分台页面
│   │       ├─ message/             # 消息模块
│   │       │   └─ index/           # 消息页面
│   │       └─ service/             # 服务模块
│   │           └─ index/           # 服务页面
│   ├─ custom-tab-bar/              # 🎯 自定义TabBar组件
│   │   └─ index.vue                # TabBar实现
│   ├─ App.vue                      # 🚀 应用根组件
│   ├─ main.ts                      # 🎯 应用入口文件
│   ├─ pages.json                   # 📋 页面路由配置
│   ├─ manifest.json                # ⚙️ 应用配置文件
│   ├─ uni.scss                     # 🎨 全局样式文件
│   ├─ env.d.ts                     # 🔧 环境类型声明
│   └─ shime-uni.d.ts              # 🔧 UniApp类型扩展
├─ docs/                            # 📖 项目文档目录
│   ├─ 企业级开发规范.md             # 📋 本开发规范文档
│   ├─ 企业级目录结构规范.md         # 📁 目录结构详细说明
│   ├─ Sass优化与警告修复总结.md     # 🎨 样式系统优化记录
│   └─ 项目需要调整的文件清单.md     # 📝 项目调整计划
├─ node_modules/                    # 📦 依赖包目录
├─ package.json                     # 📋 项目配置文件
├─ package-lock.json                # 🔒 依赖版本锁定
├─ tsconfig.json                    # 🔧 TypeScript配置
├─ vite.config.ts                   # ⚡ Vite构建配置
├─ .eslintrc.js                     # 📏 ESLint代码规范配置
├─ .gitignore                       # 🚫 Git忽略文件配置
├─ shims-uni.d.ts                   # 🔧 UniApp全局类型
└─ README.md                        # 📄 项目说明文档
```

### 2.2 核心业务模块说明

#### 认证模块 (auth)
- **功能**: 用户登录、注册、实名认证、密码管理
- **页面**: login(登录) / register(注册) / realAuth(实名认证)
- **特点**: 多种登录方式(密码/短信)、安全认证、实名验证

#### 用户模块 (user)  
- **功能**: 个人资料管理、账户设置、隐私配置
- **页面**: mine(我的) / information(基础资料) / settings(设置)
- **特点**: 个人信息完善、头像上传、偏好设置

#### 匹配模块 (match)
- **功能**: 智能推荐、缘分匹配、互动功能
- **页面**: index(缘分台主页)
- **特点**: 算法推荐、滑卡交互、匹配通知

#### 消息模块 (message)
- **功能**: 实时聊天、系统通知、消息中心
- **页面**: index(消息列表)
- **特点**: 即时通讯、多媒体消息、消息状态

#### 服务模块 (service)
- **功能**: VIP服务、增值服务、活动中心
- **页面**: index(服务中心)
- **特点**: 会员体系、付费服务、营销活动

#### 首页模块 (home)
- **功能**: 应用首页、功能导航、推荐内容
- **页面**: index(首页)
- **特点**: 统一入口、快捷导航、个性化推荐

### 2.2 API目录结构

```
api/
├─ modules/              # 业务模块接口
│   ├─ auth.ts          # 认证相关接口
│   ├─ user.ts          # 用户相关接口
│   ├─ captcha.ts       # 验证码相关接口
│   ├─ match.ts         # 匹配相关接口
│   ├─ message.ts       # 消息相关接口
│   ├─ service.ts       # 服务相关接口
│   └─ auth.ts         # 模块统一导出
├─ interceptors/         # 拦截器
│   ├─ request.ts       # 请求拦截器
│   ├─ response.ts      # 响应拦截器
│   └─ auth.ts         # 拦截器统一导出
├─ types/               # 接口类型
│   ├─ common.ts        # 通用接口类型
│   ├─ auth.ts          # 认证接口类型
│   ├─ user.ts          # 用户接口类型
│   └─ auth.ts         # 类型统一导出
└─ auth.ts             # API层统一导出
```

### 2.3 组件目录结构

```
components/
├─ business/            # 业务组件
│   ├─ CaptchaModal/   # 验证码模态框
│   │   ├─ index.vue   # 组件主文件
│   │   ├─ types.ts    # 组件类型定义
│   │   └─ README.md   # 组件文档
│   ├─ UserCard/       # 用户卡片
│   ├─ MatchCard/      # 匹配卡片
│   └─ auth.ts        # 业务组件统一导出
├─ base/               # 基础UI组件
│   ├─ Button/         # 基础按钮
│   │   ├─ index.vue   # 组件主文件
│   │   ├─ types.ts    # 组件类型定义
│   │   └─ README.md   # 组件文档
│   ├─ Input/          # 基础输入框
│   ├─ Modal/          # 基础模态框
│   ├─ Form/           # 基础表单
│   └─ auth.ts        # 基础组件统一导出
└─ auth.ts            # 组件层统一导出
```

### 2.4 页面目录结构

```
pages/modules/
├─ auth/               # 认证模块
│   ├─ login/          # 登录页面
│   │   ├─ components/ # 页面私有组件
│   │   │   ├─ LoginTabs.vue
│   │   │   ├─ PasswordLogin.vue
│   │   │   └─ SmsLogin.vue
│   │   ├─ composables/# 页面私有逻辑
│   │   │   └─ useLogin.ts
│   │   └─ index.vue   # 页面主组件
│   ├─ register/       # 注册页面
│   │   ├─ components/
│   │   ├─ composables/
│   │   └─ index.vue
│   └─ realAuth/       # 实名认证页面
├─ user/               # 用户模块
│   ├─ profile/        # 个人资料
│   ├─ settings/       # 设置
│   ├─ infomation/     # 基础资料
│   └─ mine/           # 我的页面
├─ home/               # 首页模块
│   └─ index/
├─ message/            # 消息模块
│   └─ index/
├─ match/              # 匹配模块
│   └─ index/
└─ service/            # 服务模块
    └─ index/
```

---

## 3. 命名规范

### 3.1 文件和目录命名

#### 目录命名
- 使用 **kebab-case**（短横线连接）
- 语义化命名，体现功能或业务模块
- 示例：`user-profile`、`message-list`、`captcha-modal`

#### 文件命名
- **Vue组件文件**：使用 **PascalCase**
  ```
  ✅ UserProfile.vue
  ✅ MessageList.vue
  ✅ CaptchaModal.vue
  ```

- **TypeScript文件**：使用 **camelCase**
  ```
  ✅ useAuth.ts
  ✅ userService.ts
  ✅ validationRules.ts
  ```

- **配置文件**：使用 **kebab-case**
  ```
  ✅ vite.config.ts
  ✅ eslint.config.js
  ✅ prettier.config.js
  ```

### 3.2 代码命名

#### 变量和函数命名
- 使用 **camelCase**
- 语义化命名，体现用途
```typescript
✅ const userInfo = ref()
✅ const isLoading = ref(false)
✅ const handleSubmit = () => {}
✅ const getUserProfile = async () => {}
```

#### 常量命名
- 使用 **SCREAMING_SNAKE_CASE**
```typescript
✅ const API_BASE_URL = 'https://api.example.com'
✅ const MAX_RETRY_COUNT = 3
✅ const DEFAULT_PAGE_SIZE = 20
```

#### 类和接口命名
- 使用 **PascalCase**
```typescript
✅ interface UserInfo {}
✅ interface LoginParams {}
✅ class ApiService {}
✅ type ResponseData<T> = {}
```

#### 枚举命名
- 使用 **PascalCase**，值使用 **SCREAMING_SNAKE_CASE**
```typescript
✅ enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING'
}
```

### 3.3 CSS类名命名

使用 **BEM方法论**：
```css
/* Block块 */
.user-card {}

/* Element元素 */
.user-card__avatar {}
.user-card__name {}
.user-card__description {}

/* Modifier修饰符 */
.user-card--featured {}
.user-card__avatar--large {}
```

---

## 4. TypeScript规范

### 4.1 类型定义规范

#### 接口定义
```typescript
/**
 * 用户信息接口
 */
export interface UserInfo {
  /** 用户ID */
  id: string
  /** 手机号 */
  phone: string
  /** 昵称 */
  nickname?: string
  /** 头像 */
  avatar?: string
  /** 创建时间 */
  createTime: string
}
```

#### 类型别名
```typescript
/** 登录类型 */
export type LoginType = 'password' | 'sms'

/** 用户状态 */
export type UserStatus = 'active' | 'inactive' | 'pending'

/** API响应数据 */
export type ResponseData<T = any> = {
  code: number
  message: string
  data: T
  timestamp: number
}
```

#### 泛型使用
```typescript
/**
 * 分页响应接口
 */
export interface PaginationResponse<T> {
  list: T[]
  total: number
  pageNum: number
  pageSize: number
}

/**
 * API请求封装
 */
export const request = {
  get: <T>(url: string) => Promise<ResponseData<T>>,
  post: <T>(url: string, data?: any) => Promise<ResponseData<T>>
}
```

### 4.2 类型文件组织

#### 全局类型
```typescript
// src/types/global.d.ts
declare global {
  interface Window {
    // 扩展window对象
  }
}

// UniApp类型扩展
declare module '@dcloudio/types' {
  // UniApp类型扩展
}
```

#### 模块类型
```typescript
// src/types/request.ts
export interface RequestConfig {
  url: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  headers?: Record<string, string>
}

export interface ResponseData<T = any> {
  code: number
  message: string
  data: T
}
```

### 4.3 严格模式配置

```json
// tsconfig.json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true
  }
}
```

---

## 5. 组件开发规范

### 5.1 组件结构规范

#### 标准组件模板
```vue
<template>
  <!-- 组件模板 -->
  <view class="component-name">
    <slot />
  </view>
</template>

<script setup lang="ts">
/**
 * 组件名称 - 组件描述
 * <AUTHOR>
 * @since 2024-01-01
 */

// ==================== 导入 ====================
import { ref, computed, defineProps, defineEmits } from 'vue'
import type { ComponentProps, ComponentEmits } from './types'

// ==================== Props ====================
interface Props {
  /** 属性描述 */
  modelValue?: string
  /** 是否禁用 */
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  disabled: false
})

// ==================== Emits ====================
const emit = defineEmits<{
  /** 值变化事件 */
  'update:modelValue': [value: string]
  /** 点击事件 */
  click: [event: Event]
}>()

// ==================== 响应式状态 ====================
const isActive = ref(false)
const computedValue = computed(() => {
  return props.modelValue.toUpperCase()
})

// ==================== 方法 ====================
/**
 * 处理点击事件
 */
const handleClick = (event: Event) => {
  if (props.disabled) return
  emit('click', event)
}

// ==================== 暴露方法 ====================
defineExpose({
  focus: () => {
    // 暴露给父组件的方法
  }
})
</script>

<style scoped>
.component-name {
  /* 组件样式 */
}
</style>
```

### 5.2 组件分类规范

#### 基础组件（Base Components）
- **位置**：`src/components/base/`
- **命名**：以 `Base` 前缀开头
- **职责**：提供基础UI功能，无业务逻辑
- **示例**：`BaseButton`、`BaseInput`、`BaseModal`

```vue
<!-- src/components/base/Button/index.vue -->
<template>
  <button :class="buttonClass" @click="handleClick">
    <slot />
  </button>
</template>

<script setup lang="ts">
interface Props {
  type?: 'primary' | 'secondary' | 'outline'
  size?: 'small' | 'medium' | 'large'
  disabled?: boolean
}
</script>
```

#### 业务组件（Business Components）
- **位置**：`src/components/business/`
- **命名**：语义化业务名称
- **职责**：包含特定业务逻辑的复合组件
- **示例**：`CaptchaModal`、`UserCard`、`MatchCard`

```vue
<!-- src/components/business/UserCard/index.vue -->
<template>
  <view class="user-card">
    <image :src="userInfo.avatar" class="user-card__avatar" />
    <text class="user-card__name">{{ userInfo.nickname }}</text>
  </view>
</template>

<script setup lang="ts">
import type { UserInfo } from '@/api/modules/user'

interface Props {
  userInfo: UserInfo
}
</script>
```

### 5.3 组件通信规范

#### Props传递
```typescript
// 使用接口定义Props
interface Props {
  /** 必传属性 */
  userId: string
  /** 可选属性 */
  showAvatar?: boolean
  /** 对象属性 */
  userInfo?: UserInfo
  /** 函数属性 */
  onSuccess?: (data: any) => void
}

const props = withDefaults(defineProps<Props>(), {
  showAvatar: true,
  userInfo: () => ({} as UserInfo)
})
```

#### 事件发射
```typescript
// 使用接口定义Emits
const emit = defineEmits<{
  /** 更新事件 */
  'update:modelValue': [value: string]
  /** 成功事件 */
  success: [data: UserInfo]
  /** 错误事件 */
  error: [message: string]
}>()

// 发射事件
emit('success', userData)
emit('error', '操作失败')
```

#### 插槽使用
```vue
<template>
  <view class="card">
    <!-- 默认插槽 -->
    <slot />
    
    <!-- 具名插槽 -->
    <view class="card__header">
      <slot name="header" />
    </view>
    
    <!-- 作用域插槽 -->
    <view class="card__content">
      <slot name="content" :data="cardData" />
    </view>
  </view>
</template>
```

---

## 6. API开发规范

### 6.1 API文件组织

#### 按业务模块组织
```typescript
// src/api/modules/auth.ts
/**
 * 认证相关接口
 */
import request from '@/utils/request'
import type { LoginParams, LoginResponse } from './types'

/**
 * 密码登录
 */
export const passwordLogin = (params: LoginParams) => {
  return request.post<LoginResponse>('/auth/login', params)
}

/**
 * 短信登录
 */
export const smsLogin = (params: LoginParams) => {
  return request.post<LoginResponse>('/auth/sms-login', params)
}
```

#### 类型定义
```typescript
// src/api/types/auth.ts
/** 登录参数 */
export interface LoginParams {
  phone: string
  password?: string
  smsCode?: string
  loginType: 'password' | 'sms'
  captchaVerification?: string
}

/** 登录响应 */
export interface LoginResponse {
  token: string
  refreshToken: string
  userInfo: UserInfo
}
```

### 6.2 请求封装规范

#### 请求拦截器
```typescript
// src/api/interceptors/request.ts
export const authInterceptor = (config: RequestConfig): RequestConfig => {
  const token = uni.getStorageSync('token')
  if (token) {
    config.headers = {
      ...config.headers,
      'Authorization': `Bearer ${token}`
    }
  }
  return config
}
```

#### 响应拦截器
```typescript
// src/api/interceptors/response.ts
export const errorResponseInterceptor = (response: ResponseData): ResponseData => {
  if (response.code !== 200 && response.code !== 0) {
    uni.showToast({
      title: response.message || '请求失败',
      icon: 'error'
    })
    throw new Error(response.message || '请求失败')
  }
  return response
}
```

### 6.3 错误处理规范

```typescript
/**
 * 统一错误处理
 */
export const handleApiError = (error: any) => {
  console.error('API Error:', error)
  
  if (error.code === 401) {
    // 处理认证失败
    uni.reLaunch({ url: '/pages/auth/login' })
  } else if (error.code >= 500) {
    // 处理服务器错误
    uni.showToast({ title: '服务器错误，请稍后重试', icon: 'error' })
  } else {
    // 处理其他错误
    uni.showToast({ title: error.message || '请求失败', icon: 'error' })
  }
}
```

---

## 7. 状态管理规范

### 7.1 Store组织规范

#### 按模块组织Store
```typescript
// src/store/modules/auth.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { UserInfo } from '@/api/modules/auth'

export const useAuthStore = defineStore('auth', () => {
  // ==================== 状态 ====================
  const token = ref<string>('')
  const userInfo = ref<UserInfo | null>(null)
  
  // ==================== 计算属性 ====================
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)
  
  // ==================== 操作方法 ====================
  /**
   * 设置认证信息
   */
  const setAuth = (authData: {
    token: string
    userInfo: UserInfo
  }) => {
    token.value = authData.token
    userInfo.value = authData.userInfo
    
    // 持久化存储
    uni.setStorageSync('token', authData.token)
    uni.setStorageSync('userInfo', authData.userInfo)
  }
  
  /**
   * 清除认证信息
   */
  const clearAuth = () => {
    token.value = ''
    userInfo.value = null
    uni.removeStorageSync('token')
    uni.removeStorageSync('userInfo')
  }
  
  return {
    // 状态
    token,
    userInfo,
    // 计算属性
    isLoggedIn,
    // 操作方法
    setAuth,
    clearAuth
  }
})
```

### 7.2 状态持久化规范

```typescript
/**
 * 状态持久化工具
 */
export const persistState = {
  /**
   * 保存状态到本地存储
   */
  save: <T>(key: string, value: T) => {
    try {
      uni.setStorageSync(key, JSON.stringify(value))
    } catch (error) {
      console.error('保存状态失败:', error)
    }
  },
  
  /**
   * 从本地存储恢复状态
   */
  restore: <T>(key: string, defaultValue: T): T => {
    try {
      const stored = uni.getStorageSync(key)
      return stored ? JSON.parse(stored) : defaultValue
    } catch (error) {
      console.error('恢复状态失败:', error)
      return defaultValue
    }
  }
}
```

---

## 8. Composable/Hooks规范

> **重要更新**: 项目已采用统一的 `src/hooks/` 目录来组织所有可复用逻辑，详细规范请参考 [Hooks使用规范指南](./Hooks使用规范指南.md)

### 8.1 目录组织规范

#### 推荐目录结构
```
src/hooks/                    # 统一的 Hooks 目录 (推荐)
├─ index.ts                  # 统一导出入口
├─ useAuth.ts               # 认证相关 Hook
├─ useCaptcha.ts            # 验证码相关 Hook
├─ useFormValidation.ts     # 表单验证 Hook
├─ useEnhancedFormPage.ts   # 增强表单页面 Hook
├─ useErrorHandler.ts       # 错误处理 Hook
└─ usePage.ts              # 页面逻辑 Hook

src/pages/composables/       # 页面特定逻辑 (保留)
└─ index.ts                 # 迁移说明和向后兼容
```

#### 导入规范
```typescript
// ✅ 推荐：从统一 hooks 目录导入
import { usePage, useAuth } from '@/hooks'

// ✅ 可接受：直接导入具体文件
import { usePage } from '@/hooks/usePage'

// ❌ 避免：使用已迁移的旧路径
import { usePage } from '@/pages/composables/usePage'
```

#### 命名规范
- 文件名：`use + 功能名称`，如 `useAuth.ts`、`useFormValidation.ts`
- 函数名：与文件名保持一致
- 目录：统一使用 `src/hooks/` 作为主要目录

#### 标准结构
```typescript
// src/hooks/useAuth.ts
/**
 * 认证相关 Hook
 */
import { ref, computed } from 'vue'
import { useAuthStore } from '@/store/modules/auth'
import { passwordLogin, smsLogin } from '@/api/modules/auth'
import type { LoginParams } from '@/api/types/auth'

export const useAuth = () => {
  // ==================== 状态 ====================
  const loading = ref(false)
  const authStore = useAuthStore()
  
  // ==================== 计算属性 ====================
  const isLoggedIn = computed(() => authStore.isLoggedIn)
  
  // ==================== 方法 ====================
  /**
   * 密码登录
   */
  const loginWithPassword = async (params: LoginParams) => {
    try {
      loading.value = true
      const response = await passwordLogin(params)
      
      if (response.code === 200) {
        authStore.setAuth(response.data)
        return { success: true, data: response.data }
      } else {
        throw new Error(response.message)
      }
    } catch (error: any) {
      return { success: false, message: error.message }
    } finally {
      loading.value = false
    }
  }
  
  // ==================== 返回 ====================
  return {
    // 状态
    loading,
    // 计算属性
    isLoggedIn,
    // 方法
    loginWithPassword
  }
}
```

### 8.2 业务逻辑封装

#### 表单验证Hook
```typescript
// src/hooks/useFormValidation.ts
export interface FieldConfig {
  label: string
  rules: ValidationRule[]
  value?: any
}

export interface ValidationRule {
  required?: boolean
  pattern?: RegExp
  minLength?: number
  maxLength?: number
  validator?: (value: any) => boolean | string
  message?: string
}

export const useFormValidation = () => {
  const validationState = reactive({
    errors: {} as Record<string, string>,
    isValidating: false
  })
  
  const addField = (name: string, config: FieldConfig) => {
    // 添加字段验证配置
  }
  
  const validateField = (name: string, value: any): boolean => {
    // 验证逻辑，更新 validationState.errors
  }
  
  const validateAndShowError = async (): Promise<boolean> => {
    // 验证所有字段并显示错误
  }
  
  return { 
    addField, 
    validateField, 
    validateAndShowError,
    validationState,
    getFieldError: (name: string) => validationState.errors[name]
  }
}
```

---

## 9. 样式规范 (现代化Sass语法)

### 9.1 现代Sass语法规范 ⭐

> **重要**: 完全避免即将废弃的@import语法，使用现代@use和@forward

#### 核心原则
1. **避免废弃API**: 禁用即将在Dart Sass 3.0中删除的@import语法
2. **模块化导入**: 使用`@use`导入模块，明确命名空间
3. **统一导出**: 使用`@forward`创建模块入口
4. **变量引用**: 通过命名空间访问变量，避免全局污染

#### 文件组织结构
```scss
src/static/styles/
├─ variables.scss    # 变量定义模块
├─ mixins.scss      # 混入函数模块  
├─ common.scss      # 通用样式模块
└─ index.scss       # 统一导出入口
```

#### 现代语法示例

**✅ 正确用法 (现代@use语法)**
```scss
// variables.scss - 变量定义
$primary-color: #ff6b9d;
$font-size-base: 28rpx;

// mixins.scss - 混入函数
@use './variables' as var;

@mixin button-style {
  background: var.$primary-color;
  font-size: var.$font-size-base;
}

// common.scss - 通用样式
@use './variables' as var;
@use './mixins' as mix;

.btn-primary {
  @include mix.button-style;
}

// index.scss - 统一导出
@forward './variables';
@forward './mixins';
@use './common';
```

**❌ 错误用法 (废弃@import语法)**
```scss
// ❌ 即将废弃，禁止使用
@import './variables';
@import './mixins';
@import './common';

// ❌ 会产生警告：Sass @import rules are deprecated
```

#### Vue组件中的使用

**✅ 推荐方式**
```vue
<style lang="scss">
// 使用@use导入需要的模块，通过命名空间访问
@use '@/static/styles/variables' as var;
@use '@/static/styles/mixins' as mix;

.component {
  color: var.$primary-color;
  @include mix.flex-center;
}
</style>
```

**❌ 废弃方式**
```vue
<style lang="scss">
// ❌ 避免使用@import
@import '@/static/styles/common.scss';
</style>
```

### 9.2 传统样式规范

### 9.1 CSS架构

#### 样式文件组织
```
src/
├─ static/styles/
│   ├─ variables.scss    # 变量定义
│   ├─ mixins.scss      # 混入函数
│   ├─ reset.scss       # 重置样式
│   ├─ common.scss      # 通用样式
│   └─ themes/          # 主题样式
└─ uni.scss             # UniApp全局样式
```

#### 变量定义
```scss
// src/static/styles/variables.scss

// 颜色变量
$primary-color: #007aff;
$secondary-color: #5ac461;
$warning-color: #ff9500;
$error-color: #ff3b30;

// 字体变量
$font-size-xs: 24rpx;
$font-size-sm: 28rpx;
$font-size-md: 32rpx;
$font-size-lg: 36rpx;
$font-size-xl: 40rpx;

// 间距变量
$spacing-xs: 8rpx;
$spacing-sm: 16rpx;
$spacing-md: 24rpx;
$spacing-lg: 32rpx;
$spacing-xl: 48rpx;

// 圆角变量
$border-radius-sm: 8rpx;
$border-radius-md: 12rpx;
$border-radius-lg: 16rpx;
```

### 9.2 命名规范

#### BEM方法论
```scss
// 块（Block）
.user-card {
  display: flex;
  padding: $spacing-md;
  background: white;
  border-radius: $border-radius-md;
}

// 元素（Element）
.user-card__avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.user-card__info {
  flex: 1;
  margin-left: $spacing-sm;
}

.user-card__name {
  font-size: $font-size-lg;
  font-weight: 500;
  color: #333;
}

// 修饰符（Modifier）
.user-card--featured {
  border: 2px solid $primary-color;
}

.user-card__avatar--large {
  width: 120rpx;
  height: 120rpx;
}
```

### 9.3 响应式设计

#### 断点定义
```scss
// 响应式断点
$breakpoints: (
  'mobile': 750rpx,
  'tablet': 1024rpx,
  'desktop': 1440rpx
);

// 混入函数
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}

// 使用示例
.container {
  padding: $spacing-md;
  
  @include respond-to('tablet') {
    padding: $spacing-lg;
  }
  
  @include respond-to('desktop') {
    padding: $spacing-xl;
  }
}
```

---

## 10. 文档规范

### 10.1 代码注释规范

#### 函数注释
```typescript
/**
 * 获取用户信息
 * @param userId 用户ID
 * @param includeProfile 是否包含详细资料
 * @returns 用户信息
 * @throws {Error} 当用户不存在时抛出错误
 * @example
 * ```typescript
 * const user = await getUserInfo('123', true)
 * console.log(user.nickname)
 * ```
 */
export const getUserInfo = async (
  userId: string, 
  includeProfile: boolean = false
): Promise<UserInfo> => {
  // 实现逻辑
}
```

#### 接口注释
```typescript
/**
 * 用户信息接口
 */
export interface UserInfo {
  /** 用户ID */
  id: string
  /** 手机号 */
  phone: string
  /** 昵称 */
  nickname?: string
  /** 头像URL */
  avatar?: string
  /** 用户状态 */
  status: UserStatus
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
}
```

### 10.2 README文档规范

#### 组件README模板
```markdown
# ComponentName 组件名称

> 组件简要描述

## 功能特性

- ✅ 特性1
- ✅ 特性2
- ✅ 特性3

## 使用方式

### 基础用法

\`\`\`vue
<template>
  <ComponentName v-model="value" @change="handleChange" />
</template>

<script setup>
import { ref } from 'vue'
import { ComponentName } from '@/components'

const value = ref('')
const handleChange = (newValue) => {
  console.log('值变化:', newValue)
}
</script>
\`\`\`

## API

### Props

| 参数 | 说明 | 类型 | 默认值 | 必填 |
|------|------|------|--------|------|
| modelValue | 绑定值 | string | '' | 是 |
| disabled | 是否禁用 | boolean | false | 否 |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| update:modelValue | 值变化时触发 | (value: string) |
| change | 值改变时触发 | (value: string) |

### Slots

| 插槽名 | 说明 | 参数 |
|--------|------|------|
| default | 默认内容 | - |
| prefix | 前缀内容 | - |

## 样式变量

\`\`\`scss
$component-bg-color: #fff;
$component-border-color: #ddd;
$component-text-color: #333;
\`\`\`
```

---

## 11. 代码提交规范

### 11.1 Git提交规范

#### 提交信息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

#### 类型（type）
- **feat**: 新功能
- **fix**: 修复bug
- **docs**: 文档变更
- **style**: 代码格式修改
- **refactor**: 代码重构
- **test**: 添加测试
- **chore**: 构建过程或工具变动

#### 示例
```bash
feat(auth): 添加短信登录功能

- 新增短信验证码组件
- 集成短信登录API
- 添加倒计时功能

Closes #123
```

### 11.2 分支管理规范

#### 分支命名
- **主分支**: `main`
- **开发分支**: `develop`
- **功能分支**: `feature/功能名称`
- **修复分支**: `fix/bug描述`
- **发布分支**: `release/版本号`

#### 分支流程
```bash
# 创建功能分支
git checkout -b feature/user-profile

# 开发完成后提交
git add .
git commit -m "feat(user): 添加用户资料页面"

# 推送到远程
git push origin feature/user-profile

# 创建Pull Request
# 代码审查通过后合并到develop
```

---

## 12. 开发流程规范

### 12.1 需求开发流程

1. **需求分析**
   - 理解业务需求
   - 分析技术实现方案
   - 评估开发工期

2. **设计阶段**
   - 设计API接口
   - 设计数据结构
   - 设计组件结构

3. **开发阶段**
   - 创建功能分支
   - 按模块开发功能
   - 编写单元测试

4. **测试阶段**
   - 自测功能
   - 提交测试
   - 修复bug

5. **上线阶段**
   - 代码审查
   - 合并主分支
   - 部署上线

### 12.2 代码审查清单

#### 代码质量
- [ ] 代码符合规范
- [ ] 类型定义完整
- [ ] 函数命名语义化
- [ ] 注释清晰完整

#### 功能实现
- [ ] 功能实现正确
- [ ] 边界条件处理
- [ ] 错误处理完善
- [ ] 性能考虑合理

#### 架构设计
- [ ] 模块划分合理
- [ ] 组件职责清晰
- [ ] 依赖关系简单
- [ ] 扩展性良好

### 12.3 项目构建规范

#### 构建配置
```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'

export default defineConfig({
  plugins: [uni()],
  resolve: {
    alias: {
      '@': '/src'
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: '@import "@/static/styles/variables.scss";'
      }
    }
  }
})
```

#### 环境配置
```typescript
// src/config/env.ts
interface EnvConfig {
  API_BASE_URL: string
  APP_NAME: string
  VERSION: string
}

const envConfig: Record<string, EnvConfig> = {
  development: {
    API_BASE_URL: 'http://localhost:8080',
    APP_NAME: '开发环境',
    VERSION: '1.0.0-dev'
  },
  production: {
    API_BASE_URL: 'https://api.prod.com',
    APP_NAME: '相亲交友',
    VERSION: '1.0.0'
  }
}

export const config = envConfig[process.env.NODE_ENV || 'development']
```

---

## 11. 配置文件规范

### 11.1 UniApp核心配置

#### manifest.json 应用配置
```json
{
  "name": "OTO相亲交友",
  "appid": "__UNI__XXXXXXX",
  "description": "专业的相亲交友平台",
  "versionName": "1.0.0",
  "versionCode": "100",
  
  "app-plus": {
    "usingComponents": true,
    "nvueStyleCompiler": "uni-app",
    "compilerVersion": 3,
    "distribute": {
      "android": {
        "permissions": [
          "<uses-permission android:name=\"android.permission.CAMERA\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
          "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
          "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>"
        ],
        "minSdkVersion": 21,
        "targetSdkVersion": 33
      },
      "ios": {
        "privacyDescription": {
          "NSCameraUsageDescription": "用于拍摄和上传头像照片",
          "NSLocationWhenInUseUsageDescription": "用于获取位置信息进行推荐",
          "NSPhotoLibraryUsageDescription": "用于选择和上传头像照片"
        }
      }
    }
  },
  
  "mp-weixin": {
    "appid": "",
    "setting": {
      "urlCheck": false,
      "es6": true,
      "minified": true
    },
    "permission": {
      "scope.userLocation": {
        "desc": "获取位置信息用于推荐附近的人"
      }
    }
  },
  
  "mp-toutiao": {
    "appid": "",
    "setting": {
      "urlCheck": false
    }
  }
}
```

#### pages.json 页面路由配置
```json
{
  "pages": [
    {
      "path": "pages/modules/home/<USER>/index",
      "style": {
        "navigationBarTitleText": "首页",
        "enablePullDownRefresh": true
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "OTO相亲交友",
    "navigationBarBackgroundColor": "#ffffff",
    "backgroundColor": "#ffffff"
  },
  "condition": {
    "current": 0,
    "list": [
      {
        "name": "登录页面",
        "path": "pages/auth/login"
      }
    ]
  }
}
```

### 11.2 构建配置

#### vite.config.ts
```typescript
import { defineConfig } from "vite";
import uni from "@dcloudio/vite-plugin-uni";
import { resolve } from "path";
import * as sass from 'sass';

export default defineConfig({
  plugins: [uni()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@/api': resolve(__dirname, 'src/api'),
      '@/components': resolve(__dirname, 'src/components'),
      '@/utils': resolve(__dirname, 'src/utils'),
      '@/types': resolve(__dirname, 'src/types'),
      '@/store': resolve(__dirname, 'src/store'),
      '@/config': resolve(__dirname, 'src/config')
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        implementation: sass,
        api: 'modern-compiler',
        silenceDeprecations: ['legacy-js-api']
      }
    }
  },
  define: {
    'process.env.UNI_PLATFORM': JSON.stringify(process.env.UNI_PLATFORM),
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV)
  },
  build: {
    // App端优化配置
    rollupOptions: {
      output: {
        manualChunks: {
          'vendor': ['vue', 'pinia'],
          'utils': ['crypto-js', '@/utils/index']
        }
      }
    }
  }
});
```

#### tsconfig.json TypeScript配置
```json
{
  "extends": "@vue/tsconfig/tsconfig.json",
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/api/*": ["./src/api/*"],
      "@/components/*": ["./src/components/*"],
      "@/utils/*": ["./src/utils/*"],
      "@/types/*": ["./src/types/*"],
      "@/store/*": ["./src/store/*"],
      "@/config/*": ["./src/config/*"]
    },
    "types": ["@dcloudio/types", "node"],
    "strict": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitAny": true,
    "skipLibCheck": true
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.vue",
    "src/**/*.json"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "unpackage"
  ]
}
```

### 11.3 代码质量配置

#### .eslintrc.js
```javascript
module.exports = {
  root: true,
  env: {
    browser: true,
    es2021: true,
    node: true,
    'uni-app/runtime': true
  },
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:vue/vue3-essential',
    'prettier'
  ],
  parser: 'vue-eslint-parser',
  parserOptions: {
    ecmaVersion: 2021,
    parser: '@typescript-eslint/parser',
    sourceType: 'module'
  },
  plugins: ['vue', '@typescript-eslint'],
  rules: {
    // App端开发重点规则
    'vue/multi-word-component-names': 'off',
    '@typescript-eslint/no-explicit-any': 'warn',
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'warn',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'warn'
  },
  globals: {
    uni: 'readonly',
    wx: 'readonly', // 微信小程序
    tt: 'readonly', // 抖音小程序
    plus: 'readonly' // App端
  }
}
```

---

## 15. 部署发布规范

### 15.1 App端发布流程 (最高优先级)

#### Android APK打包
```bash
# 1. 环境检查
node --version  # >= 16.0.0
npm --version   # >= 8.0.0

# 2. 依赖安装
npm install

# 3. 代码检查
npm run lint
npm run type-check

# 4. App端构建
npm run build:app-android

# 5. HBuilderX发布
# - 打开HBuilderX
# - 导入项目
# - 运行 > 运行到手机或模拟器 > Android设备
# - 发行 > 原生App-云打包 > Android
```

#### iOS IPA打包
```bash
# 1. macOS环境准备
# - Xcode v15.0+已安装
# - 开发者证书配置完成

# 2. 项目构建
npm run build:app-ios

# 3. HBuilderX发布
# - 发行 > 原生App-云打包 > iOS
# - 或者发行 > 原生App-离线打包 > 生成本地打包App资源
```

### 15.2 小程序发布流程

#### 微信小程序发布
```bash
# 1. 构建微信小程序版本
npm run build:mp-weixin

# 2. 微信开发者工具
# - 导入dist/build/mp-weixin目录
# - 上传代码
# - 提交审核
```

#### 抖音小程序发布
```bash
# 1. 构建抖音小程序版本
npm run build:mp-toutiao

# 2. 抖音开发者工具
# - 导入dist/build/mp-toutiao目录
# - 上传代码
# - 提交审核
```

### 15.3 版本管理规范

```json
{
  "version": "1.0.0",
  "versionCode": 100,
  "versionName": "1.0.0",
  "changelog": [
    "1.0.0: 初始版本发布",
    "1.0.1: 修复Android兼容性问题",
    "1.1.0: 新增抖音小程序支持"
  ]
}
```

---

## 📝 总结：App端优先的企业级开发规范

本规范文档专为 **OTO相亲交友App** 定制，采用 **App端优先** 的开发策略，确保项目能够成功打包为高质量的Android和iOS应用。

### 🎯 平台优先级与技术选型

#### 优先级策略
1. **📱 Android APK + iOS IPA** (最高优先级)
   - 主要盈利渠道，用户核心体验平台
   - 完整的原生功能支持，推送、支付、定位等
   
2. **💬 微信小程序** (高优先级)
   - 用户获取的重要渠道，微信生态深度集成
   
3. **📺 抖音小程序** (高优先级)
   - 年轻用户聚集地，字节跳动生态
   
4. **🌐 H5网页版** (调试优先级)
   - 仅用于开发调试，不做生产环境部署

#### 技术栈特点
- **稳定性优先**：选择经过验证的技术版本
- **App端优化**：所有配置都针对移动端原生应用优化
- **现代化工具链**：Vite + Modern Sass + TypeScript严格模式

### 🚀 核心架构优势

#### 1. 企业级技术选型
```typescript
// 经过验证的稳定技术栈
UniApp v3.0.0         // 跨平台框架，原生能力完整
Vue.js v3.4.21        // 前端框架，Composition API成熟
TypeScript v4.9.4     // 开发语言，4.x稳定版本
Pinia v2.1.7          // 状态管理，Vue官方推荐
Vite v5.2.8           // 构建工具，性能优异
Sass v1.89.0          // 样式预处理，兼容模式
```

#### 2. App端优化配置
```javascript
// vite.config.ts 关键配置
export default defineConfig({
  // App端代码分割优化
  build: {
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          if (id.includes('vue')) return 'vue'
          if (id.includes('pinia')) return 'pinia' 
          return 'vendor'
        }
      }
    }
  }
})
```

#### 3. 原生能力适配
```typescript
// 平台条件编译，App端优先
// #ifdef APP-PLUS
// Android/iOS原生功能实现
// #endif

// #ifdef MP-WEIXIN  
// 微信小程序特定功能
// #endif

// #ifdef MP-TOUTIAO
// 抖音小程序特定功能  
// #endif
```

### 📦 关键开发工具链

#### 必备环境 (App端开发)
```bash
# Node.js环境 (LTS稳定版)
Node.js v18.18.0+
npm v9.8.0+

# 移动端开发环境
Android Studio v2023.1+     # Android开发
Xcode v15.0+ (仅macOS)      # iOS开发  
HBuilderX v4.36+            # UniApp发布工具

# 代码质量工具
ESLint + TypeScript        # 代码检查
Prettier                   # 代码格式化
```

#### 关键构建命令
```bash
# App端优先构建流程
npm run lint              # 代码检查
npm run type-check        # 类型检查
npm run build:app-android # Android打包
npm run build:app-ios     # iOS打包

# 小程序构建
npm run build:mp-weixin   # 微信小程序
npm run build:mp-toutiao  # 抖音小程序
```

### 🏗️ 完整规范体系

本规范涵盖了从项目初始化到生产发布的完整流程：

- **🎯 项目架构**：面向业务的模块化设计，支持大型团队开发
- **📱 App端优化**：原生性能优化、平台适配、代码分割
- **📁 目录规范**：清晰的分层架构，便于代码组织和维护
- **🔤 命名规范**：统一的命名约定，提高代码可读性
- **📋 类型安全**：TypeScript严格模式，编译时错误捕获
- **🧩 组件系统**：可复用的组件架构，支持多端适配
- **🔌 API架构**：标准化的接口设计，统一错误处理
- **🗃️ 状态管理**：Pinia模块化状态，支持持久化
- **🎨 样式系统**：响应式设计，兼容多端显示
- **📖 文档规范**：完整的代码注释和项目文档
- **🔀 Git规范**：标准化的版本控制和分支管理
- **⚡ CI/CD**：自动化构建、测试、发布流程

### 🎉 预期效果与价值

通过严格遵循本规范，开发团队将获得：

#### 技术价值
- **高质量App**：确保Android/iOS应用的稳定性和性能
- **快速迭代**：统一的开发流程，提高开发效率
- **多端一致**：一套代码，多端发布，降低维护成本
- **可扩展性**：模块化架构，支持业务快速增长

#### 业务价值  
- **用户体验**：原生级别的App体验，提高用户留存
- **开发效率**：规范化开发，减少协作成本
- **质量保证**：完善的代码检查，降低Bug率
- **快速上线**：标准化发布流程，支持快速迭代

### 💡 最佳实践建议

1. **App端优先**：所有功能开发时首先确保在Android/iOS上完美运行
2. **稳定技术栈**：避免追求过于前沿的技术，优先选择稳定可靠的方案
3. **性能监控**：关注App启动速度、内存占用、网络请求等关键指标
4. **用户体验**：重视原生交互、离线功能、推送通知等移动端特性
5. **团队协作**：建立代码审查机制，确保规范的严格执行

---

**🎯 目标：打造用户喜爱、技术领先的相亲交友App！**

让我们携手遵循这套企业级开发规范，为用户提供卓越的移动端相亲交友体验！💝
