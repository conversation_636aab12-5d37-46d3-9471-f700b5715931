<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>乐享生活 - 完整原型图</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            overflow-x: auto;
        }

        .prototype-canvas {
            width: 3000px;
            min-height: 100vh;
            padding: 40px;
            position: relative;
        }

        .canvas-title {
            position: fixed;
            top: 20px;
            left: 20px;
            background: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .canvas-title h1 {
            font-size: 18px;
            color: #333;
            margin-bottom: 4px;
        }

        .canvas-title p {
            font-size: 12px;
            color: #666;
        }

        .phone-frame {
            width: 320px;
            height: 640px;
            background: #000;
            border-radius: 25px;
            padding: 8px;
            position: absolute;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .phone-frame:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 48px rgba(0,0,0,0.4);
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            background: white;
            border-radius: 18px;
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            height: 24px;
            background: #000;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 12px;
            font-size: 10px;
        }

        .page-content {
            height: calc(100% - 24px);
            overflow-y: auto;
            padding: 16px;
        }

        .page-title {
            position: absolute;
            bottom: -40px;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            background: rgba(255,255,255,0.9);
            padding: 8px;
            border-radius: 8px;
        }

        /* 连接线样式 */
        .connection-line {
            position: absolute;
            border: 2px dashed #667eea;
            z-index: 1;
        }

        .connection-arrow {
            position: absolute;
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-bottom: 12px solid #667eea;
            z-index: 2;
        }

        /* 页面特定样式 */
        .login-page {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding-top: 80px;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
        }

        .login-form {
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            margin-bottom: 12px;
            background: rgba(255,255,255,0.9);
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            background: #ff6b6b;
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
        }

        .home-page {
            background: #f8f9fa;
        }

        .search-bar {
            background: white;
            border-radius: 20px;
            padding: 8px 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .banner {
            height: 120px;
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%);
            border-radius: 12px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .grid-container {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }

        .grid-item {
            background: white;
            border-radius: 12px;
            padding: 12px 8px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .grid-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .grid-title {
            font-size: 11px;
            color: #333;
        }

        .profile-page {
            background: #f8f9fa;
        }

        .user-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            padding: 20px;
            color: white;
            margin-bottom: 16px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .avatar {
            width: 50px;
            height: 50px;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }

        .action-item {
            background: white;
            border-radius: 12px;
            padding: 16px 8px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .menu-list {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .menu-item {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .menu-item:last-child {
            border-bottom: none;
        }

        .service-page {
            background: #f8f9fa;
        }

        .category-header {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            color: white;
            padding: 20px;
            text-align: center;
            margin: -16px -16px 16px -16px;
        }

        .service-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .service-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .service-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;
        }

        .service-avatar {
            width: 40px;
            height: 40px;
            background: #667eea;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .service-info {
            flex: 1;
        }

        .service-name {
            font-weight: 600;
            margin-bottom: 2px;
        }

        .service-rating {
            font-size: 12px;
            color: #666;
        }

        .service-price {
            color: #ff6b6b;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="canvas-title">
        <h1>乐享生活 App 原型图</h1>
        <p>点击页面查看交互流程</p>
    </div>

    <div class="prototype-canvas">
        <!-- 登录页面 -->
        <div class="phone-frame" style="left: 50px; top: 50px;" onclick="navigateToPage('home')">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content login-page">
                    <div class="logo">OTO</div>
                    <h2>乐享生活</h2>
                    <p style="margin: 8px 0 20px; opacity: 0.8;">让生活更美好</p>
                    
                    <div class="login-form">
                        <input type="text" class="form-input" placeholder="手机号/邮箱" value="138****8888">
                        <input type="password" class="form-input" placeholder="密码" value="••••••••">
                        <button class="login-btn">登录</button>
                    </div>
                    
                    <p style="font-size: 12px; opacity: 0.7;">还没有账号？<span style="color: #ff6b6b;">立即注册</span></p>
                </div>
            </div>
            <div class="page-title">登录页面</div>
        </div>

        <!-- 首页 -->
        <div class="phone-frame" style="left: 450px; top: 50px;" onclick="navigateToPage('profile')">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content home-page">
                    <div class="search-bar">
                        <span style="color: #999;">🔍 搜索服务或需求</span>
                    </div>
                    
                    <div class="banner">
                        <span>新用户注册送50元优惠券</span>
                    </div>
                    
                    <div class="grid-container">
                        <div class="grid-item" onclick="navigateToPage('yoga')">
                            <div class="grid-icon" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; margin: 0 auto 4px;">瑜</div>
                            <div class="grid-title">瑜伽</div>
                        </div>
                        <div class="grid-item" onclick="navigateToPage('fitness')">
                            <div class="grid-icon" style="background: linear-gradient(135deg, #ff9a56, #ff6b6b); color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; margin: 0 auto 4px;">健</div>
                            <div class="grid-title">健身</div>
                        </div>
                        <div class="grid-item">
                            <div class="grid-icon" style="background: linear-gradient(135deg, #4ecdc4, #44a08d); color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; margin: 0 auto 4px;">游</div>
                            <div class="grid-title">游戏陪玩</div>
                        </div>
                        <div class="grid-item">
                            <div class="grid-icon" style="background: linear-gradient(135deg, #ffecd2, #fcb69f); color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; margin: 0 auto 4px;">食</div>
                            <div class="grid-title">美食聚餐</div>
                        </div>
                        <div class="grid-item">
                            <div class="grid-icon" style="background: linear-gradient(135deg, #a8edea, #fed6e3); color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; margin: 0 auto 4px;">按</div>
                            <div class="grid-title">上门按摩</div>
                        </div>
                        <div class="grid-item">
                            <div class="grid-icon" style="background: linear-gradient(135deg, #d299c2, #fef9d7); color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; margin: 0 auto 4px;">家</div>
                            <div class="grid-title">家政服务</div>
                        </div>
                        <div class="grid-item">
                            <div class="grid-icon" style="background: linear-gradient(135deg, #89f7fe, #66a6ff); color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; margin: 0 auto 4px;">美</div>
                            <div class="grid-title">美容美发</div>
                        </div>
                        <div class="grid-item">
                            <div class="grid-icon" style="background: linear-gradient(135deg, #ccc, #999); color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; margin: 0 auto 4px;">+</div>
                            <div class="grid-title">更多</div>
                        </div>
                    </div>
                    
                    <div style="background: white; border-radius: 12px; padding: 16px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                        <h3 style="margin-bottom: 12px; font-size: 14px;">热门服务</h3>
                        <div style="display: flex; gap: 8px; overflow-x: auto;">
                            <div style="min-width: 80px; text-align: center;">
                                <div style="width: 60px; height: 60px; background: #667eea; border-radius: 50%; margin: 0 auto 4px; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">瑜</div>
                                <div style="font-size: 10px;">瑜伽私教</div>
                            </div>
                            <div style="min-width: 80px; text-align: center;">
                                <div style="width: 60px; height: 60px; background: #ff6b6b; border-radius: 50%; margin: 0 auto 4px; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">健</div>
                                <div style="font-size: 10px;">健身陪练</div>
                            </div>
                            <div style="min-width: 80px; text-align: center;">
                                <div style="width: 60px; height: 60px; background: #4ecdc4; border-radius: 50%; margin: 0 auto 4px; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">游</div>
                                <div style="font-size: 10px;">游戏陪玩</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="page-title">首页</div>
        </div>

        <!-- 我的页面 -->
        <div class="phone-frame" style="left: 850px; top: 50px;" onclick="navigateToPage('settings')">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content profile-page" style="padding: 0;">
                    <!-- 顶部用户信息区 -->
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px 16px 30px; color: white;">
                        <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 20px;">
                            <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 24px; font-weight: 600;">张</div>
                            <div style="flex: 1;">
                                <div style="font-size: 18px; font-weight: 600; margin-bottom: 4px;">张小明</div>
                                <div style="font-size: 13px; opacity: 0.9; margin-bottom: 8px;">ID: 138****8888</div>
                                <div style="display: flex; gap: 8px;">
                                    <span style="background: rgba(76, 175, 80, 0.8); padding: 2px 8px; border-radius: 10px; font-size: 11px;">已认证</span>
                                    <span style="background: rgba(255, 152, 0, 0.8); padding: 2px 8px; border-radius: 10px; font-size: 11px;">服务提供者</span>
                                </div>
                            </div>
                            <div style="text-align: right;">
                                <div style="font-size: 12px; opacity: 0.8;">信用分</div>
                                <div style="font-size: 20px; font-weight: 700;">98</div>
                            </div>
                        </div>

                        <!-- 收益数据 -->
                        <div style="display: flex; justify-content: space-between; margin-top: 16px;">
                            <div style="text-align: left;">
                                <div style="font-size: 24px; font-weight: 700; margin-bottom: 2px;">¥2,580</div>
                                <div style="font-size: 12px; opacity: 0.7;">本月收益</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 16px; font-weight: 600; margin-bottom: 2px;">¥15,680</div>
                                <div style="font-size: 12px; opacity: 0.7;">总收益</div>
                            </div>
                            <div style="text-align: right;">
                                <div style="font-size: 16px; font-weight: 600; margin-bottom: 2px;">¥320</div>
                                <div style="font-size: 12px; opacity: 0.7;">待结算</div>
                            </div>
                        </div>
                    </div>



                    <!-- 功能菜单 -->
                    <div style="background: white; margin-top: 8px;">
                        <div style="padding: 0 16px;">
                            <div style="display: flex; align-items: center; padding: 12px 0; border-bottom: 1px solid #f0f0f0;">
                                <div style="width: 36px; height: 36px; background: #ff6b6b; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; margin-right: 12px; font-size: 14px;">订</div>
                                <div style="flex: 1;">
                                    <div style="font-size: 14px; color: #333; margin-bottom: 1px;">我的订单</div>
                                    <div style="font-size: 11px; color: #999;">待确认2个 · 进行中1个 · 待评价1个</div>
                                </div>
                                <div style="color: #ccc; font-size: 16px;">></div>
                            </div>
                            <div style="display: flex; align-items: center; padding: 12px 0; border-bottom: 1px solid #f0f0f0;">
                                <div style="width: 36px; height: 36px; background: #667eea; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; margin-right: 12px; font-size: 14px;">服</div>
                                <div style="flex: 1;">
                                    <div style="font-size: 14px; color: #333; margin-bottom: 1px;">服务管理</div>
                                    <div style="font-size: 11px; color: #999;">管理我发布的服务</div>
                                </div>
                                <div style="color: #ccc; font-size: 16px;">></div>
                            </div>
                            <div style="display: flex; align-items: center; padding: 12px 0; border-bottom: 1px solid #f0f0f0;">
                                <div style="width: 36px; height: 36px; background: #4caf50; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; margin-right: 12px; font-size: 14px;">需</div>
                                <div style="flex: 1;">
                                    <div style="font-size: 14px; color: #333; margin-bottom: 1px;">我的需求</div>
                                    <div style="font-size: 11px; color: #999;">查看我发布的需求</div>
                                </div>
                                <div style="position: relative;">
                                    <div style="position: absolute; top: -6px; right: 16px; background: #ff4757; color: white; border-radius: 8px; padding: 1px 4px; font-size: 8px;">2</div>
                                    <div style="color: #ccc; font-size: 16px;">></div>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; padding: 12px 0;">
                                <div style="width: 36px; height: 36px; background: #ff9800; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; margin-right: 12px; font-size: 14px;">¥</div>
                                <div style="flex: 1;">
                                    <div style="font-size: 14px; color: #333; margin-bottom: 1px;">我的钱包</div>
                                    <div style="font-size: 11px; color: #999;">余额、收益、提现</div>
                                </div>
                                <div style="color: #ccc; font-size: 16px;">></div>
                            </div>
                        </div>
                    </div>

                    <!-- 其他功能 -->
                    <div style="background: white; margin-top: 8px;">
                        <div style="padding: 0 16px;">
                            <div style="display: flex; align-items: center; padding: 12px 0; border-bottom: 1px solid #f0f0f0;">
                                <div style="width: 36px; height: 36px; background: #9c27b0; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; margin-right: 12px; font-size: 14px;">认</div>
                                <div style="flex: 1;">
                                    <div style="font-size: 14px; color: #333;">认证中心</div>
                                </div>
                                <div style="color: #ccc; font-size: 16px;">></div>
                            </div>
                            <div style="display: flex; align-items: center; padding: 12px 0; border-bottom: 1px solid #f0f0f0;">
                                <div style="width: 36px; height: 36px; background: #607d8b; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; margin-right: 12px; font-size: 14px;">设</div>
                                <div style="flex: 1;">
                                    <div style="font-size: 14px; color: #333;">设置</div>
                                </div>
                                <div style="color: #ccc; font-size: 16px;">></div>
                            </div>
                            <div style="display: flex; align-items: center; padding: 12px 0;">
                                <div style="width: 36px; height: 36px; background: #795548; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; margin-right: 12px; font-size: 14px;">?</div>
                                <div style="flex: 1;">
                                    <div style="font-size: 14px; color: #333;">帮助与反馈</div>
                                </div>
                                <div style="color: #ccc; font-size: 16px;">></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="page-title">我的</div>
        </div>

        <!-- 瑜伽服务分类页面 -->
        <div class="phone-frame" style="left: 1250px; top: 50px;" onclick="navigateToPage('service-detail')">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content service-page">
                    <div class="category-header">
                        <h2 style="margin-bottom: 8px;">瑜伽服务</h2>
                        <p style="font-size: 12px; opacity: 0.8;">专业瑜伽教练，个性化指导</p>
                    </div>

                    <div style="display: flex; gap: 8px; margin-bottom: 16px; overflow-x: auto;">
                        <div style="background: #667eea; color: white; padding: 6px 12px; border-radius: 16px; font-size: 12px; white-space: nowrap;">全部</div>
                        <div style="background: white; color: #333; padding: 6px 12px; border-radius: 16px; font-size: 12px; white-space: nowrap;">私教课程</div>
                        <div style="background: white; color: #333; padding: 6px 12px; border-radius: 16px; font-size: 12px; white-space: nowrap;">团体课</div>
                        <div style="background: white; color: #333; padding: 6px 12px; border-radius: 16px; font-size: 12px; white-space: nowrap;">上门服务</div>
                    </div>

                    <div class="service-list">
                        <div class="service-card">
                            <div class="service-header">
                                <div class="service-avatar">李</div>
                                <div class="service-info">
                                    <div class="service-name">李老师 - 专业瑜伽私教</div>
                                    <div class="service-rating">⭐ 4.9 (128条评价) | 3年经验</div>
                                </div>
                                <div class="service-price">¥120/小时</div>
                            </div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 8px;">
                                哈他瑜伽 · 阴瑜伽 · 流瑜伽
                            </div>
                            <div style="font-size: 11px; color: #999;">
                                朝阳区 · 可上门 · 认证教练
                            </div>
                        </div>

                        <div class="service-card">
                            <div class="service-header">
                                <div class="service-avatar">王</div>
                                <div class="service-info">
                                    <div class="service-name">王教练 - 瑜伽工作室</div>
                                    <div class="service-rating">⭐ 4.8 (89条评价) | 5年经验</div>
                                </div>
                                <div class="service-price">¥80/小时</div>
                            </div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 8px;">
                                热瑜伽 · 空中瑜伽 · 孕妇瑜伽
                            </div>
                            <div style="font-size: 11px; color: #999;">
                                海淀区 · 工作室 · 国际认证
                            </div>
                        </div>

                        <div class="service-card">
                            <div class="service-header">
                                <div class="service-avatar">张</div>
                                <div class="service-info">
                                    <div class="service-name">张小美 - 瑜伽生活馆</div>
                                    <div class="service-rating">⭐ 4.7 (156条评价) | 4年经验</div>
                                </div>
                                <div class="service-price">¥100/小时</div>
                            </div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 8px;">
                                普拉提 · 瑜伽理疗 · 冥想课程
                            </div>
                            <div style="font-size: 11px; color: #999;">
                                西城区 · 可上门 · 理疗师认证
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="page-title">瑜伽服务分类</div>
        </div>

        <!-- 服务详情页面 -->
        <div class="phone-frame" style="left: 1650px; top: 50px;" onclick="navigateToPage('booking')">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content" style="padding: 0;">
                    <div style="height: 200px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); position: relative; display: flex; align-items: center; justify-content: center; color: white;">
                        <div style="text-align: center;">
                            <div style="width: 80px; height: 80px; background: rgba(255,255,255,0.2); border-radius: 50%; margin: 0 auto 12px; display: flex; align-items: center; justify-content: center; font-size: 32px;">李</div>
                            <h2>李老师 - 专业瑜伽私教</h2>
                            <p style="font-size: 12px; opacity: 0.8; margin-top: 4px;">⭐ 4.9 (128条评价) | 3年经验</p>
                        </div>
                        <div style="position: absolute; top: 16px; left: 16px; background: rgba(255,255,255,0.2); border-radius: 20px; padding: 4px 8px; font-size: 12px;">← 返回</div>
                    </div>

                    <div style="padding: 16px;">
                        <div style="background: white; border-radius: 12px; padding: 16px; margin-bottom: 16px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            <h3 style="margin-bottom: 12px; font-size: 14px;">服务价格</h3>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <span>私教课程 (1小时)</span>
                                <span style="color: #ff6b6b; font-weight: 600;">¥120</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <span>团体课程 (1小时)</span>
                                <span style="color: #ff6b6b; font-weight: 600;">¥60</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span>上门服务 (1小时)</span>
                                <span style="color: #ff6b6b; font-weight: 600;">¥150</span>
                            </div>
                        </div>

                        <div style="background: white; border-radius: 12px; padding: 16px; margin-bottom: 16px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            <h3 style="margin-bottom: 12px; font-size: 14px;">服务介绍</h3>
                            <p style="font-size: 12px; color: #666; line-height: 1.5;">
                                专业瑜伽教练，拥有3年丰富教学经验。擅长哈他瑜伽、阴瑜伽、流瑜伽等多种瑜伽形式。
                                提供个性化教学方案，帮助学员改善身体柔韧性、增强核心力量、缓解压力。
                            </p>
                        </div>

                        <div style="background: white; border-radius: 12px; padding: 16px; margin-bottom: 16px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            <h3 style="margin-bottom: 12px; font-size: 14px;">服务类型</h3>
                            <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                                <span style="background: #e3f2fd; color: #1976d2; padding: 4px 8px; border-radius: 12px; font-size: 10px;">哈他瑜伽</span>
                                <span style="background: #e8f5e8; color: #4caf50; padding: 4px 8px; border-radius: 12px; font-size: 10px;">阴瑜伽</span>
                                <span style="background: #fff3e0; color: #ff9800; padding: 4px 8px; border-radius: 12px; font-size: 10px;">流瑜伽</span>
                                <span style="background: #fce4ec; color: #e91e63; padding: 4px 8px; border-radius: 12px; font-size: 10px;">可上门</span>
                                <span style="background: #f3e5f5; color: #9c27b0; padding: 4px 8px; border-radius: 12px; font-size: 10px;">认证教练</span>
                            </div>
                        </div>

                        <button style="width: 100%; background: #667eea; color: white; border: none; padding: 16px; border-radius: 12px; font-size: 16px; font-weight: 600; cursor: pointer;">
                            立即预约
                        </button>
                    </div>
                </div>
            </div>
            <div class="page-title">服务详情</div>
        </div>

        <!-- 连接线 -->
        <div class="connection-line" style="left: 370px; top: 200px; width: 80px; height: 2px;"></div>
        <div class="connection-arrow" style="left: 442px; top: 194px;"></div>

        <div class="connection-line" style="left: 770px; top: 200px; width: 80px; height: 2px;"></div>
        <div class="connection-arrow" style="left: 842px; top: 194px;"></div>

        <div class="connection-line" style="left: 1170px; top: 200px; width: 80px; height: 2px;"></div>
        <div class="connection-arrow" style="left: 1242px; top: 194px;"></div>

        <div class="connection-line" style="left: 1570px; top: 200px; width: 80px; height: 2px;"></div>
        <div class="connection-arrow" style="left: 1642px; top: 194px;"></div>
    </div>

    <script>
        function navigateToPage(page) {
            const pageNames = {
                'home': '首页',
                'profile': '我的页面',
                'yoga': '瑜伽服务分类',
                'fitness': '健身服务分类',
                'service-detail': '服务详情',
                'booking': '预约页面',
                'settings': '设置页面'
            };

            alert(`导航到: ${pageNames[page] || page}\n\n这是原型图演示\n实际应用中会进行页面跳转\n点击其他页面继续体验`);
        }

        // 添加页面滚动提示
        window.addEventListener('load', function() {
            setTimeout(() => {
                alert('乐享生活原型图\n\n5个核心页面展示\n点击任意手机页面体验交互\n可以左右滚动查看更多页面\n\n页面包括：\n• 登录页面\n• 首页 (金刚位)\n• 我的页面\n• 服务分类页面\n• 服务详情页面');
            }, 1000);
        });
    </script>
</body>
</html>
