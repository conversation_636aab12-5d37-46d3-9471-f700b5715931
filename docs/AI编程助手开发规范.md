# 🤖 AI编程助手开发规范

> **目标**: 确保AI编程助手遵循企业级开发标准，优先使用系统已有组件，避免重复造轮子  
> **适用范围**: 所有AI编程助手参与的代码开发工作  
> **版本**: v1.0  
> **更新时间**: 2025-08-31

## 📋 核心原则

### 🎯 首要原则
1. **优先使用现有组件** - 必须先检查项目中是否已有相关功能
2. **遵循项目架构** - 严格按照项目现有架构和模式开发
3. **保持代码一致性** - 与项目现有代码风格保持一致
4. **避免重复造轮子** - 禁止重复实现已有功能
5. **企业级质量标准** - 所有代码必须符合企业级开发规范

### ⚠️ 禁止行为
- ❌ 未经检查就创建新的工具类或组件
- ❌ 重复实现已有的功能模块
- ❌ 违反项目现有的架构设计
- ❌ 使用过时或不推荐的API
- ❌ 忽略项目现有的类型定义

## 🔍 开发前必检清单

### 1. 组件和工具类检查
在编写任何新功能前，必须检查以下目录：

```bash
# 必检目录列表
src/hooks/              # Composable函数
src/utils/              # 工具类
src/components/         # 组件库
src/api/                # API接口
src/store/              # 状态管理
src/types/              # 类型定义
src/assets/             # 静态资源 ✨
├── images/             # 图片资源
├── icons/              # 图标资源
└── fonts/              # 字体资源
public/                 # 纯静态文件 ✨
└── data/               # 静态数据文件
```

### 2. 功能重复性检查
```typescript
// 检查清单示例
✅ Loading管理 -> 使用 useLoading
✅ 错误处理 -> 使用 useErrorHandler  
✅ 设备信息 -> 使用 deviceInfo
✅ 存储操作 -> 使用 StoreUtil
✅ HTTP请求 -> 使用 http 实例
✅ 表单验证 -> 使用 useFormValidation
✅ Token管理 -> 使用 tokenUtil
```

### 3. 架构合规性检查
- [ ] 是否符合项目目录结构规范
- [ ] 是否使用了正确的导入路径
- [ ] 是否遵循了命名约定
- [ ] 是否添加了必要的类型定义
- [ ] 是否包含了适当的错误处理
- [ ] 是否正确使用了 assets/ 和 public/ 目录 ✨
- [ ] 是否避免了已废弃的 /static/ 路径引用 ✨

## 📚 必须使用的系统组件

### 🔧 核心工具类

#### 1. 加载状态管理
```typescript
// ✅ 正确使用
import { useLoading, useGlobalLoading } from '@/hooks/useLoading'

const loading = useLoading()
await loading.withLoading(async () => {
  // 异步操作
}, '加载中...')

// ❌ 禁止直接使用
uni.showLoading({ title: '加载中...' })
uni.hideLoading()
```

#### 2. 错误处理
```typescript
// ✅ 正确使用
import { useErrorHandler, useApiErrorHandler } from '@/hooks/useErrorHandler'

const errorHandler = useErrorHandler({ context: 'ComponentName' })
const result = await errorHandler.withErrorHandling(async () => {
  return await api.call()
}, '操作失败')

// ❌ 禁止直接使用
try {
  // 操作
} catch (error) {
  uni.showToast({ title: '错误', icon: 'none' })
}
```

#### 3. 设备信息获取
```typescript
// ✅ 正确使用
import { getPlatform, getDeviceType, isMobile } from '@/utils/common/deviceInfo'

const platform = getPlatform()
const deviceType = getDeviceType()

// ❌ 禁止直接使用
const systemInfo = uni.getSystemInfoSync()
```

#### 4. 存储操作
```typescript
// ✅ 正确使用
import { StoreUtil, StorageModule } from '@/utils/storage/StoreUtil'

await StoreUtil.set('key', data, StorageModule.USER)
const data = await StoreUtil.get('key', StorageModule.USER)

// ❌ 禁止直接使用
uni.setStorageSync('key', data)
uni.getStorageSync('key')
```

#### 5. HTTP请求
```typescript
// ✅ 正确使用
import { http } from '@/http/http'

const result = await http.get('/api/data')
const result = await http.post('/api/data', params)

// ❌ 禁止创建新的axios实例
import axios from 'axios'
const customHttp = axios.create()
```

### 🎨 UI组件规范

#### 1. 基础组件优先
```vue
<!-- ✅ 优先使用项目组件 -->
<template>
  <base-button @click="handleClick">提交</base-button>
  <base-input v-model="value" placeholder="请输入" />
</template>

<!-- ❌ 避免重复造轮子 -->
<template>
  <button class="custom-btn" @click="handleClick">提交</button>
</template>
```

#### 2. 业务组件复用
```vue
<!-- ✅ 使用现有业务组件 -->
<template>
  <captcha-modal v-model:visible="showCaptcha" />
  <address-picker v-model="address" />
</template>

<!-- ❌ 重复实现相同功能 -->
<template>
  <view class="my-captcha">
    <!-- 重复实现验证码功能 -->
  </view>
</template>
```

#### 3. 静态资源使用规范 ✨
```vue
<!-- ✅ 正确的静态资源引用 -->
<template>
  <!-- 需要处理的图片资源 -->
  <image src="@/assets/images/logo.png" />

  <!-- 图标资源 -->
  <image src="@/assets/icons/home.svg" />

  <!-- 纯静态数据文件 -->
  <script>
  import staticData from '/data/config.json'
  </script>
</template>

<!-- ❌ 错误的资源引用 -->
<template>
  <!-- 旧的路径，已不存在 -->
  <image src="/static/logo.png" />
  <image src="/static/tabbar/home.svg" />
</template>
```

#### 4. 资源导入最佳实践
```typescript
// ✅ 正确的资源导入方式
import logoImg from '@/assets/images/logo.png'
import homeIcon from '@/assets/icons/home.svg'

// ✅ 动态资源导入
const getAssetUrl = (name: string) => {
  return new URL(`../assets/images/${name}`, import.meta.url).href
}

// ❌ 避免硬编码路径
const logoUrl = '/static/logo.png'  // 旧路径，已不存在
```

## 🏗️ 代码结构规范

### 1. 文件组织规范
```typescript
// ✅ 标准的Vue组件结构
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 1. 导入系统组件（优先）
import { useLoading, useErrorHandler } from '@/hooks'
import { StoreUtil } from '@/utils/storage/StoreUtil'

// 2. 导入第三方库
import { ref, computed, onMounted } from 'vue'

// 3. 导入业务模块
import { getUserInfo } from '@/api/modules/user'

// 4. 类型定义
interface FormData {
  // 类型定义
}

// 5. 响应式数据
const loading = useLoading()
const errorHandler = useErrorHandler({ context: 'ComponentName' })

// 6. 计算属性
const computedValue = computed(() => {
  // 计算逻辑
})

// 7. 方法定义
const handleSubmit = async () => {
  // 方法实现
}

// 8. 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style scoped>
/* 样式定义 */
</style>
```

### 2. 命名规范
```typescript
// ✅ 正确命名
const userInfo = ref<UserInfo>()
const isLoading = ref(false)
const handleUserLogin = async () => {}

// ❌ 错误命名
const data = ref()
const flag = ref(false)
const doSomething = async () => {}
```

### 3. 类型定义规范
```typescript
// ✅ 使用现有类型
import type { UserInfo, ApiResponse } from '@/types'

// ✅ 扩展现有类型
interface ExtendedUserInfo extends UserInfo {
  additionalField: string
}

// ❌ 重复定义已有类型
interface User {
  id: string
  name: string
  // 重复定义
}
```

## 🔒 安全和性能规范

### 1. 安全规范
```typescript
// ✅ 使用统一的Token管理
import { tokenUtil } from '@/utils/token/TokenUtil'

const token = tokenUtil.getAccessToken()

// ✅ 使用统一的加密工具
import { aesEncrypt, aesDecrypt } from '@/utils/crypto'

// ❌ 禁止硬编码敏感信息
const apiKey = 'sk-1234567890' // 禁止
```

### 2. 性能规范
```typescript
// ✅ 使用缓存的设备信息
import { getSystemInfo } from '@/utils/common/deviceInfo'

// ✅ 使用防抖和节流
import { debounce, throttle } from '@/utils/common'

// ❌ 避免重复计算
const systemInfo = uni.getSystemInfoSync() // 每次都调用
```

## 📝 代码质量要求

### 1. 必须包含的内容
- [ ] 完整的TypeScript类型定义
- [ ] 适当的错误处理
- [ ] 必要的注释和文档
- [ ] 统一的代码格式化
- [ ] 合理的变量命名

### 2. 代码审查检查点
- [ ] 是否使用了现有组件和工具
- [ ] 是否遵循了项目架构
- [ ] 是否包含了适当的错误处理
- [ ] 是否添加了必要的类型定义
- [ ] 是否符合代码规范

### 3. 性能考虑
- [ ] 避免不必要的重复计算
- [ ] 使用适当的缓存策略
- [ ] 合理的组件拆分
- [ ] 避免内存泄漏

## 🚨 违规处理

### 警告级别
- ⚠️ **轻微违规**: 未使用推荐的工具类
- 🔶 **中等违规**: 重复实现已有功能
- 🔴 **严重违规**: 违反架构设计原则

### 处理流程
1. **发现违规** -> 立即停止开发
2. **分析原因** -> 检查是否了解现有组件
3. **修正代码** -> 使用正确的组件和方法
4. **重新审查** -> 确保符合规范

## 📚 学习资源

### 必读文档
- [项目架构设计文档](./技术架构设计文档.md)
- [Hooks使用规范指南](./Hooks使用规范指南.md)
- [代码优化迁移指南](./代码优化迁移指南.md)
- [企业级开发规范](./企业级开发规范.md)

### 示例代码
- [优化后的页面示例](../src/examples/OptimizedPageExample.vue)
- [存储服务使用示例](../src/examples/StorageUsageExample.vue)
- [智能Loading示例](../src/examples/SmartLoadingExample.vue)

## 🎯 AI助手特殊约束

### 1. 代码生成前置检查
AI助手在生成任何代码前，必须执行以下检查流程：

```typescript
// AI助手内部检查流程
1. 分析需求 -> 确定要实现的功能
2. 搜索现有组件 -> 检查是否已有相关实现
3. 评估复用性 -> 判断是否可以直接使用或扩展
4. 确定实现方案 -> 选择最佳的实现路径
5. 生成代码 -> 严格按照规范生成代码
```

### 2. 强制使用现有组件清单

#### 必须使用的Hooks
```typescript
// 加载状态管理
import { useLoading, useGlobalLoading, useLocalLoading } from '@/hooks/useLoading'

// 错误处理
import { useErrorHandler, useApiErrorHandler, useGlobalErrorHandler } from '@/hooks/useErrorHandler'

// 表单验证
import { useFormValidation } from '@/hooks/useFormValidation'

// 认证相关
import { useAuth } from '@/hooks/useAuth'

// 验证码
import { useCaptcha } from '@/hooks/useCaptcha'

// 页面相关
import { usePage, useEnhancedFormPage } from '@/hooks/usePage'
```

#### 必须使用的工具类
```typescript
// 存储服务
import { StoreUtil, StorageModule, StorageType } from '@/utils/storage/StoreUtil'

// Token管理
import { tokenUtil } from '@/utils/token/TokenUtil'

// 设备信息
import { deviceInfo, getPlatform, getDeviceType } from '@/utils/common/deviceInfo'

// 错误处理
import { ErrorHandler } from '@/utils/errorHandler'

// 加载管理
import { LoadingManager, showLoading, hideLoading } from '@/utils/common/loadingManager'

// 日志服务
import { createContextLogger } from '@/utils/logger'

// 加密工具
import { aesEncrypt, aesDecrypt } from '@/utils/crypto'

// 格式化工具
import { formatDate, formatCurrency } from '@/utils/format'

// 验证工具
import { validatePhone, validateEmail } from '@/utils/validate'
```

#### 必须使用的API模块
```typescript
// HTTP客户端
import { http } from '@/http/http'

// API管理器
import { apiManager, apiErrorHandler, apiCacheManager } from '@/api'

// 业务API
import { passwordLogin, smsLogin, register } from '@/api/auth/auth'
import { getUserInfo, updateUserInfo } from '@/api/modules/member'
import { getCaptcha, verifyCaptcha } from '@/api/modules/captcha'
```

### 3. 禁止行为详细清单

#### 🚫 绝对禁止
```typescript
// ❌ 禁止重复实现已有功能
const showLoading = () => {
  uni.showLoading({ title: '加载中...' })
}

// ❌ 禁止直接使用uni API进行存储
uni.setStorageSync('key', value)

// ❌ 禁止创建新的HTTP实例
const customAxios = axios.create()

// ❌ 禁止重复定义已有类型
interface User {
  id: string
  name: string
}

// ❌ 禁止硬编码配置信息
const API_BASE_URL = 'https://api.example.com'

// ❌ 禁止使用已废弃的静态资源路径 ✨
const logoUrl = '/static/logo.png'        // 已废弃
const iconUrl = '/static/tabbar/home.svg' // 已废弃
const dataUrl = '/static/data/config.json' // 已废弃
```

#### ⚠️ 需要特殊审批
```typescript
// 需要说明理由的情况
1. 创建新的工具类或组件
2. 修改现有的核心组件
3. 添加新的第三方依赖
4. 修改项目配置文件
5. 创建新的API接口
```

### 4. 代码生成模板

#### Vue组件模板
```vue
<template>
  <view class="component-name">
    <!-- 组件内容 -->
  </view>
</template>

<script setup lang="ts">
// 1. 导入系统组件（必须优先检查是否已有）
import { useLoading, useErrorHandler } from '@/hooks'
import { StoreUtil, StorageModule } from '@/utils/storage/StoreUtil'

// 2. 导入Vue相关
import { ref, computed, onMounted } from 'vue'

// 3. 导入业务模块
import { apiCall } from '@/api/modules/xxx'

// 4. 类型定义（优先使用现有类型）
import type { ExistingType } from '@/types'

// 5. 组件逻辑
const loading = useLoading()
const errorHandler = useErrorHandler({ context: 'ComponentName' })

// 6. 响应式数据
const data = ref<ExistingType>()

// 7. 计算属性
const computedValue = computed(() => {
  // 计算逻辑
})

// 8. 方法定义
const handleAction = async () => {
  const result = await loading.withLoading(async () => {
    return await errorHandler.withErrorHandling(async () => {
      return await apiCall()
    }, '操作失败')
  }, '处理中...')

  if (result) {
    // 处理成功结果
  }
}

// 9. 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style scoped>
.component-name {
  /* 样式定义 */
}
</style>
```

#### 工具函数模板
```typescript
/**
 * 工具函数描述
 * @param param1 参数1描述
 * @param param2 参数2描述
 * @returns 返回值描述
 */
export function utilityFunction(param1: string, param2: number): ReturnType {
  // 1. 参数验证
  if (!param1 || param2 < 0) {
    throw new Error('参数无效')
  }

  // 2. 使用现有工具类
  const logger = createContextLogger('UtilityFunction')

  try {
    // 3. 核心逻辑
    const result = processLogic(param1, param2)

    // 4. 日志记录
    logger.info('操作成功', { param1, param2, result })

    return result
  } catch (error) {
    // 5. 错误处理
    logger.error('操作失败', { param1, param2, error })
    throw error
  }
}
```

## 🔍 质量检查清单

### AI助手自检清单
在提交代码前，AI助手必须确认以下所有项目：

#### 功能检查
- [ ] 是否检查了现有组件和工具类
- [ ] 是否使用了推荐的系统组件
- [ ] 是否避免了重复实现
- [ ] 是否遵循了项目架构

#### 代码质量检查
- [ ] 是否包含完整的TypeScript类型
- [ ] 是否添加了适当的错误处理
- [ ] 是否使用了统一的命名规范
- [ ] 是否添加了必要的注释

#### 性能检查
- [ ] 是否避免了不必要的重复计算
- [ ] 是否使用了适当的缓存策略
- [ ] 是否考虑了内存使用优化
- [ ] 是否遵循了最佳实践

#### 安全检查
- [ ] 是否使用了统一的认证机制
- [ ] 是否避免了硬编码敏感信息
- [ ] 是否使用了推荐的加密方法
- [ ] 是否遵循了安全最佳实践

## 📊 违规监控和改进

### 违规统计
项目将定期统计AI助手的违规情况：

```typescript
// 违规类型统计
interface ViolationStats {
  duplicateImplementation: number    // 重复实现
  missingTypeDefinition: number     // 缺少类型定义
  improperErrorHandling: number     // 错误处理不当
  architectureViolation: number     // 架构违规
  performanceIssue: number          // 性能问题
}
```

### 改进措施
1. **定期培训** - 更新AI助手对项目组件的认知
2. **规范更新** - 根据项目发展更新开发规范
3. **工具改进** - 开发自动检查工具
4. **反馈机制** - 建立违规反馈和改进流程

---

**📞 技术支持**
如有疑问，请参考示例代码或联系开发团队进行技术咨询。

**🔄 文档更新**
本规范将根据项目发展持续更新，请关注最新版本。

## 🛠️ AI助手开发工作流

### 标准开发流程
```mermaid
graph TD
    A[接收开发需求] --> B[分析功能需求]
    B --> C[检查现有组件]
    C --> D{是否存在相关组件?}
    D -->|是| E[评估复用可能性]
    D -->|否| F[确认是否需要新建]
    E --> G{可以直接使用?}
    G -->|是| H[使用现有组件]
    G -->|否| I[扩展现有组件]
    F --> J[设计新组件方案]
    H --> K[编写业务代码]
    I --> K
    J --> L[获得架构审批]
    L --> K
    K --> M[代码质量检查]
    M --> N[提交代码]
```

### 每个步骤的详细要求

#### 1. 需求分析阶段
```typescript
// AI助手必须明确以下问题
interface RequirementAnalysis {
  functionality: string[]        // 需要实现的功能列表
  userInterface: string[]        // UI交互需求
  dataFlow: string[]            // 数据流转需求
  performance: string[]         // 性能要求
  compatibility: string[]       // 兼容性要求
}
```

#### 2. 组件检查阶段
```bash
# 必须检查的目录和文件
src/hooks/                     # 所有Composable函数
src/utils/                     # 所有工具类
src/components/base/           # 基础组件
src/components/business/       # 业务组件
src/api/                       # API接口
src/store/                     # 状态管理
src/types/                     # 类型定义
docs/                          # 相关文档

# 检查方法
1. 搜索相关关键词
2. 查看组件导出列表
3. 阅读组件文档和示例
4. 确认功能匹配度
```

#### 3. 复用评估标准
```typescript
// 复用评估矩阵
interface ReuseEvaluation {
  functionalMatch: number       // 功能匹配度 (0-100)
  interfaceCompatibility: number // 接口兼容性 (0-100)
  performanceImpact: number     // 性能影响 (-100 to 100)
  maintenanceCost: number       // 维护成本 (0-100)
  extensibility: number         // 可扩展性 (0-100)
}

// 决策规则
if (functionalMatch >= 80 && interfaceCompatibility >= 70) {
  return '直接使用现有组件'
} else if (functionalMatch >= 60 && extensibility >= 70) {
  return '扩展现有组件'
} else {
  return '需要创建新组件（需要审批）'
}
```

## 🎨 UI/UX开发约束

### 1. 设计系统遵循
```scss
// ✅ 使用项目设计系统
@import '@/styles/variables.scss';
@import '@/styles/mixins.scss';

.component {
  color: $primary-color;
  font-size: $font-size-base;
  @include border-radius($border-radius-base);
}

// ❌ 禁止硬编码样式值
.component {
  color: #007aff;           // 禁止
  font-size: 16px;          // 禁止
  border-radius: 8px;       // 禁止
}
```

### 2. 响应式设计要求
```typescript
// ✅ 使用统一的响应式工具
import { getResponsiveSize, isMobile, isTablet } from '@/utils/common/deviceInfo'

const responsiveConfig = computed(() => {
  if (isMobile()) {
    return { columns: 1, spacing: 16 }
  } else if (isTablet()) {
    return { columns: 2, spacing: 20 }
  } else {
    return { columns: 3, spacing: 24 }
  }
})

// ❌ 禁止硬编码设备判断
const isMobileDevice = window.innerWidth < 768  // 禁止
```

### 3. 无障碍访问要求
```vue
<!-- ✅ 正确的无障碍实现 -->
<template>
  <button
    :aria-label="buttonLabel"
    :aria-disabled="isDisabled"
    role="button"
    @click="handleClick"
  >
    {{ buttonText }}
  </button>
</template>

<!-- ❌ 缺少无障碍属性 -->
<template>
  <view @click="handleClick">
    {{ buttonText }}
  </view>
</template>
```

## 🔐 安全开发约束

### 1. 数据安全
```typescript
// ✅ 正确的敏感数据处理
import { aesEncrypt, aesDecrypt } from '@/utils/crypto'
import { tokenUtil } from '@/utils/token/TokenUtil'

// 加密存储敏感数据
const encryptedData = aesEncrypt(sensitiveData)
await StoreUtil.set('sensitive_key', encryptedData, StorageModule.AUTH)

// 使用统一的Token管理
const token = tokenUtil.getAccessToken()

// ❌ 禁止的不安全做法
localStorage.setItem('password', password)        // 禁止明文存储
const token = 'hardcoded-token'                   // 禁止硬编码
```

### 2. 输入验证
```typescript
// ✅ 使用统一的验证工具
import { validatePhone, validateEmail, validateRequired } from '@/utils/validate'

const validateForm = (data: FormData) => {
  const errors: string[] = []

  if (!validateRequired(data.phone)) {
    errors.push('手机号不能为空')
  } else if (!validatePhone(data.phone)) {
    errors.push('手机号格式不正确')
  }

  return errors
}

// ❌ 禁止不完整的验证
const isValidPhone = phone.length === 11  // 过于简单
```

### 3. API安全
```typescript
// ✅ 正确的API调用
import { http } from '@/http/http'

// 自动添加认证头和安全配置
const result = await http.post('/api/sensitive-operation', {
  data: encryptedData,
  timestamp: Date.now()
})

// ❌ 禁止绕过安全机制
const directRequest = axios.post('https://api.example.com/data', {
  // 直接调用，绕过安全检查
})
```

## 📱 平台兼容性约束

### 1. 跨平台API使用
```typescript
// ✅ 使用条件编译
// #ifdef H5
import { h5SpecificFunction } from '@/utils/h5'
// #endif

// #ifdef MP-WEIXIN
import { weixinSpecificFunction } from '@/utils/weixin'
// #endif

// #ifdef APP-PLUS
import { appSpecificFunction } from '@/utils/app'
// #endif

// ❌ 禁止直接使用平台特定API
if (window.wx) {  // 不推荐的平台检测
  window.wx.someAPI()
}
```

### 2. 样式兼容性
```scss
// ✅ 使用兼容的样式写法
.component {
  /* #ifdef H5 */
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  /* #endif */

  /* #ifdef MP */
  border: 1px solid #eee;
  /* #endif */
}

// ❌ 禁止使用不兼容的样式
.component {
  position: fixed;        // 小程序兼容性问题
  backdrop-filter: blur(10px);  // 兼容性问题
}
```

## 🧪 测试要求

### 1. 单元测试
```typescript
// ✅ 为新组件编写测试
import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import MyComponent from '@/components/MyComponent.vue'

describe('MyComponent', () => {
  it('should render correctly', () => {
    const wrapper = mount(MyComponent, {
      props: { title: 'Test Title' }
    })

    expect(wrapper.text()).toContain('Test Title')
  })

  it('should handle click events', async () => {
    const wrapper = mount(MyComponent)
    await wrapper.find('button').trigger('click')

    expect(wrapper.emitted('click')).toBeTruthy()
  })
})
```

### 2. 集成测试
```typescript
// ✅ 测试组件集成
import { describe, it, expect } from 'vitest'
import { useLoading, useErrorHandler } from '@/hooks'

describe('Component Integration', () => {
  it('should integrate with loading system', async () => {
    const loading = useLoading()
    const errorHandler = useErrorHandler()

    // 测试集成逻辑
    expect(loading).toBeDefined()
    expect(errorHandler).toBeDefined()
  })
})
```

**⚡ 快速参考卡**
```bash
# 开发前检查
1. 搜索现有组件: src/hooks/, src/utils/, src/components/
2. 检查API接口: src/api/
3. 确认类型定义: src/types/
4. 查看使用示例: src/examples/

# 必用组件
- 加载管理: useLoading
- 错误处理: useErrorHandler
- 存储操作: StoreUtil
- HTTP请求: http
- 设备信息: deviceInfo
- Token管理: tokenUtil

# 禁止行为
❌ 重复实现已有功能
❌ 直接使用uni API
❌ 硬编码配置信息
❌ 绕过安全机制
❌ 忽略类型定义
```
