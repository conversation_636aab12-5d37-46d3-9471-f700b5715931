# Hooks 使用规范指南

> 本文档定义了项目中 Hooks 目录的组织结构、开发规范和最佳实践

## 📋 目录结构

### 当前 Hooks 目录结构
```
src/hooks/
├─ index.ts              # 统一导出入口
├─ useAuth.ts            # 认证相关 Hook
├─ useCaptcha.ts         # 验证码相关 Hook
├─ useFormValidation.ts  # 表单验证 Hook
├─ useEnhancedFormPage.ts # 增强表单页面 Hook
├─ useErrorHandler.ts    # 错误处理 Hook
└─ usePage.ts           # 页面逻辑 Hook (新迁移)
```

### 推荐的目录组织
```
src/hooks/
├─ index.ts              # 统一导出入口
├─ auth/                 # 认证相关 Hooks
│  ├─ useAuth.ts
│  └─ useCaptcha.ts
├─ form/                 # 表单相关 Hooks
│  ├─ useFormValidation.ts
│  └─ useEnhancedFormPage.ts
├─ page/                 # 页面相关 Hooks
│  └─ usePage.ts
└─ utils/                # 工具类 Hooks
   └─ useErrorHandler.ts
```

## 🎯 Hooks 分类与职责

### 1. 页面逻辑 Hooks (`src/hooks/usePage.ts`)

**职责范围**:
- 页面状态管理
- 页面生命周期处理
- 通用页面逻辑封装

**使用示例**:
```typescript
// 导入方式
import { usePage, useListPage, useFormPage } from '@/hooks/usePage'
// 或者
import { usePage, useListPage, useFormPage } from '@/hooks'

// 在组件中使用
const { pageState, loading, error } = usePage({
  title: '用户列表',
  showBack: true
})
```

**迁移说明**:
- ✅ 已从 `@/pages/composables/usePage` 迁移至 `@/hooks/usePage`
- ✅ 包含 `usePage`、`useListPage`、`useFormPage` 三个函数
- ✅ 相关类型定义已同步迁移

### 2. 认证相关 Hooks

**包含 Hooks**:
- `useAuth.ts` - 用户认证逻辑
- `useCaptcha.ts` - 验证码处理

**使用示例**:
```typescript
import { useAuth } from '@/hooks'

const { login, logout, isLoggedIn } = useAuth()
```

### 3. 表单相关 Hooks

**包含 Hooks**:
- `useFormValidation.ts` - 表单验证逻辑
- `useEnhancedFormPage.ts` - 增强表单页面

**使用示例**:
```typescript
import { useFormValidation } from '@/hooks'

const { validate, errors, isValid } = useFormValidation()
```

### 4. 工具类 Hooks

**包含 Hooks**:
- `useErrorHandler.ts` - 错误处理逻辑

**使用示例**:
```typescript
import { useErrorHandler } from '@/hooks'

const { handleError, showError } = useErrorHandler()
```

## 📝 开发规范

### 1. 命名规范

**文件命名**:
- 使用 `use` + `功能名称` 的格式
- 采用 camelCase 命名
- 文件扩展名使用 `.ts`

**函数命名**:
- 与文件名保持一致
- 一个文件可以导出多个相关的 Hook 函数

**示例**:
```typescript
// ✅ 正确
usePage.ts -> export const usePage = () => {}
useAuth.ts -> export const useAuth = () => {}

// ❌ 错误
page.ts -> export const usePage = () => {}
auth-hook.ts -> export const useAuth = () => {}
```

### 2. 代码结构规范

**标准模板**:
```typescript
/**
 * [功能描述] Hook
 * @description 详细描述 Hook 的功能和用途
 */
import { ref, computed, reactive } from 'vue'
import type { [相关类型] } from '@/types'

export interface [Hook名称]Options {
  // 配置选项类型定义
}

export interface [Hook名称]Return {
  // 返回值类型定义
}

export const use[功能名称] = (options?: [Hook名称]Options): [Hook名称]Return => {
  // ==================== 状态定义 ====================
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // ==================== 计算属性 ====================
  const isReady = computed(() => !loading.value && !error.value)
  
  // ==================== 方法定义 ====================
  const handleAction = async () => {
    try {
      loading.value = true
      // 业务逻辑
    } catch (err: any) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }
  
  // ==================== 生命周期 ====================
  onMounted(() => {
    // 初始化逻辑
  })
  
  // ==================== 返回值 ====================
  return {
    // 状态
    loading: readonly(loading),
    error: readonly(error),
    // 计算属性
    isReady,
    // 方法
    handleAction
  }
}
```

### 3. 类型定义规范

**类型文件组织**:
```typescript
// 在 Hook 文件中定义相关类型
export interface UsePageOptions {
  title?: string
  showBack?: boolean
  showHome?: boolean
}

export interface UsePageReturn {
  pageState: Ref<PageState>
  setTitle: (title: string) => void
  goBack: () => void
  goHome: () => void
}

// 复杂类型可以单独定义文件
// src/types/hooks.ts
export * from '@/hooks/usePage'
export * from '@/hooks/useAuth'
```

### 4. 导入导出规范

**统一导出 (`src/hooks/index.ts`)**:
```typescript
// 认证相关
export * from './useAuth'
export * from './useCaptcha'

// 表单相关
export * from './useFormValidation'
export * from './useEnhancedFormPage'

// 页面相关
export * from './usePage'

// 工具相关
export * from './useErrorHandler'
```

**推荐的导入方式**:
```typescript
// ✅ 推荐：从统一入口导入
import { usePage, useAuth } from '@/hooks'

// ✅ 可接受：直接导入具体文件
import { usePage } from '@/hooks/usePage'

// ❌ 避免：导入废弃路径
import { usePage } from '@/pages/composables/usePage'
```

## 🔄 迁移指南

### 已完成的迁移

**usePage 相关 Hooks**:
- ✅ `src/pages/composables/usePage.ts` → `src/hooks/usePage.ts`
- ✅ 更新了 `src/hooks/index.ts` 导出
- ✅ 更新了 `src/pages/composables/index.ts` 注释
- ✅ 通过了类型检查验证

### 迁移验证清单

**文件迁移**:
- [x] 文件已成功移动到 `src/hooks/` 目录
- [x] 原文件路径已清理或添加迁移注释
- [x] 新路径已添加到 `src/hooks/index.ts`

**代码更新**:
- [x] 所有导入路径已更新
- [x] 类型检查通过 (`npm run type-check`)
- [x] 开发服务器正常运行

**文档更新**:
- [x] 创建 Hooks 使用规范指南
- [ ] 更新企业级开发规范文档
- [ ] 更新相关 API 文档

## 🚀 最佳实践

### 1. Hook 设计原则

**单一职责**:
- 每个 Hook 专注于一个特定功能
- 避免在一个 Hook 中处理过多不相关的逻辑

**可复用性**:
- 设计时考虑在不同组件中的复用
- 通过参数配置支持不同的使用场景

**类型安全**:
- 为所有 Hook 提供完整的 TypeScript 类型定义
- 使用泛型支持灵活的类型推导

### 2. 性能优化

**响应式优化**:
```typescript
// ✅ 使用 readonly 防止外部修改
return {
  loading: readonly(loading),
  data: readonly(data)
}

// ✅ 使用 shallowRef 优化大对象
const largeData = shallowRef({})

// ✅ 使用 computed 缓存计算结果
const processedData = computed(() => {
  return expensiveCalculation(data.value)
})
```

**内存管理**:
```typescript
// ✅ 清理定时器和事件监听
onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
  if (eventListener) {
    removeEventListener('resize', eventListener)
  }
})
```

### 3. 错误处理

**统一错误处理**:
```typescript
export const useApiCall = () => {
  const { handleError } = useErrorHandler()
  
  const fetchData = async () => {
    try {
      // API 调用
    } catch (error) {
      handleError(error, '数据获取失败')
    }
  }
  
  return { fetchData }
}
```

### 4. 测试建议

**单元测试**:
```typescript
// tests/hooks/usePage.test.ts
import { usePage } from '@/hooks/usePage'
import { mount } from '@vue/test-utils'

describe('usePage', () => {
  it('should initialize with default options', () => {
    const { pageState } = usePage()
    expect(pageState.value.title).toBe('')
  })
  
  it('should set title correctly', () => {
    const { pageState, setTitle } = usePage()
    setTitle('测试标题')
    expect(pageState.value.title).toBe('测试标题')
  })
})
```

## 📚 相关文档

- [企业级开发规范](./企业级开发规范.md) - 项目整体开发规范
- [Hooks目录重构迁移指南](./Hooks目录重构迁移指南.md) - 历史迁移记录
- [Vue 3 Composition API 官方文档](https://vuejs.org/guide/reusability/composables.html)

## 🔮 未来规划

### 短期目标
- [ ] 完善现有 Hooks 的单元测试
- [ ] 添加更多实用的工具类 Hooks
- [ ] 优化 Hooks 的性能和内存使用

### 长期目标
- [ ] 建立 Hooks 库的版本管理
- [ ] 考虑将通用 Hooks 抽取为独立的 npm 包
- [ ] 建立 Hooks 的自动化测试和文档生成

---

> 📝 **文档维护**: 本文档会随着项目发展持续更新，请定期查看最新版本
> 
> 🤝 **贡献指南**: 如有新的 Hooks 开发或规范建议，请提交 PR 或 Issue