# Token调试工具使用指南

## 📋 问题排查步骤

### 1. 访问调试页面
打开登录示例页面：`/pages/auth/loginExample`

### 2. 使用调试按钮

#### 🔍 完整诊断 (主要工具)
点击 **"完整诊断"** 按钮，查看控制台输出，检查：
- uni.storage中是否有refresh_token
- MultiStorage是否正确获取
- TokenManager是否能正确读取
- 内存缓存和备份存储状态

#### 🧪 测试保存
点击 **"测试保存"** 按钮，使用模拟数据测试Token保存流程

#### 🔧 设置测试Token
点击 **"设置测试Token"** 按钮，手动设置一个测试用的refresh_token

### 3. 控制台调试命令

如果页面调试按钮不可用，可以在浏览器控制台直接使用：

```javascript
// 完整诊断
window.TokenDebugger.diagnoseTokenStatus()

// 测试保存功能
window.TokenDebugger.testTokenSave()

// 设置测试refresh_token
window.TokenDebugger.setTestRefreshToken()

// 清除所有Token数据
window.TokenDebugger.clearAllTokenData()
```

## 🔍 常见问题排查

### 问题1: refresh_token获取不到

**排查步骤：**
1. 运行 `完整诊断`，查看各层存储状态
2. 检查 `uni.storage直接检查` 部分，看 `hasRefreshToken` 是否为 `是`
3. 如果uni.storage没有，检查 `备份存储检查`
4. 如果都没有，说明保存时就有问题

**可能原因：**
- 登录时没有正确传入refresh_token
- 存储时发生异常
- 存储权限问题

### 问题2: 存储了但TokenManager获取不到

**排查步骤：**
1. 对比 `uni.storage直接检查` 和 `TokenManager方法检查` 的结果
2. 如果uni.storage有但TokenManager没有，说明读取逻辑有问题
3. 检查 `MultiStorage检查` 部分

**可能原因：**
- MultiStorage读取逻辑异常
- 类型转换问题
- 存储键不匹配

### 问题3: 保存后立即丢失

**排查步骤：**
1. 使用 `测试保存` 功能，立即运行 `完整诊断`
2. 检查是否有异步清除操作
3. 查看错误日志

**可能原因：**
- 存储失败但没有报错
- 有其他代码清除了数据
- 浏览器存储限制

## 🛠️ 修复建议

### 方案1: 直接存储测试
```javascript
// 在控制台执行，直接测试uni.storage
const testData = {
  access_token: 'test_access_token',
  refresh_token: 'refresh_20250615194000_test',
  expire_in: 89,
  refresh_expire_in: 604800,
  obtain_time: Date.now()
}

uni.setStorageSync('token_data', testData)
console.log('存储结果:', uni.getStorageSync('token_data'))
```

### 方案2: 检查存储权限
```javascript
// 测试存储基本功能
try {
  uni.setStorageSync('test_key', 'test_value')
  const result = uni.getStorageSync('test_key')
  console.log('存储功能正常:', result === 'test_value')
  uni.removeStorageSync('test_key')
} catch (error) {
  console.error('存储功能异常:', error)
}
```

### 方案3: 绕过MultiStorage
```javascript
// 直接使用uni.storage，绕过MultiStorage
// 在TokenManager中临时修改获取逻辑
const directTokenData = uni.getStorageSync('token_data')
console.log('直接获取Token数据:', directTokenData)
```

## 📝 诊断报告模板

请将控制台输出的完整诊断信息发送，包括：

```
=== 🔍 Token状态完整诊断 ===

1. 存储键检查:
   - ACCESS_TOKEN_KEY: access_token
   - TOKEN_DATA_KEY: token_data

2. uni.storage直接检查:
   - uni.getStorageSync('access_token'): { ... }
   - uni.getStorageSync('token_data'): { ... }

3. MultiStorage检查:
   - MultiStorage.get('access_token'): { ... }
   - MultiStorage.get('token_data'): { ... }

4. TokenManager方法检查:
   - TokenManager.getTokenData(): { ... }
   - TokenManager状态: { ... }

5. 内存缓存检查:
   - 内存缓存状态: { ... }

6. 备份存储检查:
   - 备份存储状态: { ... }

=== 🔍 诊断完成 ===
```

## 🚨 紧急修复

如果完全无法存储refresh_token，可以临时使用以下方案：

```javascript
// 临时修复：直接在TokenManager中硬编码refresh_token用于测试
// 仅用于调试，不要在生产环境使用
const hardcodedRefreshToken = 'refresh_20250615194000_ef9647ed187a41df9cf5770ba53e2456_a4a155bc'

// 在TokenManager的_refreshTokenInternal方法中
const currentTokenData = this.getTokenData()
const refreshToken = currentTokenData?.refresh_token || hardcodedRefreshToken
```

这样可以先让刷新功能工作起来，再慢慢排查存储问题。