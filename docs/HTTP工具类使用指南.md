# HTTP工具类使用指南

> 基于 Axios 的 UniApp HTTP 工具类，支持多平台（H5、小程序、App）

## 📁 文件结构与命名规范

```
src/api/http/
├── http.ts          # HTTP 工具类主文件（遵循业务命名规范）
├── interceptors.ts  # 拦截器配置文件
├── tokenManager.ts  # Token 管理器（简化版）
└── auth.ts         # 统一导出文件
```

### 命名规范说明
- **文件命名**: 使用小写字母 + 驼峰命名，如 `http.ts`、`interceptors.ts`
- **避免使用**: `auth.ts` 作为业务主文件名，应使用具体的业务名称
- **导出文件**: 可以使用 `auth.ts` 作为统一导出入口
- **导入路径**: 统一使用 `@/api/http` 进行导入

## 📖 概述

本文档介绍如何使用项目中基于 Axios 的 HTTP 工具类，该工具类专为 UniApp 多平台开发设计，支持 H5、小程序、App 等平台。

## 🚀 快速开始

### 1. 导入HTTP工具类

```typescript
import { http } from '@/api/http'
```

### 2. 基础请求示例

```typescript
// GET 请求
const getUserInfo = async () => {
  try {
    const response = await http.get('/api/user/info')
    console.log('用户信息:', response.data)
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

// POST 请求
const createUser = async (userData: any) => {
  try {
    const response = await http.post('/api/user/create', userData)
    console.log('创建成功:', response.data)
  } catch (error) {
    console.error('创建用户失败:', error)
  }
}
```

## 🔧 HTTP 请求方法

### GET 请求

```typescript
// 基础 GET 请求
const response = await http.get('/api/data')

// 带参数的 GET 请求
const response = await http.get('/api/users', {
  params: {
    page: 1,
    size: 10,
    keyword: '搜索关键词'
  }
})

// 自定义配置
const response = await http.get('/api/data', {
  timeout: 10000,
  showLoading: false,
  showError: false
})
```

### POST 请求

```typescript
// 基础 POST 请求
const response = await http.post('/api/login', {
  username: 'admin',
  password: '123456'
})

// 表单数据提交
const formData = new FormData()
formData.append('file', file)
formData.append('name', 'filename')

const response = await http.post('/api/upload', formData, {
  headers: {
    'Content-Type': 'multipart/form-data'
  }
})
```

### PUT 请求

```typescript
// 更新数据
const response = await http.put('/api/user/123', {
  name: '新名称',
  email: '<EMAIL>'
})
```

### DELETE 请求

```typescript
// 删除数据
const response = await http.delete('/api/user/123')

// 批量删除
const response = await http.delete('/api/users', {
  data: {
    ids: [1, 2, 3, 4, 5]
  }
})
```

## 🔄 拦截器使用

### ⚡ 拦截器工作原理

**重要说明**: 拦截器是**配置型**的，只需要配置一次，之后所有通过该HTTP实例发送的请求都会自动应用这些拦截器。

- ✅ **全局生效**: 一次配置，所有请求都生效
- ✅ **自动应用**: 无需在每次请求时手动引入
- ✅ **链式处理**: 多个拦截器按添加顺序执行
- ✅ **持久有效**: 配置后在整个应用生命周期内有效

### 📍 拦截器配置位置

**🎯 当前项目架构（已优化）**：

```typescript
// ✅ 统一配置方式（当前使用）
// main.ts
import { setupAllInterceptors } from '@/api/http/interceptors'

export function createApp() {
  const app = createSSRApp(App)
  
  // 初始化HTTP拦截器（统一错误处理）
  setupAllInterceptors()
  
  return { app }
}
```

**📋 架构说明**：
- **统一管理**: 所有拦截器逻辑集中在 `src/api/http/interceptors.ts`
- **避免重复**: HttpUtil类专注于请求方法，不再设置拦截器
- **混合错误处理**: 支持自动处理和配置化处理
- **功能完整**: 包含Token管理、设备信息、请求追踪等

**❌ 已废弃的方式**：
```typescript
// ❌ 不再使用：HttpUtil内部设置拦截器
class HttpUtil {
  constructor() {
    this.setupInterceptors() // 已移除，避免重复
  }
}
```

### 🔧 创建拦截器配置文件

建议创建专门的拦截器配置文件：

```typescript
// /api/http/interceptors.ts
import { http } from './http'
import { TokenManager } from './tokenManager'

/**
 * 设置HTTP拦截器
 * 注意：此函数只需要调用一次，之后所有请求都会自动应用这些拦截器
 */
export const setupInterceptors = () => {
  // 请求拦截器
  setupRequestInterceptors()
  
  // 响应拦截器
  setupResponseInterceptors()
}

/**
 * 配置请求拦截器
 * 一次配置，所有请求都会自动应用
 */
const setupRequestInterceptors = () => {
  http.getInterceptors().request.use(
    async (config) => {
      console.log('🚀 [请求拦截器] 处理请求:', config.url)
      
      // 1. 自动添加Token
      try {
        const token = await TokenManager.getToken()
        if (token && config.requireAuth !== false) {
          config.headers.Authorization = `Bearer ${token}`
        }
      } catch (error) {
        console.warn('获取Token失败:', error)
      }
      
      // 2. 添加公共头部
      config.headers['Content-Type'] = config.headers['Content-Type'] || 'application/json'
      config.headers['X-Requested-With'] = 'XMLHttpRequest'
      
      // 3. 添加设备信息
      config.headers['X-Device-Type'] = getDeviceType()
      config.headers['X-Platform'] = getPlatform()
      
      // 4. 添加请求ID用于追踪
      config.headers['X-Request-ID'] = generateRequestId()
      
      // 5. 添加时间戳防止缓存（GET请求）
      if (config.method === 'get' && config.preventCache !== false) {
        config.params = {
          ...config.params,
          _t: Date.now()
        }
      }
      
      // 6. 显示加载提示
      if (config.showLoading !== false) {
        uni.showLoading({
          title: config.loadingText || '加载中...',
          mask: true
        })
      }
      
      return config
    },
    (error) => {
      console.error('❌ [请求拦截器] 请求错误:', error)
      uni.hideLoading()
      return Promise.reject(error)
    }
  )
}

/**
 * 配置响应拦截器
 * 一次配置，所有响应都会自动处理
 */
const setupResponseInterceptors = () => {
  http.getInterceptors().response.use(
    (response) => {
      console.log('✅ [响应拦截器] 收到响应:', response.config.url)
      
      // 隐藏加载提示
      uni.hideLoading()
      
      const { data } = response
      const config = response.config as any
      
      // 统一处理业务状态码
      if (data.code === 200) {
        return response
      } else if (data.code === 401) {
        // Token过期，自动处理
        console.warn('🔑 [响应拦截器] Token过期，尝试刷新')
        return handleTokenExpired(response.config)
      } else {
        // 业务错误，混合错误处理
        return handleMixedBusinessError({
          code: data.code,
          message: data.message || '操作失败',
          config,
          response
        })
      }
    },
    (error) => {
      console.error('❌ [响应拦截器] 响应错误:', error)
      
      // 隐藏加载提示
      uni.hideLoading()
      
      // 混合网络错误处理
      return handleMixedNetworkError(error)
    }
  )
}

// 辅助函数
/**
 * 获取设备类型
 */
const getDeviceType = (): string => {
  // #ifdef H5
  return 'h5'
  // #endif
  // #ifdef MP-WEIXIN
  return 'mp-weixin'
  // #endif
  // #ifdef APP-PLUS
  return 'app'
  // #endif
  return 'unknown'
}

/**
 * 获取平台信息
 */
const getPlatform = (): string => {
  const systemInfo = uni.getSystemInfoSync()
  return systemInfo.platform || 'unknown'
}

/**
 * 生成请求ID
 */
const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 处理Token过期
 */
const handleTokenExpired = async (originalConfig: any) => {
  try {
    // 刷新Token
    await TokenManager.refreshToken()
    
    // 重新发送原始请求
    const newToken = await TokenManager.getToken()
    originalConfig.headers.Authorization = `Bearer ${newToken}`
    
    return http.instance.request(originalConfig)
  } catch (refreshError) {
    // 刷新失败，跳转登录
    console.error('🔑 Token刷新失败，跳转登录页')
    await TokenManager.clearToken()
    uni.navigateTo({ url: '/pages/auth/login' })
    
    return Promise.reject(refreshError)
  }
}

/**
 * 混合业务错误处理
 */
const handleMixedBusinessError = (errorInfo: any) => {
  const { code, message, config, response } = errorInfo
  
  // 检查是否有自定义错误处理
  if (config.customErrorHandler && typeof config.customErrorHandler === 'function') {
    return config.customErrorHandler({ code, message, response })
  }
  
  // 默认错误处理
  if (config.showError !== false) {
    uni.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    })
  }
  
  return Promise.reject(new Error(message))
}

/**
 * 混合网络错误处理
 */
const handleMixedNetworkError = (error: any) => {
  const config = error.config || {}
  
  // 检查是否有自定义错误处理
  if (config.customErrorHandler && typeof config.customErrorHandler === 'function') {
    return config.customErrorHandler(error)
  }
  
  // 默认网络错误处理
  let message = '网络异常，请稍后重试'
  
  if (error.response) {
    // 服务器响应错误
    const { status, data } = error.response
    
    switch (status) {
      case 400:
        message = data?.message || '请求参数错误'
        break
      case 401:
        message = '登录已过期，请重新登录'
        break
      case 403:
        message = '没有权限访问'
        break
      case 404:
        message = '请求的资源不存在'
        break
      case 500:
        message = '服务器内部错误'
        break
      default:
        message = data?.message || `请求失败 (${status})`
    }
  } else if (error.request) {
    // 网络连接错误
    message = '网络连接失败，请检查网络设置'
  }
  
  // 显示错误提示
  if (config.showError !== false) {
    uni.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    })
  }
  
  console.error('HTTP请求错误:', {
    message,
    error,
    url: error.config?.url,
    method: error.config?.method
  })
  
  return Promise.reject(error)
}

/**
 * 统一响应错误处理
 */
const handleResponseError = (error: any) => {
  let message = '网络错误'
  
  if (error.response) {
    // HTTP状态码错误
    const { status, data } = error.response
    
    switch (status) {
      case 400:
        message = data?.message || '请求参数错误'
        break
      case 401:
        message = '未授权，请重新登录'
        break
      case 403:
        message = '权限不足'
        break
      case 404:
        message = '请求的资源不存在'
        break
      case 500:
        message = '服务器内部错误'
        break
      default:
        message = data?.message || `HTTP ${status} 错误`
    }
  } else if (error.request) {
    // 网络连接错误
    message = '网络连接失败，请检查网络设置'
  } else {
    // 其他错误
    message = error.message || '未知错误'
  }
  
  uni.showToast({
    title: message,
    icon: 'none',
    duration: 3000
  })
}
```

### 🎯 使用示例

#### 1. 应用启动时配置（推荐）

```typescript
// main.ts
import { createSSRApp } from 'vue'
import App from './App.vue'
import { setupInterceptors } from '@/api/http/interceptors'

export function createApp() {
  const app = createSSRApp(App)
  
  // 配置HTTP拦截器（只需要配置一次）
  setupInterceptors()
  
  return {
    app
  }
}
```

#### 2. 之后的所有请求都会自动应用拦截器

```typescript
// 任何页面或组件中
import { http } from '@/api/http'

// 这些请求都会自动应用拦截器，无需额外配置
const getUserInfo = async () => {
  // ✅ 自动添加Token
  // ✅ 自动显示加载提示
  // ✅ 自动处理错误
  // ✅ 自动处理Token过期
  const response = await http.get('/api/user/info')
  return response.data
}

const updateUser = async (userData: any) => {
  // ✅ 同样自动应用所有拦截器
  const response = await http.post('/api/user/update', userData)
  return response.data
}
```

### 🔄 动态拦截器管理

#### 添加新的拦截器

```typescript
// 可以在运行时添加新的拦截器
const addCustomInterceptor = () => {
  const interceptorId = http.instance.interceptors.request.use(
    (config) => {
      // 自定义逻辑
      config.headers['X-Custom-Header'] = 'custom-value'
      return config
    }
  )
  
  // 返回拦截器ID，用于后续移除
  return interceptorId
}
```

#### 移除拦截器

```typescript
// 移除特定的拦截器
const removeInterceptor = (interceptorId: number) => {
  http.instance.interceptors.request.eject(interceptorId)
}
```

#### 条件性拦截器

```typescript
// 根据条件决定是否应用拦截器逻辑
http.instance.interceptors.request.use((config) => {
  // 跳过某些请求的拦截器逻辑
  if (config.skipAuth) {
    return config // 跳过认证
  }
  
  if (config.skipLoading) {
    // 跳过加载提示，但继续其他逻辑
  } else {
    uni.showLoading({ title: '加载中...' })
  }
  
  return config
})

// 使用时指定跳过某些拦截器逻辑
const response = await http.get('/api/data', {
  skipAuth: true,     // 跳过认证
  skipLoading: true   // 跳过加载提示
})
```

### 📋 拦截器最佳实践

#### 1. 统一配置

```typescript
// ✅ 推荐：在应用启动时统一配置
setupInterceptors()

// ❌ 不推荐：在每个请求前配置
http.instance.interceptors.request.use(/* ... */)
const response = await http.get('/api/data')
```

#### 2. 模块化管理

```typescript
// /api/http/interceptors/auth.ts
export { setupInterceptors } from './setup'
export { authInterceptor } from './auth'
export { errorInterceptor } from './error'
export { loadingInterceptor } from './loading'

// /api/http/interceptors/setup.ts
import { authInterceptor } from './auth'
import { errorInterceptor } from './error'
import { loadingInterceptor } from './loading'

export const setupInterceptors = () => {
  authInterceptor.setup()
  errorInterceptor.setup()
  loadingInterceptor.setup()
}
```

#### 3. 环境区分

```typescript
// 根据环境配置不同的拦截器
export const setupInterceptors = () => {
  // 基础拦截器（所有环境）
  setupBaseInterceptors()
  
  // 开发环境专用
  if (process.env.NODE_ENV === 'development') {
    setupDevInterceptors()
  }
  
  // 生产环境专用
  if (process.env.NODE_ENV === 'production') {
    setupProdInterceptors()
  }
}
```

### ❓ 常见问题

**Q: 拦截器需要在每次请求时配置吗？**
A: 不需要。拦截器是配置型的，只需要在应用启动时配置一次，之后所有请求都会自动应用。

**Q: 如何跳过某个拦截器的逻辑？**
A: 在请求配置中添加标识字段，在拦截器中判断该字段来决定是否执行相应逻辑。

**Q: 可以添加多个拦截器吗？**
A: 可以。多个拦截器会按照添加顺序依次执行。

**Q: 拦截器什么时候失效？**
A: 拦截器在整个应用生命周期内都有效，除非手动移除或应用重启。

## 🚫 跳过拦截器

### 跳过请求拦截器

```typescript
// 使用原生 axios 实例跳过所有拦截器
import axios from 'axios'

const directRequest = async () => {
  try {
    const response = await axios.get('/api/data', {
      // 直接使用 axios，不经过工具类拦截器
      baseURL: 'https://api.example.com',
      timeout: 5000
    })
    return response.data
  } catch (error) {
    console.error('直接请求失败:', error)
  }
}
```

### 跳过特定拦截器逻辑

```typescript
// 在请求配置中添加标识跳过某些拦截器逻辑
const response = await http.get('/api/data', {
  // 自定义配置，在拦截器中判断
  skipAuth: true,        // 跳过认证
  skipLoading: true,     // 跳过加载提示
  skipErrorHandler: true // 跳过错误处理
})

// 在拦截器中判断
http.instance.interceptors.request.use((config) => {
  // 跳过认证逻辑
  if (!config.skipAuth) {
    // 添加认证头部
    config.headers.Authorization = `Bearer ${token}`
  }
  
  return config
})
```

## ⚠️ 异常处理

### 1. 网络异常

```typescript
const handleNetworkError = async () => {
  try {
    const response = await http.get('/api/data')
    return response.data
  } catch (error: any) {
    if (!error.response) {
      // 网络连接失败
      console.error('网络连接失败:', error.message)
      uni.showToast({
        title: '网络连接失败，请检查网络设置',
        icon: 'none',
        duration: 3000
      })
    }
    throw error
  }
}
```

### 2. HTTP 状态码异常

```typescript
const handleHttpError = async () => {
  try {
    const response = await http.get('/api/data')
    return response.data
  } catch (error: any) {
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          console.error('请求参数错误:', data.message)
          break
        case 401:
          console.error('未授权，请重新登录')
          // 跳转到登录页
          uni.navigateTo({ url: '/pages/login/login' })
          break
        case 403:
          console.error('权限不足')
          break
        case 404:
          console.error('请求的资源不存在')
          break
        case 500:
          console.error('服务器内部错误')
          break
        default:
          console.error(`HTTP错误: ${status}`)
      }
    }
    throw error
  }
}
```

### 3. 业务异常

```typescript
const handleBusinessError = async () => {
  try {
    const response = await http.post('/api/user/create', userData)
    
    // 检查业务状态码
    if (response.data.code !== 200) {
      throw new Error(response.data.message || '操作失败')
    }
    
    return response.data.data
  } catch (error: any) {
    // 统一错误处理
    console.error('业务操作失败:', error.message)
    
    uni.showToast({
      title: error.message || '操作失败',
      icon: 'none'
    })
    
    throw error
  }
}
```

### 4. 超时异常

```typescript
const handleTimeout = async () => {
  try {
    const response = await http.get('/api/slow-data', {
      timeout: 5000 // 5秒超时
    })
    return response.data
  } catch (error: any) {
    if (error.code === 'ECONNABORTED') {
      console.error('请求超时')
      uni.showToast({
        title: '请求超时，请稍后重试',
        icon: 'none'
      })
    }
    throw error
  }
}
```

## 📁 文件上传下载

### 文件上传

```typescript
// 选择并上传文件
const uploadFile = async () => {
  try {
    // 选择文件
    const chooseResult = await uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera']
    })
    
    const filePath = chooseResult.tempFilePaths[0]
    
    // 上传文件
    const response = await http.upload('/api/upload', {
      filePath,
      name: 'file',
      formData: {
        userId: '123',
        category: 'avatar'
      }
    })
    
    console.log('上传成功:', response.data)
  } catch (error) {
    console.error('上传失败:', error)
  }
}
```

### 文件下载

```typescript
// 下载文件
const downloadFile = async () => {
  try {
    const response = await http.download('/api/download/file.pdf')
    
    console.log('下载成功，文件路径:', response.tempFilePath)
    
    // 保存到相册（图片）
    if (response.tempFilePath.includes('.jpg') || response.tempFilePath.includes('.png')) {
      await uni.saveImageToPhotosAlbum({
        filePath: response.tempFilePath
      })
    }
  } catch (error) {
    console.error('下载失败:', error)
  }
}
```

## ⚙️ 配置选项

### 请求配置

```typescript
interface RequestConfig {
  // 基础配置
  baseURL?: string          // 基础URL
  timeout?: number          // 超时时间（毫秒）
  
  // 显示控制
  showLoading?: boolean     // 是否显示加载提示
  showError?: boolean       // 是否显示错误提示
  loadingText?: string      // 加载提示文本
  
  // 认证控制
  requireAuth?: boolean     // 是否需要认证
  
  // 重试配置
  retry?: number           // 重试次数
  retryDelay?: number      // 重试延迟（毫秒）
}

// 使用示例
const response = await http.get('/api/data', {
  timeout: 10000,
  showLoading: true,
  loadingText: '正在加载数据...',
  requireAuth: true,
  retry: 3,
  retryDelay: 1000
})
```

## 🔐 认证和Token管理

### 自动Token管理

```typescript
// HTTP工具类会自动处理Token
// 1. 自动添加Authorization头部
// 2. Token过期时自动刷新
// 3. 刷新失败时跳转登录页

// 手动设置Token
import { TokenManager } from '@/utils/tokenManager'

const setToken = async (accessToken: string, refreshToken: string) => {
  await TokenManager.setToken({
    accessToken,
    refreshToken,
    expiresIn: 3600 // 1小时
  })
}

// 清除Token
const logout = async () => {
  await TokenManager.clearToken()
  uni.navigateTo({ url: '/pages/login/login' })
}
```

## 📱 平台适配

### H5平台

```typescript
// H5平台支持所有标准HTTP功能
const h5Request = async () => {
  const response = await http.get('/api/data', {
    // H5特有配置
    withCredentials: true, // 携带Cookie
    responseType: 'json'   // 响应类型
  })
}
```

### 小程序平台

```typescript
// 小程序平台会自动使用uni.request
const miniProgramRequest = async () => {
  const response = await http.get('/api/data')
  // 自动适配小程序的网络请求限制
}
```

### App平台

```typescript
// App平台支持更多网络功能
const appRequest = async () => {
  const response = await http.get('/api/data', {
    // App特有配置
    sslVerify: false // SSL验证
  })
}
```

## 🐛 调试技巧

### 开启调试模式

```typescript
// 在开发环境开启详细日志
if (process.env.NODE_ENV === 'development') {
  // 请求日志
  http.instance.interceptors.request.use((config) => {
    console.log('🚀 [HTTP Request]', {
      method: config.method?.toUpperCase(),
      url: config.url,
      params: config.params,
      data: config.data,
      headers: config.headers
    })
    return config
  })
  
  // 响应日志
  http.instance.interceptors.response.use((response) => {
    console.log('✅ [HTTP Response]', {
      status: response.status,
      url: response.config.url,
      data: response.data
    })
    return response
  })
}
```

### 网络状态检测

```typescript
// 检测网络状态
const checkNetwork = async () => {
  try {
    const networkInfo = await uni.getNetworkType()
    console.log('网络类型:', networkInfo.networkType)
    
    if (networkInfo.networkType === 'none') {
      uni.showToast({
        title: '网络连接不可用',
        icon: 'none'
      })
      return false
    }
    
    return true
  } catch (error) {
    console.error('获取网络状态失败:', error)
    return false
  }
}

// 在请求前检查网络
const safeRequest = async () => {
  const hasNetwork = await checkNetwork()
  if (!hasNetwork) {
    throw new Error('网络不可用')
  }
  
  return await http.get('/api/data')
}
```

## 📚 最佳实践

### 1. 统一错误处理

```typescript
// 创建统一的错误处理函数
const handleApiError = (error: any, context?: string) => {
  console.error(`API错误 ${context ? `[${context}]` : ''}:`, error)
  
  let message = '操作失败'
  
  if (error.response) {
    // HTTP错误
    message = error.response.data?.message || `HTTP ${error.response.status} 错误`
  } else if (error.request) {
    // 网络错误
    message = '网络连接失败'
  } else {
    // 其他错误
    message = error.message || '未知错误'
  }
  
  uni.showToast({
    title: message,
    icon: 'none',
    duration: 3000
  })
}

// 使用示例
const getUserList = async () => {
  try {
    const response = await http.get('/api/users')
    return response.data
  } catch (error) {
    handleApiError(error, '获取用户列表')
    throw error
  }
}
```

### 2. 请求缓存

```typescript
// 简单的内存缓存
const cache = new Map()

const getCachedData = async (url: string, ttl: number = 5 * 60 * 1000) => {
  const cacheKey = url
  const cached = cache.get(cacheKey)
  
  if (cached && Date.now() - cached.timestamp < ttl) {
    console.log('使用缓存数据:', url)
    return cached.data
  }
  
  try {
    const response = await http.get(url)
    cache.set(cacheKey, {
      data: response.data,
      timestamp: Date.now()
    })
    return response.data
  } catch (error) {
    // 如果有缓存数据，在网络错误时返回缓存
    if (cached) {
      console.log('网络错误，使用过期缓存:', url)
      return cached.data
    }
    throw error
  }
}
```

### 3. 请求队列管理

```typescript
// 限制并发请求数量
class RequestQueue {
  private queue: Array<() => Promise<any>> = []
  private running = 0
  private maxConcurrent = 3
  
  async add<T>(requestFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const result = await requestFn()
          resolve(result)
        } catch (error) {
          reject(error)
        }
      })
      
      this.process()
    })
  }
  
  private async process() {
    if (this.running >= this.maxConcurrent || this.queue.length === 0) {
      return
    }
    
    this.running++
    const requestFn = this.queue.shift()!
    
    try {
      await requestFn()
    } finally {
      this.running--
      this.process()
    }
  }
}

const requestQueue = new RequestQueue()

// 使用队列发送请求
const queuedRequest = async (url: string) => {
  return requestQueue.add(() => http.get(url))
}
```

## 🔗 相关链接

- [Axios 官方文档](https://axios-http.com/)
- [UniApp 网络请求文档](https://uniapp.dcloud.net.cn/api/request/request.html)
- [TypeScript 类型定义](https://www.typescriptlang.org/docs/)

---

**注意**: 本文档基于项目实际的HTTP工具类编写，使用前请确保已正确安装相关依赖并配置好开发环境。
