# 🔒 环境变量安全使用指南

> **重要提醒**: 本项目的环境变量文件已加入版本管理，请严格遵循安全使用规范

## ⚠️ 安全警告

### 🚨 敏感信息风险
本项目的 `.env.local` 文件已被加入版本管理，这意味着：
- 所有有仓库访问权限的人都能看到配置内容
- 配置信息会被永久保存在Git历史中
- 需要特别注意敏感信息的保护

### 🛡️ 安全要求
1. **仓库访问控制**: 确保仓库为私有，严格控制访问权限
2. **敏感信息审查**: 定期检查配置文件，确保不包含真正敏感的密钥
3. **团队安全意识**: 所有团队成员都需要了解配置文件的敏感性

## 📁 环境变量文件说明

### 已加入版本管理的文件
```bash
✅ .env.local              # 本地环境配置 (已跟踪)
✅ .env                    # 基础环境配置 (已跟踪)
✅ .env.development        # 开发环境配置 (已跟踪)
✅ .env.production         # 生产环境配置 (已跟踪)
✅ .env.local.example      # 环境变量示例 (已跟踪)
```

### 仍被排除的文件
```bash
🔒 .env.staging.local      # 预发布环境敏感配置 (仍排除)
🔒 .env.secrets           # 敏感配置文件 (仍排除)
🔒 config/secrets.json    # 敏感配置JSON (仍排除)
```

## 🔧 配置文件使用规范

### 1. .env.local 使用规范
```bash
# ✅ 可以包含的配置
VITE_APP_TITLE=乐享生活平台
VITE_API_BASE_URL=http://localhost:3000
VITE_DEBUG_MODE=true

# ⚠️ 谨慎包含的配置
VITE_THIRD_PARTY_API_KEY=test_key_only  # 仅测试密钥
DATABASE_URL=sqlite://./dev.db          # 仅开发数据库

# ❌ 绝对不能包含的配置
# PRODUCTION_DATABASE_PASSWORD=xxx      # 生产数据库密码
# PAYMENT_SECRET_KEY=xxx                # 支付密钥
# REAL_API_SECRET=xxx                   # 真实API密钥
```

### 2. 配置分层管理
```bash
# 基础配置 (.env)
VITE_APP_NAME=OTO-UI
VITE_APP_VERSION=1.0.0

# 环境特定配置 (.env.development)
VITE_API_BASE_URL=http://dev-api.example.com
VITE_LOG_LEVEL=debug

# 本地覆盖配置 (.env.local)
VITE_API_BASE_URL=http://localhost:3000  # 覆盖开发环境配置
VITE_MOCK_API=true                       # 本地特定配置
```

## 🛠️ 安全最佳实践

### 1. 配置内容审查清单
在修改环境变量文件前，请检查：
- [ ] 是否包含真实的API密钥？
- [ ] 是否包含生产数据库凭据？
- [ ] 是否包含支付相关密钥？
- [ ] 是否包含第三方服务的敏感令牌？
- [ ] 是否包含可能暴露系统架构的信息？

### 2. 团队协作规范
```bash
# 修改配置文件前
1. 与团队确认修改的必要性
2. 确保新增配置不包含敏感信息
3. 在提交信息中明确说明变更内容

# 提交配置文件时
git add .env.local
git commit -m "🔧 更新本地环境配置: 添加新的API端点"

# 推送前再次确认
git log --oneline -1  # 检查提交信息
git show HEAD         # 检查变更内容
```

### 3. 定期安全检查
```bash
# 每月执行一次安全检查
1. 检查 .env.local 文件内容
2. 确认没有新增敏感信息
3. 验证仓库访问权限设置
4. 更新过期的测试密钥
```

## 🚨 应急处理

### 如果意外提交了敏感信息
```bash
# 1. 立即撤销提交 (如果还未推送)
git reset --soft HEAD~1
git reset HEAD .env.local
# 编辑文件移除敏感信息
git add .env.local
git commit -m "🔧 修复配置文件敏感信息"

# 2. 如果已经推送到远程
# 立即联系团队，考虑以下措施：
# - 更换泄露的密钥
# - 重写Git历史 (谨慎操作)
# - 通知相关服务提供商
```

### 敏感信息泄露处理流程
1. **立即行动**: 停止使用泄露的密钥
2. **更换密钥**: 在相关服务中生成新密钥
3. **通知团队**: 告知所有相关人员
4. **更新配置**: 使用新密钥更新配置
5. **安全审查**: 检查是否有其他潜在风险

## 📞 联系方式

### 安全问题报告
如果发现配置文件中的安全问题，请立即联系：
- 项目负责人
- 技术团队负责人
- 安全团队 (如有)

### 配置问题咨询
如果对环境变量配置有疑问，请参考：
- 本文档的使用规范
- `.env.local.example` 示例文件
- 团队内部技术文档

## 📚 相关文档

- [AI编程助手开发规范.md](./AI编程助手开发规范.md)
- [企业级开发规范.md](./企业级开发规范.md)
- [多环境部署和配置管理指南.md](./多环境部署和配置管理指南.md)

---

**🔒 记住：安全是团队的共同责任，请严格遵循本指南的要求！**
