# 刷新接口适配说明

## 📋 接口适配详情

### 原始后端接口设计
**接口路径**: `POST /member/auth/refresh`

**请求参数**:
```json
{
  "refreshToken": "refresh_20250615193824_9cc8d7407468498a9019f17e2e9a3189_a4a155bc"
}
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "refresh_20250615194000_ef9647ed187a41df9cf5770ba53e2456_a4a155bc",
    "expire_in": 89,
    "refresh_expire_in": 604703
  }
}
```

## 🔧 前端适配实现

### 1. API接口定义更新 (`src/api/modules/auth.ts`)
```typescript
/**
 * 刷新Token（支持跨平台双Token模式）
 * App/小程序：通过请求体传递refreshToken
 * H5：通过HttpOnly Cookie自动携带refresh_token
 */
export const refreshToken = (params?: { refreshToken?: string }) => {
  return request.post<{
    access_token: string
    refresh_token: string
    expire_in: number
    refresh_expire_in: number
  }>('/member/auth/refresh', params)
}
```

### 2. TokenManager刷新逻辑适配 (`src/utils/tokenManager.ts`)

#### 关键适配点:
1. **请求参数格式**: 使用 `{"refreshToken": "..."}` 而不是空请求体
2. **响应数据解析**: 从 `response.data.data` 中获取token信息
3. **跨平台兼容**: H5和App/小程序都通过请求体传递refreshToken

#### 适配后的刷新流程:
```typescript
// 获取当前refresh_token
const currentTokenData = this.getTokenData()
const refreshToken = currentTokenData?.refresh_token

// H5环境 - 使用fetch + 请求体
fetch('/member/auth/refresh', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  credentials: 'include',
  body: JSON.stringify({ refreshToken })
})

// App/小程序环境 - 使用统一API调用
const { refreshToken: authRefreshToken } = await import('@/api/modules/auth')
const apiResponse = await authRefreshToken({ refreshToken })
```

### 3. 响应数据处理
```typescript
if (response.statusCode === 200 && response.data?.code === 200) {
  const data = response.data.data // 从data.data中获取token信息
  
  const newTokenData = {
    access_token: data.access_token,
    refresh_token: data.refresh_token, // 新的refresh_token
    expire_in: data.expire_in,
    refresh_expire_in: data.refresh_expire_in,
  }
  
  this.setToken(newTokenData)
}
```

## 🎯 适配验证

### 预期行为:
1. **请求格式正确**: 所有平台都发送 `{"refreshToken": "..."}`
2. **响应解析正确**: 正确从 `response.data.data` 获取新token
3. **Token更新**: 同时更新 access_token 和 refresh_token
4. **跨平台兼容**: H5、App、小程序都能正常工作

### 测试方法:
1. 登录后等待access_token即将过期(剩余500ms以内)
2. 触发自动刷新或手动调用 `TokenManager.refreshToken()`
3. 检查网络请求中的参数格式和响应解析是否正确
4. 验证新的token是否正确保存

## 🚨 注意事项

1. **安全性**: refresh_token仍然敏感，需要安全存储
2. **过期处理**: refresh_token过期时需要重新登录
3. **错误处理**: 401错误表示refresh_token无效，需要清除本地数据
4. **并发控制**: 防止多个刷新请求同时发起

## 📝 更新记录

- **2024-01-15**: 初始适配，支持新的请求参数格式
- **2024-01-15**: 统一H5和App环境的请求方式
- **2024-01-15**: 完善响应数据解析逻辑 