# 无感刷新Token技术方案

## 📋 概述

基于后端返回的登录接口数据，设计一套完整的无感access_token刷新机制，确保用户在使用过程中不会因为access_token过期而被强制重新登录。

## 🔍 接口分析

### 登录接口返回数据结构

**请求方式**: `POST /member/auth/login`

**响应体 (Response Body):**
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "scope": null,
        "openid": null,
        "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "refresh_token": null,
        "expire_in": 89,
        "refresh_expire_in": 604800,
        "client_id": null
    }
}
```

**响应头 (Response Headers):**
```http
Set-Cookie: refresh_token=refresh_20250615122005_b0641c7935c34615810e481e995a647a_a4a155bc; HttpOnly; Secure; Path=/member/auth/refresh; Max-Age=604800; SameSite=None
```

### 刷新Token接口

**请求方式**: `POST /member/auth/refresh`

**请求头 (Request Headers):**
```http
Cookie: refresh_token=refresh_20250615122005_b0641c7935c34615810e481e995a647a_a4a155bc
```

**请求体**: 无需传参，通过HttpOnly Cookie携带refresh_token

**响应体 (Response Body):**
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "expire_in": 89,
        "refresh_expire_in": 604800
    }
}
```

**响应头 (Response Headers):**
```http
Set-Cookie: refresh_token=refresh_20250615130005_new_token_value; HttpOnly; Secure; Path=/member/auth/refresh; Max-Age=604800; SameSite=None
```

### 关键字段说明
| 字段/位置 | 类型 | 说明 | 示例值 | 单位 |
|-----------|------|------|--------|------|
| `access_token` (响应体) | string | 访问令牌，用于API调用 | JWT格式token | - |
| `refresh_token` (Set-Cookie) | string | 刷新令牌，HttpOnly Cookie | refresh_20250615122005_... | - |
| `expire_in` (响应体) | number | access_token过期时间 | 89 | 秒(s) |
| `refresh_expire_in` (响应体) | number | refresh_token过期时间 | 604800 | 秒(s) |

### 重要说明
- **access_token**: 存储在本地，用于API请求认证
- **refresh_token**: 通过HttpOnly Cookie自动管理，无法通过JavaScript访问
- **token清除策略**: access_token的本地存储时间与refresh_token的过期时间保持一致
- **刷新策略**: 当access_token剩余有效时间小于300秒时立即触发刷新
- **时间单位**: 所有时间相关字段均以秒(s)为单位

## 🏗️ 技术架构

### 核心原理
1. **双Token机制**：
   - `access_token`：短期有效（89秒），存储在本地，用于API调用
   - `refresh_token`：长期有效（604800秒，即7天），通过HttpOnly Cookie自动管理

2. **无感刷新策略**：
   - 当access_token剩余有效时间小于300秒时立即触发刷新
   - 使用HttpOnly Cookie中的refresh_token进行刷新
   - 刷新成功后更新本地存储的access_token数据

3. **生命周期管理**：
   - access_token本地存储时间 = refresh_expire_in（604800秒）
   - 只有refresh_token过期才需要重新登录
   - access_token过期只需要自动刷新

## 📦 实现方案

### 1. Token数据存储结构

```typescript
interface TokenData {
  /** 访问令牌 */
  access_token: string
  /** access_token过期秒数 */
  expire_in: number
  /** refresh_token过期秒数（用于计算本地存储清除时间） */
  refresh_expire_in: number
  /** token获取时间戳 */
  obtain_time: number
}
```

### 2. 存储策略

```typescript
// 登录成功后保存token数据
const saveTokenData = (loginResponse: LoginResponse) => {
  const obtainTime = Date.now()
  
  // 保存到本地存储 - 注意：本地存储时间与refresh_token过期时间一致
  uni.setStorageSync('access_token', loginResponse.data.access_token)
  uni.setStorageSync('expire_in', loginResponse.data.expire_in)
  uni.setStorageSync('refresh_expire_in', loginResponse.data.refresh_expire_in)
  uni.setStorageSync('obtain_time', obtainTime)
  
  // 计算过期时间（注意：后端返回的时间单位为秒，需要转换为毫秒）
  const accessTokenExpireTime = obtainTime + (loginResponse.data.expire_in * 1000)
  const refreshTokenExpireTime = obtainTime + (loginResponse.data.refresh_expire_in * 1000)
  
  console.log('Token过期时间:')
  console.log('- access_token过期:', new Date(accessTokenExpireTime).toLocaleString())
  console.log('- refresh_token过期:', new Date(refreshTokenExpireTime).toLocaleString())
  console.log('- 本地存储清除时间:', new Date(refreshTokenExpireTime).toLocaleString())
  
  // 设置本地存储自动清除（与refresh_token过期时间一致）
  setTimeout(() => {
    console.log('refresh_token过期，清除本地access_token数据')
    clearLocalTokenData()
  }, loginResponse.data.refresh_expire_in * 1000) // 转换为毫秒
}

// 清除本地token数据
const clearLocalTokenData = () => {
  uni.removeStorageSync('access_token')
  uni.removeStorageSync('expire_in')
  uni.removeStorageSync('refresh_expire_in')
  uni.removeStorageSync('obtain_time')
}
```

### 3. 刷新时机判断

```typescript
// 检查是否需要刷新access_token
const shouldRefreshAccessToken = (): boolean => {
  const accessToken = uni.getStorageSync('access_token')
  const expireIn = Number(uni.getStorageSync('expire_in'))
  const obtainTime = Number(uni.getStorageSync('obtain_time'))
  
  if (!accessToken || !expireIn || !obtainTime) {
    return false
  }
  
  // 计算access_token剩余时间（单位：秒）
  const now = Date.now()
  const elapsedSeconds = Math.floor((now - obtainTime) / 1000)
  const remainingSeconds = expireIn - elapsedSeconds
  
  // 当剩余时间小于300秒时立即触发刷新
  const REFRESH_THRESHOLD = 300 // 300秒
  
  console.log('[TokenRefresh] access_token状态检查:', {
    expireIn: `${expireIn}秒`,
    elapsedSeconds: `${elapsedSeconds}秒`,
    remainingSeconds: `${remainingSeconds}秒`,
    threshold: `${REFRESH_THRESHOLD}秒`,
    needRefresh: remainingSeconds < REFRESH_THRESHOLD && remainingSeconds > 0
  })
  
  return remainingSeconds < REFRESH_THRESHOLD && remainingSeconds > 0
}

// 检查refresh_token是否过期
const isRefreshTokenExpired = (): boolean => {
  const refreshExpireIn = Number(uni.getStorageSync('refresh_expire_in'))
  const obtainTime = Number(uni.getStorageSync('obtain_time'))
  
  if (!refreshExpireIn || !obtainTime) {
    return true
  }
  
  const now = Date.now()
  const elapsedSeconds = Math.floor((now - obtainTime) / 1000)
  const refreshRemainingSeconds = refreshExpireIn - elapsedSeconds
  
  return refreshRemainingSeconds <= 0
}
```

### 4. 刷新access_token实现

```typescript
// 刷新access_token
const refreshAccessToken = async (): Promise<boolean> => {
  try {
    // 检查refresh_token是否过期
    if (isRefreshTokenExpired()) {
      console.log('refresh_token已过期，需要重新登录')
      clearLocalTokenData()
      uni.reLaunch({ url: '/pages/auth/login' })
      return false
    }
    
    // 调用刷新接口（HttpOnly Cookie会自动携带refresh_token）
    const response = await request.post('/member/auth/refresh', {}, {
      withCredentials: true // 确保携带Cookie
    })
    
    if (response.code === 200) {
      // 更新本地access_token数据
      const newTokenData = {
        access_token: response.data.access_token,
        expire_in: response.data.expire_in,
        refresh_expire_in: response.data.refresh_expire_in || 604800,
        obtain_time: Date.now()
      }
      
      // 保存新的access_token数据
      uni.setStorageSync('access_token', newTokenData.access_token)
      uni.setStorageSync('expire_in', newTokenData.expire_in)
      uni.setStorageSync('refresh_expire_in', newTokenData.refresh_expire_in)
      uni.setStorageSync('obtain_time', newTokenData.obtain_time)
      
      console.log('access_token刷新成功')
      console.log(`- 新的access_token有效期: ${newTokenData.expire_in}秒`)
      console.log(`- refresh_token剩余有效期: ${newTokenData.refresh_expire_in}秒`)
      
      return true
    }
    
    return false
  } catch (error) {
    console.error('access_token刷新失败:', error)
    
    // 如果是401错误，说明refresh_token过期
    if (error?.status === 401 || error?.code === 401) {
      console.log('refresh_token过期，清除本地数据并跳转登录')
      clearLocalTokenData()
      uni.reLaunch({ url: '/pages/auth/login' })
    }
    
    return false
  }
}
```

### 5. 请求拦截器集成（兜底方案）

```typescript
// 请求拦截器
request.interceptors.request.use((config) => {
  // 主动检查：在每次请求前检查是否需要刷新access_token
  if (shouldRefreshAccessToken()) {
    console.log('请求拦截器检测到需要刷新access_token')
    // 异步刷新access_token（不阻塞当前请求）
    refreshAccessToken().catch(console.error)
  }
  
  // 添加access_token到请求头
  const accessToken = uni.getStorageSync('access_token')
  if (accessToken) {
    config.headers.Authorization = `Bearer ${accessToken}`
  }
  
  return config
})

// 响应拦截器（兜底方案）
request.interceptors.response.use(
  (response) => response,
  async (error) => {
    // 兜底方案：如果收到401错误，说明定时器可能失效或刷新失败
    if (error.response?.status === 401 || error?.code === 401) {
      console.log('收到401错误，触发兜底刷新机制')
      
      // 检查refresh_token是否过期
      if (isRefreshTokenExpired()) {
        console.log('refresh_token已过期，跳转登录页')
        clearLocalTokenData()
        uni.reLaunch({ url: '/pages/auth/login' })
        return Promise.reject(error)
      }
      
      // 尝试刷新access_token
      const refreshSuccess = await refreshAccessToken()
      
      if (refreshSuccess) {
        // 刷新成功，重试原请求
        const originalRequest = error.config
        const newAccessToken = uni.getStorageSync('access_token')
        originalRequest.headers.Authorization = `Bearer ${newAccessToken}`
        
        console.log('兜底刷新成功，重试原请求')
        return request(originalRequest)
      } else {
        // 刷新失败，说明refresh_token过期或其他问题
        console.log('兜底刷新失败，跳转登录页')
        clearLocalTokenData()
        uni.reLaunch({ url: '/pages/modules/auth/login/login' })
      }
    }
    
    return Promise.reject(error)
  }
)
```

### 兜底方案说明

1. **双重保障机制**：
   - **主动检查**：请求拦截器在每次请求前检查是否需要刷新
   - **被动兜底**：响应拦截器在收到401错误时触发刷新

2. **兜底触发场景**：
   - 定时器异常停止或失效
   - 定时器刷新失败但未被捕获
   - 网络异常导致的刷新失败
   - 应用长时间后台运行后恢复

3. **兜底处理流程**：
   ```
   收到401错误 → 检查refresh_token是否过期 → 
   未过期：尝试刷新 → 成功：重试原请求 / 失败：跳转登录
   已过期：直接跳转登录
   ```

## ⏰ 定时刷新机制

### 1. 定时器管理

```typescript
class AccessTokenRefreshManager {
  private refreshTimer: number | null = null
  private readonly REFRESH_INTERVAL = 60000 // 1分钟检查一次（60000毫秒）
  
  // 启动定时刷新
  startRefreshTimer() {
    this.stopRefreshTimer() // 先清除已有定时器
    
    this.refreshTimer = setInterval(() => {
      // 检查refresh_token是否过期
      if (isRefreshTokenExpired()) {
        console.log('refresh_token过期，停止定时器并清除数据')
        this.stopRefreshTimer()
        clearLocalTokenData()
        return
      }
      
      // 检查是否需要刷新access_token（剩余时间小于300秒时触发）
      if (shouldRefreshAccessToken()) {
        console.log('定时器触发access_token刷新（剩余时间小于300秒）')
        refreshAccessToken()
      }
    }, this.REFRESH_INTERVAL)
    
    console.log('access_token刷新定时器已启动（每60秒检查一次）')
  }
  
  // 停止定时刷新
  stopRefreshTimer() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
      console.log('access_token刷新定时器已停止')
    }
  }
  
  // 检查并启动定时器
  checkAndStart() {
    const accessToken = uni.getStorageSync('access_token')
    
    if (accessToken && !isRefreshTokenExpired()) {
      this.startRefreshTimer()
    } else {
      console.log('无有效token或refresh_token过期，不启动定时器')
    }
  }
}

// 全局实例
const accessTokenRefreshManager = new AccessTokenRefreshManager()
```

### 2. 应用生命周期集成

```typescript
// App.vue
onShow(() => {
  console.log('应用显示，检查并启动access_token刷新定时器')
  accessTokenRefreshManager.checkAndStart()
})

onHide(() => {
  console.log('应用隐藏，停止access_token刷新定时器（节省资源）')
  accessTokenRefreshManager.stopRefreshTimer()
})
```

## 🔄 完整流程图

```mermaid
graph TD
    A[用户登录] --> B[保存access_token数据]
    B --> C[设置refresh_token过期清除]
    C --> D[启动定时刷新]
    D --> E{检查refresh_token是否过期}
    E -->|是| F[清除本地数据并跳转登录]
    E -->|否| G{检查是否需要刷新access_token}
    G -->|是| H[调用刷新接口]
    G -->|否| I[继续检查]
    H --> J{刷新成功?}
    J -->|是| K[更新本地access_token]
    J -->|否| L{refresh_token过期?}
    L -->|是| F
    L -->|否| M[重试刷新]
    K --> I
    I --> E
    M --> H
    
    %% 兜底方案流程
    N[API请求] --> O{收到401错误?}
    O -->|否| P[正常响应]
    O -->|是| Q[触发兜底刷新机制]
    Q --> R{refresh_token过期?}
    R -->|是| F
    R -->|否| S[尝试刷新access_token]
    S --> T{刷新成功?}
    T -->|是| U[重试原请求]
    T -->|否| F
    U --> V[请求成功]
```

## 🛡️ 安全考虑

### 1. HttpOnly Cookie安全性
- **refresh_token通过HttpOnly Cookie传递**，防止XSS攻击
- **设置Secure属性**：仅在HTTPS下传输
- **设置SameSite=None**：支持跨站请求
- **Path=/member/auth/refresh**：限制Cookie作用域

### 2. Token有效期策略
- **access_token短期有效（89秒）**：减少泄露风险
- **refresh_token长期有效（7天）**：平衡安全性和用户体验
- **本地存储与refresh_token同步过期**：避免无效数据残留

### 3. 错误处理
- 网络错误不清除用户登录状态
- 只有确认的refresh_token过期才要求重新登录
- 401错误优先尝试刷新，失败后再跳转登录

## 📱 跨平台适配

### 1. 条件编译
```typescript
// #ifdef H5
// H5环境下的特殊处理
document.addEventListener('visibilitychange', () => {
  if (!document.hidden) {
    accessTokenRefreshManager.checkAndStart()
  } else {
    accessTokenRefreshManager.stopRefreshTimer()
  }
})
// #endif

// #ifdef APP-PLUS
// App环境下的特殊处理
plus.globalEvent.addEventListener('resume', () => {
  accessTokenRefreshManager.checkAndStart()
})
plus.globalEvent.addEventListener('pause', () => {
  accessTokenRefreshManager.stopRefreshTimer()
})
// #endif
```

### 2. Cookie处理兼容性
```typescript
// 确保跨平台Cookie支持
const requestConfig = {
  withCredentials: true,
  // #ifdef H5
  credentials: 'include',
  // #endif
  // #ifdef APP-PLUS
  // App端自动处理Cookie
  // #endif
}
```

## 🧪 测试验证

### 1. 功能测试
- [ ] 登录后access_token正确保存
- [ ] refresh_token通过HttpOnly Cookie正确设置
- [ ] 定时器正常启动和停止
- [ ] access_token过期前自动刷新
- [ ] 刷新失败时正确处理
- [ ] refresh_token过期时清除本地数据
- [ ] 页面切换不影响登录状态

### 2. 兜底方案测试
- [ ] 定时器停止后401错误触发刷新
- [ ] 定时器刷新失败后401错误兜底
- [ ] 网络异常恢复后401错误处理
- [ ] 应用长时间后台运行后的401处理
- [ ] 并发请求时的401错误处理
- [ ] refresh_token过期时的401错误处理

### 3. 边界测试
- [ ] 网络断开时的处理
- [ ] 服务器返回异常时的处理
- [ ] 多个请求并发时的access_token刷新
- [ ] 应用后台运行时的处理
- [ ] refresh_token过期边界情况
- [ ] 定时器异常停止的恢复机制

## 📊 监控指标

### 1. 关键指标
- access_token刷新成功率
- access_token刷新响应时间
- 用户重新登录频率
- 接口401错误率
- refresh_token过期率

### 2. 日志记录
```typescript
// 详细的日志记录
console.log('[TokenRefresh] Token状态:', {
  accessToken: accessToken ? '存在' : '不存在',
  accessTokenExpireIn: `${expireIn}秒`,
  accessTokenRemainingTime: `${remainingSeconds}秒`,
  refreshTokenRemainingTime: `${refreshRemainingSeconds}秒`,
  refreshThreshold: '300秒',
  needRefresh: shouldRefresh,
  refreshTokenExpired: isRefreshTokenExpired()
})
```

## 🚀 部署建议

### 1. 环境配置
- **开发环境**：较短的access_token有效期（89秒）便于测试
- **生产环境**：合理的access_token有效期平衡安全性和性能
- **Cookie配置**：确保Secure、HttpOnly、SameSite属性正确设置

### 2. 监控告警
- 设置access_token刷新失败率告警
- 监控用户重新登录频率异常
- 监控refresh_token过期率

## 📝 总结

这套无感刷新access_token方案具有以下特点：

✅ **用户体验优秀**：用户无需感知access_token刷新过程
✅ **安全性高**：使用HttpOnly Cookie和短期access_token
✅ **生命周期同步**：本地存储与refresh_token过期时间一致
✅ **刷新策略精准**：基于access_token的expire_in字段判断
✅ **跨平台兼容**：支持H5、小程序、App等多端
✅ **容错性强**：完善的错误处理和重试机制
✅ **兜底保障**：双重刷新机制确保定时器失效时的自动恢复

### 关键改进点：
1. **明确token概念**：access_token用于API调用，refresh_token用于刷新
2. **HttpOnly Cookie管理**：refresh_token通过Set-Cookie自动管理
3. **生命周期同步**：本地存储清除时间与refresh_token过期时间一致
4. **精准刷新策略**：当access_token剩余有效时间小于300秒时立即触发刷新
5. **时间单位统一**：所有时间相关字段和计算均以秒(s)为单位
6. **双重保障机制**：定时器主动刷新 + 401错误兜底刷新，确保系统稳定性

通过这套方案，可以显著提升用户体验，减少因access_token过期导致的登录中断问题。

## 📐 过期时间计算公式

### 1. 基础时间变量

```typescript
// 登录时获取的时间戳（毫秒）
const obtain_time = Date.now()

// 后端返回的过期时间（秒）
const expire_in = 89          // access_token有效期：89秒
const refresh_expire_in = 604800  // refresh_token有效期：604800秒（7天）
```

### 2. 过期时间点计算

```typescript
// access_token过期时间点（毫秒）
const access_token_expire_time = obtain_time + (expire_in * 1000)

// refresh_token过期时间点（毫秒）
const refresh_token_expire_time = obtain_time + (refresh_expire_in * 1000)
```

### 3. 剩余时间计算

```typescript
// 当前时间戳（毫秒）
const current_time = Date.now()

// 已经过去的时间（秒）
const elapsed_seconds = Math.floor((current_time - obtain_time) / 1000)

// access_token剩余时间（秒）
const access_token_remaining_seconds = expire_in - elapsed_seconds

// refresh_token剩余时间（秒）
const refresh_token_remaining_seconds = refresh_expire_in - elapsed_seconds
```

### 4. 刷新触发条件

```typescript
// 刷新阈值（秒）
const REFRESH_THRESHOLD = 300  // 300秒 = 5分钟

// 是否需要刷新access_token的判断条件
const should_refresh = access_token_remaining_seconds < REFRESH_THRESHOLD && access_token_remaining_seconds > 0

// 是否refresh_token过期的判断条件
const is_refresh_token_expired = refresh_token_remaining_seconds <= 0
```

### 5. 完整计算示例

假设登录时间为：`2025-06-15 12:20:05`

```typescript
// 示例数据
const obtain_time = 1718434805000  // 2025-06-15 12:20:05的时间戳
const expire_in = 89               // 89秒后过期
const refresh_expire_in = 604800   // 7天后过期
const REFRESH_THRESHOLD = 300      // 300秒阈值

// 计算过期时间点
const access_token_expire_time = 1718434805000 + (89 * 1000) = 1718434894000
// 对应时间：2025-06-15 12:21:34

const refresh_token_expire_time = 1718434805000 + (604800 * 1000) = 1719039605000
// 对应时间：2025-06-22 12:20:05

// 假设当前时间为：2025-06-15 12:21:00（登录后55秒）
const current_time = 1718434860000

// 计算剩余时间
const elapsed_seconds = Math.floor((1718434860000 - 1718434805000) / 1000) = 55
const access_token_remaining_seconds = 89 - 55 = 34
const refresh_token_remaining_seconds = 604800 - 55 = 604745

// 判断是否需要刷新
const should_refresh = 34 < 300 && 34 > 0 = true  // 需要刷新
const is_refresh_token_expired = 604745 <= 0 = false  // refresh_token未过期
```

### 6. 关键时间节点

| 时间节点 | 计算公式 | 说明 |
|----------|----------|------|
| 登录时间 | `obtain_time` | 获取token的时间戳（毫秒） |
| 开始刷新时间 | `obtain_time + (expire_in - 300) * 1000` | access_token剩余300秒时开始刷新 |
| access_token过期时间 | `obtain_time + expire_in * 1000` | access_token完全过期的时间 |
| refresh_token过期时间 | `obtain_time + refresh_expire_in * 1000` | refresh_token过期，需要重新登录 |
| 本地数据清除时间 | `obtain_time + refresh_expire_in * 1000` | 与refresh_token过期时间一致 |

### 7. 触发刷新的时间窗口

```typescript
// 刷新时间窗口计算
const refresh_start_time = obtain_time + (expire_in - REFRESH_THRESHOLD) * 1000
const refresh_end_time = obtain_time + expire_in * 1000

// 示例：expire_in = 89秒，REFRESH_THRESHOLD = 300秒
// 由于89 < 300，所以从登录开始就应该立即刷新
// 实际项目中，如果expire_in较小，可能需要调整REFRESH_THRESHOLD

// 更合理的阈值计算（动态调整）
const dynamic_threshold = Math.min(REFRESH_THRESHOLD, Math.floor(expire_in * 0.8))
// 使用expire_in的80%作为阈值，但不超过300秒
```

### 8. 边界情况处理

```typescript
// 情况1：expire_in小于REFRESH_THRESHOLD
if (expire_in <= REFRESH_THRESHOLD) {
  // 建议：使用expire_in的80%作为阈值
  const adjusted_threshold = Math.floor(expire_in * 0.8)
  const should_refresh = access_token_remaining_seconds < adjusted_threshold && access_token_remaining_seconds > 0
}

// 情况2：已经过期但refresh_token未过期
if (access_token_remaining_seconds <= 0 && !is_refresh_token_expired) {
  // 立即刷新
  const should_refresh = true
}

// 情况3：refresh_token即将过期
if (refresh_token_remaining_seconds < 3600) { // 小于1小时
  // 建议提醒用户即将需要重新登录
  console.warn('refresh_token将在1小时内过期，建议提醒用户')
}
```