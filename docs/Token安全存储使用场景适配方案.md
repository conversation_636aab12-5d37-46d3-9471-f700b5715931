# Token安全存储使用场景适配方案

## 📋 方案概述

本方案基于已实现的安全存储架构，提供完整的Token管理使用场景，包括自动刷新、请求拦截、生命周期管理等功能，确保refresh_token的安全性和系统的稳定性。

## 🏗️ 核心架构

### 1. 安全存储层
- **SecureStorageAdapter**: 跨平台安全存储适配器，支持XOR加密
- **TokenUtil**: 统一Token管理工具，集成安全存储
- **AuthStorageAdapter**: 认证存储适配器，专门处理认证相关数据

### 2. HTTP拦截层
- **请求拦截器**: 自动添加Token到请求头
- **响应拦截器**: 处理401错误，自动刷新Token
- **错误处理**: 统一的错误处理和用户提示

### 3. 生命周期管理
- **应用启动**: 初始化Token状态和自动刷新机制
- **应用显示/隐藏**: 智能管理定时器，节省资源
- **页面路由**: 根据页面类型决定是否启动Token检查

## 🔄 完整使用流程

### 1. 用户登录场景

```typescript
// 登录页面 - pages/auth/login.vue
import { tokenUtil } from '@/utils/token/TokenUtil'
import { authStorageAdapter } from '@/utils/storage/adapters/AuthStorageAdapter'

const handleLogin = async (loginData: LoginParams) => {
  try {
    // 1. 调用登录API
    const response = await passwordLogin(loginData)
    
    // 2. 保存Token到安全存储
    await tokenUtil.saveToken({
      accessToken: response.data.access_token,
      refreshToken: response.data.refresh_token,
      expiresAt: Date.now() + (response.data.expire_in || 7200) * 1000
    })
    
    // 3. 保存用户信息
    await authStorageAdapter.saveUserInfo(response.data.userInfo)
    
    // 4. 启动自动刷新机制（TokenUtil内部自动处理）
    console.log('✅ 登录成功，Token已保存到安全存储')
    
    // 5. 跳转到首页
    uni.reLaunch({ url: '/pages/home/<USER>' })
    
  } catch (error) {
    console.error('❌ 登录失败:', error)
    // 错误处理
  }
}
```

### 2. 自动Token刷新场景

```typescript
// TokenUtil内部自动处理
// 1. 定时检查（每5分钟前自动刷新）
// 2. 请求前检查（每次API调用前）
// 3. 401响应处理（兜底机制）

// 手动触发刷新示例
const refreshTokenManually = async () => {
  try {
    const success = await tokenUtil.refreshToken()
    if (success) {
      console.log('✅ Token刷新成功')
    } else {
      console.log('❌ Token刷新失败，需要重新登录')
      // 自动跳转到登录页
    }
  } catch (error) {
    console.error('Token刷新异常:', error)
  }
}
```

### 3. API请求场景

```typescript
// 业务页面 - 任意页面
import { http } from '@/http/http'

// 所有API请求都会自动处理Token
const fetchUserData = async () => {
  try {
    // 1. 请求拦截器自动添加Token
    // 2. 如果Token即将过期，自动刷新
    // 3. 如果收到401，自动刷新并重试
    const response = await http.get('/api/user/profile')
    return response.data
  } catch (error) {
    // 如果刷新失败，会自动跳转登录页
    console.error('获取用户数据失败:', error)
  }
}

// 批量请求场景
const fetchMultipleData = async () => {
  try {
    const [userInfo, orders, notifications] = await Promise.all([
      http.get('/api/user/info'),
      http.get('/api/orders'),
      http.get('/api/notifications')
    ])
    
    // 所有请求都会自动处理Token，无需手动管理
    return { userInfo, orders, notifications }
  } catch (error) {
    console.error('批量请求失败:', error)
  }
}
```

### 4. 应用生命周期场景

```typescript
// App.vue - 应用入口
import { tokenUtil } from '@/utils/token/TokenUtil'
import { setupInterceptors } from '@/http/interceptors'
import { integratedStorageManager } from '@/utils/storage'

onLaunch(async () => {
  try {
    // 1. 初始化存储系统
    await integratedStorageManager.initialize()
    
    // 2. 初始化HTTP拦截器
    setupInterceptors()
    
    // 3. 检查Token状态并启动自动刷新
    const tokenStatus = await tokenUtil.getTokenStatus()
    console.log('Token状态:', tokenStatus)
    
    // TokenUtil内部会自动处理定时刷新
    console.log('✅ 应用启动完成')
  } catch (error) {
    console.error('❌ 应用启动失败:', error)
  }
})

onShow(async () => {
  // 应用显示时，TokenUtil会自动检查并恢复定时器
  console.log('应用显示，Token管理器已激活')
})

onHide(() => {
  // 应用隐藏时，TokenUtil会自动暂停定时器（节省资源）
  console.log('应用隐藏，Token管理器已暂停')
})
```

### 5. 用户登出场景

```typescript
// 用户中心页面或任意需要登出的地方
import { tokenUtil } from '@/utils/token/TokenUtil'
import { authStorageAdapter } from '@/utils/storage/adapters/AuthStorageAdapter'

const handleLogout = async () => {
  try {
    // 1. 调用登出API（可选）
    await http.post('/api/auth/logout')
    
    // 2. 清除所有Token和认证数据
    await tokenUtil.clearToken()
    await authStorageAdapter.clearAuthState()
    
    // 3. 清除安全存储中的refresh_token
    // （已在clearToken和clearAuthState中自动处理）
    
    console.log('✅ 登出成功')
    
    // 4. 跳转到登录页
    uni.reLaunch({ url: '/pages/auth/login' })
    
  } catch (error) {
    console.error('登出失败:', error)
    // 即使API调用失败，也要清除本地数据
    await tokenUtil.clearToken()
    await authStorageAdapter.clearAuthState()
    uni.reLaunch({ url: '/pages/auth/login' })
  }
}
```

## 🛡️ 安全特性

### 1. refresh_token安全存储
- **加密存储**: 使用XOR加密算法
- **平台适配**: 支持H5、App、小程序等平台
- **密钥管理**: 自动生成和管理加密密钥
- **数据隔离**: 安全存储与普通存储分离

### 2. 自动迁移机制
```typescript
// AuthStorageAdapter中的自动迁移逻辑
const getRefreshToken = async (): Promise<string | null> => {
  try {
    // 1. 优先从安全存储获取
    let refreshToken = await secureStorage.getItem(AUTH_KEYS.REFRESH_TOKEN)
    
    if (!refreshToken) {
      // 2. 从普通存储获取（兼容旧数据）
      refreshToken = await unifiedStorageManager.get(AUTH_KEYS.REFRESH_TOKEN, StorageType.MULTI)
      
      if (refreshToken) {
        // 3. 迁移到安全存储
        await secureStorage.setItem(AUTH_KEYS.REFRESH_TOKEN, refreshToken)
        await unifiedStorageManager.remove(AUTH_KEYS.REFRESH_TOKEN, StorageType.MULTI)
        console.log('✅ refresh_token已迁移到安全存储')
      }
    }
    
    return refreshToken
  } catch (error) {
    console.error('获取refresh_token失败:', error)
    return null
  }
}
```

### 3. 防并发刷新
```typescript
// TokenUtil中的防并发机制
private refreshPromise: Promise<boolean> | null = null

async refreshToken(): Promise<boolean> {
  // 防止并发刷新
  if (this.refreshPromise) {
    return this.refreshPromise
  }
  
  this.refreshPromise = this.doRefreshToken()
  
  try {
    const result = await this.refreshPromise
    return result
  } finally {
    this.refreshPromise = null
  }
}
```

## 📊 监控和调试

### 1. 日志监控
```typescript
// 启用详细日志
console.log('🔐 [SecureStorage] 初始化完成，平台:', platform)
console.log('💾 [TokenUtil] Token保存成功，过期时间:', new Date(expiresAt))
console.log('🔄 [TokenUtil] 自动刷新触发，剩余时间:', remainingTime)
console.log('✅ [HTTP] 请求成功，自动添加Token')
console.log('⚠️ [HTTP] 收到401错误，触发自动刷新')
```

### 2. 状态检查工具
```typescript
// 开发调试工具
const debugTokenStatus = async () => {
  const status = await tokenUtil.getTokenStatus()
  const refreshToken = await tokenUtil.getRefreshToken()
  
  console.table({
    'Token状态': status.status,
    '是否有效': status.isValid,
    '即将过期': status.isExpiringSoon,
    '剩余时间': `${Math.round(status.remainingTime / 1000 / 60)}分钟`,
    '过期时间': new Date(status.expiresAt).toLocaleString(),
    'RefreshToken': refreshToken ? '已设置' : '未设置'
  })
}

// 在控制台调用：debugTokenStatus()
```

## 🚨 异常处理

### 1. 网络异常
- **重试机制**: 自动重试刷新请求
- **降级策略**: 网络异常时延长检查间隔
- **用户提示**: 友好的错误提示信息

### 2. 存储异常
- **备用存储**: 安全存储失败时使用普通存储
- **数据恢复**: 自动尝试从备用位置恢复数据
- **错误上报**: 记录存储异常日志

### 3. Token异常
- **过期处理**: 自动跳转登录页
- **格式错误**: 清除无效Token数据
- **刷新失败**: 多次重试后跳转登录

## 📱 平台兼容性

### 1. H5平台
- 使用localStorage作为底层存储
- 支持页面刷新后数据恢复
- 处理浏览器隐私模式限制

### 2. App平台
- 使用原生存储API
- 支持应用后台/前台切换
- 处理应用重启后数据恢复

### 3. 小程序平台
- 使用小程序存储API
- 处理小程序生命周期
- 支持小程序版本更新

## 🎯 最佳实践

### 1. 开发建议
- 始终使用tokenUtil进行Token操作
- 不要直接操作底层存储API
- 在页面onLoad中检查登录状态
- 使用try-catch处理异步操作

### 2. 性能优化
- 合理设置刷新阈值（默认5分钟）
- 避免频繁的Token状态检查
- 使用防抖机制避免重复刷新
- 在应用隐藏时暂停定时器

### 3. 安全建议
- 定期更新加密算法
- 监控异常登录行为
- 及时清理过期数据
- 避免在日志中输出敏感信息

## 📋 集成检查清单

- [ ] SecureStorageAdapter已正确初始化
- [ ] TokenUtil已集成安全存储
- [ ] HTTP拦截器已配置Token自动添加
- [ ] 401错误处理已配置自动刷新
- [ ] App.vue已启用生命周期管理
- [ ] 登录页面已使用新的Token保存方式
- [ ] 登出功能已更新清除逻辑
- [ ] 所有API调用已使用统一的http实例
- [ ] 错误处理已配置用户友好提示
- [ ] 开发调试工具已配置

通过以上完整的使用场景适配方案，可以确保Token管理的安全性、稳定性和用户体验的最优化。